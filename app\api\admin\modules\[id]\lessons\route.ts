import { NextRequest, NextResponse } from 'next/server'
import { requirePlatformAdmin } from '@/lib/auth/middleware'
import { createAdminClient } from '@/lib/supabase-admin'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const moduleId = params.id
    const supabase = createAdminClient()

    // Fetch lessons for the module
    const { data: lessons, error: lessonsError } = await supabase
      .from('course_lessons')
      .select('*')
      .eq('module_id', moduleId)
      .order('sequence_order', { ascending: true })

    if (lessonsError) {
      console.error('Error fetching lessons:', lessonsError)
      return NextResponse.json(
        { error: 'Failed to fetch lessons' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      lessons: lessons || []
    })

  } catch (error) {
    console.error('Error in GET /api/admin/modules/[id]/lessons:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const moduleId = params.id
    const body = await request.json()

    const {
      name,
      description,
      lesson_type = 'video',
      content_url,
      content_data = {},
      content_source = 'url',
      content_file_url,
      content_file_name,
      content_file_size,
      content_file_type,
      estimated_duration,
      is_mandatory = true,
      status = 'draft',
      sequence_order,
      has_assessment = false,
      assessment_type,
      weight_in_module = 0,
      required_software = [],
      downloadable_resources = [],
      external_links = [],
      lesson_prerequisites = [],
      video_duration,
      transcript,
      reading_time,
      slide_count,
      passing_score,
      attempts_allowed = 3,
      time_limit,
      submission_format = [],
      max_file_size,
      // Quiz-specific fields
      quiz_instructions,
      quiz_generation_method,
      quiz_question_count,
      quiz_question_types,
      quiz_learning_objectives
    } = body

    // Validation
    if (!name?.trim()) {
      return NextResponse.json(
        { error: 'Lesson name is required' },
        { status: 400 }
      )
    }

    const supabase = createAdminClient()

    // If no sequence_order provided, get the next available order
    let finalSequenceOrder = sequence_order
    if (!finalSequenceOrder) {
      const { data: lastLesson } = await supabase
        .from('course_lessons')
        .select('sequence_order')
        .eq('module_id', moduleId)
        .order('sequence_order', { ascending: false })
        .limit(1)
        .single()

      finalSequenceOrder = (lastLesson?.sequence_order || 0) + 1
    }

    // Create the lesson
    const { data: lesson, error: lessonError } = await supabase
      .from('course_lessons')
      .insert({
        module_id: moduleId,
        name: name.trim(),
        description: description?.trim() || '',
        lesson_type,
        content_url: content_file_url || content_url?.trim() || null,
        content_data,
        content_source,
        content_file_url,
        content_file_name,
        content_file_size,
        content_file_type,
        estimated_duration,
        is_mandatory,
        status,
        sequence_order: finalSequenceOrder,
        has_assessment,
        assessment_type: has_assessment ? assessment_type : null,
        weight_in_module,
        required_software,
        downloadable_resources,
        external_links,
        lesson_prerequisites,
        video_duration,
        transcript: transcript?.trim() || null,
        reading_time,
        slide_count,
        passing_score,
        attempts_allowed,
        time_limit,
        submission_format,
        max_file_size
      })
      .select()
      .single()

    if (lessonError) {
      console.error('Error creating lesson:', lessonError)
      return NextResponse.json(
        { error: 'Failed to create lesson' },
        { status: 500 }
      )
    }

    // Handle quiz creation if lesson type is quiz
    if (lesson_type === 'quiz' && quiz_generation_method === 'ai') {
      try {
        // Store quiz configuration for AI generation
        const quizConfig = {
          lesson_id: lesson.id,
          generation_method: quiz_generation_method,
          question_count: quiz_question_count || 10,
          question_types: quiz_question_types ? [quiz_question_types] : ['multiple_choice'],
          learning_objectives: quiz_learning_objectives?.trim() || '',
          instructions: quiz_instructions?.trim() || '',
          passing_score: passing_score || 70,
          attempts_allowed: attempts_allowed || 3,
          time_limit: time_limit || null
        }

        // Store in content_data for now (we can create a separate table later if needed)
        await supabase
          .from('course_lessons')
          .update({
            content_data: { quiz_config: quizConfig }
          })
          .eq('id', lesson.id)

      } catch (quizError) {
        console.error('Error setting up quiz configuration:', quizError)
        // Don't fail the lesson creation, just log the error
      }
    }

    return NextResponse.json({
      lesson,
      message: 'Lesson created successfully'
    })

  } catch (error) {
    console.error('Error in POST /api/admin/modules/[id]/lessons:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
