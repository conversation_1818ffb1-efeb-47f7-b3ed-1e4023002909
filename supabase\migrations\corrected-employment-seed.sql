-- Luna Employment-Based Seed Data (Corrected)
-- Only create employment relationships for existing users

-- Step 1: Set platform admin role for admin user
UPDATE users SET role = 'platform_admin' WHERE email = '<EMAIL>';

-- Step 2: Set all other users to individual role
UPDATE users SET role = 'individual' WHERE email != '<EMAIL>';

-- Step 3: Insert Organizations (created_by is now nullable)
INSERT INTO organizations (
  id, name, slug, description, industry, size_range, 
  subscription_tier, subdomain, website_url, status
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  'TechCorp Solutions',
  'techcorp',
  'Leading technology solutions provider',
  'Technology',
  'large',
  'enterprise',
  'techcorp',
  'https://techcorp.com',
  'active'
),
(
  '*************-2222-2222-************',
  'Creative Agency Inc',
  'creative-agency',
  'Full-service creative and marketing agency',
  'Marketing',
  'medium',
  'professional',
  'creative',
  'https://creativeagency.com',
  'active'
),
(
  '*************-3333-3333-************',
  'StartupHub',
  'startuphub',
  'Innovation and startup incubator',
  'Consulting',
  'small',
  'basic',
  'startup',
  'https://startuphub.com',
  'active'
);

-- Step 4: Insert Departments
INSERT INTO departments (
  id, organization_id, name, description
) VALUES 
-- TechCorp Departments
(
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111',
  'Engineering',
  'Software development and technical operations'
),
(
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '11111111-1111-1111-1111-111111111111',
  'Product Management',
  'Product strategy and roadmap planning'
),
(
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  '11111111-1111-1111-1111-111111111111',
  'Human Resources',
  'People operations and talent management'
),
-- Creative Agency Departments
(
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  '*************-2222-2222-************',
  'Design',
  'Creative design and visual branding'
),
(
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  '*************-2222-2222-************',
  'Marketing',
  'Digital marketing and campaign management'
),
-- StartupHub Departments
(
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  '*************-3333-3333-************',
  'Innovation Lab',
  'Research and development initiatives'
);

-- Step 5: Create Employment Relationships ONLY for existing users
-- First, let's see what users we have and assign them appropriately

-- TechCorp: Organization Admin (Sarah if exists)
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'organization_admin',
  'active',
  'Chief Technology Officer',
  CURRENT_DATE - INTERVAL '1 year',
  NOW()
FROM users u 
WHERE u.email = '<EMAIL>' 
AND EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');

-- TechCorp: Department Admin for Engineering (John if exists)
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'department_admin',
  'active',
  'Engineering Manager',
  CURRENT_DATE - INTERVAL '8 months',
  NOW()
FROM users u 
WHERE u.email = '<EMAIL>' 
AND EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');

-- Creative Agency: Organization Admin (Mike if exists)
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  '*************-2222-2222-************',
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'organization_admin',
  'active',
  'Creative Director',
  CURRENT_DATE - INTERVAL '2 years',
  NOW()
FROM users u 
WHERE u.email = '<EMAIL>' 
AND EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');

-- StartupHub: Organization Admin (Alex if exists)
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  '*************-3333-3333-************',
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  'organization_admin',
  'active',
  'Innovation Director',
  CURRENT_DATE - INTERVAL '6 months',
  NOW()
FROM users u 
WHERE u.email = '<EMAIL>' 
AND EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>');

-- Additional assignments for any other existing users
-- Assign remaining users as staff members to different departments
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status,
  job_title, hire_date, joined_at
)
SELECT
  u.id,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid,
  'staff_member',
  'active',
  'Product Manager',
  CURRENT_DATE - INTERVAL '6 months',
  NOW()
FROM users u
WHERE u.email NOT IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
AND u.id NOT IN (SELECT user_id FROM employment_relationships)
LIMIT 1;

-- Assign another user to Creative Agency Marketing
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status,
  job_title, hire_date, joined_at
)
SELECT
  u.id,
  '*************-2222-2222-************'::uuid,
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'::uuid,
  'department_admin',
  'active',
  'Marketing Manager',
  CURRENT_DATE - INTERVAL '1 year',
  NOW()
FROM users u
WHERE u.email NOT IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>')
AND u.id NOT IN (SELECT user_id FROM employment_relationships)
LIMIT 1;

-- Assign remaining users as staff members
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status,
  job_title, hire_date, joined_at
)
SELECT
  u.id,
  CASE
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 1 THEN '11111111-1111-1111-1111-111111111111'::uuid
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 2 THEN '*************-2222-2222-************'::uuid
    ELSE '*************-3333-3333-************'::uuid
  END,
  CASE
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 1 THEN 'cccccccc-cccc-cccc-cccc-cccccccccccc'::uuid
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 2 THEN 'dddddddd-dddd-dddd-dddd-dddddddddddd'::uuid
    ELSE 'ffffffff-ffff-ffff-ffff-ffffffffffff'::uuid
  END,
  'staff_member',
  'active',
  'Team Member',
  CURRENT_DATE - INTERVAL '3 months',
  NOW()
FROM users u
WHERE u.email != '<EMAIL>'
AND u.id NOT IN (SELECT user_id FROM employment_relationships);

-- Step 6: Set Department Heads
UPDATE departments SET department_head_id = (
  SELECT user_id FROM employment_relationships 
  WHERE department_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa' 
  AND role IN ('department_admin', 'organization_admin')
  LIMIT 1
) WHERE id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

UPDATE departments SET department_head_id = (
  SELECT user_id FROM employment_relationships 
  WHERE department_id = 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee' 
  AND role IN ('department_admin', 'organization_admin')
  LIMIT 1
) WHERE id = 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee';

-- Step 7: Create Individual Contexts for ALL Users (dual-context system)
INSERT INTO user_contexts (
  user_id, context_type, active_organization_id, 
  active_department_id, active_employment_id, last_org_context
) 
SELECT 
  u.id,
  'individual', -- Everyone starts in individual context
  NULL,
  NULL,
  NULL,
  er.organization_id -- Remember their last/primary organization for quick switching
FROM users u
LEFT JOIN employment_relationships er ON u.id = er.user_id AND er.status = 'active'
WHERE u.email != '<EMAIL>' -- Skip platform admin
ON CONFLICT (user_id) DO UPDATE SET
  context_type = 'individual',
  last_org_context = EXCLUDED.last_org_context;
