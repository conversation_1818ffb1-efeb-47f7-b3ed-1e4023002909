"use client"

import React, { useState, useCallback } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'
import 'react-pdf/dist/Page/AnnotationLayer.css'
import 'react-pdf/dist/Page/TextLayer.css'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, Download, RotateCw, Play, Pause, SkipBack, SkipForward } from 'lucide-react'
import { toast } from "sonner"

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`

interface PresentationViewerProps {
  url: string
  title?: string
  slideCount?: number
  onProgress?: (progress: { currentSlide: number; totalSlides: number }) => void
  onComplete?: () => void
  autoPlay?: boolean
  slideInterval?: number // in seconds
}

export function PresentationViewer({ 
  url, 
  title, 
  slideCount, 
  onProgress, 
  onComplete,
  autoPlay = false,
  slideInterval = 5
}: PresentationViewerProps) {
  const [numPages, setNumPages] = useState<number>(slideCount || 0)
  const [pageNumber, setPageNumber] = useState<number>(1)
  const [scale, setScale] = useState<number>(1.2)
  const [rotation, setRotation] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState<boolean>(autoPlay)
  const [intervalId, setIntervalId] = useState<NodeJS.Timeout | null>(null)

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages)
    setLoading(false)
    setError(null)
    onProgress?.({ currentSlide: 1, totalSlides: numPages })
    
    if (autoPlay) {
      startSlideshow()
    }
  }, [onProgress, autoPlay])

  const onDocumentLoadError = useCallback((error: Error) => {
    setLoading(false)
    setError('Failed to load presentation')
    console.error('Presentation load error:', error)
    toast.error('Failed to load presentation')
  }, [])

  const startSlideshow = () => {
    if (intervalId) clearInterval(intervalId)
    
    const id = setInterval(() => {
      setPageNumber(prev => {
        const nextPage = prev + 1
        if (nextPage > numPages) {
          setIsPlaying(false)
          onComplete?.()
          return prev
        }
        onProgress?.({ currentSlide: nextPage, totalSlides: numPages })
        return nextPage
      })
    }, slideInterval * 1000)
    
    setIntervalId(id)
    setIsPlaying(true)
  }

  const stopSlideshow = () => {
    if (intervalId) {
      clearInterval(intervalId)
      setIntervalId(null)
    }
    setIsPlaying(false)
  }

  const toggleSlideshow = () => {
    if (isPlaying) {
      stopSlideshow()
    } else {
      startSlideshow()
    }
  }

  const goToPrevSlide = () => {
    if (pageNumber > 1) {
      const newPage = pageNumber - 1
      setPageNumber(newPage)
      onProgress?.({ currentSlide: newPage, totalSlides: numPages })
    }
  }

  const goToNextSlide = () => {
    if (pageNumber < numPages) {
      const newPage = pageNumber + 1
      setPageNumber(newPage)
      onProgress?.({ currentSlide: newPage, totalSlides: numPages })
      
      if (newPage === numPages) {
        onComplete?.()
      }
    }
  }

  const goToSlide = (slide: number) => {
    if (slide >= 1 && slide <= numPages) {
      setPageNumber(slide)
      onProgress?.({ currentSlide: slide, totalSlides: numPages })
    }
  }

  const goToFirstSlide = () => {
    setPageNumber(1)
    onProgress?.({ currentSlide: 1, totalSlides: numPages })
  }

  const goToLastSlide = () => {
    setPageNumber(numPages)
    onProgress?.({ currentSlide: numPages, totalSlides: numPages })
  }

  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.25, 3.0))
  }

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.25, 0.5))
  }

  const rotate = () => {
    setRotation(prev => (prev + 90) % 360)
  }

  const downloadPresentation = () => {
    const link = document.createElement('a')
    link.href = url
    link.download = title || 'presentation.pdf'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Cleanup interval on unmount
  React.useEffect(() => {
    return () => {
      if (intervalId) clearInterval(intervalId)
    }
  }, [intervalId])

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-lg font-semibold">Failed to load presentation</p>
            <p className="text-sm text-muted-foreground mt-2">
              The presentation could not be loaded. Please check the file and try again.
            </p>
          </div>
          <Button onClick={() => window.location.reload()} variant="outline">
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">{title || 'Presentation'}</CardTitle>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={downloadPresentation}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          {/* Slideshow Controls */}
          <div className="flex items-center justify-between p-4 border-b bg-muted/50">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToFirstSlide}
                disabled={pageNumber <= 1}
              >
                <SkipBack className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={goToPrevSlide}
                disabled={pageNumber <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={toggleSlideshow}
                className="bg-primary/10"
              >
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={goToNextSlide}
                disabled={pageNumber >= numPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={goToLastSlide}
                disabled={pageNumber >= numPages}
              >
                <SkipForward className="h-4 w-4" />
              </Button>
              
              <div className="flex items-center space-x-2 ml-4">
                <span className="text-sm">Slide</span>
                <Input
                  type="number"
                  min={1}
                  max={numPages}
                  value={pageNumber}
                  onChange={(e) => goToSlide(parseInt(e.target.value) || 1)}
                  className="w-16 h-8 text-center"
                />
                <span className="text-sm">of {numPages}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={zoomOut} disabled={scale <= 0.5}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm min-w-[60px] text-center">
                {Math.round(scale * 100)}%
              </span>
              <Button variant="outline" size="sm" onClick={zoomIn} disabled={scale >= 3.0}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={rotate}>
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Presentation Slide */}
          <div className="flex justify-center p-6 bg-gray-100 min-h-[600px]">
            {loading ? (
              <div className="flex items-center justify-center h-96">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-2">Loading presentation...</span>
              </div>
            ) : (
              <Document
                file={url}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                loading={
                  <div className="flex items-center justify-center h-96">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-2">Loading presentation...</span>
                  </div>
                }
              >
                <Page
                  pageNumber={pageNumber}
                  scale={scale}
                  rotate={rotation}
                  loading={
                    <div className="flex items-center justify-center h-96">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                    </div>
                  }
                  className="shadow-lg border"
                />
              </Document>
            )}
          </div>
        </CardContent>
      </Card>
      
      {/* Progress indicator */}
      {numPages > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-2">
              <span>Presentation Progress</span>
              <span>{Math.round((pageNumber / numPages) * 100)}% Complete</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${(pageNumber / numPages) * 100}%` }}
              />
            </div>
            {isPlaying && (
              <div className="text-xs text-muted-foreground mt-2 text-center">
                Auto-advancing every {slideInterval} seconds
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
