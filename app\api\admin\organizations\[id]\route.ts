import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;
    
    // Parse request body
    const body = await req.json();
    const {
      name,
      slug,
      description,
      website_url,
      industry,
      size_range,
      subscription_tier,
      max_teams,
      max_members
    } = body;

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'Organization name is required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // If slug is being updated, check if it's unique
    if (slug) {
      const { data: existingOrg } = await adminClient
        .from('organizations')
        .select('id')
        .eq('slug', slug)
        .neq('id', id)
        .single();

      if (existingOrg) {
        return NextResponse.json(
          { error: 'Organization slug already exists' },
          { status: 400 }
        );
      }
    }

    // Update organization
    const { data: organization, error: orgError } = await adminClient
      .from('organizations')
      .update({
        name: name.trim(),
        slug: slug?.trim() || undefined,
        description: description?.trim() || null,
        website_url: website_url?.trim() || null,
        industry: industry?.trim() || null,
        size_range: size_range?.trim() || null,
        subscription_tier: subscription_tier || undefined,
        max_teams: max_teams || undefined,
        max_members: max_members || undefined,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();
    
    if (orgError) {
      console.error('Error updating organization:', orgError);
      return NextResponse.json(
        { error: orgError.message || 'Failed to update organization' },
        { status: 500 }
      );
    }
    
    if (!organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(organization);
  } catch (error: any) {
    console.error('Organizations PUT API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;
    
    // Create admin client
    const adminClient = createAdminClient();
    
    // Check if organization has any active employment relationships
    const { count: employmentCount } = await adminClient
      .from('employment_relationships')
      .select('*', { count: 'exact', head: true })
      .eq('organization_id', id)
      .eq('status', 'active');

    if (employmentCount && employmentCount > 0) {
      return NextResponse.json(
        { error: 'Cannot delete organization with active employees. Please remove all employees first.' },
        { status: 400 }
      );
    }
    
    // Delete organization
    const { error: deleteError } = await adminClient
      .from('organizations')
      .delete()
      .eq('id', id);
    
    if (deleteError) {
      console.error('Error deleting organization:', deleteError);
      return NextResponse.json(
        { error: deleteError.message || 'Failed to delete organization' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('Organizations DELETE API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Parse request body
    const body = await req.json();
    const { status } = body;

    // Validate status
    const validStatuses = ['active', 'suspended', 'trial', 'cancelled'];
    if (!status || !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Valid status is required (active, suspended, trial, cancelled)' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Update organization status
    const { data: organization, error: orgError } = await adminClient
      .from('organizations')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (orgError) {
      console.error('Error updating organization status:', orgError);
      return NextResponse.json(
        { error: orgError.message || 'Failed to update organization status' },
        { status: 500 }
      );
    }

    if (!organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(organization);
  } catch (error: any) {
    console.error('Organization PATCH API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
