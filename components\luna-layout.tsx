"use client"

import { useState, useEffect } from "react"
import { SidebarProvider, SidebarInset, SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { LunaSidebar } from "@/components/luna-sidebar"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { usePathname } from "next/navigation"
import { useLunaAuth, useCurrentContext } from "@/hooks/use-luna-auth"
import { useBreadcrumbs } from "@/hooks/use-navigation"
import { Bell, Search, ChevronDown, LogOut, Inbox, Home, Settings, Monitor } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface LunaLayoutProps {
  children: React.ReactNode
}

export function LunaLayout({ children }: LunaLayoutProps) {
  const [mounted, setMounted] = useState(false)
  const pathname = usePathname()
  const { user, signOut, loading } = useLunaAuth()
  const { context } = useCurrentContext()

  useEffect(() => {
    setMounted(true)
  }, [])

  // Prevent hydration mismatch by showing loading state during SSR and initial mount
  if (!mounted || loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Generate breadcrumbs based on current path
  const generateBreadcrumbs = () => {
    const segments = pathname.split('/').filter(Boolean)
    const breadcrumbs = []

    // Add home/dashboard
    if (segments[0] === 'admin') {
      breadcrumbs.push({ label: 'Admin Dashboard', href: '/admin', isLast: segments.length === 1 })
    } else if (segments[0] === 'organization') {
      breadcrumbs.push({ label: 'Organization Dashboard', href: '/organization', isLast: segments.length === 1 })
    } else if (segments[0] === 'individual') {
      breadcrumbs.push({ label: 'Individual Dashboard', href: '/individual', isLast: segments.length === 1 })
    }

    // Add subsequent segments
    for (let i = 1; i < segments.length; i++) {
      const segment = segments[i]
      const href = '/' + segments.slice(0, i + 1).join('/')
      const isLast = i === segments.length - 1
      
      // Convert segment to readable label
      const label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      breadcrumbs.push({ label, href, isLast })
    }

    return breadcrumbs
  }

  const dynamicBreadcrumbs = useBreadcrumbs()
  const breadcrumbs = generateBreadcrumbs() // Keep existing for now, can be replaced later

  // Generate page title
  const getPageTitle = () => {
    if (pathname === '/admin') return 'Admin Dashboard'
    if (pathname === '/organization') return 'Organization Dashboard'
    if (pathname === '/individual') return 'Individual Dashboard'
    
    const segments = pathname.split('/').filter(Boolean)
    if (segments.length > 1) {
      const lastSegment = segments[segments.length - 1]
      return lastSegment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    }
    
    return 'Luna Platform'
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <LunaSidebar />
        
        <SidebarInset className="flex flex-col flex-1">
          {/* Enhanced Header */}
          <header className="bg-white border-b border-gray-200 shadow-sm">
            {/* Top Navigation Bar */}
            <div className="flex h-16 shrink-0 items-center gap-2 px-6">
              <SidebarTrigger className="z-10" />
              <Separator orientation="vertical" className="mr-2 h-4" />

              {/* Breadcrumbs */}
              {breadcrumbs.length > 0 && (
                <Breadcrumb>
                  <BreadcrumbList>
                    {breadcrumbs.map((crumb, index) => (
                      <div key={crumb.href} className="flex items-center">
                        {index > 0 && <BreadcrumbSeparator />}
                        <BreadcrumbItem>
                          {crumb.isLast ? (
                            <BreadcrumbPage className="font-medium text-gray-700">{crumb.label}</BreadcrumbPage>
                          ) : (
                            <BreadcrumbLink href={crumb.href} className="font-medium text-gray-600 hover:text-gray-900">{crumb.label}</BreadcrumbLink>
                          )}
                        </BreadcrumbItem>
                      </div>
                    ))}
                  </BreadcrumbList>
                </Breadcrumb>
              )}

              {/* Spacer */}
              <div className="flex-1" />

              {/* Header Actions */}
              <div className="flex items-center gap-3">
                {/* Get Your Topline Rate Button */}
                <Button
                  variant="outline"
                  size="sm"
                  className="hidden lg:flex bg-white border-gray-300 text-gray-700 hover:bg-gray-50 font-medium px-4 py-2 rounded-lg"
                >
                  Get Your Topline Rate
                </Button>

                {/* Credits Badge */}
                <div className="hidden md:flex items-center gap-2 bg-yellow-50 px-4 py-2 rounded-full border border-yellow-200">
                  <span className="text-sm font-semibold text-yellow-700">Credits</span>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 font-bold">
                    0
                  </Badge>
                </div>

                {/* Search Icon */}
                <Button variant="ghost" size="icon" className="text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">
                  <Search className="h-5 w-5" />
                </Button>

                {/* Notifications */}
                <Button variant="ghost" size="icon" className="relative text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg">
                  <Bell className="h-5 w-5" />
                  <Badge
                    variant="destructive"
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center font-bold"
                  >
                    3
                  </Badge>
                </Button>

                {/* User Avatar Dropdown */}
                <div className="hidden sm:flex items-center gap-2">
                  {user && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="flex items-center gap-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-lg p-2">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-bold text-blue-700">
                              {user.full_name ? user.full_name.split(' ').map(n => n[0]).join('').toUpperCase() : 'U'}
                            </span>
                          </div>
                          <ChevronDown className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-56">
                        <DropdownMenuLabel className="font-normal">
                          <div className="flex flex-col space-y-1">
                            <p className="text-sm font-medium leading-none">{user.full_name || 'User'}</p>
                            <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                          </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Inbox className="mr-2 h-4 w-4" />
                          <span>Inbox</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Home className="mr-2 h-4 w-4" />
                          <span>Home</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="mr-2 h-4 w-4" />
                          <span>Settings</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Monitor className="mr-2 h-4 w-4" />
                          <span>Access Topline OS</span>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={async () => {
                            try {
                              await signOut()
                            } catch (error) {
                              console.error('Logout error:', error)
                            }
                          }}
                          className="text-red-600 focus:text-red-600"
                        >
                          <LogOut className="mr-2 h-4 w-4" />
                          <span>Logout</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            </div>


          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-auto">
            <div className="h-full">
              {children}
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}

// Convenience wrapper for pages that need the layout
export function withLunaLayout<T extends object>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  const WrappedComponent = (props: T) => (
    <LunaLayout>
      <Component {...props} />
    </LunaLayout>
  )
  
  WrappedComponent.displayName = `withLunaLayout(${Component.displayName || Component.name})`
  
  return WrappedComponent
}
