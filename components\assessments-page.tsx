"use client";

import { BrainCir<PERSON>it, Book<PERSON>pen, Keyboard, Users, <PERSON><PERSON>, PersonStanding, Timer, ArrowRight, CircleCheck } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bsContent, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { AssessmentCard } from "@/components/assessment-card"
import { useState } from "react"
import { useRouter } from "next/navigation"

// Define the status type to fix linter error
type AssessmentStatus = "not-started" | "completed" | "in-progress"

// Define assessment data
const assessments = [
  {
    id: "quick-thinking",
    title: "🧠 Quick Thinking Challenge",
    description: "Test your problem-solving abilities with numbers and logic",
    icon: <BrainCircuit className="h-5 w-5 text-primary" />,
    duration: "30 minutes",
    status: "completed" as AssessmentStatus,
    whatItChecks: "How well you solve problems and think on your feet using numbers and logic.",
    whatToExpect: "Brain-teasing puzzles, patterns, and number games to test your reasoning skills.",
    category: "technical" as const
  },
  {
    id: "english-skills",
    title: "📚 English Skills Check-In",
    description: "Evaluate your grammar, vocabulary, reading, and writing skills",
    icon: <BookOpen className="h-5 w-5 text-primary" />,
    duration: "45 minutes",
    status: "in-progress" as AssessmentStatus,
    whatItChecks: "Your grammar, vocabulary, reading, and writing skills.",
    whatToExpect: "Short quizzes and a mini writing task that helps show off how you communicate.",
    category: "soft-skills" as const
  },
  {
    id: "typing-test",
    title: "⌨️ Typing Test Drive",
    description: "Measure your typing speed and accuracy",
    icon: <Keyboard className="h-5 w-5 text-primary" />,
    duration: "10 minutes",
    status: "not-started" as AssessmentStatus,
    whatItChecks: "How fast and accurately you can type.",
    whatToExpect: "A fun typing challenge that's important for many roles in the BPO industry.",
    category: "technical" as const
  },
  {
    id: "real-world-reaction",
    title: "🎭 Real-World Reaction Game",
    description: "Test how you'd handle everyday work situations",
    icon: <PersonStanding className="h-5 w-5 text-primary" />,
    duration: "30 minutes",
    status: "not-started" as AssessmentStatus,
    whatItChecks: "How you'd handle everyday work situations and make decisions.",
    whatToExpect: "Role-play style questions where you choose the best response to workplace scenarios.",
    category: "soft-skills" as const
  },
  {
    id: "tech-try-out",
    title: "🛠️ Tech Try-Out",
    description: "Demonstrate your comfort with software and basic tech tools",
    icon: <Wrench className="h-5 w-5 text-primary" />,
    duration: "15–30 minutes",
    status: "completed" as AssessmentStatus,
    whatItChecks: "Your comfort with software and basic tech tools used on the job.",
    whatToExpect: "Simulations or tasks that reflect the kind of work done in call centers and support teams.",
    category: "technical" as const
  },
  {
    id: "personality-snapshot",
    title: "💬 Personality Snapshot",
    description: "Discover your strengths, work style, and job fit",
    icon: <Users className="h-5 w-5 text-primary" />,
    duration: "20 minutes",
    status: "not-started" as AssessmentStatus,
    whatItChecks: "Your strengths, work style, and what kind of job or team suits you best.",
    whatToExpect: "Easy multiple-choice questions that help match you to the right fit.",
    category: "soft-skills" as const
  }
]

export function AssessmentsPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("all")

  // Calculate statistics
  const completedCount = assessments.filter(a => a.status === "completed").length
  const inProgressCount = assessments.filter(a => a.status === "in-progress").length
  const notStartedCount = assessments.filter(a => a.status === "not-started").length
  const totalCount = assessments.length

  const technicalAssessments = assessments.filter(a => a.category === "technical")
  const softSkillsAssessments = assessments.filter(a => a.category === "soft-skills")

  const technicalCompletedCount = technicalAssessments.filter(a => a.status === "completed").length
  const softSkillsCompletedCount = softSkillsAssessments.filter(a => a.status === "completed").length

  const handleAssessmentClick = (id: string) => {
    // Navigate to the assessment page
    router.push(`/prospect/assessments/${id}`);
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Test Lab</h1>
        <p className="text-muted-foreground">Complete these assessments to showcase your skills and abilities</p>
      </div>

      {/* KPI Cards as shown in wireframe */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">All Assessments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
            <div className="flex items-center gap-2 mt-1">
              <div className="h-2 w-2 rounded-full bg-green-500"></div>
              <p className="text-xs text-muted-foreground">{completedCount} Completed</p>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <div className="h-2 w-2 rounded-full bg-amber-500"></div>
              <p className="text-xs text-muted-foreground">{inProgressCount} In Progress</p>
            </div>
            <div className="flex items-center gap-2 mt-1">
              <div className="h-2 w-2 rounded-full bg-gray-500"></div>
              <p className="text-xs text-muted-foreground">{notStartedCount} Not Started</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Technical</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{technicalAssessments.length}</div>
            <Progress value={(technicalCompletedCount / technicalAssessments.length) * 100} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">{technicalCompletedCount} of {technicalAssessments.length} completed</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Soft Skills & Personality</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{softSkillsAssessments.length}</div>
            <Progress value={(softSkillsCompletedCount / softSkillsAssessments.length) * 100} className="h-2 mt-2" />
            <p className="text-xs text-muted-foreground mt-1">{softSkillsCompletedCount} of {softSkillsAssessments.length} completed</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="all" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Assessments</TabsTrigger>
          <TabsTrigger value="technical">Technical</TabsTrigger>
          <TabsTrigger value="soft-skills">Soft Skills & Personality</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="pt-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {assessments.map((assessment) => (
              <AssessmentCard 
                key={assessment.id}
                title={assessment.title}
                description={assessment.description}
                icon={assessment.icon}
                duration={assessment.duration}
                status={assessment.status}
                whatItChecks={assessment.whatItChecks}
                whatToExpect={assessment.whatToExpect}
                category={assessment.category}
                onClick={() => handleAssessmentClick(assessment.id)}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="technical" className="pt-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {technicalAssessments.map((assessment) => (
              <AssessmentCard 
                key={assessment.id}
                title={assessment.title}
                description={assessment.description}
                icon={assessment.icon}
                duration={assessment.duration}
                status={assessment.status}
                whatItChecks={assessment.whatItChecks}
                whatToExpect={assessment.whatToExpect}
                category={assessment.category}
                onClick={() => handleAssessmentClick(assessment.id)}
              />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="soft-skills" className="pt-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {softSkillsAssessments.map((assessment) => (
              <AssessmentCard 
                key={assessment.id}
                title={assessment.title}
                description={assessment.description}
                icon={assessment.icon}
                duration={assessment.duration}
                status={assessment.status}
                whatItChecks={assessment.whatItChecks}
                whatToExpect={assessment.whatToExpect}
                category={assessment.category}
                onClick={() => handleAssessmentClick(assessment.id)}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Assessment Information</CardTitle>
          <CardDescription>What you need to know about these assessments</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">How We Use These Results</h3>
            <p className="mt-2 text-sm">
              These assessments help us understand your skills, abilities, and work style to find the best match for your talents. 
              They provide a well-rounded view of how you might perform in real-life scenarios, similar to assessment methodologies 
              used by leading BPO companies like BairesDev.
            </p>
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Assessment Tips</h3>
            <ul className="mt-2 list-disc pl-5 text-sm space-y-1">
              <li>Make sure you have a stable internet connection</li>
              <li>Find a quiet place without distractions</li>
              <li>Read all instructions carefully before starting</li>
              <li>Manage your time wisely for timed assessments</li>
              <li>Answer honestly on personality assessments - there are no right or wrong answers</li>
            </ul>
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Assessment Methods</h3>
            <p className="mt-2 text-sm">
              Our assessments use various methods including multiple-choice questions, simulations, scenario-based exercises, 
              typing tests, and AI-based evaluations. These methods have been proven effective in identifying candidates who 
              will excel in BPO environments.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
