import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { promptTemplateService } from '@/lib/services/prompt-template';

/**
 * Get all prompt templates
 * GET /api/admin/ai-prompt-templates
 */
export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { searchParams } = new URL(req.url);
    const category = searchParams.get('category');
    const activeOnly = searchParams.get('active') === 'true';

    let templates;
    
    if (category) {
      templates = await promptTemplateService.getTemplatesByCategory(category);
    } else if (activeOnly) {
      templates = await promptTemplateService.getActiveTemplates();
    } else {
      // Get all templates (admin view)
      templates = await promptTemplateService.getActiveTemplates(); // For now, extend this to get all
    }

    // Calculate statistics
    const stats = {
      total: templates.length,
      active: templates.filter(t => t.is_active).length,
      by_category: templates.reduce((acc, template) => {
        acc[template.category] = (acc[template.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return NextResponse.json({
      templates,
      statistics: stats
    });

  } catch (error: any) {
    console.error('Prompt templates fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch prompt templates' },
      { status: 500 }
    );
  }
}

/**
 * Create new prompt template
 * POST /api/admin/ai-prompt-templates
 */
export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const body = await req.json();
    const {
      name,
      description,
      assessment_type,
      category,
      system_prompt,
      context_prompt,
      question_generation_prompt,
      default_parameters = {},
      variable_definitions = [],
      output_schema = {},
      validation_rules = []
    } = body;

    // Validate required fields
    if (!name || !category || !system_prompt) {
      return NextResponse.json(
        { error: 'Name, category, and system prompt are required' },
        { status: 400 }
      );
    }

    // Create template
    const template = await promptTemplateService.createTemplate({
      name,
      description: description || '',
      assessment_type: assessment_type || category,
      category,
      system_prompt,
      context_prompt,
      question_generation_prompt,
      default_parameters,
      variable_definitions,
      output_schema,
      validation_rules,
      version: 1,
      is_active: true
    });

    return NextResponse.json({
      success: true,
      template,
      message: 'Prompt template created successfully'
    });

  } catch (error: any) {
    console.error('Prompt template creation error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create prompt template' },
      { status: 500 }
    );
  }
}
