/**
 * Security Module
 * Centralized exports for the Luna security system
 */

// Rate limiting
export {
  RATE_LIMITS,
  withRateLimit,
  getRateLimitStatus,
  clearRateLimit,
  getRateLimitStats,
  type RateLimitConfig
} from './rate-limiting';

// Enhanced middleware
export {
  withPublicSecurity,
  withAuthenticatedSecurity,
  withAdminSecurity,
  withUploadSecurity,
  withProfileSecurity,
  withDatabaseSecurity,
  withAuthSecurity,
  withComprehensiveSecurity,
  withDevelopmentSecurity
} from './enhanced-middleware';

// Validation schemas
export {
  ValidationSchemas,
  ProfileSchemas,
  FileUploadSchemas,
  AuthSchemas,
  AdminSchemas,
  QuerySchemas,
  OrganizationSchemas,
  ValidationUtils
} from './validation-schemas';

// Audit logging
export {
  securityAuditLogger,
  SecurityEventType,
  SecurityLevel,
  type SecurityEvent,
  type SecurityAuditLogger
} from './audit-logger';

// Security headers
export {
  withSecurityHeaders,
  withCors,
  withDevSecurityHeaders,
  getDevSecurityHeaders,
  type CorsConfig
} from './headers';

// Input validation
export {
  withInputSanitization,
  withRequestSizeLimit,
  withMethodValidation,
  withHeaderValidation,
  withRequestValidation,
  withJsonSchemaValidation,
  validateJsonSchema,
  type RequestValidationConfig
} from './validation';

// File upload security
export {
  FILE_UPLOAD_SECURITY,
  validateFileUpload,
  sanitizeFileName,
  generateSecureFilePath,
  validateFileContent,
  validateUploadedFile,
  getFileCategoryFromMimeType,
  formatFileSize,
  isFileTypeSupported,
  type FileUploadConfig
} from './file-upload';

// Comprehensive API security wrapper
import { NextRequest, NextResponse } from 'next/server';
import { withRateLimit, RATE_LIMITS, type RateLimitConfig } from './rate-limiting';
import { withSecurityHeaders } from './headers';
import { withInputSanitization, withRequestSizeLimit } from './validation';

/**
 * Comprehensive API security wrapper options
 */
export interface ApiSecurityOptions {
  rateLimit?: RateLimitConfig;
  maxRequestSize?: number;
  skipSecurityHeaders?: boolean;
  skipInputSanitization?: boolean;
}

/**
 * Comprehensive API security wrapper
 */
export function withApiSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: ApiSecurityOptions = {}
) {
  let securedHandler = handler;
  
  // Apply middleware in reverse order (last applied = first executed)
  if (!options.skipInputSanitization) {
    securedHandler = withInputSanitization(securedHandler);
  }
  
  if (options.maxRequestSize) {
    securedHandler = withRequestSizeLimit(options.maxRequestSize)(securedHandler);
  }
  
  if (!options.skipSecurityHeaders) {
    securedHandler = withSecurityHeaders(securedHandler);
  }
  
  if (options.rateLimit) {
    securedHandler = withRateLimit(options.rateLimit)(securedHandler);
  } else {
    securedHandler = withRateLimit(RATE_LIMITS.DEFAULT)(securedHandler);
  }
  
  return securedHandler;
}
