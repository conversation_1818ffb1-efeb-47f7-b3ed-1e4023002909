/**
 * Together.ai API Client for AI Assessment Generation
 * Handles communication with Together.ai for question generation and AI services
 */

export interface TogetherAIConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

export interface TogetherAIRequest {
  model: string;
  prompt: string;
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  top_k?: number;
  repetition_penalty?: number;
  stop?: string[];
}

export interface TogetherAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    text: string;
    index: number;
    logprobs: any;
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export interface AssessmentGenerationRequest {
  assessmentType: string;
  questionCount: number;
  difficultyDistribution: Record<string, number>;
  subcategories: string[];
  questionTypes: string[];
  systemPrompt: string;
  parameters: Record<string, any>;
}

export interface GeneratedQuestion {
  id: string;
  question_type: string;
  question_text: string;
  question_context?: string;
  answer_options?: string[];
  correct_answer: any;
  explanation: string;
  subcategory: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  estimated_time: number;
  ai_confidence_score?: number;
}

export interface AssessmentGenerationResponse {
  questions: GeneratedQuestion[];
  metadata: {
    generation_id: string;
    model_used: string;
    tokens_used: number;
    generation_time: number;
    quality_score: number;
  };
}

export class TogetherAIClient {
  private config: TogetherAIConfig;
  private baseUrl: string;
  private requestCount: number = 0;
  private totalTokensUsed: number = 0;
  private averageResponseTime: number = 0;

  constructor(config: TogetherAIConfig) {
    this.config = {
      baseUrl: 'https://api.together.xyz',
      timeout: 60000, // 60 seconds
      retryAttempts: 3,
      retryDelay: 1000, // 1 second
      ...config
    };
    this.baseUrl = this.config.baseUrl!;
  }

  /**
   * Get client statistics
   */
  getStats() {
    return {
      requestCount: this.requestCount,
      totalTokensUsed: this.totalTokensUsed,
      averageResponseTime: this.averageResponseTime,
      config: {
        model: this.config.baseUrl,
        timeout: this.config.timeout,
        retryAttempts: this.config.retryAttempts
      }
    };
  }

  /**
   * Reset statistics
   */
  resetStats() {
    this.requestCount = 0;
    this.totalTokensUsed = 0;
    this.averageResponseTime = 0;
  }

  /**
   * Update client statistics
   */
  private updateStats(responseTime: number, tokensUsed: number) {
    this.requestCount++;
    this.totalTokensUsed += tokensUsed;

    // Calculate rolling average response time
    this.averageResponseTime = (
      (this.averageResponseTime * (this.requestCount - 1)) + responseTime
    ) / this.requestCount;
  }

  /**
   * Generate assessment questions using Together.ai
   */
  async generateAssessmentQuestions(
    request: AssessmentGenerationRequest
  ): Promise<AssessmentGenerationResponse> {
    const startTime = Date.now();
    
    try {
      // Build the complete prompt
      const compiledPrompt = this.buildAssessmentPrompt(request);
      
      // Prepare the API request
      const aiRequest: TogetherAIRequest = {
        model: 'meta-llama/Llama-2-70b-chat-hf',
        prompt: compiledPrompt,
        temperature: 0.7,
        max_tokens: 4000,
        top_p: 0.9,
        repetition_penalty: 1.1,
        stop: ['</assessment>', '---END---']
      };

      // Call Together.ai API
      const response = await this.callTogetherAPI(aiRequest);

      // Parse the response
      const questions = this.parseAssessmentResponse(response.choices[0].text);

      // Validate questions
      const validatedQuestions = this.validateQuestions(questions, request);

      const generationTime = Date.now() - startTime;

      // Update statistics
      this.updateStats(generationTime, response.usage?.total_tokens || 0);

      return {
        questions: validatedQuestions,
        metadata: {
          generation_id: response.id,
          model_used: response.model,
          tokens_used: response.usage?.total_tokens || 0,
          generation_time: generationTime,
          quality_score: this.calculateQualityScore(validatedQuestions)
        }
      };
    } catch (error) {
      console.error('Assessment generation failed:', error);
      throw new Error(`Failed to generate assessment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Make API call to Together.ai with enhanced retry logic and error handling
   */
  private async callTogetherAPI(request: TogetherAIRequest): Promise<TogetherAIResponse> {
    let lastError: Error | null = null;
    const startTime = Date.now();

    for (let attempt = 1; attempt <= this.config.retryAttempts!; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout!);

        const response = await fetch(`${this.baseUrl}/inference`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.config.apiKey}`,
            'Content-Type': 'application/json',
            'User-Agent': 'Luna-LMS/1.0',
          },
          body: JSON.stringify(request),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        // Enhanced error handling with specific status codes
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          const errorMessage = this.getErrorMessage(response.status, errorData);

          // Don't retry on certain errors
          if (this.isNonRetryableError(response.status)) {
            throw new Error(errorMessage);
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();

        // Validate response structure
        if (!this.isValidTogetherResponse(data)) {
          throw new Error('Invalid response format from Together.ai API');
        }

        // Log successful request
        const duration = Date.now() - startTime;
        console.log(`Together.ai API success: ${duration}ms, tokens: ${data.usage?.total_tokens || 'unknown'}`);

        return data;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // Log attempt failure
        console.warn(`Together.ai API attempt ${attempt}/${this.config.retryAttempts} failed:`, lastError.message);

        // Don't retry on non-retryable errors
        if (this.isNonRetryableError(0) && lastError.message.includes('401')) {
          throw lastError;
        }

        if (attempt < this.config.retryAttempts!) {
          const delay = this.calculateRetryDelay(attempt);
          console.warn(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError || new Error('All retry attempts failed');
  }

  /**
   * Calculate exponential backoff delay
   */
  private calculateRetryDelay(attempt: number): number {
    const baseDelay = this.config.retryDelay!;
    const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 1000; // Add jitter to prevent thundering herd
    return Math.min(exponentialDelay + jitter, 30000); // Cap at 30 seconds
  }

  /**
   * Check if error should not be retried
   */
  private isNonRetryableError(statusCode: number): boolean {
    return [400, 401, 403, 404, 422].includes(statusCode);
  }

  /**
   * Get user-friendly error message based on status code
   */
  private getErrorMessage(statusCode: number, errorData: any): string {
    const baseMessage = errorData.error || errorData.message || 'Unknown error';

    switch (statusCode) {
      case 400:
        return `Bad request: ${baseMessage}`;
      case 401:
        return 'Authentication failed. Please check your Together.ai API key.';
      case 403:
        return 'Access forbidden. Your API key may not have sufficient permissions.';
      case 404:
        return 'API endpoint not found. The model may not be available.';
      case 422:
        return `Invalid request parameters: ${baseMessage}`;
      case 429:
        return 'Rate limit exceeded. Please try again later.';
      case 500:
        return 'Together.ai server error. Please try again.';
      case 503:
        return 'Together.ai service temporarily unavailable.';
      default:
        return `Together.ai API error (${statusCode}): ${baseMessage}`;
    }
  }

  /**
   * Validate Together.ai response structure
   */
  private isValidTogetherResponse(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      Array.isArray(data.choices) &&
      data.choices.length > 0 &&
      typeof data.choices[0].text === 'string'
    );
  }

  /**
   * Build the complete assessment prompt
   */
  private buildAssessmentPrompt(request: AssessmentGenerationRequest): string {
    const {
      assessmentType,
      questionCount,
      difficultyDistribution,
      subcategories,
      questionTypes,
      systemPrompt,
      parameters
    } = request;

    // Replace template variables in system prompt
    let compiledPrompt = systemPrompt;
    
    // Replace basic parameters
    compiledPrompt = compiledPrompt.replace(/\{\{question_count\}\}/g, questionCount.toString());
    compiledPrompt = compiledPrompt.replace(/\{\{assessment_type\}\}/g, assessmentType);
    compiledPrompt = compiledPrompt.replace(/\{\{difficulty_distribution\}\}/g, JSON.stringify(difficultyDistribution));
    compiledPrompt = compiledPrompt.replace(/\{\{subcategories\}\}/g, subcategories.join(', '));
    compiledPrompt = compiledPrompt.replace(/\{\{question_types\}\}/g, questionTypes.join(', '));
    
    // Replace custom parameters
    Object.entries(parameters).forEach(([key, value]) => {
      const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      compiledPrompt = compiledPrompt.replace(placeholder, String(value));
    });

    // Add output format instructions
    compiledPrompt += `\n\nIMPORTANT: Return your response as a valid JSON object with this exact structure:
{
  "questions": [
    {
      "id": "q1",
      "question_type": "multiple_choice",
      "question_text": "Your question here",
      "question_context": "Background context if needed",
      "answer_options": ["Option A", "Option B", "Option C", "Option D"],
      "correct_answer": "Option A",
      "explanation": "Why this is correct",
      "subcategory": "grammar",
      "difficulty": "medium",
      "points": 1,
      "estimated_time": 60
    }
  ]
}

Generate exactly ${questionCount} questions. Ensure all questions are relevant to BPO work and properly formatted.`;

    return compiledPrompt;
  }

  /**
   * Parse AI response into structured questions with enhanced error handling
   */
  private parseAssessmentResponse(responseText: string): GeneratedQuestion[] {
    try {
      // Clean the response text
      const cleanedText = this.cleanResponseText(responseText);

      // Try multiple JSON extraction methods
      let parsed: any;

      // Method 1: Direct JSON parsing
      try {
        parsed = JSON.parse(cleanedText);
      } catch {
        // Method 2: Extract JSON block
        const jsonMatch = cleanedText.match(/\{[\s\S]*\}/);
        if (!jsonMatch) {
          throw new Error('No JSON structure found in AI response');
        }

        try {
          parsed = JSON.parse(jsonMatch[0]);
        } catch {
          // Method 3: Try to fix common JSON issues
          const fixedJson = this.fixCommonJsonIssues(jsonMatch[0]);
          parsed = JSON.parse(fixedJson);
        }
      }

      // Validate response structure
      if (!parsed || typeof parsed !== 'object') {
        throw new Error('Response is not a valid object');
      }

      if (!parsed.questions) {
        // Sometimes AI returns questions directly as array
        if (Array.isArray(parsed)) {
          parsed = { questions: parsed };
        } else {
          throw new Error('No questions found in response');
        }
      }

      if (!Array.isArray(parsed.questions)) {
        throw new Error('Questions is not an array');
      }

      if (parsed.questions.length === 0) {
        throw new Error('No questions generated');
      }

      // Parse and validate each question
      const questions = parsed.questions.map((q: any, index: number) => {
        return this.parseAndValidateQuestion(q, index);
      }).filter(q => q !== null); // Remove invalid questions

      if (questions.length === 0) {
        throw new Error('No valid questions found after parsing');
      }

      console.log(`Successfully parsed ${questions.length} questions from AI response`);
      return questions;

    } catch (error) {
      console.error('Failed to parse AI response:', error);
      console.error('Raw response:', responseText.substring(0, 500) + '...');
      throw new Error(`Failed to parse assessment response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Clean response text for better parsing
   */
  private cleanResponseText(text: string): string {
    return text
      .trim()
      .replace(/```json\s*/g, '') // Remove markdown code blocks
      .replace(/```\s*/g, '')
      .replace(/^\s*[\r\n]/gm, '') // Remove empty lines
      .replace(/,\s*}/g, '}') // Fix trailing commas in objects
      .replace(/,\s*]/g, ']'); // Fix trailing commas in arrays
  }

  /**
   * Fix common JSON formatting issues
   */
  private fixCommonJsonIssues(jsonStr: string): string {
    return jsonStr
      .replace(/,(\s*[}\]])/g, '$1') // Remove trailing commas
      .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // Quote unquoted keys
      .replace(/:\s*'([^']*)'/g, ': "$1"') // Replace single quotes with double quotes
      .replace(/\n/g, '\\n') // Escape newlines in strings
      .replace(/\t/g, '\\t'); // Escape tabs in strings
  }

  /**
   * Parse and validate individual question
   */
  private parseAndValidateQuestion(q: any, index: number): GeneratedQuestion | null {
    try {
      // Required fields validation
      if (!q.question_text || typeof q.question_text !== 'string') {
        console.warn(`Question ${index + 1}: Missing or invalid question_text`);
        return null;
      }

      if (!q.correct_answer) {
        console.warn(`Question ${index + 1}: Missing correct_answer`);
        return null;
      }

      // Validate question type
      const validQuestionTypes = ['multiple_choice', 'single_choice', 'true_false', 'text_input', 'scenario_based', 'fill_in_blank'];
      const questionType = validQuestionTypes.includes(q.question_type) ? q.question_type : 'multiple_choice';

      // Validate difficulty
      const validDifficulties = ['easy', 'medium', 'hard'];
      const difficulty = validDifficulties.includes(q.difficulty) ? q.difficulty : 'medium';

      // Type-specific validation
      if (questionType === 'multiple_choice' || questionType === 'single_choice') {
        if (!Array.isArray(q.answer_options) || q.answer_options.length < 2) {
          console.warn(`Question ${index + 1}: Invalid answer_options for multiple choice`);
          return null;
        }

        if (!q.answer_options.includes(q.correct_answer)) {
          console.warn(`Question ${index + 1}: Correct answer not in options`);
          return null;
        }
      }

      return {
        id: q.id || `q${index + 1}`,
        question_type: questionType,
        question_text: q.question_text.trim(),
        question_context: q.question_context || null,
        answer_options: q.answer_options || [],
        correct_answer: q.correct_answer,
        explanation: q.explanation || '',
        subcategory: q.subcategory || 'general',
        difficulty: difficulty,
        points: Math.max(1, parseInt(q.points) || 1),
        estimated_time: Math.max(30, parseInt(q.estimated_time) || 60),
        ai_confidence_score: Math.min(1.0, Math.max(0.0, parseFloat(q.ai_confidence_score) || 0.8))
      };

    } catch (error) {
      console.warn(`Failed to parse question ${index + 1}:`, error);
      return null;
    }
  }

  /**
   * Validate generated questions
   */
  private validateQuestions(
    questions: GeneratedQuestion[],
    request: AssessmentGenerationRequest
  ): GeneratedQuestion[] {
    const validQuestions = questions.filter(q => {
      // Basic validation
      if (!q.question_text || q.question_text.length < 10) return false;
      if (!q.correct_answer) return false;
      if (!['easy', 'medium', 'hard'].includes(q.difficulty)) return false;
      
      // Question type specific validation
      if (q.question_type === 'multiple_choice') {
        if (!q.answer_options || q.answer_options.length < 2) return false;
        if (!q.answer_options.includes(q.correct_answer as string)) return false;
      }
      
      return true;
    });

    if (validQuestions.length < request.questionCount * 0.8) {
      throw new Error(`Too many invalid questions generated. Expected ${request.questionCount}, got ${validQuestions.length} valid questions.`);
    }

    return validQuestions.slice(0, request.questionCount);
  }

  /**
   * Calculate quality score for generated questions
   */
  private calculateQualityScore(questions: GeneratedQuestion[]): number {
    if (questions.length === 0) return 0;
    
    let totalScore = 0;
    
    questions.forEach(q => {
      let score = 0.5; // Base score
      
      // Question text quality
      if (q.question_text.length > 20) score += 0.1;
      if (q.question_text.includes('BPO') || q.question_text.includes('customer')) score += 0.1;
      
      // Has explanation
      if (q.explanation && q.explanation.length > 10) score += 0.1;
      
      // Proper difficulty distribution
      if (['easy', 'medium', 'hard'].includes(q.difficulty)) score += 0.1;
      
      // Question type specific
      if (q.question_type === 'multiple_choice' && q.answer_options && q.answer_options.length >= 4) {
        score += 0.1;
      }
      
      totalScore += Math.min(score, 1.0);
    });
    
    return Math.round((totalScore / questions.length) * 100) / 100;
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.callTogetherAPI({
        model: 'meta-llama/Llama-2-7b-chat-hf',
        prompt: 'Hello, this is a test. Please respond with "Connection successful".',
        max_tokens: 50,
        temperature: 0.1
      });
      
      return response.choices[0].text.toLowerCase().includes('connection successful');
    } catch (error) {
      console.error('Together.ai connection test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const togetherAI = new TogetherAIClient({
  apiKey: process.env.TOGETHER_AI_API_KEY || '',
});

// Export types
export type {
  TogetherAIConfig,
  TogetherAIRequest,
  TogetherAIResponse,
  AssessmentGenerationRequest,
  GeneratedQuestion,
  AssessmentGenerationResponse
};
