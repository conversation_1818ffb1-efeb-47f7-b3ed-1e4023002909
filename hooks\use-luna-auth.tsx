'use client';

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { AuthUser } from '@/lib/auth';
import { Database } from '@/types/database.types';

interface UserContext {
  type: 'individual' | 'organization';
  organization_id?: string;
  department_id?: string;
  employment_id?: string;
}

interface EmploymentRelationship {
  id: string;
  organization_id: string;
  organization_name: string;
  organization_slug: string;
  department_id: string;
  department_name: string;
  employment_role: 'organization_admin' | 'department_admin' | 'staff_member';
  job_title: string;
  status: string;
}

interface LunaAuthContextType {
  user: AuthUser | null;
  loading: boolean;
  switchContext: (contextType: 'individual' | 'organization', organizationId?: string) => Promise<void>;
  signOut: () => Promise<void>;
}

interface CurrentContextType {
  context: UserContext | null;
  availableEmployments: EmploymentRelationship[];
  loading: boolean;
}

const LunaAuthContext = createContext<LunaAuthContextType | undefined>(undefined);
const CurrentContextContext = createContext<CurrentContextType | undefined>(undefined);

export function LunaAuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const supabase = createClientComponentClient<Database>();

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          await fetchUserData(session.user.id);
        }
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        await fetchUserData(session.user.id);
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
      }
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchUserData = async (userId: string) => {
    try {
      const response = await fetch('/api/auth/user');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.user) {
          setUser(result.user);
        } else {
          console.error('Failed to fetch user data:', result.error);
          // If user not found in database, sign them out to clear the stale session
          if (result.error === 'User not found in database') {
            console.log('User not found in database, signing out to clear stale session...');
            await supabase.auth.signOut();
          }
          setUser(null);
        }
      } else {
        console.error('Failed to fetch user data - HTTP error:', response.status);
        // If unauthorized, sign out to clear stale session
        if (response.status === 401) {
          console.log('Unauthorized, signing out to clear stale session...');
          await supabase.auth.signOut();
        }
        setUser(null);
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      setUser(null);
    }
  };

  const switchContext = async (contextType: 'individual' | 'organization', organizationId?: string) => {
    try {
      const response = await fetch('/api/auth/switch-context', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contextType,
          organizationId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to switch context');
      }

      // Refresh user data to get updated context
      if (user) {
        await fetchUserData(user.id);
      }

      // Navigate to appropriate dashboard
      if (contextType === 'individual') {
        router.push('/individual');
      } else if (contextType === 'organization' && organizationId) {
        // Get organization slug from employment relationships
        const employment = user?.employmentRelationships.find(emp => emp.organization_id === organizationId);
        const orgSlug = employment?.organization_slug || 'default';
        router.push(`/org/${orgSlug}`);
      }
    } catch (error) {
      console.error('Context switch error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      router.push('/login');
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  return (
    <LunaAuthContext.Provider value={{ user, loading, switchContext, signOut }}>
      {children}
    </LunaAuthContext.Provider>
  );
}

export function CurrentContextProvider({ children }: { children: ReactNode }) {
  const { user } = useLunaAuth();
  const [context, setContext] = useState<UserContext | null>(null);
  const [availableEmployments, setAvailableEmployments] = useState<EmploymentRelationship[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      setContext(user.currentContext || null);
      setAvailableEmployments(user.employmentRelationships || []);
    } else {
      setContext(null);
      setAvailableEmployments([]);
    }
    setLoading(false);
  }, [user?.id, user?.currentContext?.type, user?.employmentRelationships?.length]); // Optimized dependencies

  return (
    <CurrentContextContext.Provider value={{ context, availableEmployments, loading }}>
      {children}
    </CurrentContextContext.Provider>
  );
}

export function useLunaAuth() {
  const context = useContext(LunaAuthContext);
  if (context === undefined) {
    throw new Error('useLunaAuth must be used within a LunaAuthProvider');
  }
  return context;
}

export function useCurrentContext() {
  const context = useContext(CurrentContextContext);
  if (context === undefined) {
    throw new Error('useCurrentContext must be used within a CurrentContextProvider');
  }
  return context;
}
