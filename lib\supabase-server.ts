import { createClient as createSupabase<PERSON>lient } from '@supabase/supabase-js';
import {
  createServerComponentClient,
  createRouteHandlerClient
} from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/database.types';

/**
 * Create a Supabase client for server components with user authentication
 * Use this in server components that need user authentication
 */
export const createServerClient = async () => {
  const cookieStore = await cookies();
  return createServerComponentClient<Database>({
    cookies: () => cookieStore
  });
};

/**
 * Create a Supabase client for API route handlers with user authentication
 * Use this in API routes that need user authentication
 */
export const createApiClient = async () => {
  console.log('[SUPABASE] 🔧 Creating API client...');
  try {
    const cookieStore = await cookies();
    console.log('[SUPABASE] 🍪 Cookies retrieved, creating route handler client...');
    const client = createRouteHandlerClient<Database>({
      cookies: () => cookieStore
    });
    console.log('[SUPABASE] ✅ API client created successfully');
    return client;
  } catch (error) {
    console.error('[SUPABASE] ❌ Error creating API client:', error);
    throw error;
  }
};

/**
 * Create a Supabase admin client (service role) for server operations
 * ONLY use this when you need to bypass RLS policies
 */
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createSupabaseClient<Database>(
    supabaseUrl,
    supabaseKey,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    }
  );
}

/**
 * Compatibility function for legacy imports
 * @deprecated Use createApiClient() instead
 */
export function createClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createSupabaseClient<Database>(
    supabaseUrl,
    supabaseKey,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      }
    }
  );
}