import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { Database } from '@/types/database.types';

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    // Parse request body
    const body = await req.json();
    const { email, password, role, full_name, timezone } = body;
    
    // Validate required fields
    if (!email || !password || !role || !full_name) {
      return NextResponse.json(
        { error: 'Email, password, role, and full name are required' },
        { status: 400 }
      );
    }
    
    // Validate password length
    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }
    
    // Create admin client for creating users
    const adminClient = createAdminClient();
    
    // Create user in Supabase Auth with display name
    const { data, error } = await adminClient.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: full_name
      },
    });
    
    if (error || !data.user) {
      return NextResponse.json(
        { error: error?.message || 'Failed to create user in Auth' },
        { status: 500 }
      );
    }
    
    // Create user in database
    const { error: profileError } = await adminClient
      .from('users')
      .insert([
        {
          id: data.user.id,
          email,
          full_name,
          role,
          timezone: timezone || 'UTC',
          status: 'active',
        },
      ]);
    
    if (profileError) {
      // If there's an error creating the profile, clean up by deleting the auth user
      await adminClient.auth.admin.deleteUser(data.user.id);
      
      return NextResponse.json(
        { error: profileError.message || 'Failed to create user profile' },
        { status: 500 }
      );
    }
    
    // Create user context record
    const { error: contextError } = await adminClient
      .from('user_contexts')
      .insert({
        user_id: data.user.id,
        active_context: role === 'individual' ? 'individual' : 'organization',
        active_organization_id: null
      });

    if (contextError) {
      console.error('Error creating user context:', contextError);
      // Don't fail the request for context creation errors, just log them
    }

    // If the user is an individual, create an individual record
    if (role === 'individual') {
      const { error: individualError } = await adminClient
        .from('individuals')
        .insert({
          user_id: data.user.id,
          contact_info: { email },
          profile_visibility: 'private',
          searchable_by_orgs: false,
          learning_status: 'not_started'
        });

      if (individualError) {
        // Log the error but don't fail the request, as the main user is created
        console.error('Error creating individual record:', individualError);
      }
    }
    
    return NextResponse.json({ 
      success: true, 
      user: { 
        id: data.user.id, 
        email, 
        role, 
        full_name 
      } 
    });
  } catch (error: any) {
    console.error('User creation API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    // Create admin client
    const adminClient = createAdminClient();

    // Get all users
    const { data: users, error: usersError } = await adminClient
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (usersError) {
      return NextResponse.json(
        { error: usersError.message || 'Failed to fetch users' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ users: users || [] });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
} 