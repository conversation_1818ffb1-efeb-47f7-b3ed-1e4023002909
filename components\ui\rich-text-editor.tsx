"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface RichTextEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export const RichTextEditor = React.forwardRef<
  HTMLTextAreaElement,
  RichTextEditorProps
>(({ value, onChange, placeholder, className, disabled, ...props }, ref) => {
  return (
    <div className={cn("relative", className)}>
      <textarea
        ref={ref}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        className={cn(
          "flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        {...props}
      />
      <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
        Rich text editor (basic)
      </div>
    </div>
  );
});

RichTextEditor.displayName = "RichTextEditor";
