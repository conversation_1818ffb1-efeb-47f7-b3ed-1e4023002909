'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Upload, 
  X, 
  File, 
  Image as ImageIcon, 
  Loader2,
  UploadCloud,
  AlertTriangle
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FileUploadProps {
  bucket?: string;
  path?: string;
  acceptedFileTypes?: string;
  maxSize?: number;
  onUploadComplete: (url: string) => void;
  className?: string;
  currentFileUrl?: string;
  label?: string;
}

export function FileUpload({
  bucket = 'training-media',
  path = '',
  acceptedFileTypes = 'image/*',
  maxSize = 5 * 1024 * 1024, // 5MB by default
  onUploadComplete,
  className,
  currentFileUrl,
  label = 'Upload File'
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentFileUrl || null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [storageAvailable, setStorageAvailable] = useState<boolean | null>(null);
  const [usingFallbackMode, setUsingFallbackMode] = useState(false);

  // Create Supabase client for storage
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  
  // Set currentFileUrl as preview if available
  useEffect(() => {
    if (currentFileUrl) {
      setPreviewUrl(currentFileUrl);
    }
  }, [currentFileUrl]);
  
  // Check if storage API is available
  useEffect(() => {
    const checkStorage = async () => {
      try {
        // Simple test to check if we can access the storage API
        await supabase.storage.getBucket(bucket);
        setStorageAvailable(true);
      } catch (err) {
        console.warn('Storage API not available:', err);
        setStorageAvailable(false);
      }
    };
    
    checkStorage();
  }, [bucket]);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;
    
    const file = acceptedFiles[0];
    setError(null);
    setIsUploading(true);
    
    // Create a local preview URL for fallback
    let dataUrl: string | null = null;
    if (file.type.startsWith('image/')) {
      try {
        dataUrl = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });
      } catch (e) {
        console.error('Failed to create data URL:', e);
      }
    }
    
    try {
      // If storage isn't available, use the local preview only
      if (storageAvailable === false) {
        if (!dataUrl) {
          throw new Error('Storage API is not available and file is not an image for local preview');
        }
        
        setPreviewUrl(dataUrl);
        setFileName(file.name);
        onUploadComplete(dataUrl);
        setUsingFallbackMode(true);
        
        setIsUploading(false);
        return;
      }
      
      // Try to upload to Supabase
      try {
        // Generate a unique filename to avoid collisions
        const fileExt = file.name.split('.').pop();
        const filePath = path ? `${path}/${uuidv4()}.${fileExt}` : `${uuidv4()}.${fileExt}`;
        
        // Upload the file to Supabase Storage
        const { data, error } = await supabase
          .storage
          .from(bucket)
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: true
          });

        if (error) {
          // If there's an RLS policy error, try to create a signed URL instead
          if (error.message && (
              error.message.includes('row-level security') || 
              error.message.includes('permission denied') ||
              error.message.includes('policy')
            )) {
            console.warn('RLS policy error, falling back to data URL:', error.message);
            throw new Error('Policy restriction: ' + error.message);
          }
          
          console.error('Storage upload error:', error);
          throw new Error(`Upload failed: ${error.message}`);
        }

        // Get the public URL
        const publicUrlResult = supabase
          .storage
          .from(bucket)
          .getPublicUrl(data.path);
        
        const publicUrl = publicUrlResult?.data?.publicUrl;
        if (!publicUrl) {
          throw new Error('Failed to get public URL for the uploaded file');
        }
        
        console.log('Upload successful, URL:', publicUrl);

        // Set preview
        if (file.type.startsWith('image/')) {
          const objectUrl = URL.createObjectURL(file);
          setPreviewUrl(objectUrl);
        } else {
          setPreviewUrl(publicUrl);
        }

        setFileName(file.name);
        
        // Call the callback with the file URL
        onUploadComplete(publicUrl);
      } catch (uploadError: any) {
        console.error('Upload to Supabase failed:', uploadError);
        
        // Use the data URL fallback if we have one
        if (dataUrl) {
          console.log('Using data URL fallback after upload error');
          setPreviewUrl(dataUrl);
          setFileName(file.name);
          onUploadComplete(dataUrl);
          setUsingFallbackMode(true);
        } else {
          throw uploadError;
        }
      }
    } catch (err: any) {
      console.error('Upload error details:', err);
      
      let errorMessage = 'Failed to upload file';
      
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'object' && err !== null) {
        errorMessage = JSON.stringify(err);
      }
      
      setError(errorMessage);
    } finally {
      setIsUploading(false);
    }
  }, [bucket, path, onUploadComplete, storageAvailable]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      [acceptedFileTypes]: []
    },
    maxSize,
    multiple: false
  });

  const clearFile = () => {
    setPreviewUrl(null);
    setFileName(null);
    onUploadComplete('');
  };

  const isImage = previewUrl && (
    previewUrl.endsWith('.jpg') || 
    previewUrl.endsWith('.jpeg') || 
    previewUrl.endsWith('.png') || 
    previewUrl.endsWith('.gif') || 
    previewUrl.endsWith('.webp')
  );

  return (
    <div className={className}>
      {storageAvailable === false && (
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Storage API not available. Using local preview mode only.
          </AlertDescription>
        </Alert>
      )}
      
      {usingFallbackMode && (
        <Alert className="mb-4 bg-amber-50 border-amber-200">
          <AlertTriangle className="h-4 w-4 text-amber-500" />
          <AlertDescription className="text-amber-800">
            Using local file preview mode. The file is not stored on the server.
          </AlertDescription>
        </Alert>
      )}
      
      {!previewUrl ? (
        <div
          {...getRootProps()}
          className={cn(
            'border-2 border-dashed rounded-md p-4 text-center cursor-pointer transition-colors',
            isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/20 hover:border-primary/50',
            isUploading && 'opacity-50 pointer-events-none'
          )}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center justify-center gap-2 p-4">
            {isUploading ? (
              <Loader2 className="h-10 w-10 text-muted-foreground animate-spin" />
            ) : (
              <UploadCloud className="h-10 w-10 text-muted-foreground" />
            )}
            <div className="space-y-1 text-center">
              <p className="text-sm font-medium">{label}</p>
              <p className="text-xs text-muted-foreground">
                Drag & drop or click to upload
              </p>
              <p className="text-xs text-muted-foreground">
                {acceptedFileTypes.replace('*', '').replace('/', '')} (max {Math.round(maxSize / 1024 / 1024)}MB)
              </p>
            </div>
          </div>
        </div>
      ) : (
        <div className="relative border rounded-md overflow-hidden">
          {isImage ? (
            <div className="relative aspect-video w-full bg-muted">
              <img 
                src={previewUrl} 
                alt="Preview" 
                className="w-full h-full object-cover"
              />
            </div>
          ) : (
            <div className="flex items-center gap-2 p-3">
              <File className="h-6 w-6 text-blue-500" />
              <span className="text-sm font-medium truncate flex-1">
                {fileName || previewUrl.split('/').pop()}
              </span>
            </div>
          )}
          <div className="absolute top-2 right-2 flex gap-1">
            <Button 
              variant="secondary" 
              size="icon" 
              onClick={clearFile}
              className="h-8 w-8 rounded-full bg-white/90 shadow-sm"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
      
      {error && (
        <p className="text-sm text-red-500 mt-1">
          {error}
        </p>
      )}
    </div>
  );
} 