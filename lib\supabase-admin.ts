import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/database.types';

/**
 * Create a Supabase admin client with service role key
 * This client bypasses RLS and should only be used in server-side code
 */
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

/**
 * Create a Supabase client for server-side operations with user context
 * This maintains RLS policies
 */
export function createServerClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables');
  }

  return createClient<Database>(supabaseUrl, supabaseAnonKey);
}

/**
 * Execute a database operation with admin privileges
 * Use this for operations that need to bypass RLS
 */
export async function executeAsAdmin<T>(
  operation: (client: ReturnType<typeof createAdminClient>) => Promise<T>
): Promise<T> {
  const adminClient = createAdminClient();
  return await operation(adminClient);
}

/**
 * Batch database operations with admin privileges
 */
export async function batchAdminOperations<T>(
  operations: Array<(client: ReturnType<typeof createAdminClient>) => Promise<T>>
): Promise<T[]> {
  const adminClient = createAdminClient();
  return await Promise.all(operations.map(op => op(adminClient)));
}

/**
 * Safe admin query with error handling
 */
export async function safeAdminQuery<T>(
  query: (client: ReturnType<typeof createAdminClient>) => Promise<{ data: T | null; error: any }>
): Promise<{ data: T | null; error: any; success: boolean }> {
  try {
    const adminClient = createAdminClient();
    const result = await query(adminClient);
    
    return {
      ...result,
      success: !result.error && result.data !== null
    };
  } catch (error) {
    console.error('Admin query error:', error);
    return {
      data: null,
      error: error instanceof Error ? error : new Error('Unknown error'),
      success: false
    };
  }
}
