'use client';

import { useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useLunaAuth } from '@/hooks/use-luna-auth';
import { LunaLayout } from '@/components/luna-layout';

interface OrganizationLayoutProps {
  children: React.ReactNode;
}

export default function OrganizationLayout({ children }: OrganizationLayoutProps) {
  const params = useParams();
  const router = useRouter();
  const { user, loading } = useLunaAuth();

  const orgSlug = params.orgSlug as string;

  useEffect(() => {
    if (loading) return;

    if (!user) {
      router.push('/login');
      return;
    }

    // Check if user has access to this organization
    const employment = user.employmentRelationships.find(
      emp => emp.organization_slug === orgSlug
    );

    if (!employment && user.role !== 'platform_admin') {
      router.push('/unauthorized');
      return;
    }

    // Check if user is organization admin
    const isOrgAdmin = employment?.employment_role === 'organization_admin' || user.role === 'platform_admin';

    if (!isOrgAdmin) {
      router.push('/unauthorized');
      return;
    }
  }, [user, loading, orgSlug, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <LunaLayout>
      <div className="p-6">
        {children}
      </div>
    </LunaLayout>
  );
}
