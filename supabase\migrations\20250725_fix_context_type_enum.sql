-- Fix context_type enum to use 'organization' instead of 'team'
-- This aligns the database with the frontend code expectations

-- Step 1: Add 'organization' value to the enum
ALTER TYPE context_type ADD VALUE IF NOT EXISTS 'organization';

-- Step 2: Update existing 'team' values to 'organization' in user_contexts table
UPDATE user_contexts 
SET context_type = 'organization' 
WHERE context_type = 'team';

-- Step 3: Update existing 'team' values to 'organization' in user_training_data table
UPDATE user_training_data 
SET training_context = 'organization' 
WHERE training_context = 'team';

-- Note: We cannot remove the 'team' value from the enum without recreating it
-- But since we've updated all data to use 'organization', the 'team' value is now unused
