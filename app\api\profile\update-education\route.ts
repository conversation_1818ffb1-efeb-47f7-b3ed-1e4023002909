import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Create admin client for bypassing RLS policies
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "";
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);

export async function POST(req: NextRequest) {
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Get session to verify user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized - You must be logged in' },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { action, educationData, educationIndex } = body;
    
    console.log('Education API - Request:', { action, educationData, educationIndex });
    
    // Get the prospect's profile - just to ensure authorization
    const { data: userData } = await supabase
      .from('users')
      .select('id, role')
      .eq('id', session.user.id)
      .single();
      
    if (!userData) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
      
    // Now get the prospect ID directly using the admin client
    const { data: prospectData, error: prospectError } = await adminClient
      .from('prospects')
      .select('id, education')
      .eq('user_id', session.user.id)
      .single();
    
    if (prospectError) {
      console.error('Education API - Error fetching prospect:', prospectError);
      return NextResponse.json(
        { error: prospectError.message || 'Failed to find prospect profile' },
        { status: 500 }
      );
    }
    
    console.log('Education API - Prospect data:', { 
      id: prospectData.id, 
      educationLength: Array.isArray(prospectData.education) ? prospectData.education.length : 0,
    });
    
    // Ensure education is always an array, even if null or undefined
    let education = Array.isArray(prospectData.education) ? JSON.parse(JSON.stringify(prospectData.education)) : [];
    
    console.log('Education API - Initial education array:', education);
    
    // Sanitize education entries to ensure they're valid
    const sanitizeEducation = (edu: any) => {
      // Get the current flag first to determine how to handle end_year
      const isCurrent = typeof edu.current === 'boolean' ? edu.current : false;
      
      return {
        institution: typeof edu.institution === 'string' ? edu.institution : '',
        degree: typeof edu.degree === 'string' ? edu.degree : '',
        field_of_study: typeof edu.field_of_study === 'string' ? edu.field_of_study : '',
        start_year: typeof edu.start_year === 'number' ? edu.start_year : null,
        // Only set end_year to null when current is true
        end_year: isCurrent ? null : (edu.end_year || null),
        location: typeof edu.location === 'string' ? edu.location : '',
        description: typeof edu.description === 'string' ? edu.description : '',
        current: isCurrent,
      };
    };
    
    // Handle the different actions
    switch (action) {
      case 'add':
        // Add a new education entry (sanitized)
        education.push(sanitizeEducation(educationData));
        break;
        
      case 'update':
        // Update an existing education entry
        if (educationIndex >= 0 && educationIndex < education.length) {
          education[educationIndex] = {
            ...sanitizeEducation(education[educationIndex]),
            ...sanitizeEducation(educationData)
          };
        } else {
          return NextResponse.json(
            { error: 'Invalid education index' },
            { status: 400 }
          );
        }
        break;
        
      case 'delete':
        // Delete an education entry
        if (educationIndex >= 0 && educationIndex < education.length) {
          education = education.filter((_: any, index: number) => index !== educationIndex);
        } else {
          return NextResponse.json(
            { error: 'Invalid education index' },
            { status: 400 }
          );
        }
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
    
    try {
      // Execute a direct database update bypassing all RLS policies
      console.log('Education API - Directly updating prospect with ID:', prospectData.id);
      const educationJson = JSON.stringify(education);
      
      // Make a direct API call to update education
      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_VERCEL_URL || "http://localhost:3000";
      const response = await fetch(`${baseUrl}/api/direct-update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.API_SECRET_KEY || "bpo-training-platform-api-key"}`
        },
        body: JSON.stringify({
          table: "prospects",
          id: prospectData.id,
          data: { education }
        })
      });
      
      if (!response.ok) {
        throw new Error("Failed to update education through direct API");
      }
      
      // Since we can't guarantee our API call worked, let's also try a direct update
      // fallback approach through the admin client
      const { error: updateError } = await adminClient
        .from('prospects')
        .update({ education })
        .eq('id', prospectData.id);
        
      if (updateError) {
        console.error("Admin client update failed as fallback, but direct API may have succeeded:", updateError);
      }
      
      return NextResponse.json({
        success: true,
        message: `Education ${action === 'add' ? 'added' : action === 'update' ? 'updated' : action === 'delete' ? 'deleted' : 'updated'} successfully`,
        education
      });
    } catch (dbError: any) {
      console.error('Education API - Database operation error:', dbError);
      
      // Last resort fallback using the admin client if the first approach failed
      try {
        const { error: fallbackError } = await adminClient
          .from('prospects')
          .update({ education })
          .eq('id', prospectData.id);
          
        if (fallbackError) {
          console.error('Education API - Fallback update error:', fallbackError);
          return NextResponse.json(
            { error: fallbackError.message || 'Failed to update education' },
            { status: 500 }
          );
        }
        
        return NextResponse.json({
          success: true,
          message: `Education ${action === 'add' ? 'added' : action === 'update' ? 'updated' : action === 'delete' ? 'deleted' : 'updated'} successfully (fallback)`,
          education
        });
      } catch (fallbackError: any) {
        console.error('Education API - Fallback error:', fallbackError);
        return NextResponse.json(
          { error: "All update methods failed" },
          { status: 500 }
        );
      }
    }
  } catch (error: any) {
    console.error('Error updating education:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 