import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase-admin';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const level = searchParams.get('level');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Create admin client
    const adminClient = createAdminClient();

    // Build query
    let query = adminClient
      .from('courses')
      .select(`
        *,
        instructor:users!courses_instructor_id_fkey(
          id,
          full_name,
          email
        ),
        course_modules(
          id,
          name,
          status,
          sequence_order
        )
      `)
      .order('created_at', { ascending: false });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (level && level !== 'all') {
      query = query.eq('level', level);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,tags.cs.{${search}}`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: courses, error: coursesError } = await query;

    if (coursesError) {
      console.error('Error fetching courses:', coursesError);
      return NextResponse.json(
        { error: coursesError.message || 'Failed to fetch courses' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = adminClient
      .from('courses')
      .select('*', { count: 'exact', head: true });

    if (status && status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    if (level && level !== 'all') {
      countQuery = countQuery.eq('level', level);
    }

    if (search) {
      countQuery = countQuery.or(`name.ilike.%${search}%,description.ilike.%${search}%,tags.cs.{${search}}`);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting courses:', countError);
    }

    return NextResponse.json({
      courses: courses || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error: any) {
    console.error('Courses GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      price,
      preview_video_url,
      cover_image_url,
      level,
      estimated_duration,
      status,
      slug,
      meta_description,
      tags,
      learning_objectives,
      target_audience,
      instructor_id,
      instructor_bio
    } = body;

    // Validate required fields
    if (!name || !description) {
      return NextResponse.json(
        { error: 'Name and description are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Generate slug if not provided
    const finalSlug = slug || name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Check if slug is unique
    const { data: existingCourse, error: slugError } = await adminClient
      .from('courses')
      .select('id')
      .eq('slug', finalSlug)
      .single();

    if (slugError && slugError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking slug uniqueness:', slugError);
      return NextResponse.json(
        { error: 'Failed to validate course slug' },
        { status: 500 }
      );
    }

    if (existingCourse) {
      return NextResponse.json(
        { error: 'A course with this slug already exists' },
        { status: 400 }
      );
    }

    // Create course
    const { data: course, error: courseError } = await adminClient
      .from('courses')
      .insert({
        name: name.trim(),
        description: description.trim(),
        price: price ? parseFloat(price) : null,
        preview_video_url,
        cover_image_url,
        level: level || 'beginner',
        estimated_duration: estimated_duration ? parseInt(estimated_duration) : null,
        status: status || 'draft',
        slug: finalSlug,
        meta_description: meta_description?.trim(),
        tags: tags || [],
        learning_objectives: learning_objectives || [],
        target_audience: target_audience?.trim(),
        instructor_id: instructor_id || authResult.user.id,
        instructor_bio: instructor_bio?.trim(),
        enrollment_count: 0,
        completion_rate: 0,
        average_rating: 0
      })
      .select(`
        *,
        instructor:users!courses_instructor_id_fkey(
          id,
          full_name,
          email
        )
      `)
      .single();

    if (courseError) {
      console.error('Error creating course:', courseError);
      return NextResponse.json(
        { error: courseError.message || 'Failed to create course' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Course created successfully',
      course
    });

  } catch (error: any) {
    console.error('Course creation API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
