/**
 * Enhanced Security Audit Logger
 * Provides comprehensive logging for security events and API access
 */

import { NextRequest } from 'next/server';

export enum SecurityEventType {
  // Authentication Events
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  
  // Authorization Events
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  ROLE_ESCALATION_ATTEMPT = 'ROLE_ESCALATION_ATTEMPT',
  ADMIN_ACCESS = 'ADMIN_ACCESS',
  
  // Rate Limiting Events
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  
  // Input Validation Events
  MALFORMED_REQUEST = 'MALFORMED_REQUEST',
  INJECTION_ATTEMPT = 'INJECTION_ATTEMPT',
  XSS_ATTEMPT = 'XSS_ATTEMPT',
  
  // File Upload Events
  FILE_UPLOAD_SUCCESS = 'FILE_UPLOAD_SUCCESS',
  FILE_UPLOAD_FAILURE = 'FILE_UPLOAD_FAILURE',
  MALICIOUS_FILE_DETECTED = 'MALICIOUS_FILE_DETECTED',
  FILE_SIZE_EXCEEDED = 'FILE_SIZE_EXCEEDED',
  
  // Data Access Events
  SENSITIVE_DATA_ACCESS = 'SENSITIVE_DATA_ACCESS',
  DATA_EXPORT = 'DATA_EXPORT',
  BULK_OPERATION = 'BULK_OPERATION',
  
  // System Events
  API_ERROR = 'API_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE'
}

export enum SecurityLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export interface SecurityEvent {
  id: string;
  timestamp: string;
  type: SecurityEventType;
  level: SecurityLevel;
  message: string;
  userId?: string;
  userEmail?: string;
  userRole?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  responseTime?: number;
  metadata?: Record<string, any>;
  stackTrace?: string;
}

class SecurityAuditLogger {
  private events: SecurityEvent[] = [];
  private maxEvents = 10000; // Keep last 10k events in memory

  /**
   * Log a security event
   */
  logEvent(
    type: SecurityEventType,
    message: string,
    level: SecurityLevel = SecurityLevel.MEDIUM,
    metadata?: Record<string, any>,
    request?: NextRequest,
    userId?: string,
    userEmail?: string,
    userRole?: string
  ): void {
    const event: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      type,
      level,
      message,
      userId,
      userEmail,
      userRole,
      ipAddress: this.getClientIP(request),
      userAgent: request?.headers.get('user-agent') || undefined,
      endpoint: request?.nextUrl.pathname,
      method: request?.method,
      metadata
    };

    // Add to in-memory store
    this.events.push(event);
    
    // Keep only recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔒 [${level}] ${type}: ${message}`, metadata || '');
    }

    // In production, you would send this to your logging service
    // Example: await this.sendToLoggingService(event);
  }

  /**
   * Log authentication events
   */
  logAuthEvent(
    type: SecurityEventType,
    userId: string,
    userEmail: string,
    success: boolean,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const level = success ? SecurityLevel.LOW : SecurityLevel.HIGH;
    const message = success 
      ? `Authentication successful for user ${userEmail}`
      : `Authentication failed for user ${userEmail}`;

    this.logEvent(type, message, level, metadata, request, userId, userEmail);
  }

  /**
   * Log API access events
   */
  logApiAccess(
    request: NextRequest,
    statusCode: number,
    responseTime: number,
    userId?: string,
    userEmail?: string,
    userRole?: string,
    metadata?: Record<string, any>
  ): void {
    const level = statusCode >= 400 ? SecurityLevel.MEDIUM : SecurityLevel.LOW;
    const message = `API access: ${request.method} ${request.nextUrl.pathname} - ${statusCode}`;

    const event: SecurityEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      type: statusCode >= 400 ? SecurityEventType.API_ERROR : SecurityEventType.SENSITIVE_DATA_ACCESS,
      level,
      message,
      userId,
      userEmail,
      userRole,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || undefined,
      endpoint: request.nextUrl.pathname,
      method: request.method,
      statusCode,
      responseTime,
      metadata
    };

    this.events.push(event);
    
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }
  }

  /**
   * Log rate limiting events
   */
  logRateLimitEvent(
    request: NextRequest,
    clientId: string,
    limit: number,
    remaining: number,
    resetTime: number
  ): void {
    this.logEvent(
      SecurityEventType.RATE_LIMIT_EXCEEDED,
      `Rate limit exceeded for client ${clientId}`,
      SecurityLevel.HIGH,
      { clientId, limit, remaining, resetTime },
      request
    );
  }

  /**
   * Log file upload events
   */
  logFileUploadEvent(
    type: SecurityEventType,
    fileName: string,
    fileSize: number,
    fileType: string,
    userId: string,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const level = type === SecurityEventType.MALICIOUS_FILE_DETECTED 
      ? SecurityLevel.CRITICAL 
      : SecurityLevel.LOW;
    
    const message = `File upload: ${fileName} (${fileSize} bytes, ${fileType})`;

    this.logEvent(type, message, level, {
      fileName,
      fileSize,
      fileType,
      ...metadata
    }, request, userId);
  }

  /**
   * Get recent security events
   */
  getRecentEvents(limit: number = 100, level?: SecurityLevel): SecurityEvent[] {
    let filteredEvents = this.events;
    
    if (level) {
      filteredEvents = this.events.filter(event => event.level === level);
    }
    
    return filteredEvents
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Get events by type
   */
  getEventsByType(type: SecurityEventType, limit: number = 100): SecurityEvent[] {
    return this.events
      .filter(event => event.type === type)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Get events for a specific user
   */
  getUserEvents(userId: string, limit: number = 100): SecurityEvent[] {
    return this.events
      .filter(event => event.userId === userId)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  /**
   * Get security statistics
   */
  getSecurityStats(): {
    totalEvents: number;
    eventsByLevel: Record<SecurityLevel, number>;
    eventsByType: Record<SecurityEventType, number>;
    recentCriticalEvents: SecurityEvent[];
  } {
    const eventsByLevel = Object.values(SecurityLevel).reduce((acc, level) => {
      acc[level] = this.events.filter(event => event.level === level).length;
      return acc;
    }, {} as Record<SecurityLevel, number>);

    const eventsByType = Object.values(SecurityEventType).reduce((acc, type) => {
      acc[type] = this.events.filter(event => event.type === type).length;
      return acc;
    }, {} as Record<SecurityEventType, number>);

    const recentCriticalEvents = this.getRecentEvents(10, SecurityLevel.CRITICAL);

    return {
      totalEvents: this.events.length,
      eventsByLevel,
      eventsByType,
      recentCriticalEvents
    };
  }

  /**
   * Clear old events (for maintenance)
   */
  clearOldEvents(olderThanDays: number = 30): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    this.events = this.events.filter(
      event => new Date(event.timestamp) > cutoffDate
    );
  }

  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getClientIP(request?: NextRequest): string | undefined {
    if (!request) return undefined;
    
    return request.headers.get('x-forwarded-for') ||
           request.headers.get('x-real-ip') ||
           request.headers.get('cf-connecting-ip') ||
           'unknown';
  }
}

// Export singleton instance
export const securityAuditLogger = new SecurityAuditLogger();

// Export types and enums
export { SecurityAuditLogger };
