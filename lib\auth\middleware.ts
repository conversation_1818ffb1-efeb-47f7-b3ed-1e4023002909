/**
 * Authentication Middleware
 * Higher-order functions for protecting API routes and components
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from './core';
import { createAuthErrorResponse, isPlatformAdmin } from './utils';
import { AuthUser } from './types';

/**
 * Require platform admin privileges
 */
export async function requirePlatformAdmin() {
  const authResult = await getAuthenticatedUser();
  
  if (!authResult.user) {
    return authResult;
  }

  if (!isPlatformAdmin(authResult.user)) {
    return {
      user: null,
      error: 'Platform admin privileges required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Require organization admin privileges for specific organization
 */
export async function requireOrganizationAdmin(organizationId: string) {
  const authResult = await getAuthenticatedUser();
  
  if (!authResult.user) {
    return authResult;
  }

  const user = authResult.user;
  
  // Platform admins have access to everything
  if (isPlatformAdmin(user)) {
    return authResult;
  }

  // Check if user is admin of the specific organization
  const hasOrgAccess = user.employmentRelationships.some(
    emp => emp.organization_id === organizationId && 
           emp.employment_role === 'organization_admin'
  );

  if (!hasOrgAccess) {
    return {
      user: null,
      error: 'Organization admin privileges required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Require department admin privileges for specific department
 */
export async function requireDepartmentAdmin(departmentId: string) {
  const authResult = await getAuthenticatedUser();
  
  if (!authResult.user) {
    return authResult;
  }

  const user = authResult.user;
  
  // Platform admins have access to everything
  if (isPlatformAdmin(user)) {
    return authResult;
  }

  // Check if user is admin of the specific department or its organization
  const hasDeptAccess = user.employmentRelationships.some(
    emp => emp.department_id === departmentId && 
           (emp.employment_role === 'organization_admin' || 
            emp.employment_role === 'department_admin')
  );

  if (!hasDeptAccess) {
    return {
      user: null,
      error: 'Department admin privileges required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Handle authentication in API routes with consistent error responses
 */
export async function withAuth<T>(
  handler: (user: AuthUser) => Promise<T>,
  requireAdmin = false
): Promise<NextResponse> {
  try {
    const authResult = requireAdmin
      ? await requirePlatformAdmin()
      : await getAuthenticatedUser();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const result = await handler(authResult.user);
    return NextResponse.json(result);

  } catch (error) {
    console.error('API handler error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Protect API route with authentication
 */
export function withAuthProtection(
  handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>
) {
  return async function (request: NextRequest): Promise<NextResponse> {
    const authResult = await getAuthenticatedUser();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return handler(request, authResult.user);
  };
}

/**
 * Protect API route with platform admin requirement
 */
export function withAdminProtection(
  handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>
) {
  return async function (request: NextRequest): Promise<NextResponse> {
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return handler(request, authResult.user);
  };
}

/**
 * Protect API route with organization admin requirement
 */
export function withOrgAdminProtection(
  getOrganizationId: (request: NextRequest) => string,
  handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>
) {
  return async function (request: NextRequest): Promise<NextResponse> {
    const organizationId = getOrganizationId(request);
    const authResult = await requireOrganizationAdmin(organizationId);

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return handler(request, authResult.user);
  };
}

/**
 * Protect API route with department admin requirement
 */
export function withDeptAdminProtection(
  getDepartmentId: (request: NextRequest) => string,
  handler: (request: NextRequest, user: AuthUser) => Promise<NextResponse>
) {
  return async function (request: NextRequest): Promise<NextResponse> {
    const departmentId = getDepartmentId(request);
    const authResult = await requireDepartmentAdmin(departmentId);

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return handler(request, authResult.user);
  };
}
