'use server';

import { createAdminClient } from '@/lib/supabase';
import { AssessmentInsert, AssessmentUpdate, AssessmentQuestionInsert, AssessmentQuestionUpdate } from '@/types/assessment';
import { revalidatePath } from 'next/cache';

export async function adminCreateAssessment(assessment: AssessmentInsert) {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessments')
    .insert(assessment)
    .select()
    .single();

  if (error) {
    throw new Error(`Error creating assessment: ${error.message}`);
  }

  revalidatePath('/admin/assessments');
  return data;
}

export async function adminUpdateAssessment(id: string, assessment: AssessmentUpdate) {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessments')
    .update(assessment)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Error updating assessment: ${error.message}`);
  }

  revalidatePath('/admin/assessments');
  return data;
}

export async function adminDeleteAssessment(id: string) {
  const adminClient = createAdminClient();
  
  const { error } = await adminClient
    .from('assessments')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Error deleting assessment: ${error.message}`);
  }

  revalidatePath('/admin/assessments');
}

export async function adminToggleAssessmentVisibility(id: string, isActive: boolean) {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessments')
    .update({ is_active: isActive })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Error toggling assessment visibility: ${error.message}`);
  }

  revalidatePath('/admin/assessments');
  return data;
}

// Assessment Questions Management

export async function adminGetAssessmentQuestions(assessmentId: string) {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessment_questions')
    .select('*')
    .eq('assessment_id', assessmentId)
    .order('created_at', { ascending: true });

  if (error) {
    throw new Error(`Error fetching assessment questions: ${error.message}`);
  }

  return data;
}

export async function adminCreateQuestion(question: AssessmentQuestionInsert) {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessment_questions')
    .insert(question)
    .select()
    .single();

  if (error) {
    throw new Error(`Error creating question: ${error.message}`);
  }

  // Update the total_questions count in the assessment
  await updateQuestionCount(question.assessment_id);
  
  revalidatePath(`/admin/assessments/${question.assessment_id}/questions`);
  revalidatePath('/admin/assessments');
  return data;
}

export async function adminUpdateQuestion(id: string, question: AssessmentQuestionUpdate) {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessment_questions')
    .update(question)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Error updating question: ${error.message}`);
  }

  revalidatePath(`/admin/assessments/${question.assessment_id}/questions`);
  return data;
}

export async function adminDeleteQuestion(id: string, assessmentId: string) {
  const adminClient = createAdminClient();
  
  const { error } = await adminClient
    .from('assessment_questions')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Error deleting question: ${error.message}`);
  }

  // Update the total_questions count in the assessment
  await updateQuestionCount(assessmentId);
  
  revalidatePath(`/admin/assessments/${assessmentId}/questions`);
  revalidatePath('/admin/assessments');
}

// Helper function to update the total_questions count in the assessment
async function updateQuestionCount(assessmentId: string) {
  const adminClient = createAdminClient();
  
  // Count the questions for this assessment
  const { count, error: countError } = await adminClient
    .from('assessment_questions')
    .select('*', { count: 'exact', head: true })
    .eq('assessment_id', assessmentId);
  
  if (countError) {
    throw new Error(`Error counting questions: ${countError.message}`);
  }
  
  // Update the assessment
  const { error: updateError } = await adminClient
    .from('assessments')
    .update({ total_questions: count || 0 })
    .eq('id', assessmentId);

  if (updateError) {
    throw new Error(`Error updating question count: ${updateError.message}`);
  }
} 