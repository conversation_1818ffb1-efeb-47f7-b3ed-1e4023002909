import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth';
import { createApiClient } from '@/lib/supabase-server';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const supabase = await createApiClient();

    // Get current user context
    const { data: contextData, error: contextError } = await supabase
      .from('user_contexts')
      .select('active_context, active_organization_id')
      .eq('user_id', authResult.user.id)
      .single();

    if (contextError && contextError.code !== 'PGRST116') {
      console.error('Error fetching user context:', contextError);
      return NextResponse.json(
        { error: 'Failed to fetch context' },
        { status: 500 }
      );
    }

    // Get available organizations with detailed information
    const { data: memberships, error: membershipError } = await supabase
      .from('employment_relationships')
      .select(`
        organization_id,
        role,
        status,
        joined_at,
        organizations!inner(
          id,
          name,
          slug,
          description,
          industry,
          size,
          status
        )
      `)
      .eq('user_id', authResult.user.id)
      .eq('status', 'active');

    if (membershipError) {
      console.error('Error fetching employment relationships:', membershipError);
      return NextResponse.json(
        { error: 'Failed to fetch employment relationships' },
        { status: 500 }
      );
    }

    const availableOrganizations = memberships?.map(m => ({
      organization_id: m.organization_id,
      organization_name: (m.organizations as any)?.name,
      organization_slug: (m.organizations as any)?.slug,
      organization_description: (m.organizations as any)?.description,
      organization_industry: (m.organizations as any)?.industry,
      organization_size: (m.organizations as any)?.size,
      organization_status: (m.organizations as any)?.status,
      role: m.role,
      status: m.status,
      joined_at: m.joined_at,
    })) || [];

    // Determine available contexts
    const availableContexts = [];

    // Individual context - available to individual users and platform admins
    if (authResult.user.role === 'individual' || authResult.user.isPlatformAdmin) {
      availableContexts.push({
        type: 'individual',
        label: 'Individual Mode',
        description: 'Personal learning and profile management',
        available: true,
      });
    }

    // Organization contexts - one for each organization membership
    availableOrganizations.forEach(org => {
      availableContexts.push({
        type: 'organization',
        organization_id: org.organization_id,
        organization_name: org.organization_name,
        organization_slug: org.organization_slug,
        label: `${org.organization_name} (${org.role})`,
        description: `Manage ${org.organization_name} as ${org.role}`,
        role: org.role,
        available: true,
      });
    });

    return NextResponse.json({
      success: true,
      data: {
        currentContext: contextData ? {
          type: contextData.active_context,
          organization_id: contextData.active_organization_id,
        } : {
          type: authResult.user.role === 'individual' ? 'individual' : 'organization',
          organization_id: null,
        },
        availableContexts,
        availableOrganizations,
        user: {
          id: authResult.user.id,
          email: authResult.user.email,
          role: authResult.user.role,
          full_name: authResult.user.full_name,
          canSwitchToIndividual: authResult.user.role === 'individual' || authResult.user.isPlatformAdmin,
          hasOrganizations: availableOrganizations.length > 0,
        },
      },
    });

  } catch (error) {
    console.error('Get user contexts error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
