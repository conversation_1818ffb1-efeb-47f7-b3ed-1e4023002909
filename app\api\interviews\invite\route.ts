import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { sendInterviewInvitationEmail } from '@/lib/email-notifications'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const {
      applicationId,
      prospectId,
      message,
      interviewType = 'video_call',
      durationMinutes = 60,
      proposedTimes = [], // Array of proposed time slots for prospect to choose from
      autoGenerateSlots = true // Whether to auto-generate slots from interviewer's schedule
    } = await request.json()

    if (!applicationId && !prospectId) {
      return NextResponse.json(
        { error: 'Application ID or Prospect ID is required' },
        { status: 400 }
      )
    }

    // Verify the user has permission to invite for this application
    // Check if user is a BPO team member for the job posting
    const { data: bpoId, error: bpoIdError } = await supabase
      .rpc('get_user_bpo_id', { user_id_param: session.user.id })

    if (bpoIdError || !bpoId) {
      return NextResponse.json(
        { error: 'Unauthorized: BPO team member access required' },
        { status: 403 }
      )
    }

    let application: any

    if (applicationId) {
      // Get application details and verify it belongs to this BPO
      const { data: appData, error: appError } = await supabase
        .from('applications')
        .select(`
          id,
          job_id,
          prospect_id,
          status,
          job_postings!inner(bpo_id)
        `)
        .eq('id', applicationId)
        .single()

      if (appError || !appData) {
        return NextResponse.json(
          { error: 'Application not found' },
          { status: 404 }
        )
      }
      application = appData
    } else if (prospectId) {
      // Find the most recent application for this prospect from this BPO
      const { data: appData, error: appError } = await supabase
        .from('applications')
        .select(`
          id,
          job_id,
          prospect_id,
          status,
          job_postings!inner(bpo_id)
        `)
        .eq('prospect_id', prospectId)
        .eq('job_postings.bpo_id', bpoId)
        .order('submitted_at', { ascending: false })
        .limit(1)
        .single()

      if (appError || !appData) {
        return NextResponse.json(
          { error: 'No application found for this prospect' },
          { status: 404 }
        )
      }
      application = appData
    }

    // Verify the job posting belongs to the user's BPO
    if (application.job_postings.bpo_id !== bpoId) {
      return NextResponse.json(
        { error: 'Unauthorized: Application does not belong to your BPO' },
        { status: 403 }
      )
    }

    // Auto-generate available time slots from interviewer's schedule if requested
    let finalProposedTimes = proposedTimes;
    if (autoGenerateSlots && proposedTimes.length === 0) {
      try {
        // Fetch available slots from interviewer's schedule
        const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
        const response = await fetch(`${baseUrl}/api/bpo/schedule/available-slots?userId=${session.user.id}&durationMinutes=${durationMinutes}`, {
          headers: {
            'Cookie': cookieStore.toString()
          }
        });

        if (response.ok) {
          const { availableSlots } = await response.json();
          // Take first 5 available slots as proposed times
          finalProposedTimes = availableSlots.slice(0, 5).map((slot: any) => ({
            startTime: slot.startTime,
            endTime: slot.endTime,
            date: slot.date
          }));
        }
      } catch (error) {
        console.error('Error fetching available slots:', error);
        // Continue with empty proposed times if schedule fetch fails
      }
    }

    // Check for existing interview invitations for this application
    const { data: existingInterview, error: existingError } = await supabase
      .from('interviews')
      .select('id, status')
      .eq('application_id', application.id)
      .single()

    if (existingError && existingError.code !== 'PGRST116') {
      console.error('Error checking existing interviews:', existingError)
      return NextResponse.json(
        { error: 'Failed to check existing interviews' },
        { status: 500 }
      )
    }

    if (existingInterview) {
      return NextResponse.json(
        { error: 'An interview invitation already exists for this application' },
        { status: 409 }
      )
    }

    // Create interview invitation
    // Use a future date as placeholder for scheduled_at since it's required
    // The prospect will later select their preferred time from proposed options
    const placeholderDate = new Date()
    placeholderDate.setDate(placeholderDate.getDate() + 7) // 7 days from now

    // Create the interview record in the database
    const { data: interview, error: interviewError } = await supabase
      .from('interviews')
      .insert({
        application_id: application.id,
        bpo_user_id: session.user.id,
        scheduled_at: placeholderDate.toISOString(), // Placeholder until prospect selects time
        duration_minutes: durationMinutes,
        location: 'virtual',
        meeting_link: 'TBD',
        notes: message || 'Interview invitation sent',
        status: 'pending_scheduling',
        feedback: {
          interview_type: interviewType,
          proposed_times: finalProposedTimes,
          invitation_message: message,
          auto_generated_slots: autoGenerateSlots
        }
      })
      .select()
      .single()

    if (interviewError) {
      console.error('Error creating interview invitation:', interviewError)

      // Provide more specific error messages
      if (interviewError.code === '23505') {
        return NextResponse.json(
          { error: 'An interview invitation already exists for this application' },
          { status: 409 }
        )
      }

      if (interviewError.code === '23503') {
        return NextResponse.json(
          { error: 'Invalid application or user reference' },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to create interview invitation. Please try again.' },
        { status: 500 }
      )
    }

    // Update application status to indicate interview invitation sent
    const { error: updateError } = await supabase
      .from('applications')
      .update({
        status: 'interview_scheduled'
      })
      .eq('id', application.id)

    if (updateError) {
      console.error('Error updating application status:', updateError)
      // Don't fail the request, just log the error
    }

    // Send email notification to prospect
    try {
      const { data: prospectData, error: prospectError } = await supabase
        .from('prospects')
        .select(`
          users!inner(full_name, email)
        `)
        .eq('id', application.prospect_id)
        .single()

      if (!prospectError && prospectData) {
        await sendInterviewInvitationEmail({
          prospectName: prospectData.users.full_name || 'Candidate',
          prospectEmail: prospectData.users.email,
          bpoName: 'BPO Company', // You might want to fetch this from the BPO table
          jobTitle: 'Position', // You might want to fetch this from job_postings
          interviewType: interviewType,
          proposedTimes: finalProposedTimes,
          invitationMessage: message,
          interviewId: interview.id
        })
      }
    } catch (emailError) {
      console.error('Error sending invitation email:', emailError)
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      interview: interview
    })

  } catch (error) {
    console.error('Error in interview invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
