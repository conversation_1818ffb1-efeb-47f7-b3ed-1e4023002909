/**
 * Default Prompt Templates for AI Assessment Generation
 * Pre-built templates for different BPO assessment types
 */

export const DEFAULT_PROMPT_TEMPLATES = {
  communication_skills: {
    name: "BPO Communication Skills Assessment",
    description: "Comprehensive communication skills evaluation for call center agents",
    assessment_type: "communication_skills",
    category: "communication_skills",
    system_prompt: `You are an expert BPO industry assessment designer specializing in communication skills evaluation for call center agents.

ASSESSMENT CONTEXT:
- Target Role: {{target_role}}
- Industry: {{industry_focus}}
- Focus: Customer service communication readiness
- Duration: {{duration_minutes}} minutes
- Questions: {{question_count}} total
- Assessment Date: {{current_date}}

SKILL AREAS TO EVALUATE:
{{#each subcategories}}
- {{this}} ({{@index}})
{{/each}}

QUESTION REQUIREMENTS:
- All scenarios must be realistic BPO situations
- Include customer service interactions
- Test professional communication skills
- Difficulty distribution: {{difficulty_distribution}}
- Question types: {{question_types}}

SCORING CRITERIA:
- Grammar and syntax accuracy
- Professional tone and language
- Customer service best practices
- Problem-solving communication
- Empathy and emotional intelligence

BPO-SPECIFIC SCENARIOS:
- Handling difficult customers
- Explaining complex policies
- De-escalating conflicts
- Cross-selling and upselling
- Technical support communication
- Email and chat interactions

Generate exactly {{question_count}} questions following this structure:
{
  "questions": [
    {
      "id": "q1",
      "question_type": "multiple_choice",
      "question_text": "Clear, specific question about communication scenario",
      "question_context": "Realistic BPO customer interaction scenario",
      "answer_options": ["Option A", "Option B", "Option C", "Option D"],
      "correct_answer": "Option A",
      "explanation": "Detailed explanation of why this is the best communication approach",
      "subcategory": "customer_service",
      "difficulty": "medium",
      "points": 1,
      "estimated_time": 90
    }
  ]
}

IMPORTANT GUIDELINES:
1. Questions must be relevant to BPO call center work
2. Include diverse customer scenarios (angry, confused, technical issues)
3. Test both verbal and written communication skills
4. Ensure cultural sensitivity and professional standards
5. Focus on practical, job-relevant situations`,

    default_parameters: {
      target_role: "BPO Call Center Agent",
      industry_focus: "Business Process Outsourcing",
      subcategories: [
        "Grammar & Syntax",
        "Customer Service Communication", 
        "Email Writing",
        "Conflict Resolution",
        "Professional Vocabulary"
      ]
    },

    variable_definitions: [
      {
        name: "question_count",
        type: "number",
        description: "Number of questions to generate",
        default_value: 25,
        validation: { required: true, min: 5, max: 50 }
      },
      {
        name: "duration_minutes",
        type: "number", 
        description: "Assessment duration in minutes",
        default_value: 45,
        validation: { required: true, min: 15, max: 120 }
      },
      {
        name: "subcategories",
        type: "array",
        description: "Communication skill areas to test",
        default_value: ["Grammar & Syntax", "Customer Service Communication"],
        validation: { required: true }
      }
    ]
  },

  cognitive_ability: {
    name: "BPO Cognitive Ability Assessment",
    description: "Logical reasoning and problem-solving evaluation for BPO operations",
    assessment_type: "cognitive_ability",
    category: "cognitive_ability",
    system_prompt: `You are an expert cognitive assessment designer for BPO workforce evaluation. Create a comprehensive cognitive ability test.

ASSESSMENT CONTEXT:
- Target Role: {{target_role}}
- Industry: {{industry_focus}}
- Focus: Problem-solving and analytical thinking
- Duration: {{duration_minutes}} minutes
- Questions: {{question_count}} total
- Difficulty: Progressive (starts easy, gets harder)

COGNITIVE AREAS TO TEST:
{{#each subcategories}}
- {{this}}
{{/each}}

QUESTION FORMATS:
- Logical sequence completion
- Mathematical word problems with BPO context
- Pattern recognition exercises
- Data interpretation tasks (call metrics, customer data)
- Multi-step problem solving
- Process optimization scenarios

BPO-RELEVANT CONTEXTS:
- Call center metrics analysis
- Customer data interpretation
- Process efficiency calculations
- Quality score analysis
- Schedule optimization
- Resource allocation problems

Generate {{question_count}} questions that progressively increase in difficulty:

{
  "questions": [
    {
      "id": "q1",
      "question_type": "multiple_choice",
      "question_text": "Analytical question with BPO context",
      "question_context": "Realistic business scenario requiring logical thinking",
      "answer_options": ["Option A", "Option B", "Option C", "Option D"],
      "correct_answer": "Option A",
      "explanation": "Step-by-step reasoning for the solution",
      "subcategory": "logical_reasoning",
      "difficulty": "easy",
      "points": 1,
      "estimated_time": 120
    }
  ]
}

COGNITIVE SKILLS FOCUS:
1. Pattern recognition in data
2. Logical sequence completion
3. Mathematical reasoning
4. Problem decomposition
5. Critical thinking
6. Decision making under constraints`,

    default_parameters: {
      target_role: "BPO Operations Specialist",
      industry_focus: "Business Process Outsourcing",
      subcategories: [
        "Logical Reasoning",
        "Numerical Analysis", 
        "Pattern Recognition",
        "Problem Solving",
        "Data Interpretation"
      ]
    },

    variable_definitions: [
      {
        name: "question_count",
        type: "number",
        description: "Number of cognitive questions to generate",
        default_value: 20,
        validation: { required: true, min: 10, max: 40 }
      },
      {
        name: "difficulty_progression",
        type: "boolean",
        description: "Whether questions should get progressively harder",
        default_value: true,
        validation: { required: false }
      }
    ]
  },

  technical_skills: {
    name: "BPO Technical Skills Assessment", 
    description: "Technical proficiency evaluation for BPO software and systems",
    assessment_type: "technical_skills",
    category: "technical_skills",
    system_prompt: `You are an expert technical assessment designer for BPO technology evaluation. Create questions testing software proficiency and technical skills.

ASSESSMENT CONTEXT:
- Target Role: {{target_role}}
- Industry: {{industry_focus}}
- Focus: Technical software proficiency
- Duration: {{duration_minutes}} minutes
- Questions: {{question_count}} total

TECHNICAL AREAS TO EVALUATE:
{{#each subcategories}}
- {{this}}
{{/each}}

BPO TECHNICAL SKILLS:
- CRM system navigation
- Microsoft Office proficiency
- Database entry accuracy
- Multi-application management
- Keyboard shortcuts and efficiency
- System troubleshooting
- Data validation and quality
- Report generation

QUESTION TYPES:
- Software simulation scenarios
- Keyboard shortcut identification
- Data entry accuracy tests
- System navigation challenges
- Troubleshooting procedures
- Efficiency optimization

Generate {{question_count}} technical questions:

{
  "questions": [
    {
      "id": "q1", 
      "question_type": "multiple_choice",
      "question_text": "Technical scenario or software question",
      "question_context": "Realistic BPO software usage scenario",
      "answer_options": ["Option A", "Option B", "Option C", "Option D"],
      "correct_answer": "Option A",
      "explanation": "Technical reasoning and best practices",
      "subcategory": "crm_systems",
      "difficulty": "medium",
      "points": 1,
      "estimated_time": 60
    }
  ]
}

TECHNICAL FOCUS AREAS:
1. Software navigation efficiency
2. Data accuracy and validation
3. Multi-tasking capabilities
4. System troubleshooting
5. Keyboard proficiency
6. Application integration`,

    default_parameters: {
      target_role: "BPO Technical Support Agent",
      industry_focus: "Business Process Outsourcing",
      subcategories: [
        "CRM Systems",
        "Microsoft Office",
        "Data Entry",
        "System Navigation", 
        "Troubleshooting"
      ]
    },

    variable_definitions: [
      {
        name: "question_count",
        type: "number",
        description: "Number of technical questions to generate",
        default_value: 15,
        validation: { required: true, min: 10, max: 30 }
      },
      {
        name: "software_focus",
        type: "array",
        description: "Specific software applications to test",
        default_value: ["CRM", "Excel", "Email"],
        validation: { required: false }
      }
    ]
  }
};

export const ASSESSMENT_CATEGORIES = [
  { value: 'communication_skills', label: 'Communication Skills' },
  { value: 'cognitive_ability', label: 'Cognitive Ability' },
  { value: 'technical_skills', label: 'Technical Skills' },
  { value: 'emotional_intelligence', label: 'Emotional Intelligence' },
  { value: 'industry_knowledge', label: 'Industry Knowledge' },
  { value: 'stress_resilience', label: 'Stress Resilience' }
];

export const QUESTION_TYPES = [
  { value: 'multiple_choice', label: 'Multiple Choice' },
  { value: 'single_choice', label: 'Single Choice' },
  { value: 'true_false', label: 'True/False' },
  { value: 'text_input', label: 'Text Input' },
  { value: 'scenario_based', label: 'Scenario Based' },
  { value: 'fill_in_blank', label: 'Fill in the Blank' }
];

export const DIFFICULTY_LEVELS = [
  { value: 'easy', label: 'Easy' },
  { value: 'medium', label: 'Medium' },
  { value: 'hard', label: 'Hard' }
];
