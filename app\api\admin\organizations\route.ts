import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    // Create admin client
    const adminClient = createAdminClient();
    
    // Get organizations with department and employment counts
    const { data: organizations, error: orgsError } = await adminClient
      .from('organizations')
      .select(`
        *,
        departments(
          id,
          employment_relationships(
            id,
            user_id
          )
        )
      `)
      .order('created_at', { ascending: false });

    if (orgsError) {
      console.error('Error fetching organizations:', orgsError);
      return NextResponse.json(
        { error: orgsError.message || 'Failed to fetch organizations' },
        { status: 500 }
      );
    }

    // Calculate member counts from employment relationships
    const organizationsWithCounts = (organizations || []).map((org: any) => {
      const uniqueUserIds = new Set();

      // Count unique users across all departments in the organization
      org.departments?.forEach((department: any) => {
        department.employment_relationships?.forEach((employment: any) => {
          uniqueUserIds.add(employment.user_id);
        });
      });

      return {
        ...org,
        admin_user: null, // Skip admin user lookup for now
        _count: {
          users: uniqueUserIds.size,
          departments: org.departments?.length || 0
        },
        departments: undefined // Remove departments data from response to keep it clean
      };
    });
    
    return NextResponse.json(organizationsWithCounts);
  } catch (error: any) {
    console.error('Organizations API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Parse request body
    const body = await req.json();
    const {
      name,
      slug,
      description,
      website_url,
      industry,
      size_range,
      subscription_tier,
      max_teams,
      max_members,
      created_by
    } = body;

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'Organization name is required' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    const finalSlug = slug || name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');

    // Create admin client
    const adminClient = createAdminClient();

    // Check if slug is unique
    const { data: existingOrg } = await adminClient
      .from('organizations')
      .select('id')
      .eq('slug', finalSlug)
      .single();

    if (existingOrg) {
      return NextResponse.json(
        { error: 'Organization slug already exists' },
        { status: 400 }
      );
    }

    // Create organization
    const { data: organization, error: orgError } = await adminClient
      .from('organizations')
      .insert([
        {
          name: name.trim(),
          slug: finalSlug,
          description: description?.trim() || null,
          website_url: website_url?.trim() || null,
          industry: industry?.trim() || null,
          size_range: size_range?.trim() || null,
          subscription_tier: subscription_tier || 'basic',
          max_teams: max_teams || 10,
          max_members: max_members || 100,
          created_by: authResult.user.id,
          status: 'active'
        }
      ])
      .select()
      .single();
    
    if (orgError) {
      console.error('Error creating organization:', orgError);
      return NextResponse.json(
        { error: orgError.message || 'Failed to create organization' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(organization);
  } catch (error: any) {
    console.error('Organizations POST API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
