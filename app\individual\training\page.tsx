import { ModuleCard } from "@/components/ui/module-card"
import { ProgressOverview } from "@/components/ui/progress-indicator"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"
import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Learning Paths | Luna Skills Platform",
  description: "Access your learning paths, training modules, and lessons for professional development",
}

export default async function LearningPaths() {
  const supabase = createServerComponentClient<Database>({ cookies })

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }

  // Get the user data from the users table
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("id, role")
    .eq("id", session.user.id)
    .single()

  if (userError || !userData || (userData.role !== "individual" && userData.role !== "platform_admin")) {
    redirect("/unauthorized")
  }
  
  // Get the individual's profile
  let individualData = null

  try {
    const { data, error } = await supabase
      .from("individuals")
      .select("id, learning_status")
      .eq("user_id", userData.id)
      .single()

    if (error) {
      console.log("Individual profile not found, this is normal for users without individual profiles")
    } else {
      individualData = data
    }
  } catch (error: any) {
    console.log("Error fetching individual data:", error.message || error)
    // Continue without individual data - will show training without progress
  }

  const individualId = individualData?.id
  
  // Fetch training data for this user
  let trainingData = []
  if (individualId) {
    try {
      const { data, error } = await supabase
        .from("user_training_data")
        .select(`
          id,
          training_type,
          training_status,
          progress_percentage,
          completion_date,
          metadata
        `)
        .eq("user_id", userData.id)
        .eq("context_type", "individual")

      if (error) {
        console.log("Error fetching training data:", error.message)
      } else {
        trainingData = data || []
      }
    } catch (error: any) {
      console.log("Error fetching training data:", error.message || error)
    }
  }
  
  // Create training modules from user training data
  const completedModules = trainingData.filter(t => t.training_status === 'completed')
  const inProgressModules = trainingData.filter(t => t.training_status === 'in_progress')
  const notStartedModules = trainingData.filter(t => t.training_status === 'not_started')

  // Create mock modules for display if no training data exists
  const allModules = trainingData.length > 0 ? trainingData.map(training => ({
    id: training.id,
    title: training.training_type || 'Training Module',
    description: training.metadata?.description || 'Professional development training',
    duration_minutes: training.metadata?.duration_minutes || 60,
    progressPercentage: training.progress_percentage || 0,
    status: training.training_status || 'not_started',
    required_order: 1
  })) : [
    {
      id: 'intro-1',
      title: 'Getting Started with Luna',
      description: 'Introduction to the Luna platform and your learning journey',
      duration_minutes: 30,
      progressPercentage: 0,
      status: 'not_started',
      required_order: 1
    },
    {
      id: 'skills-1',
      title: 'Skills Assessment',
      description: 'Evaluate your current skills and identify areas for growth',
      duration_minutes: 45,
      progressPercentage: 0,
      status: 'not_started',
      required_order: 2
    }
  ]

  // Calculate total learning time
  const totalLearningTimeHours = allModules.reduce((total, module) => {
    return total + (module.duration_minutes || 0)
  }, 0) / 60

  // Calculate overall progress
  const totalModules = allModules.length
  const overallProgressPercentage = totalModules > 0
    ? Math.round(allModules.reduce((sum, module) => sum + (module.progressPercentage || 0), 0) / totalModules)
    : 0

  // Prepare data for TrainingPage component
  const trainingPageData = {
    modules: allModules,
    stats: {
      completed: completedModules.length,
      inProgress: inProgressModules.length,
      notStarted: notStartedModules.length,
      totalCount: totalModules,
      learningTimeHours: totalLearningTimeHours.toFixed(1),
      overallProgress: overallProgressPercentage
    },
    continueLearning: inProgressModules.slice(0, 3),
    individualData
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <TrainingPageComponent trainingData={trainingPageData} />
    </div>
  )
}

// Training page component with Luna-compatible UI
function TrainingPageComponent({ trainingData }: { trainingData: any }) {
  if (!trainingData) {
    return <div className="p-8 text-center text-red-500">Error: No training data provided</div>
  }

  const { modules, stats, continueLearning } = trainingData

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold">Learning Paths</h1>
        <p className="text-gray-600 mt-1">Access your learning paths, training modules and practice sessions</p>
      </div>

      {/* Progress Overview */}
      <ProgressOverview
        stats={{
          completed: stats.completed,
          inProgress: stats.inProgress,
          notStarted: stats.notStarted,
          total: stats.totalCount
        }}
        title="Your Learning Progress"
        description={
          stats.completed > 0
            ? `Great progress! You've completed ${stats.completed} module${stats.completed > 1 ? 's' : ''}.`
            : stats.inProgress > 0
              ? `You're making progress on ${stats.inProgress} module${stats.inProgress > 1 ? 's' : ''}.`
              : "Start your training journey today!"
        }
        timeSpent={`${stats.learningTimeHours} hours total`}
      />

        {/* Training Modules - Horizontal Layout */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Your Modules</h2>

          {modules && modules.length > 0 ? (
            <Carousel
              opts={{
                align: "start",
                loop: false,
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-2 md:-ml-4">
                {modules.map((module: any) => (
                  <CarouselItem key={module.id} className="pl-2 md:pl-4 basis-full sm:basis-1/2 lg:basis-1/3">
                    <ModuleCard
                      module={{
                        id: module.id,
                        title: module.title,
                        description: module.description,
                        cover_image_url: module.cover_image_url,
                        duration_minutes: module.duration_minutes,
                        progressPercentage: module.progressPercentage
                      }}
                      href={`/individual/training/${module.id}`}
                      variant="default"
                      showProgress={true}
                      showDuration={true}
                      showDescription={false}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious />
              <CarouselNext />
            </Carousel>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No modules found</p>
            </div>
          )}
        </div>

        {/* Continue Learning Section - Below the horizontal modules */}
        {continueLearning && continueLearning.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Continue Learning</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {continueLearning.map((module: any) => (
                <ModuleCard
                  key={module.id}
                  module={{
                    id: module.id,
                    title: module.title,
                    description: module.description,
                    cover_image_url: module.cover_image_url,
                    duration_minutes: module.duration_minutes,
                    progressPercentage: module.progressPercentage
                  }}
                  href={`/individual/training/${module.id}`}
                  variant="default"
                  showProgress={true}
                  showDuration={true}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    )
}