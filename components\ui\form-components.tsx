/**
 * Form Components
 * Enhanced form components with validation, accessibility, and consistent styling
 */

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  AlertCircle, 
  CheckCircle, 
  Eye, 
  EyeOff, 
  Upload, 
  X,
  Info,
  Loader2
} from "lucide-react"

// TypeScript interfaces
export interface FormFieldProps {
  label: string
  name: string
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  placeholder?: string
  description?: string
  required?: boolean
  disabled?: boolean
  error?: string
  value?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  className?: string
  inputClassName?: string
}

export interface TextareaFieldProps {
  label: string
  name: string
  placeholder?: string
  description?: string
  required?: boolean
  disabled?: boolean
  error?: string
  value?: string
  onChange?: (value: string) => void
  onBlur?: () => void
  rows?: number
  maxLength?: number
  className?: string
  textareaClassName?: string
}

export interface FileUploadProps {
  label: string
  name: string
  accept?: string
  multiple?: boolean
  maxSize?: number
  description?: string
  required?: boolean
  disabled?: boolean
  error?: string
  onFileSelect?: (files: FileList | null) => void
  className?: string
  variant?: 'default' | 'dropzone'
}

export interface FormSectionProps {
  title: string
  description?: string
  children: React.ReactNode
  className?: string
  variant?: 'default' | 'card'
}

export interface FormActionsProps {
  children: React.ReactNode
  className?: string
  align?: 'left' | 'center' | 'right'
  sticky?: boolean
}

/**
 * Enhanced Form Field Component
 */
export const FormField = React.forwardRef<
  HTMLInputElement,
  FormFieldProps
>(({
  label,
  name,
  type = 'text',
  placeholder,
  description,
  required = false,
  disabled = false,
  error,
  value,
  onChange,
  onBlur,
  className,
  inputClassName,
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false)
  const [focused, setFocused] = React.useState(false)
  
  const inputId = `field-${name}`
  const descriptionId = description ? `${inputId}-description` : undefined
  const errorId = error ? `${inputId}-error` : undefined
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value)
  }
  
  const handleFocus = () => setFocused(true)
  const handleBlur = () => {
    setFocused(false)
    onBlur?.()
  }
  
  const inputType = type === 'password' && showPassword ? 'text' : type
  
  return (
    <div className={cn("space-y-2", className)}>
      <Label 
        htmlFor={inputId}
        className={cn(
          "text-sm font-medium",
          required && "after:content-['*'] after:ml-0.5 after:text-destructive",
          disabled && "text-muted-foreground"
        )}
      >
        {label}
      </Label>
      
      <div className="relative">
        <Input
          ref={ref}
          id={inputId}
          name={name}
          type={inputType}
          placeholder={placeholder}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          required={required}
          aria-describedby={cn(
            descriptionId,
            errorId
          )}
          aria-invalid={!!error}
          className={cn(
            "transition-all duration-200",
            focused && "ring-2 ring-ring ring-offset-2",
            error && "border-destructive focus-visible:ring-destructive",
            type === 'password' && "pr-10",
            inputClassName
          )}
          {...props}
        />
        
        {type === 'password' && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword(!showPassword)}
            disabled={disabled}
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Eye className="h-4 w-4 text-muted-foreground" />
            )}
          </Button>
        )}
      </div>
      
      {description && (
        <p 
          id={descriptionId}
          className="text-xs text-muted-foreground flex items-start gap-1"
        >
          <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
          {description}
        </p>
      )}
      
      {error && (
        <p 
          id={errorId}
          className="text-xs text-destructive flex items-start gap-1"
          role="alert"
        >
          <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
          {error}
        </p>
      )}
    </div>
  )
})

FormField.displayName = "FormField"

/**
 * Enhanced Textarea Field Component
 */
export const TextareaField = React.forwardRef<
  HTMLTextAreaElement,
  TextareaFieldProps
>(({
  label,
  name,
  placeholder,
  description,
  required = false,
  disabled = false,
  error,
  value,
  onChange,
  onBlur,
  rows = 4,
  maxLength,
  className,
  textareaClassName,
  ...props
}, ref) => {
  const [focused, setFocused] = React.useState(false)
  const [charCount, setCharCount] = React.useState(value?.length || 0)
  
  const inputId = `field-${name}`
  const descriptionId = description ? `${inputId}-description` : undefined
  const errorId = error ? `${inputId}-error` : undefined
  
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setCharCount(newValue.length)
    onChange?.(newValue)
  }
  
  const handleFocus = () => setFocused(true)
  const handleBlur = () => {
    setFocused(false)
    onBlur?.()
  }
  
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <Label 
          htmlFor={inputId}
          className={cn(
            "text-sm font-medium",
            required && "after:content-['*'] after:ml-0.5 after:text-destructive",
            disabled && "text-muted-foreground"
          )}
        >
          {label}
        </Label>
        {maxLength && (
          <span className={cn(
            "text-xs",
            charCount > maxLength * 0.9 ? "text-destructive" : "text-muted-foreground"
          )}>
            {charCount}/{maxLength}
          </span>
        )}
      </div>
      
      <Textarea
        ref={ref}
        id={inputId}
        name={name}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={disabled}
        required={required}
        rows={rows}
        maxLength={maxLength}
        aria-describedby={cn(
          descriptionId,
          errorId
        )}
        aria-invalid={!!error}
        className={cn(
          "transition-all duration-200 resize-none",
          focused && "ring-2 ring-ring ring-offset-2",
          error && "border-destructive focus-visible:ring-destructive",
          textareaClassName
        )}
        {...props}
      />
      
      {description && (
        <p 
          id={descriptionId}
          className="text-xs text-muted-foreground flex items-start gap-1"
        >
          <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
          {description}
        </p>
      )}
      
      {error && (
        <p 
          id={errorId}
          className="text-xs text-destructive flex items-start gap-1"
          role="alert"
        >
          <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
          {error}
        </p>
      )}
    </div>
  )
})

TextareaField.displayName = "TextareaField"

/**
 * File Upload Component
 */
export const FileUpload = React.forwardRef<
  HTMLInputElement,
  FileUploadProps
>(({
  label,
  name,
  accept,
  multiple = false,
  maxSize,
  description,
  required = false,
  disabled = false,
  error,
  onFileSelect,
  className,
  variant = 'default',
  ...props
}, ref) => {
  const [dragActive, setDragActive] = React.useState(false)
  const [selectedFiles, setSelectedFiles] = React.useState<File[]>([])
  
  const inputId = `field-${name}`
  const descriptionId = description ? `${inputId}-description` : undefined
  const errorId = error ? `${inputId}-error` : undefined
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      setSelectedFiles(Array.from(files))
      onFileSelect?.(files)
    }
  }
  
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const files = e.dataTransfer.files
      setSelectedFiles(Array.from(files))
      onFileSelect?.(files)
    }
  }
  
  const removeFile = (index: number) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index)
    setSelectedFiles(newFiles)
    
    // Create new FileList
    const dt = new DataTransfer()
    newFiles.forEach(file => dt.items.add(file))
    onFileSelect?.(dt.files)
  }
  
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  if (variant === 'dropzone') {
    return (
      <div className={cn("space-y-2", className)}>
        <Label 
          htmlFor={inputId}
          className={cn(
            "text-sm font-medium",
            required && "after:content-['*'] after:ml-0.5 after:text-destructive",
            disabled && "text-muted-foreground"
          )}
        >
          {label}
        </Label>
        
        <div
          className={cn(
            "relative border-2 border-dashed rounded-lg p-6 transition-colors",
            dragActive && "border-primary bg-primary/5",
            error && "border-destructive",
            disabled && "opacity-50 cursor-not-allowed",
            !disabled && "hover:border-primary/50 cursor-pointer"
          )}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            ref={ref}
            id={inputId}
            name={name}
            type="file"
            accept={accept}
            multiple={multiple}
            onChange={handleFileChange}
            disabled={disabled}
            required={required}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
            aria-describedby={cn(descriptionId, errorId)}
            {...props}
          />
          
          <div className="text-center">
            <Upload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm font-medium">
              Drop files here or click to browse
            </p>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">
                {description}
              </p>
            )}
            {maxSize && (
              <p className="text-xs text-muted-foreground mt-1">
                Max file size: {formatFileSize(maxSize)}
              </p>
            )}
          </div>
        </div>
        
        {selectedFiles.length > 0 && (
          <div className="space-y-2">
            {selectedFiles.map((file, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                <div className="flex items-center gap-2 min-w-0 flex-1">
                  <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                  <span className="text-sm truncate">{file.name}</span>
                  <Badge variant="outline" className="text-xs">
                    {formatFileSize(file.size)}
                  </Badge>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(index)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
        
        {error && (
          <p 
            id={errorId}
            className="text-xs text-destructive flex items-start gap-1"
            role="alert"
          >
            <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
            {error}
          </p>
        )}
      </div>
    )
  }
  
  return (
    <div className={cn("space-y-2", className)}>
      <Label 
        htmlFor={inputId}
        className={cn(
          "text-sm font-medium",
          required && "after:content-['*'] after:ml-0.5 after:text-destructive",
          disabled && "text-muted-foreground"
        )}
      >
        {label}
      </Label>
      
      <Input
        ref={ref}
        id={inputId}
        name={name}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileChange}
        disabled={disabled}
        required={required}
        aria-describedby={cn(descriptionId, errorId)}
        aria-invalid={!!error}
        className={cn(
          error && "border-destructive focus-visible:ring-destructive"
        )}
        {...props}
      />
      
      {description && (
        <p 
          id={descriptionId}
          className="text-xs text-muted-foreground flex items-start gap-1"
        >
          <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
          {description}
        </p>
      )}
      
      {error && (
        <p 
          id={errorId}
          className="text-xs text-destructive flex items-start gap-1"
          role="alert"
        >
          <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
          {error}
        </p>
      )}
    </div>
  )
})

FileUpload.displayName = "FileUpload"

/**
 * Form Section Component
 */
export const FormSection = React.forwardRef<
  HTMLDivElement,
  FormSectionProps
>(({
  title,
  description,
  children,
  className,
  variant = 'default',
  ...props
}, ref) => {
  if (variant === 'card') {
    return (
      <Card ref={ref} className={cn("border-none shadow-sm", className)} {...props}>
        <CardHeader>
          <CardTitle className="text-lg">{title}</CardTitle>
          {description && (
            <CardDescription>{description}</CardDescription>
          )}
        </CardHeader>
        <CardContent className="space-y-4">
          {children}
        </CardContent>
      </Card>
    )
  }
  
  return (
    <div ref={ref} className={cn("space-y-4", className)} {...props}>
      <div className="space-y-1">
        <h3 className="text-lg font-semibold">{title}</h3>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
})

FormSection.displayName = "FormSection"

/**
 * Form Actions Component
 */
export const FormActions = React.forwardRef<
  HTMLDivElement,
  FormActionsProps
>(({
  children,
  className,
  align = 'right',
  sticky = false,
  ...props
}, ref) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end'
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center gap-3 pt-6",
        alignClasses[align],
        sticky && "sticky bottom-0 bg-background border-t border-border p-4 -mx-4",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
})

FormActions.displayName = "FormActions"
