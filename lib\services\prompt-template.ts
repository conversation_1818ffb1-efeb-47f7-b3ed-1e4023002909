/**
 * Prompt Template Service
 * Handles dynamic prompt building, template management, and parameter substitution
 */

import { createClient } from '@/lib/supabase-server';

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  assessment_type: string;
  category: string;
  system_prompt: string;
  context_prompt?: string;
  question_generation_prompt?: string;
  default_parameters: Record<string, any>;
  variable_definitions: VariableDefinition[];
  output_schema: Record<string, any>;
  validation_rules: string[];
  version: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface VariableDefinition {
  name: string;
  type: 'string' | 'number' | 'array' | 'object' | 'boolean';
  description: string;
  default_value: any;
  validation: {
    required: boolean;
    min?: number;
    max?: number;
    pattern?: string;
    options?: string[];
  };
}

export interface PromptCompilationContext {
  assessmentType: string;
  questionCount: number;
  difficultyDistribution: Record<string, number>;
  subcategories: string[];
  questionTypes: string[];
  duration: number;
  passingScore: number;
  targetRole: string;
  industryFocus: string;
  customParameters: Record<string, any>;
}

export interface CompiledPrompt {
  systemPrompt: string;
  contextPrompt?: string;
  questionGenerationPrompt?: string;
  outputSchema: Record<string, any>;
  metadata: {
    templateId: string;
    templateVersion: number;
    compilationTimestamp: string;
    variablesUsed: string[];
    parametersApplied: Record<string, any>;
  };
}

export class PromptTemplateService {
  private supabase = createClient();

  /**
   * Get all active prompt templates
   */
  async getActiveTemplates(): Promise<PromptTemplate[]> {
    const { data, error } = await this.supabase
      .from('ai_prompt_templates')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching prompt templates:', error);
      throw new Error('Failed to fetch prompt templates');
    }

    return data || [];
  }

  /**
   * Get prompt template by ID
   */
  async getTemplate(templateId: string): Promise<PromptTemplate | null> {
    const { data, error } = await this.supabase
      .from('ai_prompt_templates')
      .select('*')
      .eq('id', templateId)
      .single();

    if (error) {
      console.error('Error fetching prompt template:', error);
      return null;
    }

    return data;
  }

  /**
   * Get templates by category
   */
  async getTemplatesByCategory(category: string): Promise<PromptTemplate[]> {
    const { data, error } = await this.supabase
      .from('ai_prompt_templates')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching templates by category:', error);
      throw new Error('Failed to fetch templates by category');
    }

    return data || [];
  }

  /**
   * Compile prompt template with given context
   */
  async compilePrompt(
    templateId: string,
    context: PromptCompilationContext
  ): Promise<CompiledPrompt> {
    const template = await this.getTemplate(templateId);
    if (!template) {
      throw new Error('Prompt template not found');
    }

    // Merge default parameters with custom parameters
    const allParameters = {
      ...template.default_parameters,
      ...context.customParameters,
      // Core assessment parameters
      question_count: context.questionCount,
      assessment_type: context.assessmentType,
      difficulty_distribution: context.difficultyDistribution,
      subcategories: context.subcategories,
      question_types: context.questionTypes,
      duration_minutes: context.duration,
      passing_score: context.passingScore,
      target_role: context.targetRole,
      industry_focus: context.industryFocus,
      // Dynamic context
      current_date: new Date().toISOString().split('T')[0],
      current_time: new Date().toLocaleTimeString(),
      compliance_year: new Date().getFullYear()
    };

    // Validate parameters against variable definitions
    this.validateParameters(template.variable_definitions, allParameters);

    // Compile system prompt
    const systemPrompt = this.compileTemplate(template.system_prompt, allParameters);
    
    // Compile optional prompts
    const contextPrompt = template.context_prompt 
      ? this.compileTemplate(template.context_prompt, allParameters)
      : undefined;
    
    const questionGenerationPrompt = template.question_generation_prompt
      ? this.compileTemplate(template.question_generation_prompt, allParameters)
      : undefined;

    // Track variables used
    const variablesUsed = this.extractVariablesFromTemplate(template.system_prompt);

    return {
      systemPrompt,
      contextPrompt,
      questionGenerationPrompt,
      outputSchema: template.output_schema,
      metadata: {
        templateId: template.id,
        templateVersion: template.version,
        compilationTimestamp: new Date().toISOString(),
        variablesUsed,
        parametersApplied: allParameters
      }
    };
  }

  /**
   * Compile template string with parameter substitution
   */
  private compileTemplate(template: string, parameters: Record<string, any>): string {
    let compiled = template;

    // Handle simple variable substitution {{variable_name}}
    Object.entries(parameters).forEach(([key, value]) => {
      const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      compiled = compiled.replace(placeholder, this.formatValue(value));
    });

    // Handle conditional blocks {{#if condition}}...{{/if}}
    compiled = this.processConditionals(compiled, parameters);

    // Handle loops {{#each array}}...{{/each}}
    compiled = this.processLoops(compiled, parameters);

    // Handle nested object access {{object.property}}
    compiled = this.processNestedAccess(compiled, parameters);

    return compiled;
  }

  /**
   * Format value for template substitution
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return '';
    }
    
    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        return value.join(', ');
      }
      return JSON.stringify(value, null, 2);
    }
    
    return String(value);
  }

  /**
   * Process conditional blocks in template
   */
  private processConditionals(template: string, parameters: Record<string, any>): string {
    const conditionalRegex = /\{\{#if\s+(\w+)\}\}(.*?)\{\{\/if\}\}/gs;
    
    return template.replace(conditionalRegex, (match, condition, content) => {
      const value = parameters[condition];
      const isTrue = Boolean(value) && value !== 0 && value !== '' && value !== 'false';
      return isTrue ? content : '';
    });
  }

  /**
   * Process loop blocks in template
   */
  private processLoops(template: string, parameters: Record<string, any>): string {
    const loopRegex = /\{\{#each\s+(\w+)\}\}(.*?)\{\{\/each\}\}/gs;
    
    return template.replace(loopRegex, (match, arrayName, itemTemplate) => {
      const array = parameters[arrayName];
      if (!Array.isArray(array)) {
        return '';
      }
      
      return array.map((item, index) => {
        let itemContent = itemTemplate;
        
        // Replace {{this}} with current item
        itemContent = itemContent.replace(/\{\{this\}\}/g, this.formatValue(item));
        
        // Replace {{@index}} with current index
        itemContent = itemContent.replace(/\{\{@index\}\}/g, String(index));
        
        // Replace {{this.property}} for object items
        if (typeof item === 'object' && item !== null) {
          Object.entries(item).forEach(([key, value]) => {
            const placeholder = new RegExp(`\\{\\{this\\.${key}\\}\\}`, 'g');
            itemContent = itemContent.replace(placeholder, this.formatValue(value));
          });
        }
        
        return itemContent;
      }).join('\n');
    });
  }

  /**
   * Process nested object access in template
   */
  private processNestedAccess(template: string, parameters: Record<string, any>): string {
    const nestedRegex = /\{\{(\w+)\.(\w+)\}\}/g;
    
    return template.replace(nestedRegex, (match, objectName, property) => {
      const object = parameters[objectName];
      if (typeof object === 'object' && object !== null && property in object) {
        return this.formatValue(object[property]);
      }
      return '';
    });
  }

  /**
   * Extract variable names from template
   */
  private extractVariablesFromTemplate(template: string): string[] {
    const variableRegex = /\{\{(\w+)(?:\.\w+)?\}\}/g;
    const variables = new Set<string>();
    let match;
    
    while ((match = variableRegex.exec(template)) !== null) {
      variables.add(match[1]);
    }
    
    return Array.from(variables);
  }

  /**
   * Validate parameters against variable definitions
   */
  private validateParameters(
    variableDefinitions: VariableDefinition[],
    parameters: Record<string, any>
  ): void {
    const errors: string[] = [];

    variableDefinitions.forEach(varDef => {
      const value = parameters[varDef.name];
      
      // Check required variables
      if (varDef.validation.required && (value === undefined || value === null)) {
        errors.push(`Required parameter '${varDef.name}' is missing`);
        return;
      }

      if (value !== undefined && value !== null) {
        // Type validation
        if (!this.validateType(value, varDef.type)) {
          errors.push(`Parameter '${varDef.name}' must be of type ${varDef.type}`);
        }

        // Range validation for numbers
        if (varDef.type === 'number' && typeof value === 'number') {
          if (varDef.validation.min !== undefined && value < varDef.validation.min) {
            errors.push(`Parameter '${varDef.name}' must be at least ${varDef.validation.min}`);
          }
          if (varDef.validation.max !== undefined && value > varDef.validation.max) {
            errors.push(`Parameter '${varDef.name}' must be at most ${varDef.validation.max}`);
          }
        }

        // Pattern validation for strings
        if (varDef.type === 'string' && typeof value === 'string' && varDef.validation.pattern) {
          const regex = new RegExp(varDef.validation.pattern);
          if (!regex.test(value)) {
            errors.push(`Parameter '${varDef.name}' does not match required pattern`);
          }
        }

        // Options validation
        if (varDef.validation.options && !varDef.validation.options.includes(String(value))) {
          errors.push(`Parameter '${varDef.name}' must be one of: ${varDef.validation.options.join(', ')}`);
        }
      }
    });

    if (errors.length > 0) {
      throw new Error(`Parameter validation failed: ${errors.join('; ')}`);
    }
  }

  /**
   * Validate parameter type
   */
  private validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * Create new prompt template
   */
  async createTemplate(template: Omit<PromptTemplate, 'id' | 'created_at' | 'updated_at'>): Promise<PromptTemplate> {
    const { data, error } = await this.supabase
      .from('ai_prompt_templates')
      .insert(template)
      .select()
      .single();

    if (error) {
      console.error('Error creating prompt template:', error);
      throw new Error('Failed to create prompt template');
    }

    return data;
  }

  /**
   * Update prompt template
   */
  async updateTemplate(
    templateId: string,
    updates: Partial<PromptTemplate>
  ): Promise<PromptTemplate> {
    const { data, error } = await this.supabase
      .from('ai_prompt_templates')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', templateId)
      .select()
      .single();

    if (error) {
      console.error('Error updating prompt template:', error);
      throw new Error('Failed to update prompt template');
    }

    return data;
  }

  /**
   * Delete prompt template
   */
  async deleteTemplate(templateId: string): Promise<void> {
    const { error } = await this.supabase
      .from('ai_prompt_templates')
      .delete()
      .eq('id', templateId);

    if (error) {
      console.error('Error deleting prompt template:', error);
      throw new Error('Failed to delete prompt template');
    }
  }

  /**
   * Clone prompt template
   */
  async cloneTemplate(templateId: string, newName: string): Promise<PromptTemplate> {
    const originalTemplate = await this.getTemplate(templateId);
    if (!originalTemplate) {
      throw new Error('Original template not found');
    }

    const clonedTemplate = {
      ...originalTemplate,
      name: newName,
      version: 1,
      is_active: false // Start as inactive
    };

    // Remove fields that should be auto-generated
    delete (clonedTemplate as any).id;
    delete (clonedTemplate as any).created_at;
    delete (clonedTemplate as any).updated_at;

    return this.createTemplate(clonedTemplate);
  }
}

// Export singleton instance
export const promptTemplateService = new PromptTemplateService();
