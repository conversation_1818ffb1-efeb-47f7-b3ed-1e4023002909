/**
 * Core Authentication Functions
 * Main authentication logic for the Luna platform
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { createApiClient, createServerClient } from '@/lib/supabase-server';
import type { Database } from '@/types/database.types';
import { AuthResult, AuthUser, EmploymentRelationship } from './types';
import { isPlatformAdmin, isOrganizationAdmin, isDepartmentAdmin, hasEmployment } from './utils';

/**
 * Get authenticated user with secure validation for API routes
 * Uses getUser() instead of getSession() for better security
 */
export async function getAuthenticatedUser(): Promise<AuthResult> {
  try {
    console.log('[AUTH] 🚀 Starting Luna authentication...');
    const supabase = await createApiClient();
    console.log('[AUTH] ✅ Supabase client created successfully');

    // Use getUser() for authentication
    console.log('[AUTH] 🔍 Calling supabase.auth.getUser()...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('[AUTH] 📊 Auth getUser result:', {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      userErrorMessage: userError?.message
    });

    if (userError || !user) {
      console.log('[AUTH] ❌ Auth failed - no user or error:', userError);
      return {
        user: null,
        error: 'Unauthorized - Authentication required',
        status: 401
      };
    }

    // Get user details from database
    console.log('[AUTH] 🔍 Querying users table for user ID:', user.id);
    const { data: userData, error: dbError } = await supabase
      .from('users')
      .select('id, email, role, full_name, avatar_url, timezone, status')
      .eq('id', user.id)
      .single();

    console.log('[AUTH] 📊 Database query result:', {
      hasUserData: !!userData,
      userData: userData && !dbError ? {
        id: userData.id,
        email: userData.email,
        role: userData.role,
        full_name: userData.full_name,
        avatar_url: userData.avatar_url,
        timezone: userData.timezone,
        status: userData.status
      } : null,
      dbErrorMessage: dbError?.message
    });

    if (dbError || !userData) {
      console.log('[AUTH] ❌ Database error or user not found:', dbError);
      return {
        user: null,
        error: 'User not found in database',
        status: 404
      };
    }

    // At this point, userData is guaranteed to be valid
    const validUserData = userData as {
      id: string;
      email: string;
      role: string;
      full_name: string;
      avatar_url: string | null;
      timezone: string | null;
      status: string;
    };

    // Determine user permissions
    console.log('[AUTH] 🔍 Determining user permissions...');
    const isPlatformAdminUser = validUserData.role === 'platform_admin';

    // Get employment relationships
    console.log('[AUTH] 🔍 Querying employment relationships for user ID:', user.id);
    const { data: employmentData, error: employmentError } = await supabase
      .from('employment_relationships')
      .select(`
        id,
        organization_id,
        department_id,
        role,
        job_title,
        status,
        organizations!inner(
          name,
          slug
        ),
        departments!inner(
          name
        )
      `)
      .eq('user_id', user.id)
      .eq('status', 'active');

    console.log('[AUTH] 📊 Employment relationships result:', {
      hasEmployments: !!employmentData,
      employmentCount: employmentData?.length || 0,
      employmentError: employmentError?.message
    });

    const employmentRelationships: EmploymentRelationship[] = employmentData && !employmentError ? employmentData.map(emp => ({
      id: emp.id,
      organization_id: emp.organization_id,
      organization_name: (emp.organizations as any)?.name || '',
      organization_slug: (emp.organizations as any)?.slug || '',
      department_id: emp.department_id,
      department_name: (emp.departments as any)?.name || '',
      employment_role: emp.role as 'organization_admin' | 'department_admin' | 'staff_member',
      job_title: emp.job_title || '',
      status: emp.status
    })) : [];

    // Get user context
    console.log('[AUTH] 🔍 Querying user context...');
    const { data: contextData, error: contextError } = await supabase
      .from('user_contexts')
      .select('active_context, active_organization_id, active_department_id, active_employment_id')
      .eq('user_id', user.id)
      .single();

    console.log('[AUTH] 📊 User context result:', {
      hasContext: !!contextData,
      contextData: contextData && !contextError ? {
        active_context: contextData.active_context,
        active_organization_id: contextData.active_organization_id
      } : null,
      contextError: contextError?.message
    });

    // Build AuthUser object
    console.log('[AUTH] 🔧 Building AuthUser object...');
    const authUser: AuthUser = {
      id: validUserData.id,
      email: validUserData.email,
      role: validUserData.role as 'platform_admin' | 'individual',
      full_name: validUserData.full_name || '',
      avatar_url: validUserData.avatar_url || undefined,
      timezone: validUserData.timezone || undefined,
      status: validUserData.status as 'active' | 'inactive' | 'pending',
      isPlatformAdmin: isPlatformAdminUser,
      employmentRelationships,
      currentContext: contextData && !contextError ? {
        type: contextData.active_context as 'individual' | 'organization',
        organization_id: contextData.active_organization_id || undefined,
        department_id: contextData.active_department_id || undefined,
        employment_id: contextData.active_employment_id || undefined
      } : {
        type: 'individual' as const,
        organization_id: undefined,
        department_id: undefined,
        employment_id: undefined
      },
      // Helper properties
      isOrganizationAdmin: isOrganizationAdmin({ employmentRelationships } as AuthUser),
      isDepartmentAdmin: isDepartmentAdmin({ employmentRelationships } as AuthUser),
      hasEmployment: hasEmployment({ employmentRelationships } as AuthUser)
    };

    console.log('[AUTH] ✅ Authentication successful! User:', {
      id: authUser.id,
      email: authUser.email,
      role: authUser.role,
      isPlatformAdmin: authUser.isPlatformAdmin,
      hasEmployment: authUser.hasEmployment,
      isOrganizationAdmin: authUser.isOrganizationAdmin,
      isDepartmentAdmin: authUser.isDepartmentAdmin,
      employmentCount: authUser.employmentRelationships.length,
      currentContext: authUser.currentContext
    });

    return {
      user: authUser,
      error: null,
      status: 200
    };

  } catch (error) {
    console.error('[AUTH] ❌ Authentication error:', error);
    return {
      user: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

/**
 * Get authenticated user for server components
 */
export async function getServerAuthUser(): Promise<AuthResult> {
  try {
    const supabase = await createServerClient();
    
    // Use getUser() for secure authentication
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        user: null,
        error: 'Unauthorized - Authentication required',
        status: 401
      };
    }

    // Get user details from database
    const { data: userData, error: dbError } = await supabase
      .from('users')
      .select('id, email, role, full_name, avatar_url, timezone, status')
      .eq('id', user.id)
      .single();

    if (dbError || !userData) {
      return {
        user: null,
        error: 'User not found in database',
        status: 404
      };
    }

    // At this point, userData is guaranteed to be valid
    const validUserData = userData as {
      id: string;
      email: string;
      role: string;
      full_name: string;
      avatar_url: string | null;
      timezone: string | null;
      status: string;
    };

    // Build minimal AuthUser for server components
    const authUser: AuthUser = {
      id: validUserData.id,
      email: validUserData.email,
      role: validUserData.role as 'platform_admin' | 'individual',
      full_name: validUserData.full_name || '',
      avatar_url: validUserData.avatar_url || undefined,
      timezone: validUserData.timezone || undefined,
      status: validUserData.status as 'active' | 'inactive' | 'pending',
      isPlatformAdmin: validUserData.role === 'platform_admin',
      employmentRelationships: [], // Simplified for server components
      isOrganizationAdmin: false,
      isDepartmentAdmin: false,
      hasEmployment: false
    };

    return {
      user: authUser,
      error: null,
      status: 200
    };

  } catch (error) {
    console.error('Server auth error:', error);
    return {
      user: null,
      error: 'Server authentication failed',
      status: 500
    };
  }
}
