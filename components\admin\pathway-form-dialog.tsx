"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Loader2 } from "lucide-react"

const pathwaySchema = z.object({
  title: z.string().min(1, "Pathway title is required"),
  description: z.string().min(1, "Description is required"),
  slug: z.string().optional(),
  program_id: z.string().min(1, "Program is required"),
  estimated_duration_hours: z.string().optional(),
  difficulty_level: z.string().default('1'),
  sort_order: z.string().optional(),
  is_featured: z.boolean().default(false),
  cover_image_url: z.string().optional(),
  target_roles: z.string().optional(),
  learning_objectives: z.string().optional(),
})

type PathwayFormData = z.infer<typeof pathwaySchema>

interface Program {
  id: string
  name: string
  industry: string
}

interface Pathway {
  id: string
  title: string
  description: string
  slug: string
  program_id: string
  estimated_duration_hours: number
  difficulty_level: number
  sort_order: number
  is_featured: boolean
  cover_image_url?: string
  target_roles?: string[]
  learning_objectives?: string[]
  program?: Program
}

interface PathwayFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  pathway?: Pathway | null
  onSuccess: () => void
}

export function PathwayFormDialog({
  open,
  onOpenChange,
  pathway,
  onSuccess
}: PathwayFormDialogProps) {
  const [loading, setLoading] = useState(false)
  const [programs, setPrograms] = useState<Program[]>([])
  const isEditing = !!pathway

  const form = useForm<PathwayFormData>({
    resolver: zodResolver(pathwaySchema),
    defaultValues: {
      title: '',
      description: '',
      slug: '',
      program_id: '',
      estimated_duration_hours: '',
      difficulty_level: '1',
      sort_order: '0',
      is_featured: false,
      cover_image_url: '',
      target_roles: '',
      learning_objectives: '',
    }
  })

  // Fetch programs for dropdown
  useEffect(() => {
    const fetchPrograms = async () => {
      try {
        const response = await fetch('/api/admin/programs')
        const data = await response.json()
        if (data.programs) {
          setPrograms(data.programs)
        }
      } catch (error) {
        console.error('Error fetching programs:', error)
      }
    }

    if (open) {
      fetchPrograms()
    }
  }, [open])

  // Reset form when pathway changes
  useEffect(() => {
    if (pathway) {
      form.reset({
        title: pathway.title,
        description: pathway.description,
        slug: pathway.slug,
        program_id: pathway.program_id,
        estimated_duration_hours: pathway.estimated_duration_hours?.toString() || '',
        difficulty_level: pathway.difficulty_level.toString(),
        sort_order: pathway.sort_order.toString(),
        is_featured: pathway.is_featured,
        cover_image_url: pathway.cover_image_url || '',
        target_roles: pathway.target_roles?.join(', ') || '',
        learning_objectives: pathway.learning_objectives?.join('\n') || '',
      })
    } else {
      form.reset({
        title: '',
        description: '',
        slug: '',
        program_id: '',
        estimated_duration_hours: '',
        difficulty_level: '1',
        sort_order: '0',
        is_featured: false,
        cover_image_url: '',
        target_roles: '',
        learning_objectives: '',
      })
    }
  }, [pathway, form])

  // Cleanup when dialog closes
  useEffect(() => {
    if (!open) {
      setLoading(false)
    }
  }, [open])

  const onSubmit = async (data: PathwayFormData) => {
    setLoading(true)
    try {
      const url = isEditing 
        ? `/api/admin/pathways/${pathway.id}`
        : '/api/admin/pathways'
      
      const method = isEditing ? 'PUT' : 'POST'
      
      const payload = {
        title: data.title.trim(),
        description: data.description.trim(),
        slug: data.slug?.trim() || data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
        program_id: data.program_id,
        estimated_duration_hours: data.estimated_duration_hours ? parseInt(data.estimated_duration_hours) : null,
        difficulty_level: parseInt(data.difficulty_level),
        sort_order: data.sort_order ? parseInt(data.sort_order) : 0,
        is_featured: data.is_featured,
        cover_image_url: data.cover_image_url?.trim() || null,
        target_roles: data.target_roles 
          ? data.target_roles.split(',').map(role => role.trim()).filter(role => role.length > 0)
          : [],
        learning_objectives: data.learning_objectives 
          ? data.learning_objectives.split('\n').map(obj => obj.trim()).filter(obj => obj.length > 0)
          : [],
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save pathway')
      }

      toast.success(
        isEditing 
          ? 'Pathway updated successfully' 
          : 'Pathway created successfully'
      )
      
      onOpenChange(false)
      form.reset()
      
      setTimeout(() => {
        onSuccess()
      }, 100)
      
    } catch (error: any) {
      console.error('Error saving pathway:', error)
      toast.error(error.message || 'Failed to save pathway')
    } finally {
      setLoading(false)
    }
  }

  const getDifficultyLabel = (level: string) => {
    switch (level) {
      case '1': return 'Beginner'
      case '2': return 'Intermediate'
      case '3': return 'Advanced'
      case '4': return 'Expert'
      case '5': return 'Master'
      default: return 'Beginner'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Pathway' : 'Create New Pathway'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the pathway information below.'
              : 'Create a new learning pathway within a program.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pathway Title *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="e.g., Help Desk Specialist" 
                        {...field} 
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="program_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Program *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select program" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {programs.map((program) => (
                          <SelectItem key={program.id} value={program.id}>
                            {program.name} ({program.industry})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description *</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe what this pathway covers and career outcomes..."
                      className="min-h-[100px]"
                      {...field} 
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="difficulty_level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Difficulty Level</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1">1 - Beginner</SelectItem>
                        <SelectItem value="2">2 - Intermediate</SelectItem>
                        <SelectItem value="3">3 - Advanced</SelectItem>
                        <SelectItem value="4">4 - Expert</SelectItem>
                        <SelectItem value="5">5 - Master</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="estimated_duration_hours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (Hours)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="120"
                        {...field} 
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sort_order"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sort Order</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="0"
                        {...field} 
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="slug"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>URL Slug</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="help-desk-specialist"
                        {...field} 
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_featured"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={loading}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Featured Pathway</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Show this pathway prominently
                      </p>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? 'Update Pathway' : 'Create Pathway'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
