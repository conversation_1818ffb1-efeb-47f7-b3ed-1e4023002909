'use client'

import { useState, useEffect } from "react"
import { Calendar, Clock, MapPin, MoreHorizontal, Video, User, CheckCircle2, AlertCircle, Search, XCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { supabase } from '@/lib/supabase'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'

interface Interview {
  id: string
  application_id: string
  bpo_user_id: string
  scheduled_at?: string
  duration_minutes: number
  status: 'scheduled' | 'completed' | 'cancelled' | 'pending_scheduling'
  notes?: string
  meeting_link?: string
  location?: string
  feedback?: any
  created_at: string
  updated_at: string
  // Enriched data
  bpoName?: string
  bpoLogo?: string
  position?: string
  isUpcoming?: boolean
  formattedDate?: string
  formattedTime?: string
}

export function InterviewsPage() {
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null)
  const [showAcceptDialog, setShowAcceptDialog] = useState(false)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState('')
  const [prospectNotes, setProspectNotes] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      console.log('🔍 SIMPLE: Starting fetchData...')

      // Get current user
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        console.log('🔍 SIMPLE: No session found')
        setInterviews([])
        return
      }

      // Get prospect ID
      const { data: prospect, error: prospectError } = await supabase
        .from('prospects')
        .select('id')
        .eq('user_id', session.user.id)
        .single()

      console.log('🔍 SIMPLE: Prospect:', prospect, 'Error:', prospectError)

      if (!prospect) {
        console.log('🔍 SIMPLE: No prospect found')
        setInterviews([])
        return
      }

      // Call API endpoint to get interviews (bypasses RLS issues)
      const response = await fetch('/api/prospect/interviews', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.log('🔍 SIMPLE: API call failed:', response.status)
        setInterviews([])
        return
      }

      const data = await response.json()
      console.log('🔍 SIMPLE: API response:', data)

      if (!data.interviews || data.interviews.length === 0) {
        console.log('🔍 SIMPLE: No interviews from API')
        setInterviews([])
        return
      }

      // Format the data
      const formattedInterviews = data.interviews.map(interview => ({
        ...interview,
        formattedDate: interview.scheduled_at ? new Date(interview.scheduled_at).toLocaleDateString() : 'TBD',
        formattedTime: interview.scheduled_at ? new Date(interview.scheduled_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'TBD',
        isUpcoming: interview.scheduled_at ? new Date(interview.scheduled_at) > new Date() : false
      }))

      console.log('🔍 SIMPLE: Final formatted interviews:', formattedInterviews)
      setInterviews(formattedInterviews)

    } catch (error) {
      console.error('🔍 SIMPLE: Error:', error)
      setInterviews([])
    } finally {
      setLoading(false)
    }
  }

  const handleAcceptInterview = async () => {
    if (!selectedInterview) return

    // Check if time slot selection is required
    const hasProposedTimes = selectedInterview.feedback?.proposed_times?.length > 0
    if (hasProposedTimes && !selectedTimeSlot) {
      toast({
        title: "Please select a time",
        description: "You must select a preferred time slot to accept this interview.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)

      const response = await fetch('/api/interviews/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: selectedInterview.id,
          selectedTime: selectedTimeSlot || null,
          prospectNotes: prospectNotes
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to accept interview')
      }

      if (selectedTimeSlot) {
        const selectedTime = new Date(selectedTimeSlot)
        toast({
          title: "Interview accepted!",
          description: `Your interview has been scheduled for ${selectedTime.toLocaleDateString()} at ${selectedTime.toLocaleTimeString()}.`,
        })
      } else {
        toast({
          title: "Interview accepted!",
          description: "The interviewer will contact you to arrange the final details.",
        })
      }

      setShowAcceptDialog(false)
      setSelectedInterview(null)
      setSelectedTimeSlot('')
      setProspectNotes('')
      fetchData() // Refresh the list

    } catch (error: any) {
      console.error('Error accepting interview:', error)
      toast({
        title: "Failed to accept interview",
        description: error.message || "There was an error accepting your interview. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeclineInterview = async (interview: Interview, reason: string) => {
    try {
      const response = await fetch('/api/interviews/accept', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          interviewId: interview.id,
          reason: reason
        }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.error || 'Failed to decline interview')
      }

      toast({
        title: "Interview declined",
        description: "The interview invitation has been declined.",
      })

      fetchData() // Refresh the list

    } catch (error: any) {
      console.error('Error declining interview:', error)
      toast({
        title: "Failed to decline interview",
        description: error.message || "There was an error declining the interview. Please try again.",
        variant: "destructive",
      })
    }
  }

  const openAcceptDialog = (interview: Interview) => {
    setSelectedInterview(interview)
    setSelectedTimeSlot('')
    setProspectNotes('')
    setShowAcceptDialog(true)
  }



  if (loading && interviews.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading interviews...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Interviews</h1>
        <p className="text-gray-600">Your interview invitations and scheduled interviews</p>
      </div>

      {/* Simple Interview List */}
      <div className="space-y-4">
        {interviews.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No interviews yet</h3>
              <p className="text-gray-600 mb-4">
                When you apply for jobs and get selected, interview invitations will appear here.
              </p>
              <Button asChild>
                <a href="/prospect/job-board">Browse Jobs</a>
              </Button>
            </CardContent>
          </Card>
        ) : (
          interviews.map((interview) => (
            <Card key={interview.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold text-lg">{interview.position}</h3>
                    <p className="text-gray-600">{interview.bpoName}</p>
                    <div className="mt-2 space-y-1">
                      <p className="text-sm">
                        <Clock className="h-4 w-4 inline mr-1" />
                        {interview.formattedDate} at {interview.formattedTime}
                      </p>
                      <p className="text-sm">
                        <Video className="h-4 w-4 inline mr-1" />
                        {interview.location || 'Virtual'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    {/* Show status badge */}
                    <Badge variant={interview.feedback?.proposed_times?.length > 0 ? 'outline' : 'default'}>
                      {interview.feedback?.proposed_times?.length > 0 ? 'Awaiting Response' : interview.status}
                    </Badge>

                    {/* Show Accept/Decline buttons if there are proposed times and no prospect response yet */}
                    {interview.feedback?.proposed_times?.length > 0 && !interview.feedback?.prospect_response && (
                      <div className="mt-2 space-x-2">
                        <Button size="sm" onClick={() => openAcceptDialog(interview)}>
                          Accept
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDeclineInterview(interview, 'Not available')}>
                          Decline
                        </Button>
                      </div>
                    )}

                    {/* Show Join Meeting button for scheduled interviews */}
                    {interview.meeting_link && interview.status === 'scheduled' && interview.feedback?.prospect_response && (
                      <div className="mt-2">
                        <Button size="sm" onClick={() => window.open(interview.meeting_link, '_blank')}>
                          Join Meeting
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>



      {/* Accept Interview Dialog */}
      <Dialog open={showAcceptDialog} onOpenChange={setShowAcceptDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Accept Interview Invitation</DialogTitle>
            <DialogDescription>
              {selectedInterview && (
                <>Select your preferred time for the interview with {selectedInterview.bpoName} for the {selectedInterview.position} position.</>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {selectedInterview?.feedback?.proposed_times && selectedInterview.feedback.proposed_times.length > 0 ? (
              <div>
                <Label>Select your preferred time:</Label>
                <div className="space-y-2 mt-2">
                  {selectedInterview.feedback.proposed_times.map((timeSlot: any, index: number) => (
                    <div key={index} className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id={`time-${index}`}
                        name="timeSlot"
                        value={timeSlot.start_time}
                        checked={selectedTimeSlot === timeSlot.start_time}
                        onChange={(e) => setSelectedTimeSlot(e.target.value)}
                        className="w-4 h-4"
                      />
                      <label htmlFor={`time-${index}`} className="text-sm">
                        {new Date(timeSlot.start_time).toLocaleDateString()} at{' '}
                        {new Date(timeSlot.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        {timeSlot.end_time && (
                          <> - {new Date(timeSlot.end_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</>
                        )}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-600 p-3 bg-blue-50 rounded-lg border border-blue-200">
                The interviewer will contact you to arrange a suitable time after you accept this invitation.
              </div>
            )}

            <div>
              <Label htmlFor="notes">Additional Notes (Optional)</Label>
              <Input
                id="notes"
                placeholder="Any questions or special requirements..."
                value={prospectNotes}
                onChange={(e) => setProspectNotes(e.target.value)}
              />
            </div>

            {selectedInterview && (
              <div className="text-sm text-gray-600">
                Duration: {selectedInterview.duration_minutes || 60} minutes
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAcceptDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleAcceptInterview}
              disabled={isSubmitting || (selectedInterview?.feedback?.proposed_times?.length > 0 && !selectedTimeSlot)}
            >
              {isSubmitting ? "Accepting..." : "Accept Interview"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
