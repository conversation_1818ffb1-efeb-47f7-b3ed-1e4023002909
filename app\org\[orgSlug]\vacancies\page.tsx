'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { 
  Briefcase, 
  Plus, 
  MapPin, 
  Clock, 
  Users, 
  Search, 
  MoreVertical, 
  Edit, 
  Eye, 
  Archive,
  CheckCircle2, 
  XCircle,
  CalendarDays,
  Trash2,
  AlertTriangle,
  DollarSign
} from 'lucide-react';

// Define types based on database schema
type JobPosting = {
  id: string;
  title: string;
  description: string;
  requirements: string[];
  location: string;
  employment_type: 'full_time' | 'part_time' | 'contract' | 'internship';
  salary_range_min?: number;
  salary_range_max?: number;
  status: 'draft' | 'published' | 'closed' | 'archived';
  created_at: string;
  updated_at: string;
  organization_id: string;
  department_id?: string;
  department?: {
    id: string;
    name: string;
  };
  _count?: {
    applications: number;
  };
};

export default function VacanciesPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [vacancies, setVacancies] = useState<JobPosting[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [stats, setStats] = useState({
    total: 0,
    published: 0,
    draft: 0,
    closed: 0,
    totalApplications: 0
  });

  const orgSlug = params.orgSlug as string;

  useEffect(() => {
    fetchVacancies();
  }, [orgSlug]);

  const fetchVacancies = async () => {
    try {
      setLoading(true);
      
      // First get the organization ID from slug
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError);
        toast({
          title: "Error",
          description: "Failed to load organization data.",
          variant: "destructive",
        });
        return;
      }

      // For now, use mock data since job_postings table might not exist yet
      const mockVacancies: JobPosting[] = [
        {
          id: '1',
          title: 'Senior Software Engineer',
          description: 'We are looking for a senior software engineer to join our team...',
          requirements: ['5+ years experience', 'React', 'Node.js', 'TypeScript'],
          location: 'San Francisco, CA',
          employment_type: 'full_time',
          salary_range_min: 120000,
          salary_range_max: 180000,
          status: 'published',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization_id: orgData.id,
          department: {
            id: '1',
            name: 'Engineering'
          },
          _count: {
            applications: 12
          }
        },
        {
          id: '2',
          title: 'Product Manager',
          description: 'Join our product team to drive innovation...',
          requirements: ['3+ years PM experience', 'Agile', 'Data analysis'],
          location: 'Remote',
          employment_type: 'full_time',
          salary_range_min: 100000,
          salary_range_max: 140000,
          status: 'published',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization_id: orgData.id,
          department: {
            id: '2',
            name: 'Product'
          },
          _count: {
            applications: 8
          }
        },
        {
          id: '3',
          title: 'Marketing Specialist',
          description: 'Help us grow our brand and reach new customers...',
          requirements: ['2+ years marketing', 'Digital marketing', 'Analytics'],
          location: 'New York, NY',
          employment_type: 'full_time',
          salary_range_min: 60000,
          salary_range_max: 80000,
          status: 'draft',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          organization_id: orgData.id,
          department: {
            id: '3',
            name: 'Marketing'
          },
          _count: {
            applications: 0
          }
        }
      ];

      setVacancies(mockVacancies);
      
      // Calculate stats
      const total = mockVacancies.length;
      const published = mockVacancies.filter(v => v.status === 'published').length;
      const draft = mockVacancies.filter(v => v.status === 'draft').length;
      const closed = mockVacancies.filter(v => v.status === 'closed').length;
      const totalApplications = mockVacancies.reduce((sum, v) => sum + (v._count?.applications || 0), 0);
      
      setStats({ total, published, draft, closed, totalApplications });
      
    } catch (error) {
      console.error('Error:', error);
      toast({
        title: "Error",
        description: "Failed to load vacancies.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge variant="default" className="bg-green-500"><CheckCircle2 className="h-3 w-3 mr-1" />Published</Badge>;
      case 'draft':
        return <Badge variant="secondary"><Edit className="h-3 w-3 mr-1" />Draft</Badge>;
      case 'closed':
        return <Badge variant="outline"><XCircle className="h-3 w-3 mr-1" />Closed</Badge>;
      case 'archived':
        return <Badge variant="destructive"><Archive className="h-3 w-3 mr-1" />Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getEmploymentTypeBadge = (type: string) => {
    const typeMap = {
      'full_time': 'Full Time',
      'part_time': 'Part Time',
      'contract': 'Contract',
      'internship': 'Internship'
    };
    return <Badge variant="outline">{typeMap[type as keyof typeof typeMap] || type}</Badge>;
  };

  const formatSalary = (min?: number, max?: number) => {
    if (!min && !max) return 'Salary not specified';
    if (min && max) return `$${min.toLocaleString()} - $${max.toLocaleString()}`;
    if (min) return `From $${min.toLocaleString()}`;
    if (max) return `Up to $${max.toLocaleString()}`;
    return 'Salary not specified';
  };

  const filteredVacancies = vacancies.filter(vacancy => {
    const matchesSearch = searchQuery === '' || 
      vacancy.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vacancy.department?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      vacancy.location.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || vacancy.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        <span className="ml-3 text-lg">Loading vacancies...</span>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Job Vacancies</h1>
          <p className="text-muted-foreground">
            Create and manage job postings to attract qualified candidates
          </p>
        </div>
        <Button asChild>
          <Link href={`/org/${orgSlug}/vacancies/create`}>
            <Plus className="h-4 w-4 mr-2" />
            Create Vacancy
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Vacancies</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.published}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <Edit className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.draft}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Closed</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.closed}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalApplications}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search vacancies..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-input bg-background rounded-md text-sm"
        >
          <option value="all">All Status</option>
          <option value="published">Published</option>
          <option value="draft">Draft</option>
          <option value="closed">Closed</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Vacancies Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredVacancies.map((vacancy) => (
          <Card key={vacancy.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{vacancy.title}</CardTitle>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    {vacancy.location}
                  </div>
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem asChild>
                      <Link href={`/org/${orgSlug}/vacancies/${vacancy.id}`}>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href={`/org/${orgSlug}/vacancies/${vacancy.id}/edit`}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Vacancy
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-red-600">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Vacancy
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground line-clamp-2">
                {vacancy.description}
              </p>

              <div className="flex items-center gap-2">
                {getStatusBadge(vacancy.status)}
                {getEmploymentTypeBadge(vacancy.employment_type)}
              </div>

              <div className="space-y-2">
                {vacancy.department && (
                  <div className="flex items-center gap-2 text-sm">
                    <Briefcase className="h-4 w-4 text-muted-foreground" />
                    <span>{vacancy.department.name}</span>
                  </div>
                )}

                <div className="flex items-center gap-2 text-sm">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span>{formatSalary(vacancy.salary_range_min, vacancy.salary_range_max)}</span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{vacancy._count?.applications || 0} applications</span>
                </div>

                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <CalendarDays className="h-4 w-4" />
                  <span>Posted {new Date(vacancy.created_at).toLocaleDateString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredVacancies.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Briefcase className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">
              {searchQuery || statusFilter !== 'all' ? 'No vacancies found' : 'No vacancies yet'}
            </h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchQuery || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by creating your first job vacancy to attract candidates.'
              }
            </p>
            {!searchQuery && statusFilter === 'all' && (
              <Button asChild>
                <Link href={`/org/${orgSlug}/vacancies/create`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Vacancy
                </Link>
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
