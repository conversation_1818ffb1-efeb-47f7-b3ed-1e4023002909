import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/supabase'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = createServerComponentClient<Database>({ cookies })
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: courseId } = await params

    // Check if course exists and is published
    const { data: course, error: courseError } = await supabase
      .from('courses')
      .select('id, name, status, price, is_standalone')
      .eq('id', courseId)
      .eq('status', 'published')
      .eq('is_standalone', true)
      .single()

    if (courseError || !course) {
      return NextResponse.json(
        { error: 'Course not found or not available for enrollment' },
        { status: 404 }
      )
    }

    // Check if user is already enrolled
    const { data: existingEnrollment } = await supabase
      .from('user_course_enrollments')
      .select('id')
      .eq('user_id', session.user.id)
      .eq('course_id', courseId)
      .single()

    if (existingEnrollment) {
      return NextResponse.json(
        { error: 'Already enrolled in this course' },
        { status: 400 }
      )
    }

    // Create enrollment
    const { data: enrollment, error: enrollmentError } = await supabase
      .from('user_course_enrollments')
      .insert({
        user_id: session.user.id,
        course_id: courseId,
        enrollment_context: 'individual',
        progress_percentage: 0
      })
      .select()
      .single()

    if (enrollmentError) {
      console.error('Error creating enrollment:', enrollmentError)
      return NextResponse.json(
        { error: 'Failed to enroll in course' },
        { status: 500 }
      )
    }

    // Update course enrollment count
    const { error: updateError } = await supabase
      .from('courses')
      .update({ 
        enrollment_count: course.enrollment_count + 1 
      })
      .eq('id', courseId)

    if (updateError) {
      console.error('Error updating enrollment count:', updateError)
      // Don't fail the request if this fails, enrollment is still successful
    }

    return NextResponse.json({
      message: 'Successfully enrolled in course',
      enrollment: enrollment,
      course: {
        id: course.id,
        name: course.name
      }
    })

  } catch (error) {
    console.error('Error in course enrollment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
