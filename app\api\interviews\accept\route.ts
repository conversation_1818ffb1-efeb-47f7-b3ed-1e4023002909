import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'
import { sendInterviewAcceptanceEmail } from '@/lib/email-notifications'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const { 
      interviewId, 
      selectedTime, 
      prospectNotes 
    } = await request.json()

    if (!interviewId) {
      return NextResponse.json(
        { error: 'Interview ID is required' },
        { status: 400 }
      )
    }

    // Get the interview and verify it belongs to this prospect
    const { data: interview, error: interviewError } = await supabase
      .from('interviews')
      .select(`
        id,
        application_id,
        bpo_user_id,
        status,
        feedback,
        applications!inner(
          prospect_id,
          prospects!inner(user_id)
        )
      `)
      .eq('id', interviewId)
      .single()

    if (interviewError || !interview) {
      return NextResponse.json(
        { error: 'Interview not found' },
        { status: 404 }
      )
    }

    // Verify the interview belongs to this prospect (check user_id, not prospect_id)
    if (interview.applications.prospects.user_id !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized: This interview does not belong to you' },
        { status: 403 }
      )
    }

    // Verify the interview hasn't been responded to yet
    if (interview.feedback?.prospect_response) {
      return NextResponse.json(
        { error: 'Interview invitation has already been responded to' },
        { status: 400 }
      )
    }

    // Update the interview with the selected time and accept it
    const updatedFeedback = {
      ...interview.feedback,
      prospect_response: {
        selected_time: selectedTime || null,
        prospect_notes: prospectNotes,
        responded_at: new Date().toISOString()
      }
    }

    const updateData: any = {
      status: 'scheduled',
      feedback: updatedFeedback,
      updated_at: new Date().toISOString()
    }

    // Only update scheduled_at if a specific time was selected
    if (selectedTime) {
      updateData.scheduled_at = selectedTime
    }

    const { data: updatedInterview, error: updateError } = await supabase
      .from('interviews')
      .update(updateData)
      .eq('id', interviewId)
      .select()
      .single()

    if (updateError) {
      console.error('Error accepting interview:', updateError)

      // Provide more specific error messages
      if (updateError.code === '23505') {
        return NextResponse.json(
          { error: 'This interview has already been responded to' },
          { status: 409 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to accept interview invitation. Please try again.' },
        { status: 500 }
      )
    }

    // Update application status to reflect interview is scheduled
    const { error: appUpdateError } = await supabase
      .from('applications')
      .update({
        status: 'interview_scheduled'
        // Note: interview_scheduled_at column doesn't exist, so we'll just update status for now
      })
      .eq('id', interview.application_id)

    if (appUpdateError) {
      console.error('Error updating application status:', appUpdateError)
      // Don't fail the request, just log the error
    }

    // Send email notification to BPO user
    try {
      const { data: bpoUserData, error: bpoUserError } = await supabase
        .from('users')
        .select('full_name, email')
        .eq('id', interview.bpo_user_id)
        .single()

      if (!bpoUserError && bpoUserData) {
        await sendInterviewAcceptanceEmail({
          bpoEmail: bpoUserData.email,
          prospectName: session.user.user_metadata?.full_name || 'Candidate',
          jobTitle: 'Position', // You might want to fetch this from job_postings
          selectedTime: selectedTime,
          prospectNotes: prospectNotes,
          interviewId: interviewId
        })
      }
    } catch (emailError) {
      console.error('Error sending acceptance email:', emailError)
      // Don't fail the request if email fails
    }

    return NextResponse.json({
      success: true,
      interview: updatedInterview,
      message: 'Interview invitation accepted successfully'
    })

  } catch (error) {
    console.error('Error in interview acceptance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// API to decline an interview invitation
export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Parse request body
    const { interviewId, reason } = await request.json()

    if (!interviewId) {
      return NextResponse.json(
        { error: 'Interview ID is required' },
        { status: 400 }
      )
    }

    // Get the interview and verify it belongs to this prospect
    const { data: interview, error: interviewError } = await supabase
      .from('interviews')
      .select(`
        id,
        application_id,
        status,
        feedback,
        applications!inner(
          prospect_id,
          prospects!inner(user_id)
        )
      `)
      .eq('id', interviewId)
      .single()

    if (interviewError || !interview) {
      return NextResponse.json(
        { error: 'Interview not found' },
        { status: 404 }
      )
    }

    // Verify the interview belongs to this prospect (check user_id, not prospect_id)
    if (interview.applications.prospects.user_id !== session.user.id) {
      return NextResponse.json(
        { error: 'Unauthorized: This interview does not belong to you' },
        { status: 403 }
      )
    }

    // Update the interview status to declined
    const updatedFeedback = {
      ...interview.feedback,
      prospect_response: {
        declined: true,
        decline_reason: reason,
        responded_at: new Date().toISOString()
      }
    }

    const { error: updateError } = await supabase
      .from('interviews')
      .update({
        status: 'cancelled',
        feedback: updatedFeedback,
        updated_at: new Date().toISOString()
      })
      .eq('id', interviewId)

    if (updateError) {
      console.error('Error declining interview:', updateError)
      return NextResponse.json(
        { error: 'Failed to decline interview invitation' },
        { status: 500 }
      )
    }

    // Update application status
    const { error: appUpdateError } = await supabase
      .from('applications')
      .update({
        status: 'interview_declined'
      })
      .eq('id', interview.application_id)

    if (appUpdateError) {
      console.error('Error updating application status:', appUpdateError)
      // Don't fail the request, just log the error
    }

    return NextResponse.json({
      success: true,
      message: 'Interview invitation declined'
    })

  } catch (error) {
    console.error('Error in interview decline:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
