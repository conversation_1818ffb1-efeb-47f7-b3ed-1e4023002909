'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { ArrowLeft, Clock, CheckCircle, X, FileQuestion, ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { RichTextDisplay } from '@/components/ui/rich-text-display';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import Link from 'next/link';

type QuizQuestion = {
  id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'text_input';
  options: Array<{
    id: string;
    text: string;
    is_correct: boolean;
  }>;
  correct_answer: string;
  points: number;
  order_index: number;
};

type Quiz = {
  id: string;
  title: string;
  description: string | null;
  instructions: string | null;
  total_questions: number;
  passing_score: number;
  duration: string | null;
  is_required: boolean;
  assessment_id: string;
};

export default function QuizPage() {
  const { moduleId, quizId } = useParams();
  const router = useRouter();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string | string[]>>({});
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);
  const [passedQuiz, setPassedQuiz] = useState(false);
  
  useEffect(() => {
    fetchQuizData();
  }, [moduleId, quizId]);
  
  const fetchQuizData = async () => {
    try {
      setLoading(true);
      
      // Fetch the module assessment (quiz)
      const { data: moduleAssessment, error: moduleAssessmentError } = await supabase
        .from('module_assessments')
        .select(`
          id,
          is_required,
          passing_required,
          assessments:assessment_id (
            id,
            title,
            description,
            duration,
            total_questions,
            instructions,
            passing_score
          )
        `)
        .eq('id', quizId)
        .eq('module_id', moduleId)
        .single();
        
      if (moduleAssessmentError) {
        throw moduleAssessmentError;
      }
      
      if (!moduleAssessment || !moduleAssessment.assessments) {
        throw new Error('Quiz not found');
      }
      
      const assessment = moduleAssessment.assessments as any;
      
      // Format quiz data
      const quizData = {
        id: moduleAssessment.id,
        title: assessment.title || 'Untitled Quiz',
        description: assessment.description || null,
        instructions: assessment.instructions || null,
        total_questions: assessment.total_questions || 0,
        passing_score: assessment.passing_score || 70,
        duration: assessment.duration || null,
        is_required: moduleAssessment.is_required || false,
        assessment_id: assessment.id
      };
      
      setQuiz(quizData);
      
      // Fetch quiz questions
      const { data: questionsData, error: questionsError } = await supabase
        .from('module_assessment_questions')
        .select('*')
        .eq('module_assessment_id', quizId)
        .order('order_index', { ascending: true });
        
      if (questionsError) {
        throw questionsError;
      }
      
      if (!questionsData || questionsData.length === 0) {
        throw new Error('No questions found for this quiz');
      }
      
      setQuestions(questionsData);
    } catch (error: any) {
      console.error('Error fetching quiz data:', error);
      toast({
        title: 'Error',
        description: `Failed to load quiz: ${error.message || 'Unknown error'}`,
        variant: 'destructive',
      });
      router.push(`/prospect/training/${moduleId}`);
    } finally {
      setLoading(false);
    }
  };
  
  const handleAnswerChange = (questionId: string, answer: string | string[]) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };
  
  const handleCheckboxChange = (questionId: string, optionId: string, checked: boolean) => {
    const currentAnswers = answers[questionId] as string[] || [];
    
    if (checked) {
      setAnswers(prev => ({
        ...prev,
        [questionId]: [...currentAnswers, optionId]
      }));
    } else {
      setAnswers(prev => ({
        ...prev,
        [questionId]: currentAnswers.filter(id => id !== optionId)
      }));
    }
  };
  
  const navigateToQuestion = (index: number) => {
    if (index >= 0 && index < questions.length) {
      setCurrentQuestionIndex(index);
    }
  };
  
  const calculateScore = () => {
    let correctAnswers = 0;
    let totalPoints = 0;
    
    questions.forEach(question => {
      const userAnswer = answers[question.id];
      totalPoints += question.points;
      
      if (question.question_type === 'multiple_choice') {
        // For multiple choice, check if all selected options are correct
        const selectedOptions = userAnswer as string[] || [];
        const correctOptions = question.options
          .filter(option => option.is_correct)
          .map(option => option.id);
        
        // Check if arrays have same elements (regardless of order)
        const isCorrect = 
          selectedOptions.length === correctOptions.length && 
          selectedOptions.every(id => correctOptions.includes(id));
        
        if (isCorrect) {
          correctAnswers += question.points;
        }
      } else if (question.question_type === 'true_false' || question.question_type === 'text_input') {
        // For true/false and text input, check for exact match
        const correctAnswer = 
          question.question_type === 'true_false' 
            ? question.options.find(option => option.is_correct)?.id
            : question.correct_answer;
            
        if (userAnswer === correctAnswer) {
          correctAnswers += question.points;
        }
      }
    });
    
    const finalScore = Math.round((correctAnswers / totalPoints) * 100);
    return finalScore;
  };
  
  const handleSubmitQuiz = async () => {
    try {
      setSubmitting(true);
      
      // Calculate final score
      const finalScore = calculateScore();
      setScore(finalScore);
      
      // Determine if passed based on passing score
      const passed = finalScore >= (quiz?.passing_score || 70);
      setPassedQuiz(passed);
      
      // Save quiz result to database (you would implement this part)
      // ...
      
      // Show results
      setShowResults(true);
    } catch (error: any) {
      console.error('Error submitting quiz:', error);
      toast({
        title: 'Error',
        description: `Failed to submit quiz: ${error.message || 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setSubmitting(false);
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading quiz...</p>
        </div>
      </div>
    );
  }
  
  if (!quiz) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <FileQuestion className="h-12 w-12 text-muted-foreground mx-auto" />
          <h2 className="mt-4 text-xl font-semibold">Quiz Not Found</h2>
          <p className="mt-2 text-muted-foreground">The quiz you're looking for doesn't exist or has been removed.</p>
          <Button className="mt-6" asChild>
            <Link href={`/prospect/training/${moduleId}`}>
              Back to Module
            </Link>
          </Button>
        </div>
      </div>
    );
  }
  
  // If showing results
  if (showResults) {
    return (
      <div className="max-w-3xl mx-auto px-4 py-12">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold">{quiz.title} - Results</h1>
          <p className="text-muted-foreground mt-2">You have completed this quiz</p>
        </div>
        
        <Card className="border shadow-lg overflow-hidden">
          <CardContent className="p-8">
            <div className="text-center mb-8">
              <div className={`w-24 h-24 rounded-full mx-auto flex items-center justify-center ${
                passedQuiz ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
              }`}>
                {passedQuiz ? (
                  <CheckCircle className="h-12 w-12" />
                ) : (
                  <X className="h-12 w-12" />
                )}
              </div>
              
              <h2 className="text-2xl font-bold mt-4">
                {passedQuiz ? 'Quiz Passed!' : 'Quiz Failed'}
              </h2>
              
              <div className="mt-2 text-lg">
                Your score: <span className="font-bold">{score}%</span>
              </div>
              
              <div className="text-sm text-muted-foreground mt-1">
                Passing score: {quiz.passing_score}%
              </div>
            </div>
            
            <Separator className="my-6" />
            
            <div className="mt-8 flex justify-center space-x-4">
              <Button variant="outline" asChild>
                <Link href={`/prospect/training/${moduleId}`}>
                  Back to Module
                </Link>
              </Button>
              
              {!passedQuiz && (
                <Button onClick={() => {
                  setShowResults(false);
                  setCurrentQuestionIndex(0);
                }}>
                  Retry Quiz
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  // Get current question
  const currentQuestion = questions[currentQuestionIndex];
  
  return (
    <div className="max-w-3xl mx-auto px-4 py-6">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <Button variant="ghost" size="sm" asChild className="text-muted-foreground">
            <Link href={`/prospect/training/${moduleId}`}>
              <ArrowLeft className="h-4 w-4 mr-1.5" />
              Exit Quiz
            </Link>
          </Button>
          
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-1.5 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              Question {currentQuestionIndex + 1} of {questions.length}
            </span>
          </div>
        </div>
        
        <Progress 
          value={((currentQuestionIndex + 1) / questions.length) * 100} 
          className="h-2"
        />
      </div>
      
      {currentQuestion && (
        <Card className="border shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Badge variant="outline">Question {currentQuestionIndex + 1}</Badge>
              <Badge variant="secondary" className="bg-purple-100 text-purple-700 border-purple-200">
                {currentQuestion.question_type === 'multiple_choice' 
                  ? 'Multiple Choice' 
                  : currentQuestion.question_type === 'true_false' 
                    ? 'True/False' 
                    : 'Text Input'}
              </Badge>
              {currentQuestion.points > 1 && (
                <Badge variant="outline" className="ml-auto">
                  {currentQuestion.points} points
                </Badge>
              )}
            </div>
            
            <div className="prose prose-gray dark:prose-invert max-w-none mb-6">
              <RichTextDisplay content={currentQuestion.question_text} />
            </div>
            
            <div className="mt-6 space-y-4">
              {currentQuestion.question_type === 'multiple_choice' && (
                <div className="space-y-3">
                  {currentQuestion.options.map(option => (
                    <div key={option.id} className="flex items-center space-x-2">
                      <Checkbox 
                        id={option.id} 
                        checked={(answers[currentQuestion.id] as string[] || []).includes(option.id)}
                        onCheckedChange={(checked) => 
                          handleCheckboxChange(currentQuestion.id, option.id, checked as boolean)
                        }
                      />
                      <Label 
                        htmlFor={option.id} 
                        className="cursor-pointer flex-1 p-2 hover:bg-muted/50 rounded-md"
                      >
                        {option.text}
                      </Label>
                    </div>
                  ))}
                </div>
              )}
              
              {currentQuestion.question_type === 'true_false' && (
                <RadioGroup
                  value={answers[currentQuestion.id] as string || ''}
                  onValueChange={(value) => handleAnswerChange(currentQuestion.id, value)}
                  className="space-y-3"
                >
                  {currentQuestion.options.map(option => (
                    <div key={option.id} className="flex items-center space-x-2">
                      <RadioGroupItem value={option.id} id={option.id} />
                      <Label 
                        htmlFor={option.id} 
                        className="cursor-pointer flex-1 p-2 hover:bg-muted/50 rounded-md"
                      >
                        {option.text}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              )}
              
              {currentQuestion.question_type === 'text_input' && (
                <div>
                  <Input 
                    placeholder="Type your answer here"
                    value={answers[currentQuestion.id] as string || ''}
                    onChange={(e) => handleAnswerChange(currentQuestion.id, e.target.value)}
                  />
                </div>
              )}
            </div>
            
            <div className="mt-8 flex justify-between">
              <Button
                variant="outline"
                onClick={() => navigateToQuestion(currentQuestionIndex - 1)}
                disabled={currentQuestionIndex === 0}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>
              
              {currentQuestionIndex < questions.length - 1 ? (
                <Button
                  onClick={() => navigateToQuestion(currentQuestionIndex + 1)}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmitQuiz}
                  disabled={submitting}
                >
                  {submitting ? 'Submitting...' : 'Submit Quiz'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 