"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface EnrolledCourse {
  id: string
  title: string
  provider: string
  category: string
  duration: string
  progress: number
  status: 'enrolled' | 'completed' | 'in-progress'
  skills: string[]
  level: 'beginner' | 'intermediate' | 'advanced'
  rating: number
  students: number
  enrolledAt: string
  slug: string
  coverImage?: string
}

// Function to fetch user's enrolled courses
async function fetchEnrolledCourses(): Promise<EnrolledCourse[]> {
  try {
    const response = await fetch('/api/individual/enrolled-courses', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies for authentication
    })

    if (!response.ok) {
      console.error('Failed to fetch enrolled courses:', response.status, response.statusText)
      throw new Error(`Failed to fetch enrolled courses: ${response.status}`)
    }

    const data = await response.json()
    console.log('Enrolled courses API response:', data) // Debug log

    // The API returns 'courses' array with transformed data
    return data.courses?.map((course: any) => ({
      id: course.id,
      title: course.title, // API returns 'title' field
      provider: course.instructor || 'Luna Learning', // API returns 'instructor' field
      category: course.tags?.[0] || 'General',
      duration: course.duration ?
        `${Math.ceil(course.duration / 60)} hours` :
        'Self-paced',
      progress: course.progressPercentage || 0, // API returns 'progressPercentage'
      status: course.status === 'completed' ? 'completed' :
               course.status === 'in_progress' ? 'in-progress' : 'enrolled',
      skills: course.tags || [],
      level: course.level?.toLowerCase() || 'beginner',
      rating: 4.5, // Default rating
      students: 0, // Not available in enrolled courses API
      enrolledAt: course.enrolledAt,
      slug: course.slug || course.id, // Use slug if available, fallback to ID
      coverImage: course.thumbnail // API returns 'thumbnail' field
    })) || []
  } catch (error) {
    console.error('Error fetching enrolled courses:', error)
    // Return empty array so we fall back to available courses
    return []
  }
}

// Function to fetch available courses from marketplace
async function fetchAvailableCourses(): Promise<EnrolledCourse[]> {
  try {
    const response = await fetch('/api/individual/courses?limit=8')
    if (!response.ok) {
      throw new Error('Failed to fetch available courses')
    }
    const data = await response.json()

    // Transform the API response to match our interface
    return data.courses?.map((course: any) => ({
      id: course.id,
      title: course.title,
      provider: course.instructor || 'Luna Learning',
      category: course.category || 'General',
      duration: course.duration || 'Self-paced',
      progress: 0, // No progress for available courses
      status: 'enrolled' as const, // Show as available to enroll
      skills: course.tags || [],
      level: course.level || 'beginner',
      rating: course.rating || 4.5,
      students: course.enrollmentCount || 0,
      enrolledAt: '',
      slug: course.slug,
      coverImage: course.coverImage
    })) || []
  } catch (error) {
    console.error('Error fetching available courses:', error)
    return []
  }
}

interface CourseCarouselProps {
  className?: string
}

export function CourseCarousel({ className = "" }: CourseCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [courses, setCourses] = useState<EnrolledCourse[]>([])
  const [loading, setLoading] = useState(true)
  const [showingAvailable, setShowingAvailable] = useState(false)

  // Fetch courses on component mount
  useEffect(() => {
    const loadCourses = async () => {
      setLoading(true)

      console.log('Starting to load courses...') // Debug log

      // First try to get enrolled courses
      const enrolledCourses = await fetchEnrolledCourses()
      console.log('Enrolled courses fetched:', enrolledCourses.length) // Debug log

      if (enrolledCourses.length > 0) {
        console.log('Showing enrolled courses') // Debug log
        setCourses(enrolledCourses)
        setShowingAvailable(false)
      } else {
        console.log('No enrolled courses, fetching available courses') // Debug log
        // If no enrolled courses, fetch available courses
        const availableCourses = await fetchAvailableCourses()
        console.log('Available courses fetched:', availableCourses.length) // Debug log
        setCourses(availableCourses)
        setShowingAvailable(true)
      }

      setLoading(false)
    }

    loadCourses()
  }, [])

  // Auto-slide functionality
  useEffect(() => {
    if (!isAutoPlaying || courses.length === 0) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % Math.max(1, courses.length - 3))
    }, 4000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, courses.length])

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % Math.max(1, courses.length - 3))
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + Math.max(1, courses.length - 3)) % Math.max(1, courses.length - 3))
  }

  // Show loading state
  if (loading) {
    return (
      <div className={`bg-gray-50 py-20 px-6 ${className}`}>
        <div className="max-w-7xl mx-auto">
          <div className="mb-16">
            <div className="flex items-center gap-2 mb-6">
              <div className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium">
                ✓ Luna Learning
              </div>
            </div>
            <div className="mb-4">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                Your Learning Journey
              </h2>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                Courses & Modules
              </h2>
            </div>
            <p className="text-gray-600 text-lg max-w-2xl">
              Loading your enrolled courses...
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white border border-gray-200 rounded-lg h-96 animate-pulse">
                <div className="h-48 bg-gray-100"></div>
                <div className="p-5 space-y-3">
                  <div className="h-4 bg-gray-100 rounded"></div>
                  <div className="h-3 bg-gray-100 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-100 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  // Show empty state only if no courses at all (both enrolled and available failed)
  if (courses.length === 0) {
    return (
      <div className={`bg-gray-50 py-20 px-6 ${className}`}>
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-16">
            <div className="flex items-center justify-center gap-2 mb-6">
              <div className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium">
                ✓ Luna Learning
              </div>
            </div>
            <div className="mb-4">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                Learning Platform
              </h2>
            </div>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto mb-8">
              We're preparing your learning experience. Please check back soon.
            </p>
            <Button
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
              onClick={() => window.location.href = '/individual/courses'}
            >
              Explore Courses
            </Button>
          </div>
        </div>
      </div>
    )
  }



  return (
    <div className={`bg-gray-50 py-20 px-6 ${className}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header Section - Dynamic based on content */}
        <div className="mb-16">
          {/* Luna Learning Badge */}
          <div className="flex items-center gap-2 mb-6">
            <div className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm font-medium">
              ✓ Luna Learning
            </div>
          </div>

          {/* Main Heading - Dynamic */}
          <div className="mb-4">
            {showingAvailable ? (
              <>
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                  Start Your Learning Journey
                </h2>
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                  Featured Courses & Programs
                </h2>
              </>
            ) : (
              <>
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                  Your Learning Journey
                </h2>
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
                  Courses & Modules
                </h2>
              </>
            )}
          </div>

          {/* Subtitle - Dynamic */}
          <p className="text-gray-600 text-lg max-w-2xl">
            {showingAvailable
              ? "Explore our curated selection of courses and programs to begin your professional development"
              : "Track your progress across enrolled courses and completed modules"
            }
          </p>
        </div>

        {/* Course Cards Carousel - 4 cards with 4th half-visible */}
        <div className="relative overflow-hidden mb-12">
          <div
            className="flex transition-transform duration-500 ease-in-out gap-3"
            style={{ transform: `translateX(-${currentIndex * 25}%)` }}
          >
            {courses.map((course) => (
              <div key={course.id} className="flex-none w-1/4 pr-3">
                <Card className="bg-white border border-gray-200 hover:shadow-md transition-all duration-300 rounded-lg overflow-hidden h-full">
                  {/* Course Header - Clean Professional Design */}
                  <div className="relative h-48 bg-gray-50 border-b border-gray-100">
                {/* Status Badge - Minimal Design */}
                <div className="absolute top-3 right-3">
                  <div className={`px-2 py-1 rounded text-xs font-medium border ${
                    course.status === 'completed' ? 'bg-green-50 text-green-700 border-green-200' :
                    course.status === 'in-progress' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                    'bg-gray-50 text-gray-600 border-gray-200'
                  }`}>
                    {course.status === 'completed' ? 'Completed' :
                     course.status === 'in-progress' ? 'In Progress' :
                     'Enrolled'}
                  </div>
                </div>

                {/* Course Category Icon - Professional */}
                <div className="absolute top-3 left-3">
                  <div className="w-8 h-8 bg-white border border-gray-200 rounded flex items-center justify-center">
                    <div className={`w-3 h-3 rounded-full ${
                      course.category.toLowerCase().includes('data') ? 'bg-purple-500' :
                      course.category.toLowerCase().includes('marketing') ? 'bg-orange-500' :
                      course.category.toLowerCase().includes('leadership') ? 'bg-blue-500' :
                      course.category.toLowerCase().includes('design') ? 'bg-pink-500' :
                      course.category.toLowerCase().includes('finance') ? 'bg-green-500' : 'bg-gray-500'
                    }`}></div>
                  </div>
                </div>

                {/* Course Provider Logo Area */}
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-white border border-gray-200 rounded-lg flex items-center justify-center mb-3 mx-auto">
                      <span className="text-lg font-semibold text-gray-600">
                        {course.provider.split(' ')[0].substring(0, 2).toUpperCase()}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500 font-medium">{course.category}</span>
                  </div>
                </div>

                {/* Progress Bar - Minimal (only for enrolled courses) */}
                {!showingAvailable && course.progress > 0 && (
                  <div className="absolute bottom-0 left-0 right-0">
                    <div className="bg-gray-200 h-1">
                      <div
                        className={`h-1 transition-all duration-300 ${
                          course.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

                  <CardContent className="p-5">
                    {/* Course Title */}
                    <h3 className="font-semibold text-gray-900 text-base mb-1 line-clamp-2 leading-tight">
                      {course.title}
                    </h3>

                    {/* Provider */}
                    <p className="text-sm text-gray-500 mb-3 font-medium">
                      {course.provider}
                    </p>

                    {/* Course Meta Info */}
                    <div className="mb-4 space-y-1">
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>{course.duration}</span>
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded capitalize">
                          {course.level}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span>★ {course.rating}</span>
                        <span>{course.students > 0 ? course.students.toLocaleString() : 'New'} students</span>
                      </div>
                    </div>

                    {/* Progress Info - Only for enrolled courses */}
                    {!showingAvailable && course.progress > 0 && (
                      <div className="mb-4">
                        <div className="flex items-center justify-between text-sm mb-1">
                          <span className="text-gray-600">Progress</span>
                          <span className="text-gray-900 font-medium">{course.progress}%</span>
                        </div>
                      </div>
                    )}

                    {/* Skills - Clean Tags */}
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {course.skills.slice(0, 2).map((skill, index) => (
                          <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded border">
                            {skill}
                          </span>
                        ))}
                        {course.skills.length > 2 && (
                          <span className="text-xs text-gray-400">+{course.skills.length - 2}</span>
                        )}
                      </div>
                    </div>

                    {/* Action Button - Professional */}
                    <Button
                      variant="outline"
                      className="w-full text-sm border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 py-2 font-medium"
                      onClick={() => window.location.href = `/individual/courses/${course.slug}`}
                    >
                      {showingAvailable ? 'View Course' :
                       course.status === 'completed' ? 'Review' :
                       course.status === 'in-progress' ? 'Continue' :
                       'Start Course'}
                    </Button>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Arrows - Topline Style */}
        <div className="flex justify-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={prevSlide}
            className="w-10 h-10 rounded-full border-gray-300 hover:border-gray-400 bg-white"
          >
            <ChevronLeft className="h-4 w-4 text-gray-600" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={nextSlide}
            className="w-10 h-10 rounded-full border-gray-300 hover:border-gray-400 bg-white"
          >
            <ChevronRight className="h-4 w-4 text-gray-600" />
          </Button>
        </div>
      </div>
    </div>
  )
}
