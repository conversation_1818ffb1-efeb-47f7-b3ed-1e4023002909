"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { ResponsiveContainer, AreaChart, Area, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend } from "recharts"

const data = [
  { name: "Week 1", modules: 2, hours: 5 },
  { name: "Week 2", modules: 3, hours: 8 },
  { name: "Week 3", modules: 2, hours: 6 },
  { name: "Week 4", modules: 4, hours: 10 },
  { name: "Week 5", modules: 3, hours: 7 },
  { name: "Week 6", modules: 5, hours: 12 },
  { name: "Week 7", modules: 3, hours: 9 },
]

export function LearningProgressChart() {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div className="h-[300px] flex items-center justify-center">Loading chart...</div>
  }

  const isDark = theme === "dark"

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data} margin={{ top: 10, right: 10, left: -20, bottom: 0 }}>
          <defs>
            <linearGradient id="colorModules" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#4f46e5" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#4f46e5" stopOpacity={0} />
            </linearGradient>
            <linearGradient id="colorHours" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke={isDark ? "#374151" : "#e5e7eb"} />
          <XAxis
            dataKey="name"
            tick={{
              fill: isDark ? "#9ca3af" : "#6b7280",
              fontSize: 12,
              fontWeight: 500,
            }}
          />
          <YAxis
            tick={{
              fill: isDark ? "#9ca3af" : "#6b7280",
              fontSize: 12,
              fontWeight: 500,
            }}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: isDark ? "#1f2937" : "#ffffff",
              borderColor: isDark ? "#374151" : "#e5e7eb",
              color: isDark ? "#ffffff" : "#000000",
              borderRadius: "0.375rem",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
              fontWeight: 500,
            }}
          />
          <Area
            type="monotone"
            dataKey="modules"
            name="Modules Completed"
            stroke="#4f46e5"
            strokeWidth={2}
            fillOpacity={1}
            fill="url(#colorModules)"
          />
          <Area
            type="monotone"
            dataKey="hours"
            name="Hours Spent"
            stroke="#3b82f6"
            strokeWidth={2}
            fillOpacity={1}
            fill="url(#colorHours)"
          />
          <Legend
            wrapperStyle={{
              paddingTop: 10,
              fontWeight: 500,
            }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  )
}
