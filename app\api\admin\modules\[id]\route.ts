import { NextRequest, NextResponse } from 'next/server'
import { requirePlatformAdmin } from '@/lib/auth/middleware'
import { createAdminClient } from '@/lib/supabase-admin'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const moduleId = params.id
    const body = await request.json()

    const {
      name,
      description,
      module_type = 'theory',
      difficulty_level = 'beginner',
      estimated_duration,
      is_standalone = false,
      single_price,
      status = 'draft',
      learning_objectives = [],
      required_resources = [],
      module_prerequisites = []
    } = body

    // Validation
    if (!name?.trim()) {
      return NextResponse.json(
        { error: 'Module name is required' },
        { status: 400 }
      )
    }

    const supabase = createAdminClient()

    // Update the module
    const { data: module, error: moduleError } = await supabase
      .from('course_modules')
      .update({
        name: name.trim(),
        description: description?.trim() || '',
        module_type,
        difficulty_level,
        estimated_duration,
        is_standalone,
        single_price,
        status,
        learning_objectives,
        required_resources,
        module_prerequisites,
        updated_at: new Date().toISOString()
      })
      .eq('id', moduleId)
      .select()
      .single()

    if (moduleError) {
      console.error('Error updating module:', moduleError)
      return NextResponse.json(
        { error: 'Failed to update module' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      module,
      message: 'Module updated successfully'
    })

  } catch (error) {
    console.error('Error in PUT /api/admin/modules/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication required' },
        { status: authResult.status || 401 }
      )
    }

    const moduleId = params.id
    const supabase = createAdminClient()

    // Delete the module (this will cascade delete lessons)
    const { error: deleteError } = await supabase
      .from('course_modules')
      .delete()
      .eq('id', moduleId)

    if (deleteError) {
      console.error('Error deleting module:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete module' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Module deleted successfully'
    })

  } catch (error) {
    console.error('Error in DELETE /api/admin/modules/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
