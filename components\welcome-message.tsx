"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Sparkles } from "lucide-react"

interface WelcomeMessageProps {
  name: string
  progress: number
  notificationData?: {
    newJobMatches: number
    recentBadges: number
    upcomingInterviews: number
  }
  assessmentData?: {
    completed: number
    total: number
  }
}

export function WelcomeMessage({ name, progress, notificationData, assessmentData }: WelcomeMessageProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [timeOfDay, setTimeOfDay] = useState("")

  useEffect(() => {
    const hour = new Date().getHours()
    if (hour >= 5 && hour < 12) {
      setTimeOfDay("morning")
    } else if (hour >= 12 && hour < 18) {
      setTimeOfDay("afternoon")
    } else {
      setTimeOfDay("evening")
    }
  }, [])

  if (!isVisible) return null

  return (
    <Card className="relative overflow-hidden border-none bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 shadow-sm">
      <div className="absolute inset-0 bg-grid-black/[0.02] [mask-image:linear-gradient(0deg,#fff,rgba(255,255,255,0.6))] dark:bg-grid-white/[0.02]"></div>
      <Button
        variant="ghost"
        size="icon"
        className="absolute right-2 top-2 h-8 w-8 rounded-full opacity-70 hover:opacity-100"
        onClick={() => setIsVisible(false)}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Dismiss</span>
      </Button>
      <CardContent className="p-6">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <div className="space-y-2">
            <h1 className="text-2xl font-bold tracking-tight">
              Good {timeOfDay}, {name}! <span className="inline-block animate-wave">👋</span>
            </h1>
            <p className="text-muted-foreground text-base">
              You're making great progress! You've completed{" "}
              <span className="font-semibold text-blue-600 dark:text-blue-400">{progress}%</span> of your training modules.
            </p>
          </div>
          <div className="flex items-center gap-2 self-start sm:self-center">
            <Button className="mt-2 sm:mt-0 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-sm font-medium">
              <Sparkles className="mr-2 h-4 w-4" />
              Continue Training
            </Button>
          </div>
        </div>
        <div className="mt-4 flex flex-wrap gap-2">
          {/* Show assessment progress */}
          {assessmentData && assessmentData.total > 0 && (
            <InfoPill
              icon="🎯"
              text={assessmentData.completed === assessmentData.total
                ? "All assessments completed!"
                : `${assessmentData.total - assessmentData.completed} assessments remaining`
              }
            />
          )}

          {/* Show job matches if any */}
          {notificationData && notificationData.newJobMatches > 0 && (
            <InfoPill
              icon="💼"
              text={`${notificationData.newJobMatches} new job ${notificationData.newJobMatches === 1 ? 'match' : 'matches'}`}
            />
          )}

          {/* Show recent badges if any */}
          {notificationData && notificationData.recentBadges > 0 && (
            <InfoPill
              icon="🏆"
              text={`You earned ${notificationData.recentBadges} new ${notificationData.recentBadges === 1 ? 'badge' : 'badges'}!`}
            />
          )}

          {/* Show upcoming interviews if any */}
          {notificationData && notificationData.upcomingInterviews > 0 && (
            <InfoPill
              icon="📅"
              text={`${notificationData.upcomingInterviews} upcoming ${notificationData.upcomingInterviews === 1 ? 'interview' : 'interviews'}`}
            />
          )}

          {/* Fallback message if no notifications */}
          {(!notificationData || (notificationData.newJobMatches === 0 && notificationData.recentBadges === 0 && notificationData.upcomingInterviews === 0)) &&
           (!assessmentData || assessmentData.total === 0) && (
            <InfoPill icon="🚀" text="Keep up the great work!" />
          )}
        </div>
      </CardContent>
    </Card>
  )
}

function InfoPill({ icon, text }: { icon: string; text: string }) {
  return (
    <div className="inline-flex items-center rounded-full bg-white/60 dark:bg-gray-800/60 px-3 py-1 text-sm backdrop-blur-sm">
      <span className="mr-1.5">{icon}</span>
      {text}
    </div>
  )
}
