import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { togetherAI } from '@/lib/ai/together-client';
import { getModelForAssessment, getGenerationParams } from '@/lib/ai/config';
import type { AssessmentGenerationRequest } from '@/lib/ai/together-client';

/**
 * Generate assessment questions using AI
 * POST /api/admin/ai/generate-assessment
 */
export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const body = await req.json();
    const {
      assessmentType,
      questionCount = 20,
      difficultyDistribution = { easy: 30, medium: 50, hard: 20 },
      subcategories = [],
      questionTypes = ['multiple_choice'],
      systemPrompt,
      parameters = {}
    } = body;

    // Validate required fields
    if (!assessmentType || !systemPrompt) {
      return NextResponse.json(
        { error: 'Assessment type and system prompt are required' },
        { status: 400 }
      );
    }

    // Build generation request
    const generationRequest: AssessmentGenerationRequest = {
      assessmentType,
      questionCount,
      difficultyDistribution,
      subcategories,
      questionTypes,
      systemPrompt,
      parameters: {
        ...parameters,
        target_role: 'BPO Call Center Agent',
        industry_focus: 'Business Process Outsourcing',
        current_date: new Date().toISOString().split('T')[0]
      }
    };

    console.log('Generating assessment with request:', {
      assessmentType,
      questionCount,
      subcategories: subcategories.length,
      questionTypes
    });

    // Generate questions using Together.ai
    const result = await togetherAI.generateAssessmentQuestions(generationRequest);

    console.log('Assessment generation completed:', {
      questionsGenerated: result.questions.length,
      tokensUsed: result.metadata.tokens_used,
      qualityScore: result.metadata.quality_score
    });

    return NextResponse.json({
      success: true,
      questions: result.questions,
      metadata: result.metadata,
      generation_request: {
        assessment_type: assessmentType,
        question_count: questionCount,
        model_used: getModelForAssessment(assessmentType),
        generation_params: getGenerationParams(assessmentType)
      }
    });

  } catch (error: any) {
    console.error('Assessment generation error:', error);
    
    // Return detailed error information for debugging
    return NextResponse.json(
      { 
        error: 'Failed to generate assessment',
        details: error.message,
        type: error.constructor.name
      },
      { status: 500 }
    );
  }
}

/**
 * Test prompt generation with sample data
 * POST /api/admin/ai/generate-assessment/test
 */
export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Sample test prompt for communication skills
    const testPrompt = `You are an expert BPO industry assessment designer specializing in communication skills evaluation for call center agents.

ASSESSMENT CONTEXT:
- Target Role: BPO Call Center Agent
- Industry: Business Process Outsourcing (BPO)
- Focus: Customer service communication readiness
- Duration: 30 minutes
- Questions: 5 total

SKILL AREAS TO EVALUATE:
- Grammar & Syntax (40%)
- Customer Service Communication (60%)

QUESTION REQUIREMENTS:
- All scenarios must be realistic BPO situations
- Include customer service interactions
- Test professional communication skills
- Difficulty: {"easy": 40, "medium": 60}
- Question types: multiple_choice

SCORING CRITERIA:
- Grammar and syntax accuracy
- Professional tone and language
- Customer service best practices

Generate exactly 5 questions following this structure:
{
  "questions": [
    {
      "id": "q1",
      "question_type": "multiple_choice",
      "question_text": "Clear, specific question",
      "question_context": "Background scenario if needed",
      "answer_options": ["Option A", "Option B", "Option C", "Option D"],
      "correct_answer": "Option A",
      "explanation": "Why this is correct",
      "subcategory": "grammar",
      "difficulty": "medium",
      "points": 1,
      "estimated_time": 60
    }
  ]
}

Ensure questions are relevant to BPO call center work and properly formatted.`;

    const testRequest: AssessmentGenerationRequest = {
      assessmentType: 'communication_skills',
      questionCount: 5,
      difficultyDistribution: { easy: 40, medium: 60, hard: 0 },
      subcategories: ['grammar', 'customer_service'],
      questionTypes: ['multiple_choice'],
      systemPrompt: testPrompt,
      parameters: {
        target_role: 'BPO Call Center Agent',
        industry_focus: 'Business Process Outsourcing'
      }
    };

    console.log('Running test assessment generation...');
    const result = await togetherAI.generateAssessmentQuestions(testRequest);

    return NextResponse.json({
      success: true,
      test_result: {
        questions_generated: result.questions.length,
        quality_score: result.metadata.quality_score,
        tokens_used: result.metadata.tokens_used,
        generation_time: result.metadata.generation_time
      },
      sample_questions: result.questions.slice(0, 2), // Return first 2 questions as samples
      metadata: result.metadata
    });

  } catch (error: any) {
    console.error('Test generation error:', error);
    return NextResponse.json(
      { 
        error: 'Test generation failed',
        details: error.message
      },
      { status: 500 }
    );
  }
}
