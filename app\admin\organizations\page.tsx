'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { createBrowserClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import {
  Building2,
  Users,
  Plus,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Filter,
  Download,
  TrendingUp,
  Activity,
  Crown,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Settings,
  AlertTriangle,
  UserCheck,
  Target,
  Globe,
  Calendar
} from 'lucide-react';

// Define types based on our database schema
interface Organization {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  website: string | null;
  industry: string | null;
  size: string | null;
  created_at: string | null;
  updated_at: string | null;
  _count?: {
    departments: number;
    employees: number;
    admins: number;
  };
}

interface OrganizationStats {
  totalOrganizations: number;
  totalEmployees: number;
  totalDepartments: number;
  averageOrgSize: number;
}

interface User {
  id: string;
  full_name: string;
  email: string;
}

export default function OrganizationsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);

  const supabase = createBrowserClient();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [sizeFilter, setSizeFilter] = useState('all');
  const [sortField, setSortField] = useState<keyof Organization | 'employees'>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    website: '',
    industry: '',
    size: ''
  });

  // Calculate stats
  const stats = useMemo((): OrganizationStats => {
    const totalOrganizations = organizations.length;
    const totalEmployees = organizations.reduce((sum, org) => sum + (org._count?.employees || 0), 0);
    const totalDepartments = organizations.reduce((sum, org) => sum + (org._count?.departments || 0), 0);
    const averageOrgSize = totalOrganizations > 0 ? Math.round(totalEmployees / totalOrganizations) : 0;

    return {
      totalOrganizations,
      totalEmployees,
      totalDepartments,
      averageOrgSize
    };
  }, [organizations]);

  // Filter and sort organizations
  const filteredOrganizations = useMemo(() => {
    let filtered = organizations.filter(org => {
      const matchesSearch = searchQuery === '' ||
        org.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (org.description && org.description.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesIndustry = industryFilter === 'all' ||
        (org.industry && org.industry.toLowerCase() === industryFilter.toLowerCase());

      const employeeCount = org._count?.employees || 0;
      const matchesSize = sizeFilter === 'all' ||
        (sizeFilter === 'small' && employeeCount <= 50) ||
        (sizeFilter === 'medium' && employeeCount > 50 && employeeCount <= 200) ||
        (sizeFilter === 'large' && employeeCount > 200);

      return matchesSearch && matchesIndustry && matchesSize;
    });

    // Sort organizations
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      if (sortField === 'employees') {
        aValue = a._count?.employees || 0;
        bValue = b._count?.employees || 0;
      } else {
        aValue = a[sortField];
        bValue = b[sortField];
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [organizations, searchQuery, industryFilter, sizeFilter, sortField, sortDirection]);

  // Sorting functions
  const handleSort = useCallback((field: keyof Organization | 'employees') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field as keyof Organization);
      setSortDirection('asc');
    }
  }, [sortField, sortDirection]);

  const getSortIcon = useCallback((field: keyof Organization | 'employees') => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  }, [sortField, sortDirection]);

  useEffect(() => {
    fetchOrganizations();
    fetchUsers();
  }, []);

  const fetchOrganizations = async () => {
    try {
      setLoading(true);

      // Fetch organizations
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching organizations:', error);
        // For development: use mock data if organizations table doesn't exist or is empty
        const mockOrganizations: Organization[] = [
          {
            id: '1',
            name: 'Acme Corporation',
            slug: 'acme-corp',
            description: 'Leading technology solutions provider',
            website: 'https://acme.com',
            industry: 'Technology',
            size: 'Large',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            _count: { departments: 5, employees: 120, admins: 3 }
          },
          {
            id: '2',
            name: 'Global Industries',
            slug: 'global-industries',
            description: 'Manufacturing and distribution company',
            website: 'https://globalind.com',
            industry: 'Manufacturing',
            size: 'Medium',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            _count: { departments: 3, employees: 75, admins: 2 }
          },
          {
            id: '3',
            name: 'StartupCo',
            slug: 'startupco',
            description: 'Innovative startup in fintech space',
            website: 'https://startupco.com',
            industry: 'Finance',
            size: 'Small',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            _count: { departments: 2, employees: 25, admins: 1 }
          }
        ];
        setOrganizations(mockOrganizations);
        return;
      }

      // Get counts for each organization
      const organizationsWithCounts = await Promise.all(
        (data || []).map(async (org) => {
          // Get department count
          const { data: departments, error: deptError } = await supabase
            .from('departments')
            .select('id')
            .eq('organization_id', org.id);

          // Get employee count
          const { data: employees, error: empError } = await supabase
            .from('employment_relationships')
            .select('id')
            .eq('organization_id', org.id)
            .eq('status', 'active');

          // Get admin count
          const { data: admins, error: adminError } = await supabase
            .from('employment_relationships')
            .select('id')
            .eq('organization_id', org.id)
            .eq('status', 'active')
            .in('role', ['organization_admin', 'department_admin']);

          if (deptError) console.error('Error fetching department count:', deptError);
          if (empError) console.error('Error fetching employee count:', empError);
          if (adminError) console.error('Error fetching admin count:', adminError);

          return {
            ...org,
            slug: org.slug || org.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
            _count: {
              departments: departments?.length || 0,
              employees: employees?.length || 0,
              admins: admins?.length || 0
            }
          };
        })
      );

      setOrganizations(organizationsWithCounts);
    } catch (error) {
      console.error('Error fetching organizations:', error);
      toast({
        title: "Error",
        description: "Failed to load organizations.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      // Get all platform users for admin assignment
      const { data, error } = await supabase
        .from('users')
        .select('id, full_name, email')
        .order('full_name');

      if (error) {
        console.error('Error fetching users:', error);
        return;
      }

      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      setUsers([]);
    }
  };

  const handleCreateOrganization = async () => {
    try {
      const { error } = await supabase
        .from('organizations')
        .insert({
          name: formData.name,
          description: formData.description || null,
          website: formData.website || null,
          industry: formData.industry || null,
          size: formData.size || null,
          slug: formData.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Organization created successfully.",
      });

      setIsCreateDialogOpen(false);
      setFormData({ name: '', description: '', website: '', industry: '', size: '' });
      fetchOrganizations();
    } catch (error) {
      console.error('Error creating organization:', error);
      toast({
        title: "Error",
        description: "Failed to create organization.",
        variant: "destructive",
      });
    }
  };

  const handleEditOrganization = async () => {
    if (!selectedOrganization) return;

    try {
      const { error } = await supabase
        .from('organizations')
        .update({
          name: formData.name,
          description: formData.description || null,
          website: formData.website || null,
          industry: formData.industry || null,
          size: formData.size || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedOrganization.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Organization updated successfully.",
      });

      setIsEditDialogOpen(false);
      setSelectedOrganization(null);
      setFormData({ name: '', description: '', website: '', industry: '', size: '' });
      fetchOrganizations();
    } catch (error) {
      console.error('Error updating organization:', error);
      toast({
        title: "Error",
        description: "Failed to update organization.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteOrganization = async (organizationId: string) => {
    if (!confirm('Are you sure you want to delete this organization? This will also delete all associated departments and employment relationships.')) return;

    try {
      const { error } = await supabase
        .from('organizations')
        .delete()
        .eq('id', organizationId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Organization deleted successfully.",
      });

      fetchOrganizations();
    } catch (error) {
      console.error('Error deleting organization:', error);
      toast({
        title: "Error",
        description: "Failed to delete organization.",
        variant: "destructive",
      });
    }
  };

  const openEditDialog = (organization: Organization) => {
    setSelectedOrganization(organization);
    setFormData({
      name: organization.name,
      description: organization.description || '',
      website: organization.website || '',
      industry: organization.industry || '',
      size: organization.size || ''
    });
    setIsEditDialogOpen(true);
  };

  if (loading && organizations.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading organizations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-[50px]">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Organizations</h1>
          <p className="text-muted-foreground">
            Manage organizations on the platform
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Organization
          </Button>
        </div>
      </div>

      {/* Create Organization Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Organization</DialogTitle>
              <DialogDescription>
                Add a new organization to the platform.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Organization Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter organization name"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter organization description"
                />
              </div>
              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                  placeholder="https://example.com"
                />
              </div>
              <div>
                <Label htmlFor="industry">Industry</Label>
                <Select
                  value={formData.industry}
                  onValueChange={(value) => setFormData({ ...formData, industry: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No industry specified</SelectItem>
                    <SelectItem value="Technology">Technology</SelectItem>
                    <SelectItem value="Healthcare">Healthcare</SelectItem>
                    <SelectItem value="Finance">Finance</SelectItem>
                    <SelectItem value="Education">Education</SelectItem>
                    <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                    <SelectItem value="Retail">Retail</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="size">Organization Size</Label>
                <Select
                  value={formData.size}
                  onValueChange={(value) => setFormData({ ...formData, size: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No size specified</SelectItem>
                    <SelectItem value="Small">Small (1-50 employees)</SelectItem>
                    <SelectItem value="Medium">Medium (51-200 employees)</SelectItem>
                    <SelectItem value="Large">Large (200+ employees)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateOrganization}>Create Organization</Button>
            </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Organizations</div>
              <div className="text-3xl font-bold mt-1">{stats.totalOrganizations}</div>
              <p className="text-xs text-muted-foreground">
                Active on platform
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
              <Users className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Employees</div>
              <div className="text-3xl font-bold mt-1">{stats.totalEmployees}</div>
              <p className="text-xs text-muted-foreground">
                Across all organizations
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/20">
              <Target className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Departments</div>
              <div className="text-3xl font-bold mt-1">{stats.totalDepartments}</div>
              <p className="text-xs text-muted-foreground">
                Organization departments
              </p>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg shadow-orange-500/20">
              <Activity className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Avg Org Size</div>
              <div className="text-3xl font-bold mt-1">{stats.averageOrgSize}</div>
              <p className="text-xs text-muted-foreground">
                Employees per organization
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-2 flex-1">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search organizations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>

          <Select value={industryFilter} onValueChange={setIndustryFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Industry" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Industries</SelectItem>
              <SelectItem value="technology">Technology</SelectItem>
              <SelectItem value="healthcare">Healthcare</SelectItem>
              <SelectItem value="finance">Finance</SelectItem>
              <SelectItem value="education">Education</SelectItem>
              <SelectItem value="manufacturing">Manufacturing</SelectItem>
              <SelectItem value="retail">Retail</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sizeFilter} onValueChange={setSizeFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Size" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Sizes</SelectItem>
              <SelectItem value="small">Small (1-50)</SelectItem>
              <SelectItem value="medium">Medium (51-200)</SelectItem>
              <SelectItem value="large">Large (200+)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-muted-foreground">
          {filteredOrganizations.length} of {organizations.length} organizations
        </div>
      </div>

      {/* Organizations Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px]">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Organization
                    {getSortIcon('name')}
                  </button>
                </TableHead>
                <TableHead>
                  <button
                    onClick={() => handleSort('industry')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Industry
                    {getSortIcon('industry')}
                  </button>
                </TableHead>
                <TableHead>
                  <button
                    onClick={() => handleSort('size')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Size
                    {getSortIcon('size')}
                  </button>
                </TableHead>
                <TableHead>
                  <button
                    onClick={() => handleSort('employees')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Employees
                    {getSortIcon('employees')}
                  </button>
                </TableHead>
                <TableHead>
                  <button
                    onClick={() => handleSort('created_at')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Created
                    {getSortIcon('created_at')}
                  </button>
                </TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center h-24 text-muted-foreground">
                    Loading organizations...
                  </TableCell>
                </TableRow>
              ) : filteredOrganizations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center h-24">
                    <div className="flex flex-col items-center justify-center py-8">
                      <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Organizations Found</h3>
                      <p className="text-muted-foreground text-center mb-4">
                        {searchQuery || industryFilter !== 'all' || sizeFilter !== 'all'
                          ? 'No organizations match your current filters'
                          : 'No organizations found'}
                      </p>
                      {!searchQuery && industryFilter === 'all' && sizeFilter === 'all' && (
                        <Button onClick={() => setIsCreateDialogOpen(true)}>
                          <Plus className="mr-2 h-4 w-4" />
                          Create First Organization
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredOrganizations.map((organization) => (
                  <TableRow
                    key={organization.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => router.push(`/admin/organizations/${organization.id}`)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-sm font-semibold">
                          {organization.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-medium">{organization.name}</div>
                          {organization.description && (
                            <div className="text-sm text-muted-foreground">
                              {organization.description}
                            </div>
                          )}
                          {organization.website && (
                            <div className="text-xs text-blue-600 hover:underline">
                              <Globe className="h-3 w-3 inline mr-1" />
                              {organization.website.replace(/^https?:\/\//, '')}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {organization.industry ? (
                        <Badge variant="secondary">
                          {organization.industry}
                        </Badge>
                      ) : (
                        <span className="text-sm text-muted-foreground">Not specified</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {organization.size ? (
                        <Badge
                          variant={
                            organization.size === 'Large' ? 'default' :
                            organization.size === 'Medium' ? 'secondary' :
                            'outline'
                          }
                        >
                          {organization.size}
                        </Badge>
                      ) : (
                        <span className="text-sm text-muted-foreground">Not specified</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            (organization._count?.employees || 0) > 100 ? 'default' :
                            (organization._count?.employees || 0) > 50 ? 'secondary' :
                            'outline'
                          }
                        >
                          <Users className="h-3 w-3 mr-1" />
                          {organization._count?.employees || 0}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {organization._count?.departments || 0} depts
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {organization.created_at ? (
                        new Date(organization.created_at).toLocaleDateString()
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Manage Organization</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/admin/organizations/${organization.id}`);
                            }}
                          >
                            <Building2 className="h-4 w-4 mr-2" />
                            <span>View Details</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              openEditDialog(organization);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            <span>Edit Organization</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600 focus:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteOrganization(organization.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            <span>Delete Organization</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Organization Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
            <DialogDescription>
              Update organization information.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Organization Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter organization name"
              />
            </div>
            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Input
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter organization description"
              />
            </div>
            <div>
              <Label htmlFor="edit-website">Website</Label>
              <Input
                id="edit-website"
                value={formData.website}
                onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                placeholder="https://example.com"
              />
            </div>
            <div>
              <Label htmlFor="edit-industry">Industry</Label>
              <Select
                value={formData.industry}
                onValueChange={(value) => setFormData({ ...formData, industry: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No industry specified</SelectItem>
                  <SelectItem value="Technology">Technology</SelectItem>
                  <SelectItem value="Healthcare">Healthcare</SelectItem>
                  <SelectItem value="Finance">Finance</SelectItem>
                  <SelectItem value="Education">Education</SelectItem>
                  <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                  <SelectItem value="Retail">Retail</SelectItem>
                  <SelectItem value="Other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="edit-size">Organization Size</Label>
              <Select
                value={formData.size}
                onValueChange={(value) => setFormData({ ...formData, size: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No size specified</SelectItem>
                  <SelectItem value="Small">Small (1-50 employees)</SelectItem>
                  <SelectItem value="Medium">Medium (51-200 employees)</SelectItem>
                  <SelectItem value="Large">Large (200+ employees)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditOrganization}>Update Organization</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}