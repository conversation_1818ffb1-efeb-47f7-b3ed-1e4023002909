# Luna Project Environment Variables

# Database Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Performance Optimization Flags (DISABLED by default for better performance)
# Only enable these in production if you need detailed monitoring

# Performance Monitoring
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=false
NEXT_PUBLIC_ENABLE_MEMORY_MONITORING=false
NEXT_PUBLIC_ENABLE_PERFORMANCE_REPORTING=false
NEXT_PUBLIC_ENABLE_PERFORMANCE_OBSERVERS=false
NEXT_PUBLIC_ENABLE_PERFORMANCE_SUMMARY=false

# Cache Configuration
NEXT_PUBLIC_ENABLE_CACHE_PERSISTENCE=false

# Database Setup
NEXT_PUBLIC_RUN_MIGRATIONS=false

# Development Settings
NODE_ENV=development
