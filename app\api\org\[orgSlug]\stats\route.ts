import { NextRequest, NextResponse } from 'next/server';
import { createApiClient } from '@/lib/supabase-server';

/**
 * GET /api/org/[orgSlug]/stats
 * Get organization statistics for dashboard
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orgSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug } = params;

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    const organizationId = organization.id;

    // Get total employees (employment relationships)
    const { data: employmentData, error: employmentError } = await supabase
      .from('employment_relationships')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('status', 'active');

    const totalEmployees = employmentData?.length || 0;

    // Get total departments
    const { data: departmentData, error: departmentError } = await supabase
      .from('departments')
      .select('id')
      .eq('organization_id', organizationId);

    const totalDepartments = departmentData?.length || 0;

    // Get active vacancies (job postings)
    const { data: vacancyData, error: vacancyError } = await supabase
      .from('job_postings')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('status', 'published');

    const activeVacancies = vacancyData?.length || 0;

    // Get total candidates (applications for this organization's vacancies)
    let totalCandidates = 0;
    if (vacancyData && vacancyData.length > 0) {
      const vacancyIds = vacancyData.map(v => v.id);
      const { data: applicationData } = await supabase
        .from('applications')
        .select('id')
        .in('job_id', vacancyIds);
      
      totalCandidates = applicationData?.length || 0;
    }

    // Get upcoming interviews
    let upcomingInterviews = 0;
    if (vacancyData && vacancyData.length > 0) {
      const vacancyIds = vacancyData.map(v => v.id);
      
      // Get applications for these vacancies
      const { data: applicationData } = await supabase
        .from('applications')
        .select('id')
        .in('job_id', vacancyIds);

      if (applicationData && applicationData.length > 0) {
        const applicationIds = applicationData.map(a => a.id);
        const now = new Date().toISOString();
        
        const { data: interviewData } = await supabase
          .from('interviews')
          .select('id')
          .in('application_id', applicationIds)
          .eq('status', 'scheduled')
          .gt('scheduled_at', now);
        
        upcomingInterviews = interviewData?.length || 0;
      }
    }

    // Get job roles count
    const { data: jobRoleData, error: jobRoleError } = await supabase
      .from('job_roles')
      .select('id')
      .eq('organization_id', organizationId)
      .eq('status', 'active');

    const jobRoles = jobRoleData?.length || 0;

    const stats = {
      totalEmployees,
      totalDepartments,
      activeVacancies,
      totalCandidates,
      upcomingInterviews,
      jobRoles,
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Error fetching organization stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch organization statistics' },
      { status: 500 }
    );
  }
}
