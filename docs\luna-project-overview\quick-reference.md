# 🚀 Luna Platform: Quick Reference

## 📋 Current Status Summary

**Phase 1: COMPLETE** ✅
**Phase 2: 70% COMPLETE** 🚧
**Database**: Fully implemented employment-based dual-context architecture
**Users**: 12 test users with employment relationships
**Organizations**: 3 seeded organizations with 6 departments
**Authentication**: Working with employment-aware routing
**Deployment**: Production-ready with SSR optimization
**Features**: Training modules, job board, interviews, file management

---

## 🔑 Key Concepts

### **Dual-Context Architecture**
- **Individual Context**: Personal training, career development (default)
- **Organizational Context**: Company training, department work
- **Context Switching**: Seamless transition between modes

### **Employment Model**
- **Multi-Employment**: Users can work for multiple organizations
- **Department-Based**: Organizations have custom departments
- **Role Hierarchy**: Organization Admin → Department Admin → Staff Member

### **User Types**
- **Platform Admin**: System management (`/admin/*`)
- **Individual Users**: Personal accounts (`/user/*`)
- **Organization Members**: Employed users (`/org/[slug]/*`)

---

## 🗄️ Database Quick Reference

### **Core Tables**
| Table | Purpose | Key Features |
|-------|---------|--------------|
| `users` | Universal user table | Authentication, basic profile, employment settings |
| `individuals` | Training profiles | Learning data, skills, career development |
| `organizations` | Multi-tenant orgs | Company info, settings, billing |
| `departments` | Custom departments | Org-specific department structure |
| `employment_relationships` | Multi-employment | User-org-dept relationships with roles |
| `user_contexts` | Context switching | Individual ↔ organizational modes |

### **Key Enums**
```sql
user_role: 'platform_admin' | 'individual'
employment_role: 'organization_admin' | 'department_admin' | 'staff_member'
context_type: 'individual' | 'organization'
```

---

## 🔧 Test Data

### **Organizations**
1. **TechCorp Solutions** (`techcorp`) - Engineering, Product, HR
2. **Creative Agency Inc** (`creative-agency`) - Design, Marketing
3. **StartupHub** (`startuphub`) - Innovation Lab

### **Test Users**
- **Platform Admin**: `<EMAIL>`
- **Organization Admins**: Sarah (TechCorp), Mike (Creative), Alex (StartupHub)
- **Department Admins**: John (Engineering), Emma (Marketing)
- **Staff Members**: Various users across departments
- **Default Password**: `BeeMO5317`

---

## 🚀 Next Phase Priorities

### **Phase 2: Core Features**
1. **API Migration**: ✅ Updated to employment-based architecture
2. **Dashboard UI**: Individual and organizational dashboards
3. **Context Switching**: UI for seamless mode switching
4. **Employment Management**: Invitation system and employment UI
5. **Skills Framework**: Gap analysis and competency tracking

### **Completed Tasks**
- [x] Updated `/api/admin/organization-memberships` to use employment relationships
- [x] Created `/api/admin/departments` API endpoints
- [x] Removed legacy BPO references from codebase
- [x] Updated query optimizer for employment-based architecture
- [ ] Implement context switching UI components
- [ ] Build employment invitation system

---

## 🔗 Documentation Links

- **[Project Overview](./project-overview.md)** - High-level vision and status
- **[Current Architecture](./current-architecture.md)** - Detailed technical architecture
- **[Plan of Action](./plan-of-action-chat.md)** - Complete implementation guide
- **[Phase 1 Schema](../schemas/phase-1-schema.md)** - Original schema reference
- **[Employment Schema](../schemas/employment-based-schema-changes.md)** - Current schema design

---

## 🛠️ Development Environment

### **Database**
- **Project**: `luna database` (Supabase)
- **Reference ID**: `hzrzqyuwxsvmxhrslixm`
- **URL**: `https://hzrzqyuwxsvmxhrslixm.supabase.co`

### **Application**
- **Framework**: Next.js 15 with App Router
- **Port**: `http://localhost:3001`
- **Authentication**: Supabase Auth with custom user management

### **Key Files**
- **Auth**: `lib/auth.ts`, `lib/supabase.ts`
- **API**: `app/api/` (needs employment model updates)
- **Migrations**: `supabase/migrations/`
- **Schemas**: `docs/schemas/`

---

## 📞 Quick Commands

### **Database**
```bash
# Connect to Supabase
supabase link --project-ref hzrzqyuwxsvmxhrslixm

# Push migrations
supabase db push --linked

# Reset database (if needed)
supabase db reset --linked
```

### **Development**
```bash
# Start development server
npm run dev

# Check database status
supabase status

# View logs
supabase logs
```

---

*Last Updated: Phase 1 Complete - Employment-based dual-context architecture implemented*
