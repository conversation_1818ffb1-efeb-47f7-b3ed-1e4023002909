'use client';

import React from 'react';
import { AssessmentQuestion } from '@/types/assessment';
import { MultipleChoice } from './multiple-choice';
import { LongAnswer } from './long-answer';
import { TypingTest, TypingTestStats } from './typing-test';

interface QuestionRendererProps {
  question: AssessmentQuestion;
  onAnswer: (answer: string, additionalData?: any) => void;
}

export function QuestionRenderer({ question, onAnswer }: QuestionRendererProps) {
  switch (question.question_type) {
    case 'multiple_choice':
      return (
        <MultipleChoice 
          question={question} 
          onComplete={(answer) => onAnswer(answer)} 
        />
      );
    
    case 'text_input':
      return (
        <LongAnswer 
          question={question} 
          onComplete={(answer) => onAnswer(answer)} 
          timeLimit={question.time_limit || 600} 
        />
      );
    
    case 'typing_test':
      return (
        <TypingTest 
          question={question} 
          onComplete={(answer, stats: TypingTestStats) => onAnswer(answer, stats)} 
          timeLimit={question.time_limit || 300} 
        />
      );
    
    default:
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600">Unknown question type: {question.question_type}</p>
        </div>
      );
  }
} 