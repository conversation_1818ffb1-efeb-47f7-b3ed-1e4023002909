"use client"

import React, { useState, useEffect } from 'react'
import { VideoPlayer } from './video-player'
import { PDFViewer } from './pdf-viewer'
import { PresentationViewer } from './presentation-viewer'
import { QuizInterface } from './quiz-interface'
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { AlertCircle, Loader2 } from 'lucide-react'
import { toast } from "sonner"

interface LessonData {
  id: string
  name: string
  description?: string
  lesson_type: 'video' | 'text' | 'presentation' | 'quiz'
  content_url?: string
  content_file_url?: string
  content_source: 'url' | 'upload'
  estimated_duration?: number
  
  // Video-specific fields
  video_duration?: number
  transcript?: string
  
  // Text-specific fields
  reading_time?: number
  
  // Presentation-specific fields
  slide_count?: number
  
  // Quiz-specific fields
  passing_score?: number
  attempts_allowed?: number
  time_limit?: number
  content_data?: {
    quiz_config?: {
      lesson_id: string
      generation_method: string
      question_count: number
      question_types: string[]
      learning_objectives: string
      instructions: string
      passing_score: number
      attempts_allowed: number
      time_limit?: number
    }
  }
}

interface LessonContentViewerProps {
  lesson: LessonData
  onProgress?: (progress: any) => void
  onComplete?: () => void
  onQuizSubmit?: (answers: Record<string, any>, timeSpent: number) => void
}

export function LessonContentViewer({
  lesson,
  onProgress,
  onComplete,
  onQuizSubmit
}: LessonContentViewerProps) {
  const [quizConfig, setQuizConfig] = useState<any>(null)
  const [loadingQuiz, setLoadingQuiz] = useState(false)
  const [generatingQuiz, setGeneratingQuiz] = useState(false)

  const contentUrl = lesson.content_file_url || lesson.content_url

  // Load existing quiz if lesson type is quiz
  useEffect(() => {
    if (lesson.lesson_type === 'quiz') {
      loadExistingQuiz()
    }
  }, [lesson.id, lesson.lesson_type])

  const loadExistingQuiz = async () => {
    try {
      setLoadingQuiz(true)
      const response = await fetch(`/api/lessons/${lesson.id}/generate-quiz`)

      if (response.ok) {
        const data = await response.json()
        setQuizConfig(data.data.quiz_config)
      } else if (response.status === 404) {
        // No quiz generated yet, that's okay
        setQuizConfig(null)
      } else {
        console.error('Failed to load quiz')
      }
    } catch (error) {
      console.error('Error loading quiz:', error)
    } finally {
      setLoadingQuiz(false)
    }
  }

  const generateQuiz = async () => {
    try {
      setGeneratingQuiz(true)
      const quizConfigData = lesson.content_data?.quiz_config

      const response = await fetch(`/api/lessons/${lesson.id}/generate-quiz`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question_count: quizConfigData?.question_count || 10,
          question_types: quizConfigData?.question_types || ['multiple_choice'],
          learning_objectives: quizConfigData?.learning_objectives || '',
          difficulty_level: 'medium'
        }),
      })

      if (response.ok) {
        const data = await response.json()
        toast.success('Quiz generated successfully!')
        await loadExistingQuiz() // Reload the quiz
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'Failed to generate quiz')
      }
    } catch (error) {
      console.error('Error generating quiz:', error)
      toast.error('Failed to generate quiz')
    } finally {
      setGeneratingQuiz(false)
    }
  }

  const handleQuizSubmit = async (answers: Record<string, any>, timeSpent: number) => {
    try {
      const response = await fetch(`/api/lessons/${lesson.id}/submit-quiz`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          generation_id: quizConfig?.generation_id,
          answers,
          time_spent: timeSpent
        }),
      })

      if (response.ok) {
        const data = await response.json()
        toast.success(`Quiz completed! Score: ${data.data.score}%`)
        onQuizSubmit?.(answers, timeSpent)
        if (data.data.passed) {
          onComplete?.()
        }
      } else {
        const errorData = await response.json()
        toast.error(errorData.error || 'Failed to submit quiz')
      }
    } catch (error) {
      console.error('Error submitting quiz:', error)
      toast.error('Failed to submit quiz')
    }
  }

  if (!contentUrl && lesson.lesson_type !== 'quiz') {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-semibold mb-2">No Content Available</h3>
          <p className="text-muted-foreground">
            This lesson doesn't have any content configured yet.
          </p>
        </CardContent>
      </Card>
    )
  }

  const renderContent = () => {
    switch (lesson.lesson_type) {
      case 'video':
        return (
          <VideoPlayer
            url={contentUrl!}
            title={lesson.name}
            transcript={lesson.transcript}
            onProgress={onProgress}
            onEnded={onComplete}
            controls={true}
          />
        )

      case 'text':
        return (
          <PDFViewer
            url={contentUrl!}
            title={lesson.name}
            onProgress={onProgress}
            onComplete={onComplete}
          />
        )

      case 'presentation':
        return (
          <PresentationViewer
            url={contentUrl!}
            title={lesson.name}
            slideCount={lesson.slide_count}
            onProgress={onProgress}
            onComplete={onComplete}
            autoPlay={false}
            slideInterval={5}
          />
        )

      case 'quiz':
        if (loadingQuiz) {
          return (
            <Card>
              <CardContent className="p-8 text-center">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                <p>Loading quiz...</p>
              </CardContent>
            </Card>
          )
        }

        if (!quizConfig) {
          const quizConfigData = lesson.content_data?.quiz_config

          if (!quizConfigData) {
            return (
              <Card>
                <CardContent className="p-8 text-center">
                  <AlertCircle className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Quiz Not Configured</h3>
                  <p className="text-muted-foreground mb-4">
                    This quiz hasn't been set up yet. Please contact your instructor.
                  </p>
                </CardContent>
              </Card>
            )
          }

          return (
            <Card>
              <CardContent className="p-8 text-center">
                <AlertCircle className="h-16 w-16 mx-auto mb-4 text-blue-500" />
                <h3 className="text-lg font-semibold mb-2">Generate Your Quiz</h3>
                <p className="text-muted-foreground mb-4">
                  This quiz will be uniquely generated for you with {quizConfigData.question_count} questions.
                </p>
                <Button
                  onClick={generateQuiz}
                  disabled={generatingQuiz}
                  size="lg"
                >
                  {generatingQuiz ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Generating Quiz...
                    </>
                  ) : (
                    'Generate Quiz'
                  )}
                </Button>
              </CardContent>
            </Card>
          )
        }

        return (
          <QuizInterface
            config={quizConfig}
            onSubmit={handleQuizSubmit}
            onProgress={onProgress}
            autoSave={true}
          />
        )

      default:
        return (
          <Card>
            <CardContent className="p-8 text-center">
              <AlertCircle className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">Unsupported Content Type</h3>
              <p className="text-muted-foreground">
                This lesson type ({lesson.lesson_type}) is not supported yet.
              </p>
            </CardContent>
          </Card>
        )
    }
  }

  return (
    <div className="space-y-6">
      {/* Lesson Header */}
      <div className="space-y-2">
        <h1 className="text-2xl font-bold">{lesson.name}</h1>
        {lesson.description && (
          <p className="text-muted-foreground">{lesson.description}</p>
        )}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span className="capitalize">{lesson.lesson_type} Lesson</span>
          {lesson.estimated_duration && (
            <span>{lesson.estimated_duration} minutes</span>
          )}
          {lesson.lesson_type === 'video' && lesson.video_duration && (
            <span>Video: {lesson.video_duration} minutes</span>
          )}
          {lesson.lesson_type === 'text' && lesson.reading_time && (
            <span>Reading: {lesson.reading_time} minutes</span>
          )}
          {lesson.lesson_type === 'presentation' && lesson.slide_count && (
            <span>{lesson.slide_count} slides</span>
          )}
        </div>
      </div>

      {/* Lesson Content */}
      {renderContent()}
    </div>
  )
}
