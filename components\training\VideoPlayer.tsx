"use client";

import { useState, useRef, useEffect } from "react";
import { Play, Pause, Volume2, VolumeX, Maximize, SkipForward, SkipBack, Loader2 } from "lucide-react";
import { Slider } from "@/components/ui/slider";
import { cn } from "@/lib/utils";

interface VideoPlayerProps {
  src: string;
  title?: string;
  onComplete?: () => void;
  className?: string;
  thumbnailUrl?: string; // Optional thumbnail URL
}

export function VideoPlayer({ src, title, onComplete, className, thumbnailUrl }: VideoPlayerProps) {
  const [playing, setPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [volume, setVolume] = useState(1);
  const [muted, setMuted] = useState(false);
  const [controlsVisible, setControlsVisible] = useState(true);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [loading, setLoading] = useState(true);
  const [watched, setWatched] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [videoStarted, setVideoStarted] = useState(false);
  const [thumbnailLoaded, setThumbnailLoaded] = useState(false);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<HTMLDivElement>(null);
  const controlsTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Format time in MM:SS format
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };
  
  // Handle video progress updates
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const currentTime = videoRef.current.currentTime;
      const duration = videoRef.current.duration;
      const progressPercent = (currentTime / duration) * 100;
      
      setCurrentTime(currentTime);
      setProgress(progressPercent);
      
      // Mark as watched when 90% complete
      if (progressPercent >= 90 && !watched) {
        setWatched(true);
        onComplete?.();
      }
    }
  };

  // Initialize video duration when metadata is loaded
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setLoading(false);
    }
  };

  // Handle video loading error
  const handleError = () => {
    setLoading(false);
    
    // Get specific error information if available
    let errorMessage = 'Failed to load video. The file might be corrupted or in an unsupported format.';
    
    if (videoRef.current?.error) {
      const videoError = videoRef.current.error;
      console.error('Video error details:', {
        code: videoError.code,
        message: videoError.message,
        MEDIA_ERR_ABORTED: 1,
        MEDIA_ERR_NETWORK: 2,
        MEDIA_ERR_DECODE: 3,
        MEDIA_ERR_SRC_NOT_SUPPORTED: 4
      });
      
      // Provide more specific error messages based on error code
      switch (videoError.code) {
        case 1:
          errorMessage = 'Video playback was aborted.';
          break;
        case 2:
          errorMessage = 'A network error occurred while loading the video.';
          break;
        case 3:
          errorMessage = 'The video is corrupted or in a format that cannot be played.';
          break;
        case 4:
          errorMessage = 'No supported video format found. The video format or MIME type is not supported.';
          break;
      }
    }
    
    setError(errorMessage);
  };
  
  // Toggle play/pause
  const togglePlay = () => {
    if (videoRef.current && !error) {
      if (playing) {
        videoRef.current.pause();
      } else {
        videoRef.current.play().catch(err => {
          console.error('Play error:', err);
          setError('Could not play video. The file might be corrupted or in an unsupported format.');
        });
      }
      setPlaying(!playing);
      
      if (!videoStarted) {
        setVideoStarted(true);
      }
    }
  };
  
  // Handle screen click - both to start and to pause/resume
  const handleScreenClick = () => {
    if (!isYouTube && !error) {
      togglePlay();
    }
  };
  
  // Handle seeking when slider is moved
  const handleSeek = (value: number[]) => {
    if (videoRef.current) {
      const newTime = (value[0] / 100) * duration;
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
      setProgress(value[0]);
    }
  };
  
  // Handle volume change
  const handleVolumeChange = (value: number[]) => {
    if (videoRef.current) {
      const newVolume = value[0] / 100;
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setMuted(newVolume === 0);
    }
  };
  
  // Toggle mute
  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !muted;
      setMuted(!muted);
    }
  };
  
  // Handle fullscreen toggle
  const toggleFullscreen = () => {
    if (playerRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        playerRef.current.requestFullscreen();
      }
    }
  };
  
  // Skip forward/backward
  const skip = (seconds: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime += seconds;
    }
  };
  
  // Hide controls after inactivity
  const showControls = () => {
    setControlsVisible(true);
    
    // Clear any existing timer
    if (controlsTimerRef.current) {
      clearTimeout(controlsTimerRef.current);
    }
    
    // Set a new timer to hide controls after 3 seconds of inactivity
    controlsTimerRef.current = setTimeout(() => {
      if (playing) {
        setControlsVisible(false);
      }
    }, 3000);
  };
  
  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (controlsTimerRef.current) {
        clearTimeout(controlsTimerRef.current);
      }
    };
  }, []);
  
  // Show controls when paused
  useEffect(() => {
    if (!playing) {
      setControlsVisible(true);
      if (controlsTimerRef.current) {
        clearTimeout(controlsTimerRef.current);
      }
    } else {
      showControls();
    }
  }, [playing]);
  
  // Reset error state when src changes
  useEffect(() => {
    setError(null);
    setLoading(true);
    setVideoStarted(false);
    
    // Make sure we have a valid source
    if (!src || src.trim() === '') {
      setError('No video source provided.');
      setLoading(false);
    }
  }, [src]);

  // Determine if the source is from YouTube
  const isYouTubeUrl = (url: string) => {
    return url.match(/^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)/i) !== null;
  };
  
  // Extract YouTube video ID from URL
  const getYouTubeVideoId = (url: string) => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    
    return (match && match[2].length === 11) ? match[2] : null;
  };

  // Determine source type
  const getSourceType = (url: string) => {
    if (!url) return null;
    
    const fileExtension = url.split('.').pop()?.toLowerCase();
    
    switch (fileExtension) {
      case 'mp4':
        return 'video/mp4';
      case 'webm':
        return 'video/webm';
      case 'ogg':
        return 'video/ogg';
      case 'mov':
        return 'video/quicktime';
      default:
        return null; // Let browser detect
    }
  };

  // Generate thumbnail URL
  const getThumbnailUrl = () => {
    if (thumbnailUrl) return thumbnailUrl;
    
    // Check if YouTube video and generate thumbnail
    if (isYouTube && youtubeVideoId) {
      return `https://img.youtube.com/vi/${youtubeVideoId}/hqdefault.jpg`;
    }
    
    // Default thumbnail
    return null;
  };

  // Check if this is a YouTube video
  const isYouTube = src ? isYouTubeUrl(src) : false;
  const youtubeVideoId = isYouTube ? getYouTubeVideoId(src) : null;
  const videoThumbnail = getThumbnailUrl();
  const hasThumbnail = Boolean(videoThumbnail);
  
  return (
    <div 
      ref={playerRef}
      className={cn(
        "relative group rounded-lg overflow-hidden bg-black aspect-video", 
        className
      )}
      onMouseMove={!isYouTube ? showControls : undefined}
      onClick={handleScreenClick}
    >
      {/* Thumbnail overlay (shown before video starts) */}
      {!videoStarted && !isYouTube && videoThumbnail && (
        <div className="absolute inset-0 z-10 bg-black">
          <img 
            src={videoThumbnail}
            alt={title || "Video thumbnail"}
            className="w-full h-full object-cover opacity-80"
            onLoad={() => setThumbnailLoaded(true)}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
          
          {/* Center play button with text per wireframe */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="flex flex-col items-center">
              <button 
                onClick={(e) => { e.stopPropagation(); togglePlay(); }}
                className="flex items-center justify-center w-24 h-24 rounded-full bg-primary/90 text-white shadow-lg hover:bg-primary transition-all transform hover:scale-110 mb-4"
                aria-label="Play video"
              >
                <Play className="h-12 w-12" fill="white" />
              </button>
              <p className="text-white text-lg font-medium">Click to Start Training Video</p>
            </div>
          </div>
          
          {/* Lesson title on right side per wireframe */}
          {title && (
            <div className="absolute top-1/2 right-8 transform -translate-y-1/2 text-right">
              <div className="bg-black/50 backdrop-blur-sm p-4 rounded-lg">
                <h3 className="text-2xl font-semibold text-white mb-1">Lesson</h3>
                <h4 className="text-xl font-medium text-white/90">{title}</h4>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Default Play Button (when no thumbnail is available) */}
      {!videoStarted && !isYouTube && !videoThumbnail && !loading && !error && (
        <div className="absolute inset-0 z-10 bg-gradient-to-br from-gray-900 to-black/90">
          {/* Center play button with text per wireframe */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="flex flex-col items-center">
              <button 
                onClick={(e) => { e.stopPropagation(); togglePlay(); }}
                className="flex items-center justify-center w-24 h-24 rounded-full bg-primary/90 text-white shadow-lg hover:bg-primary transition-all transform hover:scale-110 mb-4"
                aria-label="Play video"
              >
                <Play className="h-12 w-12" fill="white" />
              </button>
              <p className="text-white text-lg font-medium">Click to Start Training Video</p>
            </div>
          </div>
          
          {/* Lesson title on right side per wireframe */}
          {title && (
            <div className="absolute top-1/2 right-8 transform -translate-y-1/2 text-right">
              <div className="bg-black/50 backdrop-blur-sm p-4 rounded-lg">
                <h3 className="text-2xl font-semibold text-white mb-1">Lesson</h3>
                <h4 className="text-xl font-medium text-white/90">{title}</h4>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Loading indicator */}
      {loading && !error && !isYouTube && (
        <div className="absolute inset-0 flex flex-col items-center justify-center z-10 bg-black/80 backdrop-blur-sm">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-white/90 font-medium">Loading video...</p>
        </div>
      )}
      
      {error ? (
        <div className="absolute inset-0 flex flex-col items-center justify-center z-10 bg-black/90 text-white p-6 text-center">
          <div className="rounded-full bg-red-500/10 p-4 mb-4">
            <div className="text-red-500 h-8 w-8" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Video Error</h3>
          <p className="max-w-md">{error}</p>
        </div>
      ) : isYouTube && youtubeVideoId ? (
        // YouTube Embed Player
        <iframe
          className="w-full h-full"
          src={`https://www.youtube-nocookie.com/embed/${youtubeVideoId}?autoplay=0&rel=0&modestbranding=1&enablejsapi=1`}
          title={title || "YouTube video player"}
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          onLoad={() => setLoading(false)}
        ></iframe>
      ) : (
        // Native Video Player
      <video
        ref={videoRef}
        className="w-full h-full object-contain"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onPlay={() => setPlaying(true)}
        onPause={() => setPlaying(false)}
        onEnded={() => setPlaying(false)}
          onError={handleError}
        onClick={(e) => e.stopPropagation()}
          playsInline
          preload="auto"
        >
          {/* Add source elements for better browser compatibility */}
          {src && (
            <>
              <source src={src} type={getSourceType(src) || undefined} />
              {/* Add fallback message for unsupported browsers */}
              Your browser does not support HTML5 video.
            </>
          )}
        </video>
      )}
      
      {/* Play button overlay (visible when paused but video has started) */}
      {!loading && !playing && !error && !isYouTube && videoStarted && (
        <div 
          className="absolute inset-0 flex items-center justify-center z-10 cursor-pointer bg-black/20"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="rounded-full bg-primary/90 p-5 backdrop-blur-sm transition-transform hover:scale-110 shadow-xl">
            <Play className="h-10 w-10 text-white" fill="white" />
          </div>
        </div>
      )}
      
      {/* Video title */}
      {title && controlsVisible && !error && !isYouTube && videoStarted && (
        <div className="absolute top-0 left-0 right-0 p-4 bg-gradient-to-b from-black/80 to-transparent text-white font-medium z-20">
          {title}
        </div>
      )}
      
      {/* Controls - only show for non-YouTube videos */}
      {!error && !isYouTube && (videoStarted || (!videoStarted && !hasThumbnail)) && (
      <div 
        className={cn(
          "absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent px-4 pb-3 pt-16 transition-all duration-300 z-20",
          controlsVisible ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={(e) => e.stopPropagation()}
      >
          {/* Progress bar - without the blue circle */}
        <div className="relative group/progress">
          <Slider
            value={[progress]}
            min={0}
            max={100}
            step={0.1}
            onValueChange={handleSeek}
              className="mt-2 mb-3 cursor-pointer h-1.5 [&>span]:h-1.5 [&>span>span]:bg-blue-500 [&>span>span>span]:opacity-0 [&>span>span>span]:h-0 [&>span>span>span]:w-0"
          />
          <div className="absolute -top-8 left-0 transform -translate-x-1/2 bg-black/80 text-white px-2 py-1 rounded text-xs opacity-0 group-hover/progress:opacity-100 transition-opacity pointer-events-none" style={{ left: `${progress}%` }}>
            {formatTime(currentTime)}
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {/* Play/Pause button */}
            <button 
              className="text-white hover:text-blue-400 transition-colors focus:outline-none" 
                onClick={(e) => { e.stopPropagation(); togglePlay(); }}
            >
              {playing ? 
                <Pause className="h-5 w-5" /> : 
                <Play className="h-5 w-5" />
              }
            </button>
            
            {/* Skip backward button */}
            <button 
              className="text-white hover:text-blue-400 transition-colors focus:outline-none" 
                onClick={(e) => { e.stopPropagation(); skip(-10); }}
            >
              <SkipBack className="h-4 w-4" />
            </button>
            
            {/* Skip forward button */}
            <button 
              className="text-white hover:text-blue-400 transition-colors focus:outline-none" 
                onClick={(e) => { e.stopPropagation(); skip(10); }}
            >
              <SkipForward className="h-4 w-4" />
            </button>
            
            {/* Volume control */}
            <div className="relative flex items-center">
              <button 
                className="text-white hover:text-blue-400 transition-colors focus:outline-none"
                  onClick={(e) => { e.stopPropagation(); toggleMute(); }}
                onMouseEnter={() => setShowVolumeSlider(true)}
              >
                {muted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
              </button>
              
              {showVolumeSlider && (
                <div 
                  className="absolute left-6 bottom-0 w-24 h-10 bg-black/90 rounded p-2 flex items-center"
                  onMouseEnter={() => setShowVolumeSlider(true)}
                  onMouseLeave={() => setShowVolumeSlider(false)}
                >
                  <Slider
                    value={[muted ? 0 : volume * 100]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={handleVolumeChange}
                      className="[&>span>span]:bg-blue-500 [&>span>span>span]:opacity-0 [&>span>span>span]:h-0 [&>span>span>span]:w-0"
                  />
                </div>
              )}
            </div>
            
            {/* Time display */}
            <div className="text-white text-xs ml-2">
              {formatTime(currentTime)} / {formatTime(duration)}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Fullscreen button */}
            <button 
              className="text-white hover:text-blue-400 transition-colors focus:outline-none" 
                onClick={(e) => { e.stopPropagation(); toggleFullscreen(); }}
            >
              <Maximize className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
      )}
    </div>
  );
} 