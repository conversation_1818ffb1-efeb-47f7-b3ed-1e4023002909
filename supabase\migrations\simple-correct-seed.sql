-- Luna Employment Seed Data - SIMPLE AND CORRECT
-- Based on actual table structure

-- Step 1: Set user roles
UPDATE users SET role = 'platform_admin' WHERE email = '<EMAIL>';
UPDATE users SET role = 'individual' WHERE email != '<EMAIL>';

-- Step 2: Insert Organizations
INSERT INTO organizations (
  id, name, slug, description, industry, size_range, 
  subscription_tier, subdomain, website_url, status
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  'TechCorp Solutions',
  'techcorp',
  'Leading technology solutions provider',
  'Technology',
  'large',
  'enterprise',
  'techcorp',
  'https://techcorp.com',
  'active'
),
(
  '*************-2222-2222-************',
  'Creative Agency Inc',
  'creative-agency',
  'Full-service creative and marketing agency',
  'Marketing',
  'medium',
  'professional',
  'creative',
  'https://creativeagency.com',
  'active'
),
(
  '*************-3333-3333-************',
  'StartupHub',
  'startuphub',
  'Innovation and startup incubator',
  'Consulting',
  'small',
  'basic',
  'startup',
  'https://startuphub.com',
  'active'
);

-- Step 3: Insert Departments
INSERT INTO departments (
  id, organization_id, name, description
) VALUES 
(
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111',
  'Engineering',
  'Software development and technical operations'
),
(
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '11111111-1111-1111-1111-111111111111',
  'Product Management',
  'Product strategy and roadmap planning'
),
(
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  '11111111-1111-1111-1111-111111111111',
  'Human Resources',
  'People operations and talent management'
),
(
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  '*************-2222-2222-************',
  'Design',
  'Creative design and visual branding'
),
(
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  '*************-2222-2222-************',
  'Marketing',
  'Digital marketing and campaign management'
),
(
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  '*************-3333-3333-************',
  'Innovation Lab',
  'Research and development initiatives'
);

-- Step 4: Create Employment Relationships for existing users only
-- Get the first available user for TechCorp Organization Admin
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid,
  'organization_admin',
  'active',
  'Chief Technology Officer',
  CURRENT_DATE - INTERVAL '1 year',
  NOW()
FROM users u 
WHERE u.email != '<EMAIL>'
ORDER BY u.created_at
LIMIT 1;

-- Get the second available user for Creative Agency Organization Admin
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  '*************-2222-2222-************'::uuid,
  'dddddddd-dddd-dddd-dddd-dddddddddddd'::uuid,
  'organization_admin',
  'active',
  'Creative Director',
  CURRENT_DATE - INTERVAL '2 years',
  NOW()
FROM users u 
WHERE u.email != '<EMAIL>'
AND u.id NOT IN (SELECT user_id FROM employment_relationships)
ORDER BY u.created_at
LIMIT 1;

-- Get the third available user for StartupHub Organization Admin
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  '*************-3333-3333-************'::uuid,
  'ffffffff-ffff-ffff-ffff-ffffffffffff'::uuid,
  'organization_admin',
  'active',
  'Innovation Director',
  CURRENT_DATE - INTERVAL '6 months',
  NOW()
FROM users u 
WHERE u.email != '<EMAIL>'
AND u.id NOT IN (SELECT user_id FROM employment_relationships)
ORDER BY u.created_at
LIMIT 1;

-- Assign remaining users as department admins and staff members
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status, 
  job_title, hire_date, joined_at
)
SELECT 
  u.id,
  CASE 
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 1 THEN '11111111-1111-1111-1111-111111111111'::uuid
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 2 THEN '*************-2222-2222-************'::uuid
    ELSE '*************-3333-3333-************'::uuid
  END,
  CASE 
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 1 THEN 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) % 3 = 2 THEN 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'::uuid
    ELSE 'ffffffff-ffff-ffff-ffff-ffffffffffff'::uuid
  END,
  CASE
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) <= 2 THEN 'department_admin'::employment_role
    ELSE 'staff_member'::employment_role
  END,
  'active',
  CASE 
    WHEN ROW_NUMBER() OVER (ORDER BY u.created_at) <= 2 THEN 'Department Manager'
    ELSE 'Team Member'
  END,
  CURRENT_DATE - INTERVAL '3 months',
  NOW()
FROM users u 
WHERE u.email != '<EMAIL>'
AND u.id NOT IN (SELECT user_id FROM employment_relationships);

-- Step 5: Set Department Heads
UPDATE departments SET department_head_id = (
  SELECT user_id FROM employment_relationships 
  WHERE department_id = departments.id
  AND role IN ('department_admin', 'organization_admin')
  LIMIT 1
);

-- Step 6: Create User Contexts using ACTUAL column names
INSERT INTO user_contexts (
  user_id, active_context, active_organization_id, 
  active_department_id, active_employment_id
) 
SELECT 
  u.id,
  'individual', -- Everyone starts in individual context
  NULL,
  NULL,
  NULL
FROM users u
WHERE u.email != '<EMAIL>'
ON CONFLICT (user_id) DO UPDATE SET
  active_context = 'individual';

-- Step 7: Show what we created
SELECT 'Organizations Created:' as summary, COUNT(*) as count FROM organizations
UNION ALL
SELECT 'Departments Created:', COUNT(*) FROM departments
UNION ALL
SELECT 'Employment Relationships:', COUNT(*) FROM employment_relationships
UNION ALL
SELECT 'User Contexts Created:', COUNT(*) FROM user_contexts;
