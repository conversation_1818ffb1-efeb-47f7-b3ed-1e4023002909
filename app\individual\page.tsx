import React from "react"
import { DashboardOverview } from "@/components/dashboard/dashboard-overview"
import { HeroSection } from "@/components/hero-section"
import { CourseCarousel } from "@/components/course-carousel"
import { createServerClient, createAdminClient } from "@/lib/supabase-server"
import { redirect } from "next/navigation"
import { Metadata } from "next"
import { Database } from "@/types/database.types"

type UserRow = Database['public']['Tables']['users']['Row']

// Force dynamic rendering for this page due to authentication requirements
export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: "Dashboard | Luna Skills Platform",
  description: "Your personal dashboard with training progress, upcoming assessments, and skill development insights",
}

// Move interface outside of function to fix linting
interface ModuleData {
  id: string
  title: string
  description: string
  currentLesson: string
  nextLessonId?: string
  progress: number
  duration: number
  color: string
}

export default async function IndividualDashboard(): Promise<React.JSX.Element> {
  // Get the authenticated user using server-side auth check
  const supabase = await createServerClient()

  const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()

  if (authError || !authUser) {
    redirect('/login')
  }

  // Get user data from database - using type assertion for Supabase strict typing
  const { data: userData, error: userError } = await (supabase as any)
    .from('users')
    .select('*')
    .eq('id', authUser.id)
    .single()

  if (userError || !userData) {
    redirect('/login')
  }

  // Type assertion for user data (safe because we checked for errors above)
  const user = userData as unknown as UserRow

  // Allow individual users and platform admins to access this page
  if (user.role !== 'individual' && user.role !== 'platform_admin') {
    redirect('/unauthorized')
  }

  // Extract first name from full name with proper null checks
  const firstName = user.full_name
    ? user.full_name.split(' ')[0]
    : (user.email ? user.email.split('@')[0] : "User")

  // Get the individual's profile using proper RLS (no admin client needed!)
  let individualData = null

  try {
    const { data, error } = await (supabase as any)
      .from("individuals")
      .select("id, learning_status")
      .eq("user_id", user.id)
      .single()

    if (error) {
      console.log("Individual profile not found for user:", user.id, "- This is normal for users without individual profiles")
      // Create a default individual record for users without profiles
      individualData = { id: user.id, learning_status: 'not_started' }
    } else {
      individualData = data
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error("Error fetching individual data:", errorMessage)
    // For now, create a mock individual record since table doesn't exist yet
    individualData = { id: user.id, learning_status: 'not_started' }
  }

  const individualId = (individualData as any)?.id

  // Fetch training statistics with optimized queries
  let trainingStats = {
    totalModules: 0,
    completedModules: 0,
    inProgressModules: 0,
    progressPercentage: 0,
    badges: 0,
    callPracticeHours: 0,
    callPracticeScore: 0,
    completedAssessments: 0,
    totalAssessments: 0
  }

  // Fetch additional data for notifications
  let notificationData = {
    newJobMatches: 0,
    recentBadges: 0,
    upcomingInterviews: 0
  }

  if (individualId) {
    try {
      // For now, use mock data since training tables don't exist yet
      // TODO: Replace with actual queries once training tables are created
      const modules: any[] = []
      const badges: any[] = []

      trainingStats.totalModules = modules.length
      trainingStats.badges = badges.length

      // For now, set default values since we don't have training data yet
      trainingStats.completedModules = 0
      trainingStats.inProgressModules = 0
      trainingStats.progressPercentage = 0

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error("Error fetching training statistics:", errorMessage)
    }
  }

  // Get top 3 in-progress modules (OPTIMIZED)
  let inProgressModules: ModuleData[] = []

  try {
    if (individualId) {
      // For now, use mock data since training tables don't exist yet
      // TODO: Replace with actual queries once training tables are created

      // Simple fallback for now
      inProgressModules = [{
        id: "intro-1",
        title: "Getting Started with Luna",
        description: "Begin your learning journey with Luna platform",
        currentLesson: "Introduction",
        nextLessonId: undefined,
        progress: 0,
        duration: 2,
        color: "blue"
      }]
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error("Error fetching in-progress modules:", errorMessage)

    // Simple fallback
    inProgressModules = [{
      id: "fallback-1",
      title: "Getting Started",
      description: "Begin your learning journey",
      currentLesson: "Introduction",
      nextLessonId: undefined,
      progress: 0,
      duration: 2,
      color: "blue"
    }]
  }

  // Prepare dashboard data
  const dashboardData = {
    userInfo: {
      name: firstName,
      email: user.email || "",
      avatar_url: user.avatar_url || null
    },
    trainingStats,
    inProgressModules,
    notificationData
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <HeroSection />

      {/* Course Carousel Section */}
      <CourseCarousel />

      {/* Dashboard Content */}
      <div className="px-[50px] pt-8 pb-6 bg-white">
        <DashboardOverview data={dashboardData} />
      </div>
    </div>
  )
}