import { NextRequest } from 'next/server';
import { requirePlatformAdmin } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-server';
import {
  createApiSuccessResponse,
  handleApiError,
  withApi<PERSON>rror<PERSON><PERSON><PERSON>
} from '@/lib/api-error-handler';

export async function GET(req: NextRequest) {
  return withApiErrorHandler(async () => {
    // Authenticate user and require admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      throw new Error('Platform admin access required');
    }

    // Create admin client that bypasses RLS
    const adminClient = createAdminClient();

    // Get total users count
    const { count: totalUsers, error: usersError } = await adminClient
      .from('users')
      .select('*', { count: 'exact', head: true });

    if (usersError) {
      throw usersError; // Will be handled by handleSupabaseApiError
    }

    // Get total organizations count
    const { count: totalOrganizations, error: orgsError } = await adminClient
      .from('organizations')
      .select('*', { count: 'exact', head: true });

    if (orgsError) {
      throw orgsError;
    }

    // Get total individuals count
    const { count: totalIndividuals, error: individualsError } = await adminClient
      .from('individuals')
      .select('*', { count: 'exact', head: true });

    if (individualsError) {
      throw individualsError;
    }

    // Get total departments count
    const { count: totalDepartments, error: departmentsError } = await adminClient
      .from('departments')
      .select('*', { count: 'exact', head: true });

    if (departmentsError) {
      throw departmentsError;
    }

    // Get total employment relationships count
    const { count: totalEmployments, error: employmentsError } = await adminClient
      .from('employment_relationships')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    if (employmentsError) {
      throw employmentsError;
    }

    // Get employment role breakdown
    const { count: organizationAdmins, error: orgAdminsError } = await adminClient
      .from('employment_relationships')
      .select('*', { count: 'exact', head: true })
      .eq('role', 'organization_admin')
      .eq('status', 'active');

    if (orgAdminsError) {
      throw orgAdminsError;
    }

    const { count: departmentAdmins, error: deptAdminsError } = await adminClient
      .from('employment_relationships')
      .select('*', { count: 'exact', head: true })
      .eq('role', 'department_admin')
      .eq('status', 'active');

    if (deptAdminsError) {
      throw deptAdminsError;
    }

    const { count: staffMembers, error: staffError } = await adminClient
      .from('employment_relationships')
      .select('*', { count: 'exact', head: true })
      .eq('role', 'staff_member')
      .eq('status', 'active');

    if (staffError) {
      throw staffError;
    }

    // Get context analytics
    const { count: individualContextUsers, error: individualContextError } = await adminClient
      .from('user_contexts')
      .select('*', { count: 'exact', head: true })
      .eq('active_context', 'individual');

    if (individualContextError) {
      throw individualContextError;
    }

    const { count: organizationalContextUsers, error: orgContextError } = await adminClient
      .from('user_contexts')
      .select('*', { count: 'exact', head: true })
      .eq('active_context', 'organization');

    if (orgContextError) {
      throw orgContextError;
    }

    // Get multi-employment users (users with more than one employment relationship)
    const { data: multiEmploymentData, error: multiEmpError } = await adminClient
      .from('employment_relationships')
      .select('user_id')
      .eq('status', 'active');

    if (multiEmpError) {
      throw multiEmpError;
    }

    const userEmploymentCounts = multiEmploymentData?.reduce((acc, emp) => {
      acc[emp.user_id] = (acc[emp.user_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const multiEmploymentUsers = Object.values(userEmploymentCounts).filter(count => count > 1).length;

    // Get recent activity (users active in last 24 hours)
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const { count: activeUsers24h, error: activeUsersError } = await adminClient
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('last_sign_in_at', twentyFourHoursAgo);

    if (activeUsersError) {
      throw activeUsersError;
    }

    return {
      stats: {
        // User & Organization Stats
        totalUsers: totalUsers || 0,
        totalOrganizations: totalOrganizations || 0,
        totalIndividuals: totalIndividuals || 0,
        totalDepartments: totalDepartments || 0,
        totalEmployments: totalEmployments || 0,

        // Employment Analytics
        organizationAdmins: organizationAdmins || 0,
        departmentAdmins: departmentAdmins || 0,
        staffMembers: staffMembers || 0,
        activeEmployments: totalEmployments || 0,

        // Platform Analytics
        individualContextUsers: individualContextUsers || 0,
        organizationalContextUsers: organizationalContextUsers || 0,
        multiEmploymentUsers,

        // System Health
        platformUptime: 99.9,
        avgResponseTime: 120,
        activeUsers24h: activeUsers24h || 0
      }
    };
  }, 'GET /api/admin/stats')();
}