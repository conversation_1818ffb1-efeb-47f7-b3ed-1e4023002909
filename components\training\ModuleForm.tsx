'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetFooter, 
  SheetHeader, 
  SheetTitle 
} from '@/components/ui/sheet';
import { supabase } from '@/lib/supabase';
import { FileUpload } from '@/components/ui/file-upload';
import { RichTextEditor } from '@/components/ui/rich-text-editor';

// Define form schema with Zod
const moduleFormSchema = z.object({
  title: z.string().min(3, {
    message: 'Title must be at least 3 characters.',
  }).max(100, {
    message: 'Title must not be longer than 100 characters.',
  }),
  description: z.string().min(10, {
    message: 'Description must be at least 10 characters.',
  }).max(5000, {
    message: 'Description must not be longer than 5000 characters.',
  }),
  coverImageUrl: z.string().url({
    message: 'Please upload a valid image.',
  }).optional().or(z.literal('')),
  status: z.enum(['draft', 'published', 'archived']),
  durationMinutes: z.coerce.number().int().positive().optional(),
  requiredOrder: z.coerce.number().int().nonnegative().optional(),
});

type ModuleFormValues = z.infer<typeof moduleFormSchema>;

interface ModuleFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  moduleId?: string;
}

export function ModuleForm({ isOpen, onClose, onSuccess, moduleId }: ModuleFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isEditing = !!moduleId;
  
  // Initialize the form
  const form = useForm<ModuleFormValues>({
    resolver: zodResolver(moduleFormSchema),
    defaultValues: {
      title: '',
      description: '',
      coverImageUrl: '',
      status: 'draft',
      durationMinutes: undefined,
      requiredOrder: undefined,
    },
  });
  
  // Fetch module data if editing
  useEffect(() => {
    if (isEditing && moduleId) {
      fetchModuleData(moduleId);
    }
  }, [isEditing, moduleId]);
  
  const fetchModuleData = async (id: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await supabase
        .from('training_modules')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      
      if (data) {
        form.reset({
          title: data.title,
          description: data.description || '',
          coverImageUrl: data.cover_image_url || '',
          status: data.status || 'draft',
          durationMinutes: data.duration_minutes || undefined,
          requiredOrder: data.required_order || undefined,
        });
      }
    } catch (err: any) {
      console.error('Error fetching module:', err);
      setError(err.message || 'Failed to load module data');
    } finally {
      setIsLoading(false);
    }
  };
  
  const onSubmit = async (values: ModuleFormValues) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Get current user's ID
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('You must be logged in to manage training modules');
      }
      
      // Prepare data for Supabase
      const moduleData = {
        title: values.title,
        description: values.description,
        cover_image_url: values.coverImageUrl || null,
        status: values.status,
        duration_minutes: values.durationMinutes || null,
        required_order: values.requiredOrder || null,
        created_by: session.user.id,
      };
      
      let result;
      
      if (isEditing) {
        // Update existing module
        result = await supabase
          .from('training_modules')
          .update(moduleData)
          .eq('id', moduleId);
      } else {
        // Create new module
        result = await supabase
          .from('training_modules')
          .insert([moduleData]);
      }
      
      const { error } = result;
      
      if (error) throw error;
      
      // Success! Reset form and close
      form.reset();
      onSuccess();
      onClose();
    } catch (err: any) {
      console.error('Error saving module:', err);
      setError(err.message || 'Failed to save module');
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageUpload = (url: string) => {
    form.setValue('coverImageUrl', url, { 
      shouldValidate: true,
      shouldDirty: true 
    });
  };
  
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="sm:max-w-xl overflow-y-auto">
        <SheetHeader>
          <SheetTitle>{isEditing ? 'Edit Training Module' : 'Create Training Module'}</SheetTitle>
          <SheetDescription>
            {isEditing 
              ? 'Update the details of this training module.' 
              : 'Add a new training module to the platform.'}
          </SheetDescription>
        </SheetHeader>
        
        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 my-4">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}
        
        <div className="py-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter module title" {...field} />
                    </FormControl>
                    <FormDescription>
                      The name of the training module.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="coverImageUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cover Image</FormLabel>
                    <FormControl>
                      <FileUpload 
                        bucket="training-modules"
                        path="covers"
                        acceptedFileTypes="image/*"
                        maxSize={2 * 1024 * 1024} // 2MB
                        onUploadComplete={handleImageUpload}
                        currentFileUrl={field.value}
                        label="Upload Cover Image"
                      />
                    </FormControl>
                    <FormDescription>
                      Upload an image that represents this module (recommended: 16:9 ratio).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <RichTextEditor 
                        value={field.value} 
                        onChange={field.onChange}
                        placeholder="Describe this training module..."
                        minHeight="300px"
                      />
                    </FormControl>
                    <FormDescription>
                      Explain what prospects will learn in this module.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="published">Published</SelectItem>
                          <SelectItem value="archived">Archived</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Current status of the module.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="durationMinutes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration (minutes)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="e.g. 60"
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => {
                            const value = e.target.value === '' ? undefined : parseInt(e.target.value, 10);
                            field.onChange(value);
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        Estimated time to complete (optional).
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="requiredOrder"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Order</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="e.g. 1"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => {
                          const value = e.target.value === '' ? undefined : parseInt(e.target.value, 10);
                          field.onChange(value);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      The display order of this module (optional). Leave blank for no specific order.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <SheetFooter>
                <Button 
                  type="submit" 
                  disabled={isLoading}
                >
                  {isLoading 
                    ? (isEditing ? 'Updating...' : 'Creating...') 
                    : (isEditing ? 'Update Module' : 'Create Module')}
                </Button>
              </SheetFooter>
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
} 