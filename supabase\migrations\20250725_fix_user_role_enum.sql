-- Fix user_role enum to only include platform_admin and individual
-- Migration: 20250725_fix_user_role_enum.sql

-- Step 1: Update any existing users with old roles to individual
UPDATE users SET role = 'individual' WHERE role IN ('org_owner', 'org_admin', 'org_member');

-- Step 2: Drop and recreate the enum with only the correct values
-- First rename the old enum
ALTER TYPE user_role RENAME TO user_role_old;

-- Create new enum with only the correct Luna values
CREATE TYPE user_role AS ENUM ('platform_admin', 'individual');

-- Step 3: Update the users table to use the new enum
ALTER TABLE users ALTER COLUMN role TYPE user_role USING role::text::user_role;

-- Step 4: Drop the old enum
DROP TYPE user_role_old;

-- Step 5: Add comment for documentation
COMMENT ON TYPE user_role IS 'Luna user roles: platform_admin for system admins, individual for all regular users. Employment roles are handled separately in employment_relationships table.';
