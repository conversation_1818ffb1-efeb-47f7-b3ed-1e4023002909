/**
 * Authentication Query Caching System
 * Optimizes authentication performance by caching frequently accessed data
 */

import { userCache, createCacheKey } from '@/lib/cache-manager'
import type { AuthUser, EmploymentRelationship } from './auth/types'

// Cache durations (in milliseconds)
const CACHE_DURATIONS = {
  USER_DATA: 5 * 60 * 1000,      // 5 minutes
  EMPLOYMENT_DATA: 10 * 60 * 1000, // 10 minutes
  USER_CONTEXT: 2 * 60 * 1000,    // 2 minutes
  PERMISSIONS: 5 * 60 * 1000      // 5 minutes
}

/**
 * Cache user authentication data
 */
export async function cacheUserAuth(userId: string, authUser: AuthUser): Promise<void> {
  const cacheKey = createCacheKey('auth-user', userId)
  await userCache.set(cacheKey, authUser, CACHE_DURATIONS.USER_DATA)
}

/**
 * Get cached user authentication data
 */
export async function getCachedUserAuth(userId: string): Promise<AuthUser | null> {
  const cacheKey = createCacheKey('auth-user', userId)
  return await userCache.get(cacheKey)
}

/**
 * Cache employment relationships
 */
export async function cacheEmploymentRelationships(
  userId: string, 
  relationships: EmploymentRelationship[]
): Promise<void> {
  const cacheKey = createCacheKey('employment-relationships', userId)
  await userCache.set(cacheKey, relationships, CACHE_DURATIONS.EMPLOYMENT_DATA)
}

/**
 * Get cached employment relationships
 */
export async function getCachedEmploymentRelationships(userId: string): Promise<EmploymentRelationship[] | null> {
  const cacheKey = createCacheKey('employment-relationships', userId)
  return await userCache.get(cacheKey)
}

/**
 * Cache user context data
 */
export async function cacheUserContext(userId: string, contextData: any): Promise<void> {
  const cacheKey = createCacheKey('user-context', userId)
  await userCache.set(cacheKey, contextData, CACHE_DURATIONS.USER_CONTEXT)
}

/**
 * Get cached user context data
 */
export async function getCachedUserContext(userId: string): Promise<any | null> {
  const cacheKey = createCacheKey('user-context', userId)
  return await userCache.get(cacheKey)
}

/**
 * Cache user permissions
 */
export async function cacheUserPermissions(userId: string, permissions: any): Promise<void> {
  const cacheKey = createCacheKey('user-permissions', userId)
  await userCache.set(cacheKey, permissions, CACHE_DURATIONS.PERMISSIONS)
}

/**
 * Get cached user permissions
 */
export async function getCachedUserPermissions(userId: string): Promise<any | null> {
  const cacheKey = createCacheKey('user-permissions', userId)
  return await userCache.get(cacheKey)
}

/**
 * Invalidate all cached data for a user
 */
export async function invalidateUserCache(userId: string): Promise<void> {
  const cacheKeys = [
    createCacheKey('auth-user', userId),
    createCacheKey('employment-relationships', userId),
    createCacheKey('user-context', userId),
    createCacheKey('user-permissions', userId)
  ]

  await Promise.all(cacheKeys.map(key => userCache.delete(key)))
}

/**
 * Warm up cache with frequently accessed data
 */
export async function warmUpAuthCache(userId: string, authUser: AuthUser): Promise<void> {
  await Promise.all([
    cacheUserAuth(userId, authUser),
    cacheEmploymentRelationships(userId, authUser.employmentRelationships),
    cacheUserContext(userId, authUser.currentContext)
  ])
}

/**
 * Get cache statistics for monitoring
 */
export function getAuthCacheStats() {
  return {
    cacheHitRate: userCache.getStats?.()?.hitRate || 0,
    totalKeys: userCache.getStats?.()?.totalKeys || 0,
    memoryUsage: userCache.getStats?.()?.memoryUsage || 0
  }
}

/**
 * Batch cache multiple users' data
 */
export async function batchCacheUsers(users: Array<{ userId: string, authUser: AuthUser }>): Promise<void> {
  await Promise.all(
    users.map(({ userId, authUser }) => warmUpAuthCache(userId, authUser))
  )
}

/**
 * Cache invalidation strategies
 */
export const CacheInvalidationStrategies = {
  /**
   * Invalidate cache when user data changes
   */
  onUserUpdate: async (userId: string) => {
    await invalidateUserCache(userId)
  },

  /**
   * Invalidate cache when employment relationships change
   */
  onEmploymentChange: async (userId: string) => {
    const cacheKeys = [
      createCacheKey('employment-relationships', userId),
      createCacheKey('user-permissions', userId),
      createCacheKey('auth-user', userId)
    ]
    await Promise.all(cacheKeys.map(key => userCache.delete(key)))
  },

  /**
   * Invalidate cache when user context changes
   */
  onContextChange: async (userId: string) => {
    const cacheKey = createCacheKey('user-context', userId)
    await userCache.delete(cacheKey)
  }
}
