-- Luna Schema Cleanup and Fix Migration
-- Fix issues from previous migration and complete the transformation

-- Step 1: Drop old tables that should have been removed
DROP TABLE IF EXISTS team_memberships CASCADE;
DROP TABLE IF EXISTS teams CASCADE;

-- Step 2: Drop old enum types
DROP TYPE IF EXISTS team_member_role CASCADE;

-- Step 3: Update user_role enum to include employment-related roles
-- First, add new values to existing enum
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'organization_admin';
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'department_admin';

-- Step 4: Make created_by nullable in organizations table (for seeding purposes)
ALTER TABLE organizations ALTER COLUMN created_by DROP NOT NULL;

-- Step 5: Clean up any existing data that might conflict
DELETE FROM user_contexts WHERE active_team_id IS NOT NULL;
DELETE FROM user_training_data WHERE team_id IS NOT NULL;

-- Step 6: Ensure all employment-related tables exist with correct structure
-- (These should already exist from previous migration, but ensuring they're correct)

-- Verify departments table structure
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'departments') THEN
    CREATE TABLE departments (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      name TEXT NOT NULL,
      description TEXT,
      department_head_id UUID REFERENCES users(id),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      
      CONSTRAINT departments_org_name_unique UNIQUE(organization_id, name)
    );
  END IF;
END $$;

-- Verify employment_relationships table structure
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'employment_relationships') THEN
    CREATE TABLE employment_relationships (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
      organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      department_id UUID NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
      role employment_role NOT NULL DEFAULT 'staff_member',
      status employment_status NOT NULL DEFAULT 'invited',
      job_title TEXT,
      hire_date DATE,
      termination_date DATE,
      invited_by UUID REFERENCES users(id),
      invited_at TIMESTAMP WITH TIME ZONE,
      joined_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      
      CONSTRAINT employment_user_org_unique UNIQUE(user_id, organization_id),
      CONSTRAINT employment_termination_check CHECK (termination_date IS NULL OR termination_date >= hire_date)
    );
  END IF;
END $$;

-- Verify employment_invitations table structure
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'employment_invitations') THEN
    CREATE TABLE employment_invitations (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      email TEXT NOT NULL,
      organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
      department_id UUID NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
      role employment_role NOT NULL DEFAULT 'staff_member',
      job_title TEXT,
      invited_by UUID NOT NULL REFERENCES users(id),
      invitation_token TEXT UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
      expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
      accepted_at TIMESTAMP WITH TIME ZONE,
      declined_at TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      
      CONSTRAINT invitation_email_org_unique UNIQUE(email, organization_id),
      CONSTRAINT invitation_expires_check CHECK (expires_at > created_at)
    );
  END IF;
END $$;

-- Step 7: Ensure user_contexts table has correct columns
DO $$
BEGIN
  -- Remove old team-related columns if they exist
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'active_team_id') THEN
    ALTER TABLE user_contexts DROP COLUMN active_team_id;
  END IF;
  
  -- Add new employment-related columns if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'active_department_id') THEN
    ALTER TABLE user_contexts ADD COLUMN active_department_id UUID REFERENCES departments(id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'active_employment_id') THEN
    ALTER TABLE user_contexts ADD COLUMN active_employment_id UUID REFERENCES employment_relationships(id);
  END IF;
END $$;

-- Step 8: Ensure user_training_data table has correct columns
DO $$
BEGIN
  -- Remove old team-related columns if they exist
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_training_data' AND column_name = 'team_id') THEN
    ALTER TABLE user_training_data DROP COLUMN team_id;
  END IF;
  
  -- Add new employment-related columns if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_training_data' AND column_name = 'department_id') THEN
    ALTER TABLE user_training_data ADD COLUMN department_id UUID REFERENCES departments(id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_training_data' AND column_name = 'employment_id') THEN
    ALTER TABLE user_training_data ADD COLUMN employment_id UUID REFERENCES employment_relationships(id);
  END IF;
END $$;

-- Step 9: Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_departments_organization_id ON departments(organization_id);
CREATE INDEX IF NOT EXISTS idx_departments_head ON departments(department_head_id);
CREATE INDEX IF NOT EXISTS idx_employment_user_id ON employment_relationships(user_id);
CREATE INDEX IF NOT EXISTS idx_employment_organization_id ON employment_relationships(organization_id);
CREATE INDEX IF NOT EXISTS idx_employment_department_id ON employment_relationships(department_id);
CREATE INDEX IF NOT EXISTS idx_employment_status ON employment_relationships(status);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON employment_invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON employment_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_invitations_expires ON employment_invitations(expires_at);

-- Step 10: Ensure triggers exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_departments_updated_at ON departments;
CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON departments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employment_relationships_updated_at ON employment_relationships;
CREATE TRIGGER update_employment_relationships_updated_at BEFORE UPDATE ON employment_relationships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
