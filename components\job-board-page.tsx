"use client"

import {
  <PERSON>riefcase,
  Building,
  Clock,
  MapPin,
  Search,
  Calendar,
  Users,
  Heart,
  SlidersHorizontal,
} from "lucide-react"
import { useState, useEffect } from "react"
import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"
import { JobApplicationConfirmationDialog } from "@/components/job-application-confirmation-dialog"
import { toast } from "sonner"

interface JobProps {
  id: string
  title: string
  company: string
  companyLogo: string | null
  location: string
  jobType: string
  salary: string
  postedDate: string
  description: string
  skills: string[]
  isNew: boolean
  deadline: string | null
}

export function JobBoardPage({ jobs = [] }: { jobs?: JobProps[] }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [locationFilter, setLocationFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [experienceFilter, setExperienceFilter] = useState("all")
  const [jobTypeFilter, setJobTypeFilter] = useState("all")
  const [companySizeFilter, setCompanySizeFilter] = useState("all")
  const [sortBy, setSortBy] = useState("relevance")
  const [mounted, setMounted] = useState(false)
  const [savedJobs, setSavedJobs] = useState<string[]>([])
  const { theme } = useTheme()
  const [isLoading, setIsLoading] = useState(true)
  const [showApplyDialog, setShowApplyDialog] = useState(false)
  const [selectedJob, setSelectedJob] = useState<JobProps | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Simulate loading state
  useEffect(() => {
    setMounted(true)
    const timer = setTimeout(() => setIsLoading(false), 800)
    return () => clearTimeout(timer)
  }, [])

  // Sort jobs based on selected option
  const sortJobs = (jobs: JobProps[]) => {
    switch (sortBy) {
      case "recent":
        return [...jobs].sort((a, b) => {
          const dateA = a.postedDate.replace("Posted ", "").replace(" days ago", "")
          const dateB = b.postedDate.replace("Posted ", "").replace(" days ago", "")
          return parseInt(dateA) - parseInt(dateB)
        })
      case "salary-high":
        return [...jobs].sort((a, b) => {
          const salaryA = a.salary.includes("-") 
            ? parseInt(a.salary.split("-")[1].replace(/\D/g, "")) 
            : parseInt(a.salary.replace(/\D/g, "")) || 0
          const salaryB = b.salary.includes("-") 
            ? parseInt(b.salary.split("-")[1].replace(/\D/g, "")) 
            : parseInt(b.salary.replace(/\D/g, "")) || 0
          return salaryB - salaryA
        })
      case "salary-low":
        return [...jobs].sort((a, b) => {
          const salaryA = a.salary.includes("-") 
            ? parseInt(a.salary.split("-")[0].replace(/\D/g, "")) 
            : parseInt(a.salary.replace(/\D/g, "")) || 0
          const salaryB = b.salary.includes("-") 
            ? parseInt(b.salary.split("-")[0].replace(/\D/g, "")) 
            : parseInt(b.salary.replace(/\D/g, "")) || 0
          return salaryA - salaryB
        })
      default:
        return jobs
    }
  }
  
  // Filter jobs based on all criteria
  const filteredJobs = sortJobs(jobs.filter(job => {
    // Search filter
    const searchLower = searchTerm.toLowerCase()
    const matchesSearch = searchTerm === "" ||
      job.title.toLowerCase().includes(searchLower) ||
      job.company.toLowerCase().includes(searchLower) ||
      job.description.toLowerCase().includes(searchLower) ||
      job.skills.some(skill => skill.toLowerCase().includes(searchLower))

    // Location filter
    const matchesLocation = locationFilter === "all" ||
      job.location.toLowerCase().includes(locationFilter.toLowerCase())

    // Job type filter
    const matchesJobType = jobTypeFilter === "all" ||
      job.jobType.toLowerCase() === jobTypeFilter

    return matchesSearch && matchesLocation && matchesJobType
  }))
  
  // Handle filter changes
  const toggleSaveJob = (jobId: string) => {
    setSavedJobs(prev =>
      prev.includes(jobId)
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    )
  }

  const handleApplyNow = (jobId: string) => {
    const job = jobs.find(j => j.id === jobId)
    if (job) {
      setSelectedJob(job)
      setShowApplyDialog(true)
    }
  }

  const handleConfirmApplication = async () => {
    if (!selectedJob) return

    setIsSubmitting(true)
    try {
      const response = await fetch('/api/applications/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jobId: selectedJob.id
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit application')
      }

      // Success
      toast.success('Application submitted successfully!', {
        description: `Your profile has been sent to ${selectedJob.company}. They will review your application and contact you if you're a good fit.`
      })

      setShowApplyDialog(false)
      setSelectedJob(null)

    } catch (error: any) {
      toast.error('Failed to submit application', {
        description: error.message || 'Please try again later.'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const clearAllFilters = () => {
    setSearchTerm("")
    setLocationFilter("all")
    setCategoryFilter("all")
    setExperienceFilter("all")
    setJobTypeFilter("all")
    setCompanySizeFilter("all")
  }

  if (!mounted) return null

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 dark:from-blue-800 dark:via-purple-800 dark:to-indigo-800">
        <div className="absolute inset-0 bg-black/10" />
        <div className="relative flex justify-center py-16 lg:py-24">
          <div className="w-full max-w-[1000px] text-center">
            <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
              The #1 job board for
            </h1>
            <h2 className="mt-2 text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
              BPO professionals
            </h2>
            <p className="mx-auto mt-6 max-w-2xl text-xl text-blue-100">
              Looking for a job? Browse latest BPO job openings to view & apply!
            </p>

            {/* Hero Search */}
            <div className="mt-10">
              <div className="flex flex-col gap-4 rounded-2xl bg-white p-6 shadow-2xl dark:bg-gray-800 sm:flex-row">
                <div className="relative flex-1">
                  <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Customer Service Representative"
                    className="h-14 pl-12 text-lg border-0 bg-gray-50 dark:bg-gray-700 focus:ring-2 focus:ring-blue-500"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="relative flex-1">
                  <MapPin className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-gray-400" />
                  <Select value={locationFilter} onValueChange={setLocationFilter}>
                    <SelectTrigger className="h-14 pl-12 text-lg border-0 bg-gray-50 dark:bg-gray-700 focus:ring-2 focus:ring-blue-500">
                      <SelectValue placeholder="Select District" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Districts</SelectItem>
                      <SelectItem value="belize">Belize District</SelectItem>
                      <SelectItem value="cayo">Cayo District</SelectItem>
                      <SelectItem value="corozal">Corozal District</SelectItem>
                      <SelectItem value="orange-walk">Orange Walk District</SelectItem>
                      <SelectItem value="stann-creek">Stann Creek District</SelectItem>
                      <SelectItem value="toledo">Toledo District</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button size="lg" className="h-14 px-8 text-lg bg-gray-900 hover:bg-gray-800 dark:bg-white dark:text-gray-900 dark:hover:bg-gray-100">
                  Search
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex justify-center py-8">
        <div className="w-full max-w-[1000px]">
        {/* Results Header */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Showing results ({filteredJobs.length})
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Sort: <span className="font-medium">Newest</span>
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Sort: Newest</SelectItem>
                <SelectItem value="recent">Sort: Most Recent</SelectItem>
                <SelectItem value="salary-high">Sort: Salary (High)</SelectItem>
                <SelectItem value="salary-low">Sort: Salary (Low)</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFilters}
              className="gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400"
            >
              Reset
            </Button>
          </div>
        </div>

        {/* Horizontal Filters */}
        <div className="mb-8 rounded-xl bg-white p-6 shadow-lg dark:bg-gray-800">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-900 dark:text-white">Filters:</span>
            </div>

            {/* Categories Filter */}
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="customer-service">Customer Service</SelectItem>
                <SelectItem value="data-entry">Data Entry</SelectItem>
                <SelectItem value="virtual-assistant">Virtual Assistant</SelectItem>
                <SelectItem value="technical-support">Technical Support</SelectItem>
                <SelectItem value="sales">Sales & Marketing</SelectItem>
                <SelectItem value="quality-assurance">Quality Assurance</SelectItem>
                <SelectItem value="team-lead">Team Lead</SelectItem>
                <SelectItem value="training">Training & Development</SelectItem>
              </SelectContent>
            </Select>

            {/* Experience Level Filter */}
            <Select value={experienceFilter} onValueChange={setExperienceFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Experience Level" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="entry">Entry Level (1028)</SelectItem>
                <SelectItem value="intermediate">Intermediate (902)</SelectItem>
                <SelectItem value="expert">Expert (140)</SelectItem>
              </SelectContent>
            </Select>

            {/* Job Type Filter */}
            <Select value={jobTypeFilter} onValueChange={setJobTypeFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Job Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="full_time">Full-time (820)</SelectItem>
                <SelectItem value="part_time">Part-time (232)</SelectItem>
                <SelectItem value="remote">Remote (1872)</SelectItem>
                <SelectItem value="contract">Contract (113)</SelectItem>
              </SelectContent>
            </Select>

            {/* Company Size Filter */}
            <Select value={companySizeFilter} onValueChange={setCompanySizeFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Company Size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sizes</SelectItem>
                <SelectItem value="startup">Startup (1-50)</SelectItem>
                <SelectItem value="medium">Medium (51-200)</SelectItem>
                <SelectItem value="large">Large (201-1000)</SelectItem>
                <SelectItem value="enterprise">Enterprise (1000+)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Job Listings */}
        <div className="space-y-6">
            {isLoading ? (
              // Skeleton loading UI
              Array(3).fill(0).map((_, i) => (
                <Card key={i} className="border border-gray-200 bg-white dark:bg-gray-800 dark:border-gray-700">
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <Skeleton className="h-12 w-12 rounded-lg" />
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center justify-between">
                            <Skeleton className="h-5 w-48" />
                            <Skeleton className="h-4 w-16" />
                          </div>
                          <Skeleton className="h-4 w-64" />
                          <div className="flex gap-2 mt-2">
                            <Skeleton className="h-5 w-16 rounded-full" />
                            <Skeleton className="h-5 w-20 rounded-full" />
                            <Skeleton className="h-5 w-12 rounded-full" />
                          </div>
                        </div>
                      </div>
                      <Skeleton className="h-8 w-8 rounded-full" />
                    </div>
                  </div>
                </Card>
              ))
            ) : filteredJobs.length === 0 ? (
              <Card className="border border-gray-200 bg-white dark:bg-gray-800 dark:border-gray-700 p-12 text-center">
                <div className="flex flex-col items-center gap-4">
                  <Search className="h-16 w-16 text-gray-400" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">No jobs found</h3>
                  <p className="text-gray-600 dark:text-gray-400 max-w-md">
                    No jobs match your current search criteria. Try adjusting your filters or search terms.
                  </p>
                  <Button onClick={clearAllFilters} className="mt-4">
                    Clear Filters
                  </Button>
                </div>
              </Card>
            ) : (
              filteredJobs.map((job) => (
                <Card
                  key={job.id}
                  className="group border border-gray-200 bg-white transition-all hover:shadow-md dark:bg-gray-800 dark:border-gray-700 cursor-pointer"
                  onClick={() => window.location.href = `/prospect/job-board/${job.id}`}
                >
                  <div className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        {/* Company Logo */}
                        <div className="relative flex-shrink-0">
                          <Avatar className="h-12 w-12 rounded-lg border border-gray-200 dark:border-gray-700">
                            {job.companyLogo ? (
                              <AvatarImage src={job.companyLogo} alt={job.company} />
                            ) : (
                              <AvatarFallback className="rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-sm">
                                {job.company.substring(0, 2).toUpperCase()}
                              </AvatarFallback>
                            )}
                          </Avatar>
                        </div>

                        {/* Job Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-1">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors truncate pr-2">
                              {job.title}
                            </h3>
                            <span className="text-sm text-gray-500 dark:text-gray-400 flex-shrink-0">
                              {job.postedDate.replace('Posted ', '').replace(' ago', '')}
                            </span>
                          </div>

                          <div className="flex items-center gap-1 mb-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{job.company}</span>
                            <span className="text-sm text-gray-500">•</span>
                            <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                              {job.jobType.replace('_', ' ')}
                            </span>
                            <span className="text-sm text-gray-500">•</span>
                            <span className="text-sm text-gray-600 dark:text-gray-400">{job.location}</span>
                          </div>

                          {/* Skills */}
                          <div className="flex flex-wrap gap-1 mt-2">
                            {job.skills.slice(0, 3).map((skill, i) => (
                              <Badge key={i} variant="secondary" className="bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 text-xs px-2 py-0.5">
                                {skill}
                              </Badge>
                            ))}
                            {job.skills.length > 3 && (
                              <Badge variant="outline" className="text-gray-500 text-xs px-2 py-0.5">
                                +{job.skills.length - 3}
                              </Badge>
                            )}
                          </div>

                          {/* Action Buttons */}
                          <div className="flex items-center gap-2 mt-3">
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 text-xs px-3 py-1 h-7"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleApplyNow(job.id)
                              }}
                            >
                              Apply Now
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Save Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          toggleSaveJob(job.id)
                        }}
                        className={cn(
                          "h-8 w-8 p-0 transition-colors rounded-full flex-shrink-0 ml-2",
                          savedJobs.includes(job.id)
                            ? "text-red-500 hover:text-red-600 bg-red-50 dark:bg-red-900/20"
                            : "text-gray-400 hover:text-red-500 hover:bg-gray-50 dark:hover:bg-gray-700"
                        )}
                      >
                        <Heart className={cn("h-4 w-4", savedJobs.includes(job.id) && "fill-current")} />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            )}
        </div>
        </div>
      </div>

      {/* Job Application Confirmation Dialog */}
      {selectedJob && (
        <JobApplicationConfirmationDialog
          isOpen={showApplyDialog}
          onClose={() => {
            setShowApplyDialog(false)
            setSelectedJob(null)
          }}
          onConfirm={handleConfirmApplication}
          job={{
            id: selectedJob.id,
            title: selectedJob.title,
            company: selectedJob.company,
            companyLogo: selectedJob.companyLogo
          }}
          isSubmitting={isSubmitting}
        />
      )}
    </div>
  )
}
