/**
 * Authentication Utilities
 * Helper functions for authentication and authorization
 */

import { NextResponse } from 'next/server';
import { AuthResult, AuthUser } from './types';

/**
 * Create standardized authentication error responses
 */
export function createAuthErrorResponse(authResult: AuthResult): NextResponse {
  return NextResponse.json(
    { 
      error: authResult.error,
      message: 'Authentication required'
    },
    { status: authResult.status }
  );
}

/**
 * Check if user has platform admin privileges
 */
export function isPlatformAdmin(user: AuthUser): boolean {
  return user.role === 'platform_admin' && user.isPlatformAdmin;
}

/**
 * Check if user is an organization admin for any organization
 */
export function isOrganizationAdmin(user: AuthUser): boolean {
  return user.employmentRelationships.some(
    emp => emp.employment_role === 'organization_admin'
  );
}

/**
 * Check if user is a department admin for any department
 */
export function isDepartmentAdmin(user: AuthUser): boolean {
  return user.employmentRelationships.some(
    emp => emp.employment_role === 'department_admin'
  );
}

/**
 * Check if user has employment relationships
 */
export function hasEmployment(user: AuthUser): boolean {
  return user.employmentRelationships.length > 0;
}

/**
 * Get user's organization IDs
 */
export function getUserOrganizationIds(user: AuthUser): string[] {
  return user.employmentRelationships.map(emp => emp.organization_id);
}

/**
 * Get user's department IDs
 */
export function getUserDepartmentIds(user: AuthUser): string[] {
  return user.employmentRelationships.map(emp => emp.department_id);
}

/**
 * Check if user has access to specific organization
 */
export function hasOrganizationAccess(user: AuthUser, organizationId: string): boolean {
  if (isPlatformAdmin(user)) return true;
  return user.employmentRelationships.some(
    emp => emp.organization_id === organizationId
  );
}

/**
 * Check if user has access to specific department
 */
export function hasDepartmentAccess(user: AuthUser, departmentId: string): boolean {
  if (isPlatformAdmin(user)) return true;
  return user.employmentRelationships.some(
    emp => emp.department_id === departmentId
  );
}

/**
 * Get user's role in specific organization
 */
export function getUserRoleInOrganization(
  user: AuthUser, 
  organizationId: string
): 'organization_admin' | 'department_admin' | 'staff_member' | null {
  const employment = user.employmentRelationships.find(
    emp => emp.organization_id === organizationId
  );
  return employment?.employment_role || null;
}

/**
 * Check if user can manage organization
 */
export function canManageOrganization(user: AuthUser, organizationId: string): boolean {
  if (isPlatformAdmin(user)) return true;
  const role = getUserRoleInOrganization(user, organizationId);
  return role === 'organization_admin';
}

/**
 * Check if user can manage department
 */
export function canManageDepartment(user: AuthUser, departmentId: string): boolean {
  if (isPlatformAdmin(user)) return true;
  const employment = user.employmentRelationships.find(
    emp => emp.department_id === departmentId
  );
  return employment?.employment_role === 'organization_admin' || 
         employment?.employment_role === 'department_admin';
}

/**
 * Validate user status
 */
export function isActiveUser(user: AuthUser): boolean {
  return user.status === 'active';
}

/**
 * Get user display name
 */
export function getUserDisplayName(user: AuthUser): string {
  return user.full_name || user.email;
}

/**
 * Get user's current context organization
 */
export function getCurrentOrganization(user: AuthUser): string | null {
  return user.currentContext?.organization_id || null;
}

/**
 * Get user's current context department
 */
export function getCurrentDepartment(user: AuthUser): string | null {
  return user.currentContext?.department_id || null;
}
