import { Metadata } from 'next';

export const organizationMetadata: Metadata = {
  title: "Organization Dashboard | Luna Skills Platform",
  description: "Manage your organization's talent pipeline, training programs, and team development",
};

export const organizationPagesMetadata = {
  dashboard: {
    title: "Organization Dashboard | Luna Skills Platform",
    description: "Overview of your organization's recruitment, training, and team management activities",
  },
  vacancies: {
    title: "Job Vacancies | Luna Skills Platform",
    description: "Create and manage job postings to attract qualified candidates",
  },
  applications: {
    title: "Applications | Luna Skills Platform",
    description: "Review and manage candidate applications for your job postings",
  },
  candidates: {
    title: "Candidates | Luna Skills Platform",
    description: "Browse and evaluate potential candidates from the Luna talent pool",
  },
  interviews: {
    title: "Interviews | Luna Skills Platform",
    description: "Schedule and manage interviews with candidates",
  },
  departments: {
    title: "Department Management | Luna Skills Platform",
    description: "Manage your organization's departments and their staff members",
  },
  analytics: {
    title: "Analytics | Luna Skills Platform",
    description: "View insights and analytics about your recruitment and training activities",
  },
  schedule: {
    title: "Schedule | Luna Skills Platform",
    description: "Manage your organization's interview and training schedules",
  },
  profile: {
    title: "Organization Profile | Luna Skills Platform",
    description: "Manage your organization's public profile and company information",
  },
  settings: {
    title: "Organization Settings | Luna Skills Platform",
    description: "Configure your organization's settings, preferences, and integrations",
  },
};
