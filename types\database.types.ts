export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          role: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member'
          status: 'active' | 'inactive' | 'pending'
          avatar_url: string | null
          timezone: string | null
          last_login: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name: string
          role?: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member'
          status?: 'active' | 'inactive' | 'pending'
          avatar_url?: string | null
          timezone?: string | null
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member'
          status?: 'active' | 'inactive' | 'pending'
          avatar_url?: string | null
          timezone?: string | null
          last_login?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      organizations: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          logo_url: string | null
          website: string | null
          industry: string | null
          size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise' | null
          status: 'active' | 'inactive' | 'suspended'
          settings: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          logo_url?: string | null
          website?: string | null
          industry?: string | null
          size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise' | null
          status?: 'active' | 'inactive' | 'suspended'
          settings?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          logo_url?: string | null
          website?: string | null
          industry?: string | null
          size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise' | null
          status?: 'active' | 'inactive' | 'suspended'
          settings?: Json
          created_at?: string
          updated_at?: string
        }
      }
      individuals: {
        Row: {
          id: string
          user_id: string
          contact_info: Json
          education: Json[]
          experience: Json[]
          skills: Json[]
          certifications: Json[]
          profile_image_url: string | null
          intro_video_url: string | null
          resume_url: string | null
          portfolio_url: string | null
          learning_status: 'not_started' | 'in_progress' | 'completed' | 'paused'
          current_learning_path_id: string | null
          total_learning_hours: number
          completed_courses: number
          skill_assessments: Json
          career_interests: Json
          salary_expectations: Json
          learning_style: Json
          availability: Json
          notification_preferences: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          contact_info?: Json
          education?: Json[]
          experience?: Json[]
          skills?: Json[]
          certifications?: Json[]
          profile_image_url?: string | null
          intro_video_url?: string | null
          resume_url?: string | null
          portfolio_url?: string | null
          learning_status?: 'not_started' | 'in_progress' | 'completed' | 'paused'
          current_learning_path_id?: string | null
          total_learning_hours?: number
          completed_courses?: number
          skill_assessments?: Json
          career_interests?: Json
          salary_expectations?: Json
          learning_style?: Json
          availability?: Json
          notification_preferences?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          contact_info?: Json
          education?: Json[]
          experience?: Json[]
          skills?: Json[]
          certifications?: Json[]
          profile_image_url?: string | null
          intro_video_url?: string | null
          resume_url?: string | null
          portfolio_url?: string | null
          learning_status?: 'not_started' | 'in_progress' | 'completed' | 'paused'
          current_learning_path_id?: string | null
          total_learning_hours?: number
          completed_courses?: number
          skill_assessments?: Json
          career_interests?: Json
          salary_expectations?: Json
          learning_style?: Json
          availability?: Json
          notification_preferences?: Json
          created_at?: string
          updated_at?: string
        }
      }
      organization_memberships: {
        Row: {
          id: string
          user_id: string
          organization_id: string
          role: 'owner' | 'admin' | 'manager' | 'member' | 'viewer'
          status: 'pending' | 'active' | 'inactive' | 'removed'
          permissions: Json
          invited_by: string | null
          invited_at: string | null
          joined_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          organization_id: string
          role?: 'owner' | 'admin' | 'manager' | 'member' | 'viewer'
          status?: 'pending' | 'active' | 'inactive' | 'removed'
          permissions?: Json
          invited_by?: string | null
          invited_at?: string | null
          joined_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          organization_id?: string
          role?: 'owner' | 'admin' | 'manager' | 'member' | 'viewer'
          status?: 'pending' | 'active' | 'inactive' | 'removed'
          permissions?: Json
          invited_by?: string | null
          invited_at?: string | null
          joined_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      user_contexts: {
        Row: {
          id: string
          user_id: string
          active_context: 'individual' | 'organization'
          active_organization_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          active_context?: 'individual' | 'organization'
          active_organization_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          active_context?: 'individual' | 'organization'
          active_organization_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      is_platform_admin: {
        Args: {
          user_id_param: string
        }
        Returns: boolean
      }
      is_organization_member: {
        Args: {
          user_id_param: string
          org_id_param: string
        }
        Returns: boolean
      }
      get_user_organizations: {
        Args: {
          user_id_param: string
        }
        Returns: {
          organization_id: string
          organization_name: string
          role: string
          status: string
        }[]
      }
    }
    Enums: {
      user_role: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member'
      user_status: 'active' | 'inactive' | 'pending'
      organization_status: 'active' | 'inactive' | 'suspended'
      organization_size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise'
      membership_role: 'owner' | 'admin' | 'manager' | 'member' | 'viewer'
      membership_status: 'pending' | 'active' | 'inactive' | 'removed'
      context_type: 'individual' | 'organization'
      learning_status: 'not_started' | 'in_progress' | 'completed' | 'paused'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}