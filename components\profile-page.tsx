"use client"

import { Camera, Edit, Plus, FileText, Download, Briefcase, GraduationCap, Award, Video, Upload, Trash2, Eye, EyeOff, Phone } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"
import { PersonalInfoDialog } from "@/components/profile/personal-info-dialog"
import { FileUploadDialog } from "@/components/profile/file-upload-dialog"
import { ExperienceDialog } from "@/components/profile/experience-dialog"
import { EducationDialog } from "@/components/profile/education-dialog"
import { SkillDialog } from "@/components/profile/skill-dialog"
import { VideoUploadDialog } from "@/components/profile/video-upload-dialog"
import { BadgeGrid } from "@/components/ui/badge-display"
import { useRouter } from "next/navigation"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { toast } from "sonner"
import { PerformanceReport } from "@/components/role-call/performance-report"
import { DetailedPerformanceAssessment } from "@/components/role-call/detailed-performance-assessment"

interface Skill {
  name: string
  level: string
  proficiency: number
}

interface Certificate {
  id: string
  module_title: string
  issue_date: string
  certificate_url: string
}

interface BadgeItem {
  id: string
  title: string
  description: string
  badge_type: string
}

interface Module {
  id: string
  title: string
  progress: number
  status: string
}

interface ProfileData {
  user: {
    id: string
    full_name: string
    email: string
    role: string
    avatar_url?: string
  }
  prospect: {
    id: string
    contact_info: any
    education: any[]
    experience: any[]
    skills: any[]
    intro_video_url: string | null
    resume_url: string | null
    profile_visibility: boolean
    training_status: string
  } | null
  training: {
    progress: number
    aiCallHours: number
    avgScore: number
    totalCalls?: number
    modules: Module[]
    certificates: Certificate[]
    badges: BadgeItem[]
    skills: Skill[]
    allFiles?: any[] // For storing all files including documents
    assessmentStats?: {
      totalCompleted: number // Total attempts across all assessments
      totalUniqueAssessments: number // Unique assessments completed
      averageScore: number // Average of best scores per module
      moduleScores: Array<{
        moduleId: string
        moduleTitle: string
        bestScore: number
        totalAttempts: number
        assessmentTitle: string
      }>
      recentAssessments: Array<{
        id: string
        title: string
        module_title: string
        score: number
        completed_at: string
        status: string
      }>
    }
  }
}

export function ProfilePage({ data }: { data: ProfileData }) {
  const { user, prospect, training } = data
  const router = useRouter()
  
  // Extract user info
  const fullName = user.full_name
  const email = user.email
  const avatarUrl = user.avatar_url
  
  console.log("Profile data:", {
    name: fullName,
    email,
    avatarUrl,
    hasProspect: !!prospect,
    prospectId: prospect?.id,
  });
  
  // Function to ensure avatar URL is valid
  const getValidAvatarUrl = () => {
    if (!avatarUrl) return null;
    
    // Make sure the URL is a valid Supabase Storage URL
    if (avatarUrl.includes('storage.googleapis.com') || 
        avatarUrl.includes('supabase.co') || 
        avatarUrl.startsWith('/')) {
      return avatarUrl;
    }
    
    // If it's not a valid URL, return null
    return null;
  }
  
  // Generate avatar initials from full name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }
  
  const initials = getInitials(fullName)
  
  // Format contact info
  const contactInfo = prospect?.contact_info || {}
  const phoneNumber = contactInfo.phone || "Not provided"
  const location = contactInfo.location ? 
    `${contactInfo.location.city || ''}, ${contactInfo.location.country || ''}` : 
    "Not provided"
  
  // Format resume info
  const resumeUrl = prospect?.resume_url || ""
  const hasResume = !!resumeUrl
  
  // Handle experience items
  const experience = prospect?.experience || []
  
  // Handle education items
  const education = prospect?.education || []
  
  // Sort experience entries from most recent to oldest based on end date
  const sortedExperience = [...experience].sort((a, b) => {
    // Handle 'Present' or empty end dates (ongoing experiences)
    const aEndDate = a.end_date && a.end_date !== 'Present'
      ? new Date(parseDate(a.end_date))
      : new Date();
    const bEndDate = b.end_date && b.end_date !== 'Present'
      ? new Date(parseDate(b.end_date))
      : new Date();

    // Compare end dates (newer dates first)
    return bEndDate.getTime() - aEndDate.getTime();
  });
  
  // Helper function to parse date strings in various formats
  function parseDate(dateStr: string) {
    // Try to handle various date formats (MM/DD/YYYY, MM-DD-YYYY, Month YYYY, etc.)
    const parsed = new Date(dateStr);
    if (!isNaN(parsed.getTime())) {
      return parsed;
    }
    
    // If standard parsing fails, try to extract month and year
    const monthYearMatch = dateStr.match(/([A-Za-z]+)\s+(\d{4})/);
    if (monthYearMatch) {
      const month = monthYearMatch[1];
      const year = monthYearMatch[2];
      const monthIndex = [
        'january', 'february', 'march', 'april', 'may', 'june',
        'july', 'august', 'september', 'october', 'november', 'december'
      ].findIndex(m => m.toLowerCase().startsWith(month.toLowerCase()));
      
      if (monthIndex >= 0) {
        return new Date(parseInt(year), monthIndex, 1);
      }
    }
    
    // Default to current date if parsing fails
    return new Date();
  }
  
  // Sort education entries from most recent to oldest based on end year
  const sortedEducation = [...education].sort((a, b) => {
    // Handle 'Present' or current education (ongoing)
    const aEndYear = a.current || !a.end_year ? new Date().getFullYear() : parseInt(a.end_year);
    const bEndYear = b.current || !b.end_year ? new Date().getFullYear() : parseInt(b.end_year);

    // Compare end years (newer dates first)
    return bEndYear - aEndYear;
  });
  
  // Handle profile refresh
  const refreshProfile = () => {
    console.log("🔄 Refreshing profile page");
    
    // Add a small delay before refreshing to ensure the API has time to complete
    setTimeout(() => {
      // Use a full page reload for more reliable refresh
      window.location.reload();
    }, 1000);
  }
  
  // Handle avatar upload success
  const handleAvatarUploadSuccess = (url: string) => {
    console.log("Avatar upload success callback with URL:", url);
    // Force a hard refresh to ensure the browser loads the new avatar
    window.location.reload();
  }
  
  return (
    <div className="w-full">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Left Column - Profile Info */}
        <div className="md:w-1/3">
          <Card className="border-none shadow-sm dark:shadow-none dark:bg-gray-900/60 dark:border dark:border-gray-800 overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 dark:from-blue-600 dark:to-indigo-800 h-24 relative"></div>
            <div className="px-6 pb-6">
              <div className="flex justify-center -mt-12">
                <Avatar className="h-24 w-24 border-4 border-white dark:border-gray-900 relative group">
                  <AvatarImage 
                    src={getValidAvatarUrl() || "/placeholder.svg?height=96&width=96"} 
                    alt={fullName} 
                    onError={(e) => {
                      console.error("Failed to load avatar image from URL:", avatarUrl);
                      // If avatar URL failed to load, fall back to placeholder
                      const imgElement = e.target as HTMLImageElement;
                      imgElement.src = "/placeholder.svg?height=96&width=96";
                    }} 
                  />
                  <AvatarFallback className="text-2xl bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300">{initials}</AvatarFallback>
                  
                  {/* Overlay with hover effect */}
                  <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <Camera className="h-5 w-5 text-white" />
                  </div>
                  
                  <FileUploadDialog 
                    fileType="avatar"
                    onSuccess={handleAvatarUploadSuccess}
                    triggerComponent={
                      <Button
                        size="icon"
                        variant="outline"
                        className="absolute bottom-0 right-0 h-8 w-8 rounded-full bg-white hover:bg-blue-50 dark:bg-gray-800 shadow dark:shadow-gray-950/50 border-2 border-white dark:border-gray-700"
                      >
                        <Camera className="h-4 w-4" />
                        <span className="sr-only">Change profile picture</span>
                      </Button>
                    }
                  />
                </Avatar>
              </div>

              <div className="text-center mt-4">
                <h2 className="text-xl font-semibold">{fullName}</h2>
                <p className="text-sm text-muted-foreground">{email}</p>
                <Badge className="mt-2 bg-blue-100 text-blue-700 hover:bg-blue-100 dark:bg-blue-900/80 dark:text-blue-200 dark:hover:bg-blue-900/90">
                  BPO Trainee
                </Badge>
                {!avatarUrl && (
                  <p className="text-xs text-muted-foreground mt-2">Click the camera icon to upload your profile picture</p>
                )}
              </div>

              <Separator className="my-4 dark:bg-gray-800" />

              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="font-medium">Training Progress</span>
                    <span className="text-blue-600 dark:text-blue-400">{training.progress}%</span>
                  </div>
                  <Progress value={training.progress} className="h-2 dark:bg-gray-800" />
                </div>

                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="bg-gray-50 dark:bg-gray-800/80 rounded-lg p-3">
                    <p className="text-2xl font-bold">{training.aiCallHours}</p>
                    <p className="text-xs text-muted-foreground">AI Call Hours</p>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-800/80 rounded-lg p-3">
                    <p className="text-2xl font-bold">{training.avgScore}</p>
                    <p className="text-xs text-muted-foreground">Avg. Score</p>
                  </div>
                </div>

                <PersonalInfoDialog
                  fullName={fullName}
                  phone={phoneNumber}
                  location={location}
                  onSuccess={refreshProfile}
                  triggerComponent={
                    <Button variant="outline" className="w-full dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700/70">
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Profile
                    </Button>
                  }
                />
              </div>

              {/* Preview Public Profile Button */}
              {prospect?.id && (
                <div className="flex justify-center mt-4">
                  <a href={`/prospect/public/${prospect.id}`} target="_blank" rel="noopener noreferrer">
                    <Button variant="secondary" size="sm">
                      Preview Public Profile
                    </Button>
                  </a>
                </div>
              )}
            </div>
          </Card>

          <Card className="border-none shadow-sm dark:shadow-none dark:bg-gray-900/60 dark:border dark:border-gray-800 mt-6">
            <CardHeader>
              <CardTitle className="text-base">Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Email Address</p>
                  <p className="text-sm font-medium">{email}</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Phone Number</p>
                  <p className="text-sm font-medium">{phoneNumber}</p>
                </div>
                <PersonalInfoDialog
                  fullName={fullName}
                  phone={phoneNumber}
                  location={location}
                  onSuccess={refreshProfile}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Location</p>
                  <p className="text-sm font-medium">{location}</p>
                </div>
                <PersonalInfoDialog
                  fullName={fullName}
                  phone={phoneNumber}
                  location={location}
                  onSuccess={refreshProfile}
                />
              </div>
              
              <Separator className="my-2 dark:bg-gray-800" />
              
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Profile Visibility</p>
                  <p className="text-sm font-medium flex items-center">
                    {prospect?.profile_visibility ? (
                      <>
                        <Eye className="h-3.5 w-3.5 mr-1.5 text-green-500" />
                        <span>Visible to employers</span>
                      </>
                    ) : (
                      <>
                        <EyeOff className="h-3.5 w-3.5 mr-1.5 text-amber-500" />
                        <span>Hidden from employers</span>
                      </>
                    )}
                  </p>
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/profile/toggle-visibility', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                      });
                      
                      if (!response.ok) {
                        throw new Error('Failed to update visibility');
                      }
                      
                      toast(prospect?.profile_visibility 
                        ? "Your profile is now hidden from employers" 
                        : "Your profile is now visible to employers");
                      
                      refreshProfile();
                    } catch (error) {
                      toast.error("Error updating visibility. Please try again later.");
                    }
                  }}
                >
                  {prospect?.profile_visibility ? "Hide Profile" : "Show Profile"}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="border-none shadow-sm dark:shadow-none dark:bg-gray-900/60 dark:border dark:border-gray-800 mt-6">
            <CardHeader>
              <CardTitle className="text-base">Introduction Video</CardTitle>
              <CardDescription>Introduce yourself to potential employers</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {prospect?.intro_video_url ? (
                <div className="flex flex-col items-center">
                  <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden w-full max-w-2xl">
                    <video 
                      src={prospect.intro_video_url} 
                      controls 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="mt-4">
                    <VideoUploadDialog
                      onSuccess={refreshProfile}
                      triggerComponent={
                        <Button size="sm" variant="outline" className="bg-white/90 hover:bg-white dark:bg-gray-800/90 dark:hover:bg-gray-800">
                          <Edit className="h-4 w-4 mr-1" />
                          Replace
                        </Button>
                      }
                    />
                  </div>
                </div>
              ) : (
                <div className="relative aspect-video bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex flex-col items-center justify-center">
                  <div className="text-center p-6">
                    <div className="mx-auto w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/60 flex items-center justify-center mb-3">
                      <Video className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 className="font-medium text-sm">Add an introduction video</h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      Record or upload a 30-60 second video introducing yourself
                    </p>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent flex items-end justify-center p-4 opacity-0 hover:opacity-100 transition-opacity">
                    <div className="flex gap-2">
                      <VideoUploadDialog
                        onSuccess={refreshProfile}
                        triggerComponent={
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800">
                            <Video className="h-4 w-4 mr-1" />
                            Add Video
                          </Button>
                        }
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="text-xs text-muted-foreground">
                <p>Tips for a great introduction:</p>
                <ul className="list-disc pl-4 mt-1 space-y-1">
                  <li>Keep it brief (30-60 seconds)</li>
                  <li>Mention your name and career goals</li>
                  <li>Highlight your key skills and strengths</li>
                  <li>Speak clearly and maintain eye contact</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Tabs Content */}
        <div className="md:w-2/3">
          <Tabs defaultValue="resume" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6 dark:bg-gray-800/80">
              <TabsTrigger value="resume">Resume</TabsTrigger>
              <TabsTrigger value="training">Training Report</TabsTrigger>
              <TabsTrigger value="skills">Skills & Certifications</TabsTrigger>
            </TabsList>

            <TabsContent value="resume" className="space-y-6">
              <Card className="border-none shadow-sm bg-gray-50 dark:bg-gray-900/60 dark:border dark:border-gray-800">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div>
                    <CardTitle>Resume</CardTitle>
                    <CardDescription>Your professional resume</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <FileUploadDialog
                      fileType="resume"
                      onSuccess={refreshProfile}
                      triggerComponent={
                    <Button variant="outline" size="sm" className="dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700/70">
                      <Edit className="mr-2 h-4 w-4" />
                          {hasResume ? "Update" : "Upload"}
                    </Button>
                      }
                    />
                    {hasResume && (
                      <Button size="sm" asChild className="dark:bg-blue-700 dark:hover:bg-blue-800">
                        <a href={resumeUrl} target="_blank" rel="noopener noreferrer">
                      <Download className="mr-2 h-4 w-4" />
                      Download
                        </a>
                    </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {hasResume ? (
                  <div className="flex items-center gap-3 mb-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/60 dark:text-blue-400">
                      <FileText className="h-5 w-5" />
                    </div>
                    <div>
                        <p className="font-medium">{resumeUrl.split('/').pop() || 'Resume'}</p>
                        <p className="text-xs text-muted-foreground">Ready to download</p>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-muted-foreground">No resume uploaded yet</p>
                      <FileUploadDialog
                        fileType="resume"
                        onSuccess={refreshProfile}
                        triggerComponent={
                          <Button className="mt-3 dark:bg-blue-700 dark:hover:bg-blue-800">
                            <Upload className="mr-2 h-4 w-4" />
                            Upload Resume
                          </Button>
                        }
                      />
                  </div>
                  )}
                </CardContent>
              </Card>

              <div className="space-y-4">
                {/* Work Experience Section */}
                <div className="rounded-lg bg-slate-100 dark:bg-gray-900/60 shadow-sm dark:shadow-none dark:border dark:border-gray-800 overflow-hidden">
                  <div className="px-4 py-3 bg-slate-200 dark:bg-gray-800/80 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-700 dark:bg-blue-900/60 dark:text-blue-400">
                        <Briefcase className="h-4 w-4" />
                      </div>
                      <div>
                        <h3 className="font-medium text-slate-800 dark:text-slate-200">Work Experience</h3>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Your professional history</p>
                      </div>
                    </div>
                    <ExperienceDialog 
                      action="add"
                      onSuccess={refreshProfile}
                      triggerComponent={
                        <Button size="sm" variant="ghost" className="h-8 gap-1 dark:text-slate-200 dark:hover:bg-gray-700/70">
                          <Plus className="h-4 w-4" />
                          <span className="text-xs">Add</span>
                        </Button>
                      }
                    />
                  </div>
                  
                  <div className="p-3">
                    {sortedExperience.length > 0 ? (
                      <Accordion type="multiple" className="space-y-2">
                        {sortedExperience.map((job: any, index: number) => (
                          <AccordionItem key={index} value={`job-${index}`} className="border border-slate-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-800/70">
                            <AccordionTrigger className="px-3 py-2 hover:no-underline hover:bg-slate-50 dark:hover:bg-gray-800">
                              <div className="flex items-center gap-2 text-left">
                                <div className="flex-1">
                                  <p className="font-medium text-sm text-slate-800 dark:text-slate-200">{job.title}</p>
                                  <p className="text-xs text-slate-500 dark:text-slate-400">{job.company} • {job.start_date} - {job.end_date || 'Present'}</p>
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-3 py-2 bg-slate-50 dark:bg-gray-800/50 border-t border-slate-200 dark:border-gray-700">
                              <div className="flex justify-between items-start">
                                <div className="space-y-2 flex-1">
                                  {job.location && (
                                    <p className="text-xs text-slate-600 dark:text-slate-300">
                                      <span className="font-medium">Location:</span> {job.location}
                                    </p>
                                  )}
                                  {job.description && (
                                    <p className="text-xs text-slate-600 dark:text-slate-300">{job.description}</p>
                                  )}
                                </div>
                                <div className="flex gap-1 ml-2">
                                  <ExperienceDialog 
                                    action="update"
                                    experience={job}
                                    experienceIndex={experience.findIndex(exp => 
                                      exp.company === job.company && 
                                      exp.title === job.title && 
                                      exp.start_date === job.start_date
                                    )}
                                    onSuccess={refreshProfile}
                                    triggerComponent={
                                      <Button size="icon" variant="ghost" className="h-6 w-6 dark:hover:bg-gray-700/70">
                                        <Edit className="h-3 w-3" />
                                      </Button>
                                    }
                                  />
                                  <ExperienceDialog 
                                    action="delete"
                                    experience={job}
                                    experienceIndex={experience.findIndex(exp => 
                                      exp.company === job.company && 
                                      exp.title === job.title && 
                                      exp.start_date === job.start_date
                                    )}
                                    onSuccess={refreshProfile}
                                    triggerComponent={
                                      <Button size="icon" variant="ghost" className="h-6 w-6 text-red-500 dark:text-red-400 dark:hover:bg-gray-700/70">
                                        <Trash2 className="h-3 w-3" />
                                      </Button>
                                    }
                                  />
                                </div>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    ) : (
                      <div className="text-center py-3 bg-white dark:bg-gray-800/70 rounded-md border border-slate-200 dark:border-gray-700">
                        <p className="text-slate-500 dark:text-slate-400 text-sm">No work experience added yet</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Education Section */}
                <div className="rounded-lg bg-slate-100 dark:bg-gray-900/60 shadow-sm dark:shadow-none dark:border dark:border-gray-800 overflow-hidden">
                  <div className="px-4 py-3 bg-slate-200 dark:bg-gray-800/80 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-100 text-indigo-700 dark:bg-indigo-900/60 dark:text-indigo-400">
                        <GraduationCap className="h-4 w-4" />
                      </div>
                      <div>
                        <h3 className="font-medium text-slate-800 dark:text-slate-200">Education</h3>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Your academic background</p>
                      </div>
                    </div>
                    <EducationDialog 
                      action="add"
                      onSuccess={refreshProfile}
                      triggerComponent={
                        <Button size="sm" variant="ghost" className="h-8 gap-1 dark:text-slate-200 dark:hover:bg-gray-700/70">
                          <Plus className="h-4 w-4" />
                          <span className="text-xs">Add</span>
                        </Button>
                      }
                    />
                  </div>
                  
                  <div className="p-3">
                    {sortedEducation.length > 0 ? (
                      <Accordion type="multiple" className="space-y-2">
                        {sortedEducation.map((edu: any, index: number) => (
                          <AccordionItem key={index} value={`edu-${index}`} className="border border-slate-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-800/70">
                            <AccordionTrigger className="px-3 py-2 hover:no-underline hover:bg-slate-50 dark:hover:bg-gray-800">
                              <div className="flex items-center gap-2 text-left">
                                <div className="flex-1">
                                  <p className="font-medium text-sm text-slate-800 dark:text-slate-200">{edu.degree}</p>
                                  <p className="text-xs text-slate-500 dark:text-slate-400">{edu.institution} • {edu.start_year} - {edu.current ? 'Present' : edu.end_year || 'Present'}</p>
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-3 py-2 bg-slate-50 dark:bg-gray-800/50 border-t border-slate-200 dark:border-gray-700">
                              <div className="flex justify-between items-start">
                                <div className="space-y-2 flex-1">
                                  {edu.field_of_study && (
                                    <p className="text-xs text-slate-600 dark:text-slate-300">
                                      <span className="font-medium">Field of Study:</span> {edu.field_of_study}
                                    </p>
                                  )}
                                  {edu.location && (
                                    <p className="text-xs text-slate-600 dark:text-slate-300">
                                      <span className="font-medium">Location:</span> {edu.location}
                                    </p>
                                  )}
                                  {edu.description && (
                                    <p className="text-xs text-slate-600 dark:text-slate-300">{edu.description}</p>
                                  )}
                                </div>
                                <div className="flex gap-1 ml-2">
                                  <EducationDialog 
                                    action="update"
                                    education={edu}
                                    educationIndex={education.findIndex(e => 
                                      e.institution === edu.institution && 
                                      e.degree === edu.degree && 
                                      e.start_year === edu.start_year
                                    )}
                                    onSuccess={refreshProfile}
                                    triggerComponent={
                                      <Button size="icon" variant="ghost" className="h-6 w-6 dark:hover:bg-gray-700/70">
                                        <Edit className="h-3 w-3" />
                                      </Button>
                                    }
                                  />
                                  <EducationDialog 
                                    action="delete"
                                    education={edu}
                                    educationIndex={education.findIndex(e => 
                                      e.institution === edu.institution && 
                                      e.degree === edu.degree && 
                                      e.start_year === edu.start_year
                                    )}
                                    onSuccess={refreshProfile}
                                    triggerComponent={
                                      <Button size="icon" variant="ghost" className="h-6 w-6 text-red-500 dark:text-red-400 dark:hover:bg-gray-700/70">
                                        <Trash2 className="h-3 w-3" />
                                      </Button>
                                    }
                                  />
                                </div>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        ))}
                      </Accordion>
                    ) : (
                      <div className="text-center py-3 bg-white dark:bg-gray-800/70 rounded-md border border-slate-200 dark:border-gray-700">
                        <p className="text-slate-500 dark:text-slate-400 text-sm">No education added yet</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Skills Section */}
                <div className="rounded-lg bg-slate-100 dark:bg-gray-900/60 shadow-sm dark:shadow-none dark:border dark:border-gray-800 overflow-hidden">
                  <div className="px-4 py-3 bg-slate-200 dark:bg-gray-800/80 flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 text-green-700 dark:bg-green-900/60 dark:text-green-400">
                        <Award className="h-4 w-4" />
                      </div>
                      <div>
                        <h3 className="font-medium text-slate-800 dark:text-slate-200">Skills</h3>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Your professional abilities</p>
                      </div>
                    </div>
                    <SkillDialog 
                      action="add"
                      onSuccess={refreshProfile}
                      triggerComponent={
                        <Button size="sm" variant="ghost" className="h-8 gap-1 dark:text-slate-200 dark:hover:bg-gray-700/70">
                          <Plus className="h-4 w-4" />
                          <span className="text-xs">Add</span>
                        </Button>
                      }
                    />
                  </div>
                  
                  <div className="p-3">
                    {prospect?.skills && prospect.skills.length > 0 ? (
                      <div className="space-y-2">
                        {prospect.skills.map((skill, index) => (
                          <div key={index} className="border border-slate-200 dark:border-gray-700 rounded-md overflow-hidden bg-white dark:bg-gray-800/70">
                            <div className="px-3 py-2 flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <Badge 
                                  variant="outline" 
                                  className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:border-blue-800/70"
                                >
                                  {skill.name}
                                </Badge>
                                <div className="text-xs text-slate-500 dark:text-slate-400">{skill.level}</div>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="h-2 w-24 bg-slate-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                  <div 
                                    className="h-full bg-blue-500 dark:bg-blue-600" 
                                    style={{ width: `${skill.proficiency}%` }}
                                  ></div>
                                </div>
                                <div className="text-xs text-blue-600 dark:text-blue-400 w-8 text-right">{skill.proficiency}%</div>
                                <div className="flex gap-1">
                                  <SkillDialog 
                                    action="update"
                                    skill={skill}
                                    skillIndex={index}
                                    onSuccess={refreshProfile}
                                    triggerComponent={
                                      <Button size="icon" variant="ghost" className="h-6 w-6 dark:hover:bg-gray-700/70">
                                        <Edit className="h-3 w-3" />
                                      </Button>
                                    }
                                  />
                                  <SkillDialog 
                                    action="delete"
                                    skill={skill}
                                    skillIndex={index}
                                    onSuccess={refreshProfile}
                                    triggerComponent={
                                      <Button size="icon" variant="ghost" className="h-6 w-6 text-red-500 dark:text-red-400 dark:hover:bg-gray-700/70">
                                        <Trash2 className="h-3 w-3" />
                                      </Button>
                                    }
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-3 bg-white dark:bg-gray-800/70 rounded-md border border-slate-200 dark:border-gray-700">
                        <p className="text-slate-500 dark:text-slate-400 text-sm">No skills added yet</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="training" className="space-y-6">
              <Card className="border-none shadow-sm dark:shadow-none dark:bg-gray-900/60 dark:border dark:border-gray-800">
                <CardHeader>
                  <CardTitle>Training Performance</CardTitle>
                  <CardDescription>System-generated report of your training progress</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-sm font-medium mb-3">Module Completion</h3>
                    <div className="space-y-4">
                      {training.modules.map((module, index) => (
                        <div key={index}>
                          <div className="flex justify-between text-sm mb-1">
                            <span>{module.title}</span>
                            {module.status === 'completed' ? (
                              <span className="text-green-600 dark:text-green-400 font-medium">Completed</span>
                            ) : (
                              <span className="text-blue-600 dark:text-blue-400 font-medium">{module.progress}%</span>
                            )}
                          </div>
                          <Progress value={module.progress} className="h-2 dark:bg-gray-800" />
                        </div>
                      ))}
                      
                      {training.modules.length === 0 && (
                        <p className="text-muted-foreground text-sm">No modules started yet</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-3">Assessment Performance</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="bg-gray-50 dark:bg-gray-800/80 rounded-lg p-4">
                        <p className="text-xs text-muted-foreground">Total Attempts</p>
                        <p className="text-lg font-medium">{training.assessmentStats?.totalCompleted || 0}</p>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800/80 rounded-lg p-4">
                        <p className="text-xs text-muted-foreground">Modules Assessed</p>
                        <p className="text-lg font-medium">{training.assessmentStats?.totalUniqueAssessments || 0}</p>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800/80 rounded-lg p-4">
                        <p className="text-xs text-muted-foreground">Average Score</p>
                        <p className="text-lg font-medium">{training.assessmentStats?.averageScore || 0}%</p>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800/80 rounded-lg p-4">
                        <p className="text-xs text-muted-foreground">Success Rate</p>
                        <p className="text-lg font-medium">
                          {training.assessmentStats?.moduleScores ?
                            Math.round((training.assessmentStats.moduleScores.filter(m => m.bestScore >= 70).length / training.assessmentStats.moduleScores.length) * 100) : 0}%
                        </p>
                      </div>
                    </div>
                  </div>

                  {training.assessmentStats?.moduleScores && training.assessmentStats.moduleScores.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium mb-3">Best Scores by Module</h3>
                      <div className="space-y-3">
                        {training.assessmentStats.moduleScores.map((moduleScore, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/80 rounded-lg">
                            <div>
                              <p className="font-medium text-sm">{moduleScore.moduleTitle}</p>
                              <p className="text-xs text-muted-foreground">{moduleScore.assessmentTitle}</p>
                              <p className="text-xs text-muted-foreground">
                                {moduleScore.totalAttempts} attempt{moduleScore.totalAttempts !== 1 ? 's' : ''}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className={`text-lg font-bold ${
                                moduleScore.bestScore >= 90 ? 'text-green-600 dark:text-green-400' :
                                moduleScore.bestScore >= 70 ? 'text-blue-600 dark:text-blue-400' :
                                'text-orange-600 dark:text-orange-400'
                              }`}>
                                {moduleScore.bestScore}%
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {moduleScore.bestScore >= 90 ? 'Excellent' :
                                 moduleScore.bestScore >= 70 ? 'Good' : 'Needs Improvement'}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {training.assessmentStats?.recentAssessments && training.assessmentStats.recentAssessments.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium mb-3">Recent Assessment Results</h3>
                      <div className="space-y-3">
                        {training.assessmentStats.recentAssessments.map((assessment, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/80 rounded-lg">
                            <div>
                              <p className="font-medium text-sm">{assessment.title}</p>
                              <p className="text-xs text-muted-foreground">{assessment.module_title}</p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(assessment.completed_at).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className={`text-lg font-bold ${
                                assessment.score >= 90 ? 'text-green-600 dark:text-green-400' :
                                assessment.score >= 70 ? 'text-blue-600 dark:text-blue-400' :
                                'text-orange-600 dark:text-orange-400'
                              }`}>
                                {assessment.score}%
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {assessment.score >= 90 ? 'Excellent' :
                                 assessment.score >= 70 ? 'Good' : 'Needs Improvement'}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Role-Call Training Performance */}
                  <div>
                    <h3 className="text-sm font-medium mb-3 flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Role-Call Training Performance
                    </h3>
                    <DetailedPerformanceAssessment variant="full" />
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="text-sm font-medium mb-2">Strengths</h3>
                      <ul className="list-disc pl-5 text-sm space-y-1 dark:text-gray-300">
                        <li>Excellent communication skills</li>
                        <li>Strong problem-solving abilities</li>
                        <li>Quick to learn new systems</li>
                        <li>Maintains composure under pressure</li>
                      </ul>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium mb-2">Areas for Improvement</h3>
                      <ul className="list-disc pl-5 text-sm space-y-1 dark:text-gray-300">
                        <li>Technical knowledge of CRM systems</li>
                        <li>Call handling time optimization</li>
                        <li>Advanced troubleshooting techniques</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="skills" className="space-y-6">
              <Card className="border-none shadow-sm dark:shadow-none dark:bg-gray-900/60 dark:border dark:border-gray-800">
                <CardHeader>
                  <CardTitle>Certifications</CardTitle>
                  <CardDescription>Your earned certifications</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {training.certificates.length > 0 ? (
                    training.certificates.map((cert, index) => (
                      <div key={index} className="rounded-lg border dark:border-gray-700 dark:bg-gray-800/50 p-4 flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {/* Certificate Thumbnail */}
                          <div className="flex-shrink-0">
                            {cert.certificate_url ? (
                              <div className="w-16 h-12 bg-gray-50 dark:bg-gray-800 rounded-lg overflow-hidden border dark:border-gray-700">
                                {cert.certificate_url.toLowerCase().includes('.pdf') ? (
                                  <div className="w-full h-full flex items-center justify-center bg-red-50 dark:bg-red-900/20">
                                    <FileText className="h-6 w-6 text-red-500" />
                                  </div>
                                ) : (
                                  <img
                                    src={cert.certificate_url}
                                    alt={cert.module_title}
                                    className="w-full h-full object-cover"
                                    onError={(e) => {
                                      const target = e.target as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.parentElement?.querySelector('.fallback-icon') as HTMLElement
                                      if (fallback) {
                                        fallback.style.display = 'flex'
                                      }
                                    }}
                                  />
                                )}
                                <div className="fallback-icon hidden w-full h-full items-center justify-center bg-blue-50 dark:bg-blue-900/20">
                                  <Award className="h-6 w-6 text-blue-500" />
                                </div>
                              </div>
                            ) : (
                              <div className="w-16 h-12 flex items-center justify-center rounded-lg bg-blue-100 text-blue-700 dark:bg-blue-900/60 dark:text-blue-400">
                                <Award className="h-6 w-6" />
                              </div>
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium">{cert.module_title}</h3>
                            <p className="text-sm text-muted-foreground">
                              Completed on {cert.issue_date ? format(new Date(cert.issue_date), 'MMMM d, yyyy') : 'N/A'}
                            </p>
                          </div>
                        </div>
                        {cert.certificate_url && (
                          <Button variant="outline" size="sm" className="dark:bg-gray-800 dark:border-gray-700 dark:hover:bg-gray-700/70" asChild>
                            <a href={cert.certificate_url} target="_blank" rel="noopener noreferrer">
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </a>
                          </Button>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">No certifications earned yet</p>
                      <p className="text-xs mt-1">Complete training modules to earn certificates</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="border-none shadow-sm dark:shadow-none dark:bg-gray-900/60 dark:border dark:border-gray-800">
                <CardHeader>
                  <CardTitle>Badges</CardTitle>
                  <CardDescription>Your earned achievement badges</CardDescription>
                </CardHeader>
                <CardContent>
                  <BadgeGrid
                    badges={training.badges}
                    size="sm"
                    showDescription={false}
                    emptyMessage="No badges earned yet. Complete assessments and training modules to earn your first badge!"
                  />
                </CardContent>
              </Card>

              <Card className="border-none shadow-sm dark:shadow-none dark:bg-gray-900/60 dark:border dark:border-gray-800">
                <CardHeader>
                  <CardTitle>Skill Assessment</CardTitle>
                  <CardDescription>Your skill proficiency levels</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {prospect?.skills && prospect.skills.length > 0 ? (
                    prospect.skills.map((skill, index) => (
                      <div key={index}>
                        <div className="flex justify-between text-sm mb-1">
                          <span>{skill.name}</span>
                          <span className="text-blue-600 dark:text-blue-400 font-medium">{skill.level}</span>
                        </div>
                        <Progress value={skill.proficiency} className="h-2 dark:bg-gray-800" />
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <p className="text-muted-foreground">No skills assessed yet</p>
                    </div>
                  )}
                </CardContent>

              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
