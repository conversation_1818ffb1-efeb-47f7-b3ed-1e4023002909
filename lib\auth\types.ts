/**
 * Authentication Types
 * Centralized type definitions for the Luna authentication system
 */

export interface AuthUser {
  id: string;
  email: string;
  role: 'platform_admin' | 'individual';
  full_name: string;
  avatar_url?: string;
  timezone?: string;
  status: 'active' | 'inactive' | 'pending';
  isPlatformAdmin: boolean;
  employmentRelationships: Array<{
    id: string;
    organization_id: string;
    organization_name: string;
    organization_slug: string;
    department_id: string;
    department_name: string;
    employment_role: 'organization_admin' | 'department_admin' | 'staff_member';
    job_title: string;
    status: string;
  }>;
  currentContext?: {
    type: 'individual' | 'organization';
    organization_id?: string;
    department_id?: string;
    employment_id?: string;
  };
  // Helper properties for role checking
  isOrganizationAdmin: boolean;
  isDepartmentAdmin: boolean;
  hasEmployment: boolean;
}

export interface AuthResult {
  user: AuthUser | null;
  error: string | null;
  status: number;
}

export interface EmploymentRelationship {
  id: string;
  organization_id: string;
  organization_name: string;
  organization_slug: string;
  department_id: string;
  department_name: string;
  employment_role: 'organization_admin' | 'department_admin' | 'staff_member';
  job_title: string;
  status: string;
}

export interface UserContext {
  type: 'individual' | 'organization';
  organization_id?: string;
  department_id?: string;
  employment_id?: string;
}
