# API Migration Guide

## Overview
This guide helps migrate from old scattered API endpoints to new consolidated ones.

## ✅ Legacy Content Cleanup Complete

**Date**: January 27, 2025
**Status**: All BPO references and legacy team-based architecture have been cleaned up and updated to employment-based dual-context architecture.

### What Was Updated:
- ✅ Removed `/app/admin/bpos/` directory and components
- ✅ Updated `/api/admin/teams/` → `/api/admin/departments/`
- ✅ Updated `/api/admin/organization-memberships/` to use employment relationships
- ✅ Updated query optimizer to use employment-based architecture
- ✅ Cleaned up BPO references in components and utilities
- ✅ Updated documentation to reflect current architecture
- ✅ Removed legacy BPO assets from public folder

## File Upload Migration

### Before (Multiple Endpoints)
```typescript
// Old avatar upload
const formData = new FormData();
formData.append('file', file);
const response = await fetch('/api/files/upload', {
  method: 'POST',
  body: formData
});

// Old logo upload
const logoData = new FormData();
logoData.append('file', logoFile);
logoData.append('bpoId', orgId);
const logoResponse = await fetch('/api/upload', {
  method: 'POST',
  body: logoData
});

// Old media upload
const mediaData = new FormData();
mediaData.append('file', mediaFile);
const mediaResponse = await fetch('/api/courses/media/upload', {
  method: 'POST',
  body: mediaData
});
```

### After (Consolidated Endpoint)
```typescript
// New unified upload
const uploadFile = async (file: File, type: 'avatar' | 'resume' | 'document' | 'logo' | 'media', entityId?: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);
  if (entityId) formData.append('entityId', entityId);
  
  const response = await fetch('/api/upload/consolidated', {
    method: 'POST',
    body: formData
  });
  
  return response.json();
};

// Usage examples
await uploadFile(avatarFile, 'avatar');
await uploadFile(logoFile, 'logo', organizationId);
await uploadFile(resumeFile, 'resume');
await uploadFile(mediaFile, 'media');
```

## Profile Management Migration

### Before (Multiple Endpoints)
```typescript
// Old personal info update
await fetch('/api/profile/update-personal-info', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ full_name, bio, location })
});

// Old skills update
await fetch('/api/profile/update-skills', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'add', skillData: newSkill })
});

// Old experience update
await fetch('/api/profile/update-experience', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'add', experience: newExperience })
});

// Old education update
await fetch('/api/profile/update-education', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ action: 'add', education: newEducation })
});
```

### After (Consolidated Endpoint)
```typescript
// New unified profile update
const updateProfile = async (section: string, data: any) => {
  const response = await fetch('/api/profile/consolidated', {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ section, data })
  });
  
  return response.json();
};

// Usage examples
await updateProfile('personal_info', {
  full_name: 'John Doe',
  bio: 'Software Developer',
  location: 'New York, NY'
});

await updateProfile('skills', {
  action: 'add',
  skill: { name: 'React', level: 'Expert' }
});

await updateProfile('experience', {
  action: 'add',
  experience: {
    company: 'Tech Corp',
    position: 'Senior Developer',
    startDate: '2020-01-01',
    endDate: '2023-12-31'
  }
});

await updateProfile('education', {
  action: 'add',
  education: {
    institution: 'University',
    degree: 'Computer Science',
    graduationYear: 2019
  }
});
```

## React Hook Examples

### Consolidated Upload Hook
```typescript
import { useState } from 'react';

export const useFileUpload = () => {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadFile = async (
    file: File, 
    type: 'avatar' | 'resume' | 'document' | 'logo' | 'media',
    entityId?: string
  ) => {
    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      if (entityId) formData.append('entityId', entityId);

      const response = await fetch('/api/upload/consolidated', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Upload failed');
      }

      const result = await response.json();
      return result.file;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
      throw err;
    } finally {
      setUploading(false);
    }
  };

  return { uploadFile, uploading, error };
};
```

### Consolidated Profile Hook
```typescript
import { useState } from 'react';

export const useProfile = () => {
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateProfile = async (section: string, data: any) => {
    setUpdating(true);
    setError(null);

    try {
      const response = await fetch('/api/profile/consolidated', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ section, data })
      });

      if (!response.ok) {
        throw new Error('Update failed');
      }

      const result = await response.json();
      return result.profile;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Update failed');
      throw err;
    } finally {
      setUpdating(false);
    }
  };

  const getProfile = async () => {
    try {
      const response = await fetch('/api/profile/consolidated');
      if (!response.ok) {
        throw new Error('Failed to fetch profile');
      }
      const result = await response.json();
      return result.profile;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Fetch failed');
      throw err;
    }
  };

  return { updateProfile, getProfile, updating, error };
};
```

## Component Migration Examples

### File Upload Component
```typescript
import React from 'react';
import { useFileUpload } from '@/hooks/useFileUpload';

export const FileUploadComponent = ({ 
  type, 
  entityId, 
  onUploadComplete 
}: {
  type: 'avatar' | 'resume' | 'document' | 'logo' | 'media';
  entityId?: string;
  onUploadComplete: (file: any) => void;
}) => {
  const { uploadFile, uploading, error } = useFileUpload();

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const uploadedFile = await uploadFile(file, type, entityId);
      onUploadComplete(uploadedFile);
    } catch (err) {
      console.error('Upload failed:', err);
    }
  };

  return (
    <div>
      <input
        type="file"
        onChange={handleFileChange}
        disabled={uploading}
      />
      {uploading && <p>Uploading...</p>}
      {error && <p className="error">{error}</p>}
    </div>
  );
};
```

## Error Handling

### Standardized Error Response
All consolidated endpoints return consistent error responses:

```typescript
{
  "success": false,
  "error": "Error message",
  "details": "Detailed error information",
  "code": "ERROR_CODE"
}
```

### Error Handling Pattern
```typescript
const handleApiCall = async () => {
  try {
    const response = await fetch('/api/consolidated-endpoint', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || 'Operation failed');
    }

    return result;
  } catch (error) {
    console.error('API call failed:', error);
    // Handle error appropriately
    throw error;
  }
};
```

## Testing Migration

### Before Migration Testing
1. Document current functionality
2. Create test cases for existing endpoints
3. Verify all current features work

### After Migration Testing
1. Test all consolidated endpoints
2. Verify backward compatibility (if maintained)
3. Test error scenarios
4. Performance testing
5. User acceptance testing

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: Revert frontend changes to use old endpoints
2. **Gradual Migration**: Migrate one feature at a time
3. **Feature Flags**: Use feature flags to toggle between old and new endpoints
4. **Monitoring**: Monitor error rates and performance metrics

## Benefits Verification

After migration, verify these benefits:

- ✅ Reduced number of API calls for related operations
- ✅ Consistent error handling across all features
- ✅ Improved code maintainability
- ✅ Better type safety with unified interfaces
- ✅ Enhanced security with centralized validation
