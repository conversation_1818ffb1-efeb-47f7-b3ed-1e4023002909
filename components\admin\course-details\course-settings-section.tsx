"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Edit, Save, X, Upload, Link, Trash2 } from 'lucide-react'
import { toast } from "sonner"

interface Course {
  id: string
  name: string
  description: string
  slug: string
  level: string
  estimated_duration: number
  status: string
  price: number
  cover_image_url?: string
  preview_video_url?: string
  meta_description?: string
  tags: string[]
  learning_objectives: string[]
  target_audience?: string
  instructor_id?: string
  instructor_bio?: string
  enrollment_count: number
  completion_rate: number
  average_rating: number
  course_complexity: string
  certification_available: boolean
  is_standalone: boolean
  required_software: string[]
  hardware_requirements?: string
  language: string
  accessibility_features: string[]
  prerequisite_courses: string[]
  created_at: string
  updated_at: string
}

interface CourseSettingsSectionProps {
  course: Course
  onUpdate: () => void
}

export function CourseSettingsSection({ course, onUpdate }: CourseSettingsSectionProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    cover_image_url: course.cover_image_url || '',
    preview_video_url: course.preview_video_url || '',
    meta_description: course.meta_description || '',
    target_audience: course.target_audience || '',
    instructor_bio: course.instructor_bio || '',
    hardware_requirements: course.hardware_requirements || '',
    language: course.language,
    tags: course.tags.join(', '),
    learning_objectives: course.learning_objectives.join('\n'),
    required_software: course.required_software.join(', '),
    accessibility_features: course.accessibility_features.join(', '),
    slug: course.slug || ''
  })

  const handleSave = async () => {
    try {
      setLoading(true)
      
      const payload = {
        cover_image_url: formData.cover_image_url?.trim() || null,
        preview_video_url: formData.preview_video_url?.trim() || null,
        meta_description: formData.meta_description?.trim() || null,
        target_audience: formData.target_audience?.trim() || null,
        instructor_bio: formData.instructor_bio?.trim() || null,
        hardware_requirements: formData.hardware_requirements?.trim() || null,
        language: formData.language,
        slug: formData.slug?.trim() || null,
        tags: formData.tags 
          ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
          : [],
        learning_objectives: formData.learning_objectives 
          ? formData.learning_objectives.split('\n').map(obj => obj.trim()).filter(obj => obj.length > 0)
          : [],
        required_software: formData.required_software 
          ? formData.required_software.split(',').map(sw => sw.trim()).filter(sw => sw.length > 0)
          : [],
        accessibility_features: formData.accessibility_features 
          ? formData.accessibility_features.split(',').map(feat => feat.trim()).filter(feat => feat.length > 0)
          : []
      }

      const response = await fetch(`/api/admin/courses/${course.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update course settings')
      }

      toast.success('Course settings updated successfully')
      setIsEditing(false)
      onUpdate()
      
    } catch (error: any) {
      console.error('Error updating course settings:', error)
      toast.error(error.message || 'Failed to update course settings')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      cover_image_url: course.cover_image_url || '',
      preview_video_url: course.preview_video_url || '',
      meta_description: course.meta_description || '',
      target_audience: course.target_audience || '',
      instructor_bio: course.instructor_bio || '',
      hardware_requirements: course.hardware_requirements || '',
      language: course.language,
      tags: course.tags.join(', '),
      learning_objectives: course.learning_objectives.join('\n'),
      required_software: course.required_software.join(', '),
      accessibility_features: course.accessibility_features.join(', '),
      slug: course.slug || ''
    })
    setIsEditing(false)
  }

  return (
    <div className="space-y-6">
      {/* Media & URLs */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Media & URLs</CardTitle>
            <div className="flex items-center space-x-2">
              {isEditing ? (
                <>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleCancel}
                    disabled={loading}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={handleSave}
                    disabled={loading}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {loading ? 'Saving...' : 'Save'}
                  </Button>
                </>
              ) : (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setIsEditing(true)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {isEditing ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cover-image">Cover Image URL</Label>
                <div className="flex space-x-2">
                  <Input
                    id="cover-image"
                    value={formData.cover_image_url}
                    onChange={(e) => setFormData({ ...formData, cover_image_url: e.target.value })}
                    placeholder="https://example.com/image.jpg"
                  />
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="preview-video">Preview Video URL</Label>
                <div className="flex space-x-2">
                  <Input
                    id="preview-video"
                    value={formData.preview_video_url}
                    onChange={(e) => setFormData({ ...formData, preview_video_url: e.target.value })}
                    placeholder="https://youtube.com/watch?v=..."
                  />
                  <Button variant="outline" size="sm">
                    <Link className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData({ ...formData, slug: e.target.value })}
                  placeholder="course-url-slug"
                />
                <p className="text-xs text-muted-foreground">
                  Used in the course URL. Leave empty to auto-generate from course name.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="meta-description">SEO Meta Description</Label>
                <Textarea
                  id="meta-description"
                  value={formData.meta_description}
                  onChange={(e) => setFormData({ ...formData, meta_description: e.target.value })}
                  placeholder="Brief description for search engines (150-160 characters)"
                  rows={3}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Cover Image</h4>
                {course.cover_image_url ? (
                  <div className="flex items-center space-x-3">
                    <img 
                      src={course.cover_image_url} 
                      alt="Course cover" 
                      className="w-20 h-12 object-cover rounded border"
                    />
                    <p className="text-sm text-muted-foreground truncate">{course.cover_image_url}</p>
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No cover image set</p>
                )}
              </div>

              <div>
                <h4 className="font-medium mb-2">Preview Video</h4>
                <p className="text-sm text-muted-foreground">
                  {course.preview_video_url || 'No preview video set'}
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">URL Slug</h4>
                <p className="text-sm text-muted-foreground">
                  {course.slug || 'Auto-generated from course name'}
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">SEO Description</h4>
                <p className="text-sm text-muted-foreground">
                  {course.meta_description || 'No SEO description set'}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Learning Details */}
      <Card>
        <CardHeader>
          <CardTitle>Learning Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {isEditing ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="target-audience">Target Audience</Label>
                <Textarea
                  id="target-audience"
                  value={formData.target_audience}
                  onChange={(e) => setFormData({ ...formData, target_audience: e.target.value })}
                  placeholder="Who is this course designed for?"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="learning-objectives">Learning Objectives</Label>
                <Textarea
                  id="learning-objectives"
                  value={formData.learning_objectives}
                  onChange={(e) => setFormData({ ...formData, learning_objectives: e.target.value })}
                  placeholder="Enter each objective on a new line"
                  rows={5}
                />
                <p className="text-xs text-muted-foreground">
                  Enter each learning objective on a separate line
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="instructor-bio">Instructor Bio</Label>
                <Textarea
                  id="instructor-bio"
                  value={formData.instructor_bio}
                  onChange={(e) => setFormData({ ...formData, instructor_bio: e.target.value })}
                  placeholder="Brief instructor biography for this course"
                  rows={4}
                />
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Target Audience</h4>
                <p className="text-sm text-muted-foreground">
                  {course.target_audience || 'Not specified'}
                </p>
              </div>

              {course.learning_objectives && course.learning_objectives.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Learning Objectives</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    {course.learning_objectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary">•</span>
                        {objective}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div>
                <h4 className="font-medium mb-2">Instructor Bio</h4>
                <p className="text-sm text-muted-foreground">
                  {course.instructor_bio || 'No instructor bio provided'}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Technical Requirements */}
      <Card>
        <CardHeader>
          <CardTitle>Technical Requirements</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {isEditing ? (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="required-software">Required Software</Label>
                <Input
                  id="required-software"
                  value={formData.required_software}
                  onChange={(e) => setFormData({ ...formData, required_software: e.target.value })}
                  placeholder="e.g., Microsoft Office, Adobe Photoshop, Python"
                />
                <p className="text-xs text-muted-foreground">
                  Separate multiple items with commas
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="hardware-requirements">Hardware Requirements</Label>
                <Textarea
                  id="hardware-requirements"
                  value={formData.hardware_requirements}
                  onChange={(e) => setFormData({ ...formData, hardware_requirements: e.target.value })}
                  placeholder="Minimum system requirements, special equipment needed, etc."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">Primary Language</Label>
                <Select value={formData.language} onValueChange={(value) => setFormData({ ...formData, language: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="it">Italian</SelectItem>
                    <SelectItem value="pt">Portuguese</SelectItem>
                    <SelectItem value="zh">Chinese</SelectItem>
                    <SelectItem value="ja">Japanese</SelectItem>
                    <SelectItem value="ko">Korean</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="accessibility-features">Accessibility Features</Label>
                <Input
                  id="accessibility-features"
                  value={formData.accessibility_features}
                  onChange={(e) => setFormData({ ...formData, accessibility_features: e.target.value })}
                  placeholder="e.g., Closed captions, Screen reader compatible, Keyboard navigation"
                />
                <p className="text-xs text-muted-foreground">
                  Separate multiple features with commas
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Required Software</h4>
                {course.required_software && course.required_software.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {course.required_software.map((software, index) => (
                      <Badge key={index} variant="outline">{software}</Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No special software required</p>
                )}
              </div>

              <div>
                <h4 className="font-medium mb-2">Hardware Requirements</h4>
                <p className="text-sm text-muted-foreground">
                  {course.hardware_requirements || 'Standard computer/device'}
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-2">Primary Language</h4>
                <Badge variant="outline">{course.language.toUpperCase()}</Badge>
              </div>

              <div>
                <h4 className="font-medium mb-2">Accessibility Features</h4>
                {course.accessibility_features && course.accessibility_features.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {course.accessibility_features.map((feature, index) => (
                      <Badge key={index} variant="outline">{feature}</Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">Standard accessibility</p>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tags & Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Tags & Categories</CardTitle>
        </CardHeader>
        <CardContent>
          {isEditing ? (
            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                value={formData.tags}
                onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                placeholder="e.g., programming, web development, javascript, react"
              />
              <p className="text-xs text-muted-foreground">
                Separate tags with commas. Used for search and categorization.
              </p>
            </div>
          ) : (
            <div>
              <h4 className="font-medium mb-2">Course Tags</h4>
              {course.tags && course.tags.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {course.tags.map((tag, index) => (
                    <Badge key={index} variant="outline">{tag}</Badge>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No tags assigned</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
