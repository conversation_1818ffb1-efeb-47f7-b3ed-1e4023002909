-- =============================================================================
-- LUNA DATABASE QUERY OPTIMIZATION INDEXES
-- Phase 3B: Database Query Optimization
-- =============================================================================

-- Performance indexes for frequently queried tables

-- =============================================================================
-- AUTHENTICATION & USER MANAGEMENT INDEXES
-- =============================================================================

-- Index for user authentication queries
CREATE INDEX IF NOT EXISTS idx_users_email_status 
ON users(email, status) 
WHERE status = 'active';

-- Index for user role queries
CREATE INDEX IF NOT EXISTS idx_users_role_status 
ON users(role, status) 
WHERE status = 'active';

-- =============================================================================
-- EMPLOYMENT RELATIONSHIPS INDEXES
-- =============================================================================

-- Primary index for employment relationship queries (most common)
CREATE INDEX IF NOT EXISTS idx_employment_relationships_user_status 
ON employment_relationships(user_id, status) 
WHERE status = 'active';

-- Index for organization-based queries
CREATE INDEX IF NOT EXISTS idx_employment_relationships_org_status 
ON employment_relationships(organization_id, status) 
WHERE status = 'active';

-- Index for department-based queries
CREATE INDEX IF NOT EXISTS idx_employment_relationships_dept_status 
ON employment_relationships(department_id, status) 
WHERE status = 'active';

-- Composite index for role-based queries
CREATE INDEX IF NOT EXISTS idx_employment_relationships_role_org 
ON employment_relationships(role, organization_id, status) 
WHERE status = 'active';

-- =============================================================================
-- USER CONTEXT INDEXES
-- =============================================================================

-- Primary index for user context queries
CREATE INDEX IF NOT EXISTS idx_user_contexts_user_id 
ON user_contexts(user_id);

-- Index for active context queries
CREATE INDEX IF NOT EXISTS idx_user_contexts_active_context 
ON user_contexts(active_context, active_organization_id) 
WHERE active_context = 'organization';

-- =============================================================================
-- TRAINING & PROGRESS INDEXES
-- =============================================================================

-- Index for training module queries
CREATE INDEX IF NOT EXISTS idx_training_modules_status_order 
ON training_modules(status, required_order) 
WHERE status = 'published';

-- Index for user progress queries
CREATE INDEX IF NOT EXISTS idx_user_training_data_user_context 
ON user_training_data(user_id, training_context);

-- Index for lesson queries
CREATE INDEX IF NOT EXISTS idx_lessons_module_order 
ON lessons(module_id, order_index);

-- =============================================================================
-- APPLICATION & JOB POSTING INDEXES
-- =============================================================================

-- Index for job posting queries
CREATE INDEX IF NOT EXISTS idx_job_postings_status_created 
ON job_postings(status, created_at DESC) 
WHERE status IN ('published', 'open');

-- Index for organization job postings
CREATE INDEX IF NOT EXISTS idx_job_postings_org_status 
ON job_postings(organization_id, status) 
WHERE status IN ('published', 'open');

-- Index for application queries
CREATE INDEX IF NOT EXISTS idx_applications_prospect_status 
ON applications(prospect_id, status, submitted_at DESC);

-- Index for job-based application queries
CREATE INDEX IF NOT EXISTS idx_applications_job_status 
ON applications(job_id, status, submitted_at DESC);

-- =============================================================================
-- ORGANIZATION & DEPARTMENT INDEXES
-- =============================================================================

-- Index for organization queries
CREATE INDEX IF NOT EXISTS idx_organizations_status_name 
ON organizations(status, name) 
WHERE status = 'active';

-- Index for organization slug queries
CREATE INDEX IF NOT EXISTS idx_organizations_slug 
ON organizations(slug) 
WHERE status = 'active';

-- Index for department queries
CREATE INDEX IF NOT EXISTS idx_departments_org_status 
ON departments(organization_id, status) 
WHERE status = 'active';

-- =============================================================================
-- FILE & MEDIA INDEXES
-- =============================================================================

-- Index for user file queries
CREATE INDEX IF NOT EXISTS idx_files_user_type 
ON files(user_id, file_type, created_at DESC);

-- Index for organization file queries
CREATE INDEX IF NOT EXISTS idx_files_org_type 
ON files(organization_id, file_type, created_at DESC) 
WHERE organization_id IS NOT NULL;

-- =============================================================================
-- AUDIT & LOGGING INDEXES
-- =============================================================================

-- Index for audit log queries
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_action 
ON audit_logs(user_id, action, created_at DESC);

-- Index for security event queries
CREATE INDEX IF NOT EXISTS idx_security_events_type_timestamp 
ON security_events(event_type, created_at DESC);

-- =============================================================================
-- PERFORMANCE MONITORING INDEXES
-- =============================================================================

-- Index for API performance monitoring
CREATE INDEX IF NOT EXISTS idx_api_performance_endpoint_timestamp 
ON api_performance_logs(endpoint, created_at DESC);

-- =============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =============================================================================

-- Complex query for user dashboard data
CREATE INDEX IF NOT EXISTS idx_user_dashboard_composite 
ON employment_relationships(user_id, organization_id, role, status) 
WHERE status = 'active';

-- Complex query for organization analytics
CREATE INDEX IF NOT EXISTS idx_org_analytics_composite 
ON applications(job_id, status, submitted_at DESC) 
INCLUDE (prospect_id);

-- =============================================================================
-- PARTIAL INDEXES FOR SPECIFIC USE CASES
-- =============================================================================

-- Index for active platform admins
CREATE INDEX IF NOT EXISTS idx_users_platform_admin 
ON users(id, email, full_name) 
WHERE role = 'platform_admin' AND status = 'active';

-- Index for organization admins
CREATE INDEX IF NOT EXISTS idx_employment_org_admin 
ON employment_relationships(user_id, organization_id) 
WHERE role = 'organization_admin' AND status = 'active';

-- =============================================================================
-- QUERY OPTIMIZATION COMMENTS
-- =============================================================================

COMMENT ON INDEX idx_employment_relationships_user_status IS 
'Optimizes authentication queries for user employment relationships';

COMMENT ON INDEX idx_user_contexts_user_id IS 
'Optimizes user context switching queries';

COMMENT ON INDEX idx_training_modules_status_order IS 
'Optimizes training module listing queries';

COMMENT ON INDEX idx_applications_prospect_status IS 
'Optimizes user application history queries';

COMMENT ON INDEX idx_job_postings_org_status IS 
'Optimizes organization job posting queries';

-- =============================================================================
-- INDEX MAINTENANCE
-- =============================================================================

-- Analyze tables to update statistics after index creation
ANALYZE users;
ANALYZE employment_relationships;
ANALYZE user_contexts;
ANALYZE training_modules;
ANALYZE applications;
ANALYZE job_postings;
ANALYZE organizations;
ANALYZE departments;
