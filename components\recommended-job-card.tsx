import { MapPin, DollarSign, Clock } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"

type RecommendedJobCardProps = {
  title: string
  company: string
  logo: string
  logoColor: "blue" | "green" | "purple" | "amber" | "red"
  location: string
  salary: string
  postedDays: number
  isNew?: boolean
}

export function RecommendedJobCard({
  title,
  company,
  logo,
  logoColor,
  location,
  salary,
  postedDays,
  isNew = false,
}: RecommendedJobCardProps) {
  const colorMap = {
    blue: "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400",
    green: "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400",
    purple: "bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400",
    amber: "bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400",
    red: "bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400",
  }

  return (
    <div className="rounded-lg border bg-card dark:bg-gray-800/50 p-3 transition-all duration-200 hover:shadow-sm">
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10 rounded-md">
          <AvatarFallback className={`rounded-md ${colorMap[logoColor]}`}>{logo}</AvatarFallback>
        </Avatar>
        <div>
          <div className="flex items-center gap-2">
            <h3 className="font-medium">{title}</h3>
            {isNew && (
              <Badge className="bg-blue-100 text-blue-600 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400">
                New
              </Badge>
            )}
          </div>
          <div className="text-sm text-muted-foreground">{company}</div>
        </div>
      </div>
      <div className="mt-3 flex flex-wrap items-center gap-x-3 gap-y-1 text-xs text-muted-foreground">
        <div className="flex items-center">
          <MapPin className="mr-1 h-3 w-3" />
          {location}
        </div>
        <div className="flex items-center">
          <DollarSign className="mr-1 h-3 w-3" />
          {salary}
        </div>
        <div className="flex items-center">
          <Clock className="mr-1 h-3 w-3" />
          Posted {postedDays} {postedDays === 1 ? "day" : "days"} ago
        </div>
      </div>
      <div className="mt-3">
        <Button variant="outline" size="sm" className="w-full">
          View Job
        </Button>
      </div>
    </div>
  )
}
