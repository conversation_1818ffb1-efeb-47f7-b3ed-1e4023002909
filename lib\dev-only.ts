/**
 * Development-only utilities
 * Provides middleware to gate debug/test endpoints behind development environment
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * Middleware to restrict access to development-only endpoints
 */
export function withDevOnly(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>
) {
  return async function (request: NextRequest, ...args: any[]): Promise<NextResponse> {
    // Only allow in development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { 
          error: 'Not Found',
          message: 'This endpoint is only available in development mode'
        },
        { status: 404 }
      );
    }

    return handler(request, ...args);
  };
}

/**
 * Check if we're in development mode
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

// Removed unused devLog and devWarn functions
// These were not being used anywhere in the codebase
