import { Award, Download, ExternalLink, Upload } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"

export function CertificatesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Certificates</h1>
        <p className="text-muted-foreground">View and manage your earned and uploaded certificates</p>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
            <Award className="h-5 w-5 text-blue-700" />
          </div>
          <div>
            <p className="font-medium">Total Certificates</p>
            <p className="text-2xl font-bold">5</p>
          </div>
        </div>
        <Button>
          <Upload className="mr-2 h-4 w-4" />
          Upload Certificate
        </Button>
      </div>

      <Tabs defaultValue="earned">
        <TabsList>
          <TabsTrigger value="earned">Earned Certificates</TabsTrigger>
          <TabsTrigger value="uploaded">Uploaded Certificates</TabsTrigger>
        </TabsList>
        <TabsContent value="earned" className="space-y-4 pt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Earned</Badge>
                  <div className="text-sm text-muted-foreground">May 5, 2025</div>
                </div>
                <CardTitle className="mt-2">Introduction to Professional Services</CardTitle>
                <CardDescription>Fundamentals of Professional Service Excellence</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                    <Award className="h-4 w-4 text-blue-700" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Issued by</p>
                    <p className="text-muted-foreground">BPO Training Platform</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex gap-2">
                <Button variant="outline" className="flex-1">
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
                <Button variant="outline" className="flex-1">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">In Progress</Badge>
                  <div className="text-sm text-muted-foreground">68% Complete</div>
                </div>
                <CardTitle className="mt-2">Customer Service Professional</CardTitle>
                <CardDescription>Comprehensive customer service certification</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                    <Award className="h-4 w-4 text-blue-700" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Issued by</p>
                    <p className="text-muted-foreground">BPO Training Platform</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full">Continue Training</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="uploaded" className="space-y-4 pt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Uploaded</Badge>
                  <div className="text-sm text-muted-foreground">Apr 15, 2025</div>
                </div>
                <CardTitle className="mt-2">Communication Skills</CardTitle>
                <CardDescription>Professional communication certification</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                    <Award className="h-4 w-4 text-blue-700" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Issued by</p>
                    <p className="text-muted-foreground">State University</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex gap-2">
                <Button variant="outline" className="flex-1">
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
                <Button variant="outline" className="flex-1">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View
                </Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between">
                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Uploaded</Badge>
                  <div className="text-sm text-muted-foreground">Mar 10, 2025</div>
                </div>
                <CardTitle className="mt-2">Microsoft Office Specialist</CardTitle>
                <CardDescription>Proficiency in Microsoft Office applications</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                    <Award className="h-4 w-4 text-blue-700" />
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Issued by</p>
                    <p className="text-muted-foreground">Microsoft</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex gap-2">
                <Button variant="outline" className="flex-1">
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
                <Button variant="outline" className="flex-1">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Certificate Information</CardTitle>
          <CardDescription>Learn about our certification process</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Earning Certificates</h3>
            <p className="mt-2 text-sm">
              Certificates are awarded upon successful completion of training modules and passing the required
              assessments. Most certificates require a minimum score of 70% on all assessments.
            </p>
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Certificate Verification</h3>
            <p className="mt-2 text-sm">
              All certificates issued by the BPO Training Platform include a unique verification code. Employers can
              verify the authenticity of your certificates using our online verification system.
            </p>
            <Button variant="outline" size="sm" className="mt-3">
              Verification System
            </Button>
          </div>
          <div className="rounded-lg border p-4">
            <h3 className="font-medium">Uploading External Certificates</h3>
            <p className="mt-2 text-sm">
              You can upload certificates earned from other institutions to keep all your credentials in one place.
              Supported file formats include PDF, JPG, and PNG.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
