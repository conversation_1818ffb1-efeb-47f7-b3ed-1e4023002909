/**
 * Secure File Upload API Endpoint
 * Demonstrates comprehensive file upload security
 */

import { NextRequest, NextResponse } from 'next/server';
import { withApiErrorHandler } from '@/lib/api-error-handler';
import { getAuthenticatedUser } from '@/lib/auth';
import { withApiSecurity, RATE_LIMITS, validateFileUpload, FILE_UPLOAD_SECURITY } from '@/lib/api-security';
import { withApiVersioning, createVersionedResponse } from '@/lib/api-versioning';
import { securityAudit, SecurityEventType } from '@/lib/security-audit';
import { createAdminClient } from '@/lib/supabase-server';
import { v4 as uuidv4 } from 'uuid';

/**
 * POST /api/v1/upload
 * Secure file upload with comprehensive validation
 */
export const POST = withApiSecurity(
  withApiVersioning(
    withApiErrorHandler(async (request: NextRequest, version: string) => {
      // Authenticate user
      const authResult = await getAuthenticatedUser();
      if (!authResult.user) {
        securityAudit.logApiSecurityEvent(
          SecurityEventType.UNAUTHORIZED_ACCESS,
          request,
          'Attempted file upload without authentication'
        );
        throw new Error('Authentication required');
      }

      const user = authResult.user;
      
      // Parse form data
      let formData: FormData;
      try {
        formData = await request.formData();
      } catch (error) {
        securityAudit.logApiSecurityEvent(
          SecurityEventType.MALFORMED_REQUEST,
          request,
          'Failed to parse multipart form data',
          { error: String(error) }
        );
        throw new Error('Invalid form data');
      }

      const file = formData.get('file') as File;
      const fileType = formData.get('fileType') as string;
      const category = formData.get('category') as string;

      // Validate required fields
      if (!file) {
        throw new Error('No file provided');
      }

      if (!fileType || !['avatar', 'resume', 'video', 'document'].includes(fileType)) {
        throw new Error('Invalid file type specified');
      }

      if (!category || !['image', 'document', 'video'].includes(category)) {
        throw new Error('Invalid file category specified');
      }

      // Enhanced file validation
      const fileValidation = validateFileUpload(file, category as keyof typeof FILE_UPLOAD_SECURITY.ALLOWED_TYPES);
      if (!fileValidation.isValid) {
        securityAudit.logFileUploadEvent(
          SecurityEventType.FILE_UPLOAD_BLOCKED,
          file.name,
          file.size,
          file.type,
          user.id,
          request,
          { 
            reason: fileValidation.error,
            category,
            fileType
          }
        );
        throw new Error(fileValidation.error);
      }

      // Additional security checks
      if (await isFileContentSuspicious(file)) {
        securityAudit.logFileUploadEvent(
          SecurityEventType.MALICIOUS_FILE_DETECTED,
          file.name,
          file.size,
          file.type,
          user.id,
          request,
          { category, fileType }
        );
        throw new Error('File content appears to be malicious');
      }

      // Get user profile
      const supabase = createAdminClient();
      const { data: profile, error: profileError } = await supabase
        .from('prospects')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (profileError || !profile) {
        throw new Error('User profile not found');
      }

      // Generate secure filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
      const sanitizedOriginalName = file.name
        .replace(/[^a-zA-Z0-9.-]/g, '_')
        .substring(0, 50);
      
      const secureFileName = `${user.id}_${uuidv4()}_${timestamp}_${sanitizedOriginalName}`;

      // Determine storage configuration
      const storageConfig = getStorageConfig(fileType);
      
      // Convert file to buffer
      const fileBuffer = Buffer.from(await file.arrayBuffer());

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(storageConfig.bucket)
        .upload(secureFileName, fileBuffer, {
          cacheControl: '3600',
          upsert: false, // Prevent overwriting
          contentType: file.type || 'application/octet-stream'
        });

      if (uploadError) {
        securityAudit.logFileUploadEvent(
          SecurityEventType.FILE_UPLOAD_BLOCKED,
          file.name,
          file.size,
          file.type,
          user.id,
          request,
          { 
            reason: uploadError.message,
            storageError: true
          }
        );
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(storageConfig.bucket)
        .getPublicUrl(secureFileName);

      // Update user profile if needed
      if (storageConfig.profileColumn) {
        const updateData = { [storageConfig.profileColumn]: publicUrl };
        
        const { error: updateError } = await supabase
          .from('prospects')
          .update(updateData)
          .eq('id', profile.id);

        if (updateError) {
          // Log but don't fail - file was uploaded successfully
          console.error('Profile update error:', updateError);
        }

        // Update auth user avatar if it's an avatar upload
        if (fileType === 'avatar') {
          await supabase
            .from('users')
            .update({ avatar_url: publicUrl })
            .eq('id', user.id);
        }
      }

      // Log successful upload
      securityAudit.logFileUploadEvent(
        SecurityEventType.FILE_UPLOAD_SUCCESS,
        file.name,
        file.size,
        file.type,
        user.id,
        request,
        {
          category,
          fileType,
          publicUrl,
          bucket: storageConfig.bucket
        }
      );

      // Return success response
      return NextResponse.json(
        createVersionedResponse(
          {
            message: 'File uploaded successfully',
            file: {
              name: file.name,
              size: file.size,
              type: file.type,
              url: publicUrl,
              uploadedAt: new Date().toISOString()
            },
            security: {
              validated: true,
              scanned: true,
              safe: true
            }
          },
          version
        ),
        { status: 201 }
      );
    })
  ),
  {
    rateLimit: RATE_LIMITS.UPLOAD,
    maxRequestSize: 100 * 1024 * 1024, // 100MB max for file uploads
    skipInputSanitization: true // Skip for multipart form data
  }
);

/**
 * Get storage configuration for file type
 */
function getStorageConfig(fileType: string): {
  bucket: string;
  profileColumn?: string;
} {
  switch (fileType) {
    case 'avatar':
      return { bucket: 'profile-images', profileColumn: 'profile_image' };
    case 'resume':
      return { bucket: 'resumes', profileColumn: 'resume_url' };
    case 'video':
      return { bucket: 'profile-videos', profileColumn: 'intro_video_url' };
    case 'document':
      return { bucket: 'documents' };
    default:
      throw new Error('Invalid file type');
  }
}

/**
 * Check if file content appears suspicious
 * This is a basic implementation - in production, integrate with virus scanning services
 */
async function isFileContentSuspicious(file: File): Promise<boolean> {
  try {
    // Read first few bytes to check for suspicious patterns
    const buffer = await file.slice(0, 1024).arrayBuffer();
    const bytes = new Uint8Array(buffer);
    
    // Check for executable file signatures
    const suspiciousSignatures = [
      [0x4D, 0x5A], // PE executable (MZ)
      [0x7F, 0x45, 0x4C, 0x46], // ELF executable
      [0xCA, 0xFE, 0xBA, 0xBE], // Mach-O executable
      [0x50, 0x4B, 0x03, 0x04], // ZIP (could contain executables)
    ];
    
    for (const signature of suspiciousSignatures) {
      if (bytes.length >= signature.length) {
        let matches = true;
        for (let i = 0; i < signature.length; i++) {
          if (bytes[i] !== signature[i]) {
            matches = false;
            break;
          }
        }
        if (matches) {
          return true;
        }
      }
    }
    
    // Check for suspicious strings in text files
    if (file.type.startsWith('text/') || file.type === 'application/javascript') {
      const text = await file.text();
      const suspiciousPatterns = [
        /<script[^>]*>/i,
        /javascript:/i,
        /eval\s*\(/i,
        /document\.write/i,
        /window\.location/i
      ];
      
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(text)) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    // If we can't scan the file, err on the side of caution
    console.error('File content scanning error:', error);
    return true;
  }
}

/**
 * GET /api/v1/upload/status
 * Check upload service status
 */
export const GET = withApiSecurity(
  withApiVersioning(
    withApiErrorHandler(async (request: NextRequest, version: string) => {
      // This endpoint can be public for health checks
      return NextResponse.json(
        createVersionedResponse(
          {
            status: 'operational',
            maxFileSize: {
              image: `${FILE_UPLOAD_SECURITY.MAX_SIZES.image / (1024 * 1024)}MB`,
              document: `${FILE_UPLOAD_SECURITY.MAX_SIZES.document / (1024 * 1024)}MB`,
              video: `${FILE_UPLOAD_SECURITY.MAX_SIZES.video / (1024 * 1024)}MB`,
              audio: `${FILE_UPLOAD_SECURITY.MAX_SIZES.audio / (1024 * 1024)}MB`
            },
            allowedTypes: FILE_UPLOAD_SECURITY.ALLOWED_TYPES,
            securityFeatures: [
              'File type validation',
              'Size limits',
              'Content scanning',
              'Malware detection',
              'Rate limiting',
              'Authentication required'
            ]
          },
          version
        )
      );
    })
  ),
  {
    rateLimit: RATE_LIMITS.PUBLIC,
    maxRequestSize: 1024 // Small size for status checks
  }
);
