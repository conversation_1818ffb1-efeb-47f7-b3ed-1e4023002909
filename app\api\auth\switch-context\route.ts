import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth';
import { createApiClient } from '@/lib/supabase-server';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { contextType, organizationId } = await request.json();

    // Validate input
    if (!contextType || !['individual', 'organization'].includes(contextType)) {
      return NextResponse.json(
        { error: 'Invalid context type. Must be "individual" or "organization"' },
        { status: 400 }
      );
    }

    if (contextType === 'organization' && !organizationId) {
      return NextResponse.json(
        { error: 'Organization ID is required for organization context' },
        { status: 400 }
      );
    }

    const supabase = await createApiClient();

    // If switching to organization context, validate employment relationship
    if (contextType === 'organization') {
      const { data: employment, error: employmentError } = await supabase
        .from('employment_relationships')
        .select('id, role, status')
        .eq('user_id', authResult.user.id)
        .eq('organization_id', organizationId)
        .eq('status', 'active')
        .single();

      if (employmentError || !employment) {
        return NextResponse.json(
          { error: 'You are not employed by this organization' },
          { status: 403 }
        );
      }
    }

    // Update or create user context
    const { error: contextError } = await supabase
      .from('user_contexts')
      .upsert({
        user_id: authResult.user.id,
        active_context: contextType,
        active_organization_id: contextType === 'organization' ? organizationId : null,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id'
      });

    if (contextError) {
      console.error('Error updating user context:', contextError);
      return NextResponse.json(
        { error: 'Failed to update context' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      context: {
        type: contextType,
        organization_id: contextType === 'organization' ? organizationId : null,
      },
    });

  } catch (error) {
    console.error('Context switch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const supabase = await createApiClient();

    // Get current user context
    const { data: contextData, error: contextError } = await supabase
      .from('user_contexts')
      .select('active_context, active_organization_id')
      .eq('user_id', authResult.user.id)
      .single();

    if (contextError && contextError.code !== 'PGRST116') {
      console.error('Error fetching user context:', contextError);
      return NextResponse.json(
        { error: 'Failed to fetch context' },
        { status: 500 }
      );
    }

    // Get available organizations through employment relationships
    const { data: employments, error: employmentError } = await supabase
      .from('employment_relationships')
      .select(`
        organization_id,
        role,
        status,
        organizations!inner(name)
      `)
      .eq('user_id', authResult.user.id)
      .eq('status', 'active');

    const availableOrganizations = employments?.map(e => ({
      organization_id: e.organization_id,
      organization_name: (e.organizations as any)?.name,
      role: e.role,
      status: e.status
    })) || [];

    return NextResponse.json({
      currentContext: contextData ? {
        type: contextData.active_context,
        organization_id: contextData.active_organization_id,
      } : null,
      availableOrganizations,
      canSwitchToIndividual: authResult.user.role === 'individual' || authResult.user.isPlatformAdmin,
    });

  } catch (error) {
    console.error('Get context error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
