{"name": "luna-skills-platform", "version": "1.0.0", "description": "Universal skills gap assessment and training platform with employment-based dual-context architecture", "private": true, "author": "Luna Development Team", "repository": {"type": "git", "url": "https://github.com/JennineHamilton/luna.git"}, "homepage": "https://github.com/JennineHamilton/luna#readme", "keywords": ["skills-assessment", "training-platform", "multi-tenant", "employment-based", "nextjs", "supabase", "typescript"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next && npm cache clean --force", "dev:fast": "next dev --turbo"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "critters": "^0.0.23", "date-fns": "^2.30.0", "embla-carousel-react": "^8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-day-picker": "^9.1.3", "react-dom": "^19", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-pdf": "^10.0.1", "react-player": "^3.3.1", "recharts": "^2.15.4", "sonner": "^1.7.1", "swiper": "^11.2.10", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^22.15.18", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^8.3.4", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^9.28.0", "postcss": "^8.5.3", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "typescript": "^5"}}