import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from '@supabase/supabase-js'
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"
import { ArrowLeft, BookOpen, Clock, CheckCircle, FileText } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { LessonCompleteButton } from "@/components/training/LessonCompleteButton"
import { ModuleSidebar } from "@/components/training/ModuleSidebar"
import { VideoPlayer } from "@/components/training/VideoPlayer"
import { RichTextDisplay } from "@/components/ui/rich-text-display"
import { Separator } from "@/components/ui/separator"
import { PageTitle } from "@/components/page-title"

// Create admin client for operations that need to bypass RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ""
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || ""
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey)

export default async function LessonDetailsPage({
  params,
}: {
  params: { moduleId: string; lessonId: string }
}) {
  const { moduleId, lessonId } = params;
  const cookiesStore = cookies();
  const supabase = createServerComponentClient<Database>({ cookies: () => cookiesStore });
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }
  
  // Get user data
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("id, role")
    .eq("id", session.user.id)
    .single()
  
  if (userError || !userData || userData.role !== "prospect") {
    redirect("/unauthorized")
  }
  
  // Get the prospect's profile with admin client to bypass RLS
  let prospectData = null
  
  try {
    const { data, error } = await adminClient
      .from("prospects" as any)
      .select("id, training_status")
      .eq("user_id", userData.id)
    
    if (error) {
      throw error
    }
    
    if (data && data.length > 0) {
      prospectData = data[0]
    }
  } catch (error: any) {
    console.error("Error fetching prospect data:", error.message || error)
  }
  
  const prospectId = prospectData?.id
  
  // Fetch the module using admin client
  const { data: module, error: moduleError } = await adminClient
    .from("training_modules")
    .select("*")
    .eq("id", moduleId)
    .eq("status", "published")
    .single()
  
  if (moduleError || !module) {
    redirect("/prospect/training")
  }
  
  // Fetch the lesson using admin client
  let lesson = null;
  let isQuiz = false;
  let quizData = null;
  
  // First, try to fetch as a regular lesson
  const { data: lessonData, error: lessonError } = await adminClient
    .from("lessons")
    .select("*")
    .eq("id", lessonId)
    .eq("module_id", moduleId)
    .single();
  
  if (lessonError) {
    // If not found as a lesson, try as a module assessment (quiz)
    const { data: moduleAssessmentData, error: moduleAssessmentError } = await adminClient
      .from("module_assessments")
      .select(`
        id, 
        order_index, 
        is_required, 
        passing_required,
        assessments:assessment_id (
          id, 
          title, 
          description, 
          duration, 
          total_questions,
          instructions,
          passing_score
        )
      `)
      .eq("id", lessonId)
      .eq("module_id", moduleId)
      .single();
      
    if (moduleAssessmentError) {
      console.error("Error fetching as module assessment:", moduleAssessmentError);
      redirect(`/prospect/training/${moduleId}`);
    }
    
    if (moduleAssessmentData) {
      isQuiz = true;
      quizData = moduleAssessmentData;
      
      // Create a lesson-like object from the quiz data for consistent UI
      lesson = {
        id: moduleAssessmentData.id,
        title: moduleAssessmentData.assessments?.title || "Untitled Quiz",
        description: moduleAssessmentData.assessments?.description || null,
        order_index: moduleAssessmentData.order_index,
        duration_minutes: moduleAssessmentData.assessments?.duration 
          ? parseInt(moduleAssessmentData.assessments.duration) 
          : null,
        lesson_type: "quiz",
        is_required: moduleAssessmentData.is_required,
        passing_required: moduleAssessmentData.passing_required,
        assessment_id: moduleAssessmentData.assessments?.id,
        total_questions: moduleAssessmentData.assessments?.total_questions || 0,
        instructions: moduleAssessmentData.assessments?.instructions,
        passing_score: moduleAssessmentData.assessments?.passing_score
      };
      
      // Fetch quiz questions
      const { data: questionsData, error: questionsError } = await adminClient
        .from("assessment_questions")
        .select("*")
        .eq("assessment_id", lesson.assessment_id)
        .order("order_index", { ascending: true });
        
      if (!questionsError && questionsData) {
        lesson.questions = questionsData;
      }
    }
  } else {
    lesson = lessonData;
  }
  
  if (!lesson) {
    redirect(`/prospect/training/${moduleId}`);
  }
  
  // Log video URL info for debugging
  if (lesson.video_url) {
    console.log(`[Server] Lesson ${lessonId} video URL: ${lesson.video_url}`)
    
    // Validate video URL format
    try {
      new URL(lesson.video_url)
    } catch (e) {
      console.error(`[Server] Invalid video URL format for lesson ${lessonId}: ${lesson.video_url}`)
    }
  } else {
    console.log(`[Server] No video URL for lesson ${lessonId}`)
  }
  
  // Fetch all lessons for this module for the sidebar
  const { data: allLessons, error: allLessonsError } = await adminClient
    .from("lessons")
    .select("id, title, description, duration_minutes, order_index")
    .eq("module_id", moduleId)
    .order("order_index", { ascending: true });
    
  // Also fetch module assessments (quizzes)
  const { data: moduleAssessmentsData, error: moduleAssessmentsError } = await adminClient
    .from("module_assessments")
    .select(`
      id, 
      order_index, 
      is_required, 
      passing_required,
      assessments:assessment_id (
        id, 
        title, 
        description, 
        duration, 
        total_questions
      )
    `)
    .eq("module_id", moduleId)
    .order("order_index", { ascending: true });
    
  if (moduleAssessmentsError) {
    console.error("Error fetching module assessments:", moduleAssessmentsError);
  }
  
  // Convert module assessments to lesson format for the sidebar
  const quizLessons = moduleAssessmentsData ? moduleAssessmentsData.map(moduleAssessment => {
    const assessment = moduleAssessment.assessments as any;
    
    return {
      id: moduleAssessment.id,
      title: assessment?.title || "Untitled Quiz",
      description: assessment?.description || null,
      order_index: moduleAssessment.order_index,
      duration_minutes: assessment?.duration 
        ? parseInt(assessment.duration) 
        : null,
      is_quiz: true,
      lesson_type: "quiz",
      total_questions: assessment?.total_questions || 0
    };
  }) : [];
  
  // Combine regular lessons and quizzes, sort by order_index
  const allContent = [...(allLessons || []), ...quizLessons].sort((a, b) => a.order_index - b.order_index);
  
  // Calculate progress for all lessons to display in the sidebar
  let lessonProgress: Record<string, any> = {}
  
  if (prospectId && allContent && allContent.length > 0) {
    for (const lessonItem of allContent) {
      try {
        // Get activities for this lesson
        const { data: lessonActivities, error: activitiesError } = await adminClient
          .from("activities")
          .select("id")
          .eq("lesson_id", lessonItem.id)
        
        if (activitiesError) {
          throw activitiesError
        }
        
        if (!lessonActivities || lessonActivities.length === 0) {
          // No activities, consider lesson as not started
          lessonProgress[lessonItem.id] = {
            status: "not_started",
            progressPercentage: 0,
          }
          continue
        }
        
        // Get progress records for these activities
        const activityIds = lessonActivities.map(activity => activity.id)
        const { data: progressRecords, error: progressError } = await adminClient
          .from("progress_records")
          .select("*")
          .eq("prospect_id", prospectId)
          .in("activity_id", activityIds)
        
        if (progressError) {
          throw progressError
        }
        
        // Calculate progress percentage
        const completedActivities = progressRecords?.filter(record => record.status === "completed") || []
        const progressPercentage = Math.round((completedActivities.length / lessonActivities.length) * 100)
        
        // Determine lesson status
        let status = "not_started"
        if (progressPercentage === 100) {
          status = "completed"
        } else if (progressPercentage > 0) {
          status = "in_progress"
        }
        
        lessonProgress[lessonItem.id] = {
          status,
          progressPercentage,
        }
      } catch (error: any) {
        console.error(`Error fetching progress for lesson ${lessonItem.id}:`, error.message || error)
        continue
      }
    }
  }
  
  // Format lessons with progress info for the sidebar
  const formattedLessons = allContent?.map(lessonItem => ({
    ...lessonItem,
    progress: lessonProgress[lessonItem.id] || {
      status: "not_started",
      progressPercentage: 0,
    },
  })) || []
  
  return (
    <div className="flex h-full bg-background">
      {/* Sidebar navigation */}
      <ModuleSidebar 
        moduleId={moduleId}
        moduleName={module.title}
        currentLessonId={lessonId}
        lessons={formattedLessons}
      />
      
      {/* Page title updater */}
      <PageTitle title={lesson.title} />
      
      {/* Main content area */}
      <main className="flex-1 overflow-y-auto">
        <div className="max-w-5xl mx-auto px-4 md:px-6 py-6">
          {/* Lesson title and navigation */}
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-2xl font-bold tracking-tight">{lesson.title}</h1>
            
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                asChild
              >
                <Link href={`/prospect/training/${moduleId}`}>
                  <ArrowLeft className="h-4 w-4 mr-1.5" />
                  Back to Module
                </Link>
              </Button>
            </div>
          </div>
          
          <div className="space-y-8">
            {isQuiz ? (
              <Card className="border shadow-sm overflow-hidden bg-card">
                <CardContent className="p-6 md:p-8">
                  {/* Quiz header */}
                  <div className="mb-6 flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="secondary" className="bg-purple-100 text-purple-700 border-purple-200">
                          Quiz
                        </Badge>
                        {lesson.total_questions > 0 && (
                          <span className="text-sm text-muted-foreground">
                            {lesson.total_questions} {lesson.total_questions === 1 ? 'question' : 'questions'}
                          </span>
                        )}
                        {lesson.is_required && (
                          <Badge variant="outline" className="border-blue-200 text-blue-700">
                            Required
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground mb-4">
                        {lesson.duration_minutes && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>Time limit: {lesson.duration_minutes} minutes</span>
                          </div>
                        )}
                        {lesson.passing_score && (
                          <div className="mt-1">
                            Passing score: {lesson.passing_score}%
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Quiz description */}
                  <div className="prose prose-gray prose-sm dark:prose-invert max-w-none">
                    <RichTextDisplay 
                      content={lesson.description || "<p>No description available for this quiz.</p>"} 
                    />
                  </div>
                  
                  {/* Quiz instructions */}
                  {lesson.instructions && (
                    <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md">
                      <h3 className="text-base font-medium mb-2">Instructions</h3>
                      <div className="prose prose-gray prose-sm dark:prose-invert max-w-none">
                        <RichTextDisplay content={lesson.instructions} />
                      </div>
                    </div>
                  )}
                  
                  {/* Start Quiz Button */}
                  <div className="mt-6 flex justify-center">
                    <Button size="lg" className="px-8" asChild>
                      <Link href={`/prospect/training/${moduleId}/quiz/${lessonId}`}>
                        Start Quiz
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <>
                {/* Video section */}
                {lesson.video_url ? (
                  <div className="rounded-xl overflow-hidden shadow-lg">
                    <VideoPlayer 
                      src={lesson.video_url} 
                      title={lesson.title}
                      thumbnailUrl={(lesson as any).thumbnail_url}
                      className="w-full aspect-video bg-black"
                    />
                  </div>
                ) : (
                  <Card className="border shadow-sm overflow-hidden bg-card">
                    <CardContent className="p-6 md:p-8 flex flex-col items-center justify-center text-center">
                      <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium">No Video Available</h3>
                      <p className="text-muted-foreground mt-2">This lesson doesn't include a video component.</p>
                    </CardContent>
                  </Card>
                )}
                
                {/* Lesson content */}
                <Card className="border shadow-sm overflow-hidden bg-card">
                  <CardContent className="p-6 md:p-8">
                    <div className="prose prose-gray prose-sm dark:prose-invert max-w-none">
                      <RichTextDisplay 
                        content={lesson.description || "<p>No description available for this lesson.</p>"} 
                      />
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
            
            {/* Footer */}
            <Separator className="my-6" />
            
            <div className="flex justify-center mt-6">
              <LessonCompleteButton lessonId={lessonId} moduleId={moduleId} />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
} 