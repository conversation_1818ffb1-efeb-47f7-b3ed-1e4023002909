import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth/middleware';
import { createAdminClient } from '@/lib/supabase-admin';

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    // Create admin client
    const adminClient = createAdminClient();
    
    // Get departments with organization and employment information
    const { data: departments, error: departmentsError } = await adminClient
      .from('departments')
      .select(`
        *,
        organization:organizations!departments_organization_id_fkey(
          id,
          name,
          slug
        ),
        employment_relationships(
          id,
          user_id,
          role,
          status,
          user:users!employment_relationships_user_id_fkey(
            id,
            email,
            full_name
          )
        )
      `)
      .order('created_at', { ascending: false });

    if (departmentsError) {
      console.error('Error fetching departments:', departmentsError);
      return NextResponse.json(
        { error: departmentsError.message || 'Failed to fetch departments' },
        { status: 500 }
      );
    }

    // Add employee counts and clean up data
    const departmentsWithCounts = (departments || []).map((department: any) => ({
      ...department,
      _count: {
        employees: department.employment_relationships?.length || 0,
        active_employees: department.employment_relationships?.filter((e: any) => e.status === 'active').length || 0
      },
      employees: department.employment_relationships?.map((e: any) => ({
        id: e.id,
        user_id: e.user_id,
        role: e.role,
        status: e.status,
        user: e.user
      })) || [],
      employment_relationships: undefined // Remove raw data
    }));
    
    return NextResponse.json(departmentsWithCounts);
  } catch (error: any) {
    console.error('Departments API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      organization_id,
      department_type,
      max_employees
    } = body;

    // Validate required fields
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'Department name is required' },
        { status: 400 }
      );
    }

    if (!organization_id) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Verify organization exists
    const { data: org, error: orgError } = await adminClient
      .from('organizations')
      .select('id')
      .eq('id', organization_id)
      .single();

    if (orgError || !org) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Create department
    const { data: department, error: departmentError } = await adminClient
      .from('departments')
      .insert([
        {
          name: name.trim(),
          description: description?.trim() || null,
          organization_id,
          department_type: department_type || 'general',
          max_employees: max_employees || 50,
          created_by: authResult.user.id,
          status: 'active'
        }
      ])
      .select(`
        *,
        organization:organizations!departments_organization_id_fkey(
          id,
          name,
          slug
        )
      `)
      .single();
    
    if (departmentError) {
      console.error('Error creating department:', departmentError);
      return NextResponse.json(
        { error: departmentError.message || 'Failed to create department' },
        { status: 500 }
      );
    }

    return NextResponse.json(department, { status: 201 });
  } catch (error: any) {
    console.error('Create department API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
