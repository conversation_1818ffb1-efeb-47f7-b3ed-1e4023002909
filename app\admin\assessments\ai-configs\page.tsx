'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus,
  Settings,
  Brain,
  Edit,
  Trash2,
  Play,
  Eye,
  Copy,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';

type AssessmentConfig = {
  id: string;
  name: string;
  description: string;
  category: string;
  question_count: number;
  duration_minutes: number;
  passing_score: number;
  retake_limit: number;
  retake_fee: number;
  certification_eligible: boolean;
  status: 'draft' | 'published' | 'archived';
  ai_model_name: string;
  created_at: string;
  updated_at: string;
  prompt_template?: {
    id: string;
    name: string;
    system_prompt: string;
  };
};

type AIServiceStatus = {
  together_ai: { status: string; error: string | null };
  elevenlabs: { status: string; error: string | null };
  pinecone: { status: string; error: string | null };
};

export default function AIAssessmentConfigsPage() {
  const [configs, setConfigs] = useState<AssessmentConfig[]>([]);
  const [aiStatus, setAiStatus] = useState<AIServiceStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [testingConnection, setTestingConnection] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchConfigs();
    checkAIStatus();
  }, []);

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/ai-assessment-configs');
      
      if (!response.ok) {
        throw new Error('Failed to fetch AI assessment configurations');
      }
      
      const data = await response.json();
      setConfigs(data.configs || []);
    } catch (error: any) {
      console.error('Error fetching configs:', error);
      toast({
        title: "Error",
        description: error.message || 'Failed to fetch AI assessment configurations',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const checkAIStatus = async () => {
    try {
      const response = await fetch('/api/admin/ai/test-connection');
      if (response.ok) {
        const data = await response.json();
        setAiStatus(data.services);
      }
    } catch (error) {
      console.error('Error checking AI status:', error);
    }
  };

  const testAIConnection = async () => {
    try {
      setTestingConnection(true);
      const response = await fetch('/api/admin/ai/test-connection', {
        method: 'POST'
      });
      
      const data = await response.json();
      setAiStatus(data.services);
      
      toast({
        title: data.success ? "Connection Successful" : "Connection Issues",
        description: data.message,
        variant: data.success ? "default" : "destructive",
      });
    } catch (error: any) {
      toast({
        title: "Connection Test Failed",
        description: error.message || 'Failed to test AI connections',
        variant: "destructive",
      });
    } finally {
      setTestingConnection(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      published: 'default',
      archived: 'destructive'
    } as const;
    
    return variants[status as keyof typeof variants] || 'secondary';
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      communication_skills: 'bg-blue-100 text-blue-800',
      cognitive_ability: 'bg-purple-100 text-purple-800',
      technical_skills: 'bg-green-100 text-green-800',
      emotional_intelligence: 'bg-pink-100 text-pink-800',
      industry_knowledge: 'bg-orange-100 text-orange-800',
      stress_resilience: 'bg-red-100 text-red-800'
    } as const;
    
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getServiceStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed': return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'testing': return <Clock className="h-4 w-4 text-yellow-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="px-[50px] pt-8 pb-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-2">Loading AI assessment configurations...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="px-[50px] pt-8 pb-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Assessment Configurations</h1>
          <p className="text-muted-foreground">
            Manage AI-powered assessment generation settings and prompts
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={testAIConnection} disabled={testingConnection}>
            <Settings className="h-4 w-4 mr-2" />
            {testingConnection ? 'Testing...' : 'Test AI Services'}
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Configuration
          </Button>
        </div>
      </div>

      {/* AI Services Status */}
      {aiStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI Services Status
            </CardTitle>
            <CardDescription>
              Current status of integrated AI services
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Together.ai</p>
                  <p className="text-sm text-muted-foreground">Question Generation</p>
                </div>
                <div className="flex items-center gap-2">
                  {getServiceStatusIcon(aiStatus.together_ai.status)}
                  <span className="text-sm capitalize">{aiStatus.together_ai.status}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">ElevenLabs</p>
                  <p className="text-sm text-muted-foreground">Voice & Audio</p>
                </div>
                <div className="flex items-center gap-2">
                  {getServiceStatusIcon(aiStatus.elevenlabs.status)}
                  <span className="text-sm capitalize">{aiStatus.elevenlabs.status}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Pinecone</p>
                  <p className="text-sm text-muted-foreground">Vector Database</p>
                </div>
                <div className="flex items-center gap-2">
                  {getServiceStatusIcon(aiStatus.pinecone.status)}
                  <span className="text-sm capitalize">{aiStatus.pinecone.status}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Assessment Configurations */}
      {configs.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No AI Assessment Configurations</h3>
            <p className="text-muted-foreground mb-4">
              Create your first AI assessment configuration to start generating dynamic assessments.
            </p>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create First Configuration
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {configs.map((config) => (
            <Card key={config.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Brain className="h-5 w-5" />
                      {config.name}
                    </CardTitle>
                    <CardDescription>{config.description}</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={getStatusBadge(config.status)}>
                      {config.status}
                    </Badge>
                    <Badge className={getCategoryColor(config.category)}>
                      {config.category.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium">Questions</p>
                    <p className="text-sm text-muted-foreground">{config.question_count}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Duration</p>
                    <p className="text-sm text-muted-foreground">{config.duration_minutes} min</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Passing Score</p>
                    <p className="text-sm text-muted-foreground">{config.passing_score}%</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">AI Model</p>
                    <p className="text-sm text-muted-foreground">{config.ai_model_name}</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <span className="text-sm text-muted-foreground">Retakes:</span>
                      <span className="text-sm font-medium">{config.retake_limit}</span>
                      {config.retake_fee > 0 && (
                        <span className="text-sm text-muted-foreground">(${config.retake_fee})</span>
                      )}
                    </div>
                    {config.certification_eligible && (
                      <Badge variant="outline" className="text-xs">
                        Certification Eligible
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Play className="h-3 w-3 mr-1" />
                      Test Generate
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Button variant="outline" size="sm">
                      <Copy className="h-3 w-3 mr-1" />
                      Clone
                    </Button>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
