import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    const { data: categories, error } = await supabase
      .from('skill_categories')
      .select(`
        *,
        skills(count)
      `)
      .order('sort_order');

    if (error) {
      console.error('Error fetching skill categories:', error);
      return NextResponse.json(
        { error: 'Failed to fetch skill categories' },
        { status: 500 }
      );
    }

    // Transform the data to include skill counts
    const transformedCategories = (categories || []).map((category: any) => ({
      ...category,
      skill_count: Array.isArray(category.skills) ? category.skills.length : 0
    }));

    return NextResponse.json(transformedCategories);

  } catch (error) {
    console.error('Unexpected error in skill categories API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.color_code) {
      return NextResponse.json(
        { error: 'Name and color code are required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('skill_categories')
      .insert([{
        name: body.name,
        description: body.description || null,
        color_code: body.color_code,
        icon: body.icon || 'folder',
        is_active: body.is_active !== undefined ? body.is_active : true,
        sort_order: body.sort_order || 0,
      }])
      .select()
      .single();

    if (error) {
      console.error('Error creating skill category:', error);
      return NextResponse.json(
        { error: 'Failed to create skill category' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Unexpected error in skill categories POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
