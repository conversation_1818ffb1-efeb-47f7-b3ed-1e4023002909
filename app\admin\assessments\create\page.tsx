'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';

export default function CreateAssessmentPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    instructions: '',
    passing_score: 70,
    duration_minutes: 30,
    question_count: 10,
    price: 0,
    question_types: ['multiple_choice'],
    system_prompt: '',
    status: 'draft'
  });

  const questionTypeOptions = [
    { id: 'multiple_choice', label: 'Multiple Choice' },
    { id: 'true_false', label: 'True/False' },
    { id: 'scenario_based', label: 'Scenario Based' },
    { id: 'text_input', label: 'Text Input' }
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleQuestionTypeChange = (typeId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      question_types: checked
        ? [...prev.question_types, typeId]
        : prev.question_types.filter(t => t !== typeId)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.system_prompt) {
      toast({
        title: "Validation Error",
        description: "Name and system prompt are required",
        variant: "destructive",
      });
      return;
    }

    if (formData.question_types.length === 0) {
      toast({
        title: "Validation Error",
        description: "At least one question type must be selected",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/admin/assessments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create assessment');
      }

      const data = await response.json();

      toast({
        title: "Success",
        description: "Assessment created successfully",
      });

      router.push('/admin/assessments');
    } catch (error: any) {
      console.error('Error creating assessment:', error);
      toast({
        title: "Error",
        description: error.message || 'Failed to create assessment',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" asChild>
            <Link href="/admin/assessments">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Assessments
            </Link>
          </Button>
        </div>

        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Assessment</h1>
          <p className="text-muted-foreground">
            Create a new AI-powered assessment for your platform
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Assessment Details</CardTitle>
              <CardDescription>
                Configure the basic settings for your assessment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Assessment Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., BPO Communication Skills Test"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="instructions">Instructions</Label>
                <Textarea
                  id="instructions"
                  value={formData.instructions}
                  onChange={(e) => handleInputChange('instructions', e.target.value)}
                  placeholder="Provide clear instructions for test takers..."
                  rows={3}
                />
              </div>

              {/* Assessment Settings */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <Input
                    id="duration"
                    type="number"
                    value={formData.duration_minutes}
                    onChange={(e) => handleInputChange('duration_minutes', parseInt(e.target.value) || 0)}
                    min="1"
                    max="180"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="questions">Question Count</Label>
                  <Input
                    id="questions"
                    type="number"
                    value={formData.question_count}
                    onChange={(e) => handleInputChange('question_count', parseInt(e.target.value) || 0)}
                    min="1"
                    max="100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passing">Passing Score (%)</Label>
                  <Input
                    id="passing"
                    type="number"
                    value={formData.passing_score}
                    onChange={(e) => handleInputChange('passing_score', parseInt(e.target.value) || 0)}
                    min="1"
                    max="100"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price">Price ($)</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                    min="0"
                  />
                </div>
              </div>

              {/* Question Types */}
              <div className="space-y-3">
                <Label>Question Types *</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {questionTypeOptions.map((type) => (
                    <div key={type.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={type.id}
                        checked={formData.question_types.includes(type.id)}
                        onCheckedChange={(checked) => handleQuestionTypeChange(type.id, checked as boolean)}
                      />
                      <Label htmlFor={type.id} className="text-sm font-normal">
                        {type.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* System Prompt */}
              <div className="space-y-2">
                <Label htmlFor="prompt">AI System Prompt *</Label>
                <Textarea
                  id="prompt"
                  value={formData.system_prompt}
                  onChange={(e) => handleInputChange('system_prompt', e.target.value)}
                  placeholder="You are an expert assessment designer. Create questions that test..."
                  rows={6}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  This prompt tells the AI what kind of questions to generate. Be specific about the subject matter, difficulty level, and question style.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-4 mt-6">
            <Button type="button" variant="outline" asChild>
              <Link href="/admin/assessments">Cancel</Link>
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Assessment
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
