/**
 * Performance Optimization Utilities
 * React performance optimization hooks and components
 */

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

// =============================================================================
// MEMOIZATION UTILITIES
// =============================================================================

/**
 * Enhanced useMemo with dependency comparison
 */
export function useDeepMemo<T>(
  factory: () => T,
  deps: React.DependencyList
): T {
  const ref = React.useRef<{ deps: React.DependencyList; value: T }>()
  
  if (!ref.current || !areEqual(ref.current.deps, deps)) {
    ref.current = { deps, value: factory() }
  }
  
  return ref.current.value
}

/**
 * Memoized callback with stable reference
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T
): T {
  const callbackRef = React.useRef(callback)
  callbackRef.current = callback
  
  return React.useCallback(
    ((...args: any[]) => callbackRef.current(...args)) as T,
    []
  )
}

/**
 * Debounced value hook
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState<T>(value)
  
  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])
  
  return debouncedValue
}

/**
 * Throttled value hook
 */
export function useThrottle<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = React.useState<T>(value)
  const lastRan = React.useRef(Date.now())
  
  React.useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))
    
    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])
  
  return throttledValue
}

// =============================================================================
// LAZY LOADING COMPONENTS
// =============================================================================

interface LazyComponentProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  threshold?: number
  rootMargin?: string
  className?: string
}

/**
 * Lazy loading component with Intersection Observer
 */
export const LazyComponent = React.memo<LazyComponentProps>(({
  children,
  fallback = <div className="h-32 bg-muted animate-pulse rounded" />,
  threshold = 0.1,
  rootMargin = "50px",
  className,
}) => {
  const [isVisible, setIsVisible] = React.useState(false)
  const [hasLoaded, setHasLoaded] = React.useState(false)
  const ref = React.useRef<HTMLDivElement>(null)
  
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasLoaded) {
          setIsVisible(true)
          setHasLoaded(true)
          observer.disconnect()
        }
      },
      { threshold, rootMargin }
    )
    
    if (ref.current) {
      observer.observe(ref.current)
    }
    
    return () => observer.disconnect()
  }, [threshold, rootMargin, hasLoaded])
  
  return (
    <div ref={ref} className={className}>
      {isVisible ? children : fallback}
    </div>
  )
})

LazyComponent.displayName = "LazyComponent"

/**
 * Lazy image component with loading states
 */
interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  fallback?: React.ReactNode
  className?: string
  containerClassName?: string
}

export const LazyImage = React.memo<LazyImageProps>(({
  src,
  alt,
  fallback,
  className,
  containerClassName,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [hasError, setHasError] = React.useState(false)
  const [isVisible, setIsVisible] = React.useState(false)
  const ref = React.useRef<HTMLDivElement>(null)
  
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1, rootMargin: "50px" }
    )
    
    if (ref.current) {
      observer.observe(ref.current)
    }
    
    return () => observer.disconnect()
  }, [])
  
  const handleLoad = () => setIsLoaded(true)
  const handleError = () => setHasError(true)
  
  return (
    <div ref={ref} className={cn("relative overflow-hidden", containerClassName)}>
      {isVisible && !hasError && (
        <img
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0",
            className
          )}
          {...props}
        />
      )}
      
      {(!isVisible || !isLoaded || hasError) && (
        <div className={cn(
          "absolute inset-0 flex items-center justify-center bg-muted",
          !hasError && "animate-pulse"
        )}>
          {hasError ? (
            fallback || (
              <div className="text-muted-foreground text-sm">
                Failed to load image
              </div>
            )
          ) : (
            <div className="w-8 h-8 bg-muted-foreground/20 rounded animate-pulse" />
          )}
        </div>
      )}
    </div>
  )
})

LazyImage.displayName = "LazyImage"

// =============================================================================
// VIRTUAL SCROLLING
// =============================================================================

interface VirtualScrollProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
  className?: string
}

/**
 * Virtual scrolling component for large lists
 */
export function VirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className,
}: VirtualScrollProps<T>) {
  const [scrollTop, setScrollTop] = React.useState(0)
  const scrollElementRef = React.useRef<HTMLDivElement>(null)
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )
  
  const visibleItems = items.slice(startIndex, endIndex + 1)
  
  const handleScroll = React.useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])
  
  return (
    <div
      ref={scrollElementRef}
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${startIndex * itemHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {visibleItems.map((item, index) =>
            renderItem(item, startIndex + index)
          )}
        </div>
      </div>
    </div>
  )
}

// =============================================================================
// PERFORMANCE MONITORING
// =============================================================================

// Removed unused performance monitoring functions
// These were duplicates of functions in performance-monitor.ts

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Deep equality comparison
 */
function areEqual(a: any, b: any): boolean {
  if (a === b) return true
  
  if (a == null || b == null) return a === b
  
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false
    return a.every((item, index) => areEqual(item, b[index]))
  }
  
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a)
    const keysB = Object.keys(b)
    
    if (keysA.length !== keysB.length) return false
    
    return keysA.every(key => areEqual(a[key], b[key]))
  }
  
  return false
}

/**
 * HOC for memoizing components with custom comparison
 */
export function withMemo<P extends object>(
  Component: React.ComponentType<P>,
  arePropsEqual?: (prevProps: P, nextProps: P) => boolean
) {
  const MemoizedComponent = React.memo(Component, arePropsEqual)
  MemoizedComponent.displayName = `withMemo(${Component.displayName || Component.name})`
  return MemoizedComponent
}

/**
 * HOC for adding lazy loading to components
 */
export function withLazyLoading<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  const LazyLoadedComponent = React.forwardRef<any, P>((props, ref) => (
    <LazyComponent fallback={fallback}>
      <Component {...props} ref={ref} />
    </LazyComponent>
  ))
  
  LazyLoadedComponent.displayName = `withLazyLoading(${Component.displayName || Component.name})`
  return LazyLoadedComponent
}

/**
 * Hook for managing component state with performance optimizations
 */
export function useOptimizedState<T>(
  initialState: T | (() => T),
  equalityFn?: (prev: T, next: T) => boolean
) {
  const [state, setState] = React.useState(initialState)
  
  const setOptimizedState = React.useCallback((newState: T | ((prev: T) => T)) => {
    setState(prevState => {
      const nextState = typeof newState === 'function' 
        ? (newState as (prev: T) => T)(prevState)
        : newState
      
      if (equalityFn ? equalityFn(prevState, nextState) : prevState === nextState) {
        return prevState
      }
      
      return nextState
    })
  }, [equalityFn])
  
  return [state, setOptimizedState] as const
}

// =============================================================================
// EXPORTS
// =============================================================================

export {
  areEqual,
}
