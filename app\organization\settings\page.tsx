'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Avatar, 
  AvatarFallback, 
  AvatarImage 
} from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import {
  UserCog,
  KeyRound,
  Bell,
  Moon,
  Sun,
  Languages,
  Upload,
  LogOut,
  Save,
  Shield
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { useRouter } from 'next/navigation';

export default function BPOSettingsPage() {
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [userSettings, setUserSettings] = useState({
    name: '',
    email: '',
    phone: '',
    avatar_url: '',
    language: 'en',
    notifications: {
      email_new_applications: true,
      email_interview_reminders: true,
      browser_notifications: true
    },
    theme: 'system'
  });
  
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Get the current user
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) {
          router.push('/login');
          return;
        }
        
        setUser(session.user);
        
        // Get user settings
        const { data: settings, error: settingsError } = await supabase
          .from('user_settings')
          .select('*')
          .eq('user_id', session.user.id)
          .single();
          
        if (settingsError && settingsError.code !== 'PGRST116') {
          console.error('Error fetching user settings:', settingsError);
        }
        
        // Get user profile info
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();
          
        if (profileError && profileError.code !== 'PGRST116') {
          console.error('Error fetching user profile:', profileError);
        }
        
        // Merge settings and defaults
        setUserSettings({
          name: profile?.name || session.user.user_metadata?.name || '',
          email: session.user.email || '',
          phone: profile?.phone || '',
          avatar_url: profile?.avatar_url || '',
          language: settings?.language || 'en',
          notifications: settings?.notifications || {
            email_new_applications: true,
            email_interview_reminders: true,
            browser_notifications: true
          },
          theme: settings?.theme || 'system'
        });
        
        // Set theme from settings
        if (settings?.theme && settings.theme !== 'system') {
          setTheme(settings.theme);
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [router, setTheme]);

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserSettings({ ...userSettings, [name]: value });
  };

  const handleNotificationChange = (key: string, checked: boolean) => {
    setUserSettings({
      ...userSettings,
      notifications: {
        ...userSettings.notifications,
        [key]: checked
      }
    });
  };

  const handleThemeChange = (value: string) => {
    setUserSettings({ ...userSettings, theme: value });
    setTheme(value);
  };

  const handleLanguageChange = (value: string) => {
    setUserSettings({ ...userSettings, language: value });
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm({ ...passwordForm, [name]: value });
  };

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      // Update profile
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          name: userSettings.name,
          phone: userSettings.phone,
          avatar_url: userSettings.avatar_url,
          updated_at: new Date()
        });
        
      if (profileError) {
        throw profileError;
      }
      
      // Update user settings
      const { error: settingsError } = await supabase
        .from('user_settings')
        .upsert({
          user_id: user.id,
          language: userSettings.language,
          notifications: userSettings.notifications,
          theme: userSettings.theme,
          updated_at: new Date()
        });
        
      if (settingsError) {
        throw settingsError;
      }
      
      toast({
        title: "Settings saved",
        description: "Your settings have been updated successfully",
      });
      
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: "Save failed",
        description: "There was an error saving your settings",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation must match",
        variant: "destructive",
      });
      return;
    }
    
    try {
      setSaving(true);
      
      const { error } = await supabase.auth.updateUser({
        password: passwordForm.newPassword
      });
      
      if (error) {
        throw error;
      }
      
      // Reset form
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      
      toast({
        title: "Password updated",
        description: "Your password has been changed successfully",
      });
      
    } catch (error) {
      console.error('Error changing password:', error);
      toast({
        title: "Update failed",
        description: "There was an error updating your password",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !e.target.files.length) return;
    
    try {
      setSaving(true);
      
      const file = e.target.files[0];
      const fileExt = file.name.split('.').pop();
      const filePath = `avatars/${user.id}-${Date.now()}.${fileExt}`;
      
      // Upload file to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('user-assets')
        .upload(filePath, file);
        
      if (uploadError) {
        throw uploadError;
      }
      
      // Get public URL for the uploaded file
      const { data: urlData } = supabase.storage
        .from('user-assets')
        .getPublicUrl(filePath);
        
      if (!urlData.publicUrl) {
        throw new Error('Could not get public URL for avatar');
      }
      
      // Update user settings with new avatar URL
      setUserSettings({
        ...userSettings,
        avatar_url: urlData.publicUrl
      });
      
      toast({
        title: "Avatar uploaded",
        description: "Your avatar has been uploaded successfully",
      });
      
    } catch (error) {
      console.error('Error uploading avatar:', error);
      toast({
        title: "Upload failed",
        description: "There was an error uploading your avatar",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      router.push('/login');
    } catch (error) {
      console.error('Error signing out:', error);
      toast({
        title: "Sign out failed",
        description: "There was an error signing out",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <div className="flex items-start">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Manage your account preferences and settings
          </p>
        </div>
      </div>
      
      <div className="mt-8">
        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full md:w-[400px] grid-cols-3">
            <TabsTrigger value="profile">Profile</TabsTrigger>
            <TabsTrigger value="password">Password</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
          </TabsList>
          
          {/* Profile Settings */}
          <TabsContent value="profile" className="mt-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update your personal information and contact details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSaveProfile} className="space-y-6">
                  <div className="flex flex-col sm:flex-row items-center gap-6 pb-6">
                    <div className="flex flex-col items-center">
                      <Avatar className="h-24 w-24">
                        <AvatarImage src={userSettings.avatar_url} alt={userSettings.name} />
                        <AvatarFallback>
                          {userSettings.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      
                      <Label
                        htmlFor="avatar-upload"
                        className="cursor-pointer mt-4 inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        <span>Change Avatar</span>
                        <Input
                          id="avatar-upload"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleAvatarUpload}
                        />
                      </Label>
                    </div>
                    
                    <div className="space-y-4 flex-1">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name</Label>
                        <Input
                          id="name"
                          name="name"
                          value={userSettings.name}
                          onChange={handleProfileChange}
                          placeholder="Enter your full name"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          name="email"
                          value={userSettings.email}
                          disabled
                          readOnly
                          placeholder="Your email address"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          Contact support to change your email address
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={userSettings.phone}
                          onChange={handleProfileChange}
                          placeholder="Enter your phone number"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <Button type="submit" disabled={saving}>
                    {saving ? 'Saving...' : 'Save Changes'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Password Settings */}
          <TabsContent value="password" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>
                  Update your password to keep your account secure
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleChangePassword} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">Current Password</Label>
                    <Input
                      id="currentPassword"
                      name="currentPassword"
                      type="password"
                      value={passwordForm.currentPassword}
                      onChange={handlePasswordChange}
                      placeholder="Enter your current password"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      name="newPassword"
                      type="password"
                      value={passwordForm.newPassword}
                      onChange={handlePasswordChange}
                      placeholder="Enter new password"
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      value={passwordForm.confirmPassword}
                      onChange={handlePasswordChange}
                      placeholder="Confirm new password"
                      required
                    />
                  </div>
                  
                  <div className="pt-4">
                    <Button type="submit" disabled={saving}>
                      {saving ? 'Updating...' : 'Update Password'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Preferences & Notifications */}
          <TabsContent value="preferences" className="mt-6 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Theme & Appearance</CardTitle>
                <CardDescription>
                  Customize the appearance of the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Theme</Label>
                    <div className="flex gap-4">
                      <Button
                        variant={userSettings.theme === 'light' ? 'default' : 'outline'}
                        className="flex items-center gap-2"
                        onClick={() => handleThemeChange('light')}
                      >
                        <Sun className="h-4 w-4" />
                        <span>Light</span>
                      </Button>
                      
                      <Button
                        variant={userSettings.theme === 'dark' ? 'default' : 'outline'}
                        className="flex items-center gap-2"
                        onClick={() => handleThemeChange('dark')}
                      >
                        <Moon className="h-4 w-4" />
                        <span>Dark</span>
                      </Button>
                      
                      <Button
                        variant={userSettings.theme === 'system' ? 'default' : 'outline'}
                        className="flex items-center gap-2"
                        onClick={() => handleThemeChange('system')}
                      >
                        <span>System</span>
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Language</Label>
                    <div className="flex gap-4">
                      <Button
                        variant={userSettings.language === 'en' ? 'default' : 'outline'}
                        className="flex items-center gap-2"
                        onClick={() => handleLanguageChange('en')}
                      >
                        <Languages className="h-4 w-4" />
                        <span>English</span>
                      </Button>
                      
                      <Button
                        variant={userSettings.language === 'es' ? 'default' : 'outline'}
                        className="flex items-center gap-2"
                        onClick={() => handleLanguageChange('es')}
                      >
                        <Languages className="h-4 w-4" />
                        <span>Español</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure how you receive notifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="email_new_applications">New Application Emails</Label>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Receive email notifications when new candidates apply
                      </div>
                    </div>
                    <Switch
                      id="email_new_applications"
                      checked={userSettings.notifications.email_new_applications}
                      onCheckedChange={(checked) => handleNotificationChange('email_new_applications', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="email_interview_reminders">Interview Reminder Emails</Label>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Receive email reminders before scheduled interviews
                      </div>
                    </div>
                    <Switch
                      id="email_interview_reminders"
                      checked={userSettings.notifications.email_interview_reminders}
                      onCheckedChange={(checked) => handleNotificationChange('email_interview_reminders', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="browser_notifications">Browser Notifications</Label>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Receive browser notifications for important updates
                      </div>
                    </div>
                    <Switch
                      id="browser_notifications"
                      checked={userSettings.notifications.browser_notifications}
                      onCheckedChange={(checked) => handleNotificationChange('browser_notifications', checked)}
                    />
                  </div>
                </div>
                
                <div className="mt-6">
                  <Button onClick={handleSaveProfile} disabled={saving}>
                    {saving ? 'Saving...' : 'Save Preferences'}
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Account Actions</CardTitle>
                <CardDescription>
                  Manage your account security and session
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button
                    variant="destructive"
                    className="w-full sm:w-auto"
                    onClick={handleSignOut}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    <span>Sign Out</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
} 