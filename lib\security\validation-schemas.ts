/**
 * Input Validation Schemas
 * Defines validation schemas for common API operations
 */

import { z } from 'zod';

/**
 * User Profile Validation Schemas
 */
export const ProfileSchemas = {
  personalInfo: z.object({
    full_name: z.string().min(1).max(100).optional(),
    phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/).max(20).optional(),
    location: z.string().max(100).optional(),
    bio: z.string().max(1000).optional(),
    portfolio_url: z.string().url().optional().or(z.literal('')),
    linkedin_url: z.string().url().optional().or(z.literal('')),
    github_url: z.string().url().optional().or(z.literal('')),
    website_url: z.string().url().optional().or(z.literal(''))
  }),

  skills: z.object({
    action: z.enum(['add', 'remove', 'update']),
    skill: z.object({
      name: z.string().min(1).max(50),
      level: z.enum(['Beginner', 'Intermediate', 'Advanced', 'Expert']),
      years: z.number().min(0).max(50).optional()
    }).optional(),
    skillIndex: z.number().min(0).optional(),
    skills: z.array(z.object({
      name: z.string().min(1).max(50),
      level: z.enum(['Beginner', 'Intermediate', 'Advanced', 'Expert']),
      years: z.number().min(0).max(50).optional()
    })).optional()
  }),

  experience: z.object({
    action: z.enum(['add', 'remove', 'update']),
    experience: z.object({
      company: z.string().min(1).max(100),
      position: z.string().min(1).max(100),
      startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
      endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
      description: z.string().max(1000).optional(),
      current: z.boolean().optional()
    }).optional(),
    experienceIndex: z.number().min(0).optional()
  }),

  education: z.object({
    action: z.enum(['add', 'remove', 'update']),
    education: z.object({
      institution: z.string().min(1).max(100),
      degree: z.string().min(1).max(100),
      field: z.string().max(100).optional(),
      graduationYear: z.number().min(1950).max(2030),
      gpa: z.number().min(0).max(4).optional()
    }).optional(),
    educationIndex: z.number().min(0).optional()
  }),

  preferences: z.object({
    availability_status: z.enum(['available', 'busy', 'not_available']).optional(),
    hourly_rate: z.number().min(0).max(1000).optional(),
    preferred_work_type: z.enum(['full_time', 'part_time', 'contract', 'freelance']).optional(),
    years_of_experience: z.number().min(0).max(50).optional()
  })
};

/**
 * File Upload Validation Schemas
 */
export const FileUploadSchemas = {
  upload: z.object({
    type: z.enum(['avatar', 'resume', 'document', 'logo', 'media']),
    entityId: z.string().uuid().optional(),
    category: z.string().max(50).optional()
  }),

  fileMetadata: z.object({
    name: z.string().min(1).max(255),
    size: z.number().min(1).max(100 * 1024 * 1024), // 100MB max
    type: z.string().regex(/^[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_]*\/[a-zA-Z0-9][a-zA-Z0-9!#$&\-\^_.]*$/)
  })
};

/**
 * Authentication Validation Schemas
 */
export const AuthSchemas = {
  login: z.object({
    email: z.string().email().max(255),
    password: z.string().min(8).max(128)
  }),

  register: z.object({
    email: z.string().email().max(255),
    password: z.string().min(8).max(128),
    full_name: z.string().min(1).max(100),
    role: z.enum(['individual', 'organization']).optional()
  }),

  passwordReset: z.object({
    email: z.string().email().max(255)
  }),

  contextSwitch: z.object({
    context: z.enum(['individual', 'organization']),
    organization_id: z.string().uuid().optional()
  })
};

/**
 * Admin Operation Validation Schemas
 */
export const AdminSchemas = {
  userManagement: z.object({
    user_id: z.string().uuid(),
    action: z.enum(['activate', 'deactivate', 'delete', 'update_role']),
    role: z.enum(['individual', 'organization', 'platform_admin']).optional(),
    reason: z.string().max(500).optional()
  }),

  organizationManagement: z.object({
    name: z.string().min(1).max(100),
    slug: z.string().min(1).max(50).regex(/^[a-z0-9-]+$/),
    description: z.string().max(1000).optional(),
    website: z.string().url().optional(),
    industry: z.string().max(100).optional()
  }),

  systemSettings: z.object({
    setting_key: z.string().min(1).max(100),
    setting_value: z.union([z.string(), z.number(), z.boolean()]),
    description: z.string().max(500).optional()
  })
};

/**
 * Database Query Validation Schemas
 */
export const QuerySchemas = {
  pagination: z.object({
    page: z.number().min(1).max(1000).optional(),
    limit: z.number().min(1).max(100).optional(),
    offset: z.number().min(0).optional()
  }),

  sorting: z.object({
    sort_by: z.string().max(50).optional(),
    sort_order: z.enum(['asc', 'desc']).optional()
  }),

  filtering: z.object({
    status: z.string().max(50).optional(),
    category: z.string().max(50).optional(),
    date_from: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    date_to: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(),
    search: z.string().max(100).optional()
  })
};

/**
 * Organization Management Validation Schemas
 */
export const OrganizationSchemas = {
  create: z.object({
    name: z.string().min(1).max(100),
    slug: z.string().min(1).max(50).regex(/^[a-z0-9-]+$/),
    description: z.string().max(1000).optional(),
    website: z.string().url().optional(),
    industry: z.string().max(100).optional(),
    size: z.enum(['1-10', '11-50', '51-200', '201-1000', '1000+']).optional()
  }),

  update: z.object({
    name: z.string().min(1).max(100).optional(),
    description: z.string().max(1000).optional(),
    website: z.string().url().optional(),
    industry: z.string().max(100).optional(),
    size: z.enum(['1-10', '11-50', '51-200', '201-1000', '1000+']).optional()
  }),

  memberInvite: z.object({
    email: z.string().email().max(255),
    role: z.enum(['member', 'admin']),
    department_id: z.string().uuid().optional(),
    message: z.string().max(500).optional()
  })
};

/**
 * Common validation utilities
 */
export const ValidationUtils = {
  /**
   * Validate UUID format
   */
  uuid: z.string().uuid(),

  /**
   * Validate email format
   */
  email: z.string().email().max(255),

  /**
   * Validate URL format
   */
  url: z.string().url().max(500),

  /**
   * Validate date format (YYYY-MM-DD)
   */
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),

  /**
   * Validate phone number format
   */
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/).max(20),

  /**
   * Validate slug format (lowercase, alphanumeric, hyphens)
   */
  slug: z.string().regex(/^[a-z0-9-]+$/).max(50),

  /**
   * Sanitize string input
   */
  sanitizedString: (maxLength: number = 255) => 
    z.string().max(maxLength).transform(str => str.trim()),

  /**
   * Validate file size
   */
  fileSize: (maxSize: number) => z.number().min(1).max(maxSize),

  /**
   * Validate MIME type
   */
  mimeType: (allowedTypes: string[]) => 
    z.string().refine(type => allowedTypes.includes(type), {
      message: `File type must be one of: ${allowedTypes.join(', ')}`
    })
};

/**
 * Export all schemas for easy access
 */
export const ValidationSchemas = {
  Profile: ProfileSchemas,
  FileUpload: FileUploadSchemas,
  Auth: AuthSchemas,
  Admin: AdminSchemas,
  Query: QuerySchemas,
  Organization: OrganizationSchemas,
  Utils: ValidationUtils
};
