"use client"

import { LunaLayout } from "@/components/luna-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { 
  BookOpen, 
  Users, 
  TrendingUp, 
  Award,
  Clock,
  CheckCircle2,
  ArrowRight
} from "lucide-react"

export function SidebarDemoPage() {
  return (
    <LunaLayout>
      <div className="p-6 space-y-6">
        {/* Page Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">New Luna Sidebar Demo</h1>
          <p className="text-muted-foreground">
            Experience the enhanced navigation with context-aware design and improved usability.
          </p>
        </div>

        {/* Key Features */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Context Switching</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Seamless</div>
              <p className="text-xs text-muted-foreground">
                Switch between Individual and Organization modes
              </p>
              <div className="mt-4">
                <Badge variant="secondary">Individual</Badge>
                <Badge variant="outline" className="ml-2">Organization</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Navigation Groups</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Organized</div>
              <p className="text-xs text-muted-foreground">
                Logical grouping of features by context
              </p>
              <div className="mt-4 space-y-1">
                <div className="text-xs text-muted-foreground">Learning • Career • Support</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Theme Support</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Adaptive</div>
              <p className="text-xs text-muted-foreground">
                Light, dark, and system theme support
              </p>
              <div className="mt-4">
                <Badge variant="default">Built-in</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Improvements Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Key Improvements
            </CardTitle>
            <CardDescription>
              What's new and improved in the Luna sidebar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Unified Navigation</div>
                    <div className="text-sm text-muted-foreground">
                      Single sidebar component for all contexts
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Better Mobile Support</div>
                    <div className="text-sm text-muted-foreground">
                      Optimized touch targets and responsive design
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Accessibility Enhanced</div>
                    <div className="text-sm text-muted-foreground">
                      Proper ARIA labels and keyboard navigation
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Design Token Integration</div>
                    <div className="text-sm text-muted-foreground">
                      Consistent with Luna's design system
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Performance Optimized</div>
                    <div className="text-sm text-muted-foreground">
                      Reduced bundle size and faster rendering
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Context-Aware Content</div>
                    <div className="text-sm text-muted-foreground">
                      Navigation adapts to user role and context
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Usage Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              How to Use
            </CardTitle>
            <CardDescription>
              Quick guide to implementing the new sidebar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Badge variant="outline" className="mt-0.5">1</Badge>
                <div>
                  <div className="font-medium">Import the LunaLayout</div>
                  <div className="text-sm text-muted-foreground">
                    Replace existing layout components with <code className="bg-muted px-1 rounded">LunaLayout</code>
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Badge variant="outline" className="mt-0.5">2</Badge>
                <div>
                  <div className="font-medium">Wrap Your Pages</div>
                  <div className="text-sm text-muted-foreground">
                    Use the layout wrapper or HOC pattern for consistent navigation
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Badge variant="outline" className="mt-0.5">3</Badge>
                <div>
                  <div className="font-medium">Test Context Switching</div>
                  <div className="text-sm text-muted-foreground">
                    Verify navigation updates correctly when switching between contexts
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t">
              <Button className="w-full sm:w-auto">
                View Implementation Guide
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Progress Indicator */}
        <Card>
          <CardHeader>
            <CardTitle>Implementation Progress</CardTitle>
            <CardDescription>
              Track the rollout of the new sidebar across the platform
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Core Sidebar Component</span>
                <span>100%</span>
              </div>
              <Progress value={100} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Layout Integration</span>
                <span>100%</span>
              </div>
              <Progress value={100} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Page Migration</span>
                <span>25%</span>
              </div>
              <Progress value={25} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Testing & QA</span>
                <span>10%</span>
              </div>
              <Progress value={10} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    </LunaLayout>
  )
}
