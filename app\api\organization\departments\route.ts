import { NextRequest, NextResponse } from 'next/server';
import { createBrowserClient } from '@/lib/supabase';

/**
 * GET /api/organization/departments
 * Get all departments for an organization
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orgId = searchParams.get('org_id');

    if (!orgId) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    const supabase = createBrowserClient();

    // Get departments with employee count
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select(`
        id,
        name,
        description,
        department_head_id,
        created_at,
        updated_at
      `)
      .eq('organization_id', orgId)
      .order('name');

    if (deptError) {
      console.error('Error fetching departments:', deptError);
      return NextResponse.json(
        { error: 'Failed to fetch departments' },
        { status: 500 }
      );
    }

    // Get employee counts for each department
    const departmentsWithCount = [];
    for (const dept of departments || []) {
      const { data: employments } = await supabase
        .from('employment_relationships')
        .select('id')
        .eq('department_id', dept.id)
        .eq('status', 'active');

      departmentsWithCount.push({
        ...dept,
        employee_count: employments?.length || 0
      });
    }

    return NextResponse.json(departmentsWithCount);

  } catch (error) {
    console.error('Error in departments API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/organization/departments
 * Create a new department
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { organization_id, name, description } = body;

    // Validate required fields
    if (!organization_id || !name) {
      return NextResponse.json(
        { error: 'Organization ID and department name are required' },
        { status: 400 }
      );
    }

    const supabase = createBrowserClient();

    // Check if department name already exists in this organization
    const { data: existingDept } = await supabase
      .from('departments')
      .select('id')
      .eq('organization_id', organization_id)
      .eq('name', name.trim())
      .single();

    if (existingDept) {
      return NextResponse.json(
        { error: 'A department with this name already exists' },
        { status: 409 }
      );
    }

    // Create department
    const { data: newDepartment, error: createError } = await supabase
      .from('departments')
      .insert({
        organization_id,
        name: name.trim(),
        description: description?.trim() || null
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating department:', createError);
      return NextResponse.json(
        { error: 'Failed to create department' },
        { status: 500 }
      );
    }

    return NextResponse.json(newDepartment, { status: 201 });

  } catch (error) {
    console.error('Error in create department API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
