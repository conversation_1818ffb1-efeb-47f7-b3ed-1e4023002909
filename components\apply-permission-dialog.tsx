'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Briefcase, 
  GraduationCap, 
  Star,
  Shield,
  CheckCircle2,
  AlertCircle
} from 'lucide-react'

interface Job {
  id: string
  title: string
  company: string
  companyLogo?: string | null
}

interface ApplyPermissionDialogProps {
  job: Job
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => Promise<void>
}

export function ApplyPermissionDialog({ job, open, onOpenChange, onConfirm }: ApplyPermissionDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const handleConfirm = async () => {
    try {
      setIsSubmitting(true)
      await onConfirm()
      onOpenChange(false)
      toast({
        title: "Application submitted successfully!",
        description: `Your application for ${job.title} at ${job.company} has been submitted.`,
        duration: 5000,
      })
    } catch (error: any) {
      console.error('Error submitting application:', error)
      toast({
        title: "Application failed",
        description: error.message || "There was an error submitting your application. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const profileData = [
    { icon: User, label: "Personal Information", description: "Name, contact details, and profile summary" },
    { icon: Briefcase, label: "Work Experience", description: "Employment history and professional background" },
    { icon: GraduationCap, label: "Education", description: "Educational qualifications and certifications" },
    { icon: Star, label: "Skills & Competencies", description: "Technical and soft skills assessment" },
  ]

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-blue-600" />
            Share Your Professional Profile
          </DialogTitle>
          <DialogDescription>
            {job.company} would like to review your professional profile information to consider your application for the {job.title} position.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Job Information */}
          <Card className="border-blue-100 bg-blue-50/50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                {job.companyLogo && (
                  <img 
                    src={job.companyLogo} 
                    alt={job.company}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                )}
                <div>
                  <h3 className="font-semibold text-gray-900">{job.title}</h3>
                  <p className="text-gray-600">{job.company}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Information to be shared */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              Information that will be shared:
            </h4>
            <div className="grid gap-3">
              {profileData.map((item, index) => (
                <div key={index} className="flex items-start gap-3 p-3 rounded-lg border border-gray-100 bg-gray-50/50">
                  <item.icon className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-sm text-gray-900">{item.label}</p>
                    <p className="text-xs text-gray-600">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Privacy Notice */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-amber-800">Privacy Notice</p>
                <p className="text-amber-700 mt-1">
                  Your information will only be used for this job application and will be handled according to our privacy policy. 
                  You can withdraw your application at any time.
                </p>
              </div>
            </div>
          </div>

          {/* Benefits of applying */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">What happens next:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Your application will be reviewed by the hiring team</li>
              <li>• You'll receive updates on your application status</li>
              <li>• If selected, you'll be invited to schedule an interview</li>
              <li>• The process typically takes 3-5 business days</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="flex gap-3">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm}
            disabled={isSubmitting}
            className="bg-green-600 hover:bg-green-700"
          >
            {isSubmitting ? "Submitting..." : "Share Profile & Apply"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
