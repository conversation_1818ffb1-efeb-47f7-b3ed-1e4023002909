"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface TimePickerProps {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
}

export const TimePicker = React.forwardRef<HTMLInputElement, TimePickerProps>(
  ({ value, onChange, className, disabled, label, ...props }, ref) => {
    const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange?.(e.target.value);
    };

    return (
      <div className={cn("space-y-2", className)}>
        {label && (
          <Label htmlFor="time-picker" className="text-sm font-medium">
            {label}
          </Label>
        )}
        <Input
          ref={ref}
          id="time-picker"
          type="time"
          value={value}
          onChange={handleTimeChange}
          disabled={disabled}
          className="w-full"
          {...props}
        />
      </div>
    );
  }
);

TimePicker.displayName = "TimePicker";

// Alternative component with separate hour/minute inputs
interface TimePickerSeparateProps {
  hours?: number;
  minutes?: number;
  onHoursChange?: (hours: number) => void;
  onMinutesChange?: (minutes: number) => void;
  className?: string;
  disabled?: boolean;
}

export const TimePickerSeparate = React.forwardRef<
  HTMLDivElement,
  TimePickerSeparateProps
>(
  (
    {
      hours = 0,
      minutes = 0,
      onHoursChange,
      onMinutesChange,
      className,
      disabled,
      ...props
    },
    ref
  ) => {
    return (
      <div
        ref={ref}
        className={cn("flex items-center space-x-2", className)}
        {...props}
      >
        <div className="flex items-center space-x-1">
          <Input
            type="number"
            min="0"
            max="23"
            value={hours.toString().padStart(2, "0")}
            onChange={(e) => onHoursChange?.(parseInt(e.target.value) || 0)}
            disabled={disabled}
            className="w-16 text-center"
          />
          <span className="text-sm text-muted-foreground">:</span>
          <Input
            type="number"
            min="0"
            max="59"
            value={minutes.toString().padStart(2, "0")}
            onChange={(e) => onMinutesChange?.(parseInt(e.target.value) || 0)}
            disabled={disabled}
            className="w-16 text-center"
          />
        </div>
        <span className="text-sm text-muted-foreground">
          {hours.toString().padStart(2, "0")}:
          {minutes.toString().padStart(2, "0")}
        </span>
      </div>
    );
  }
);

TimePickerSeparate.displayName = "TimePickerSeparate";
