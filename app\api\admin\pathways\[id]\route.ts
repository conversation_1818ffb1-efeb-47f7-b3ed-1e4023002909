import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-admin';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Get pathway with related data
    const { data: pathway, error: pathwayError } = await adminClient
      .from('learning_paths')
      .select(`
        *,
        program:programs!learning_paths_program_id_fkey(
          id,
          name,
          industry,
          status
        ),
        created_by_user:users!learning_paths_created_by_fkey(
          id,
          full_name,
          email
        ),
        pathway_courses(
          id,
          sequence_order,
          is_required,
          unlock_conditions,
          course:courses(
            id,
            name,
            description,
            slug,
            level,
            estimated_duration,
            status,
            cover_image_url,
            learning_objectives,
            tags
          )
        )
      `)
      .eq('id', id)
      .single();

    if (pathwayError) {
      console.error('Error fetching pathway:', pathwayError);
      return NextResponse.json(
        { error: pathwayError.message || 'Pathway not found' },
        { status: pathwayError.code === 'PGRST116' ? 404 : 500 }
      );
    }

    // Sort courses by sequence order
    if (pathway.pathway_courses) {
      pathway.pathway_courses.sort((a: any, b: any) => a.sequence_order - b.sequence_order);
    }

    // Add counts
    const pathwayWithCounts = {
      ...pathway,
      _count: {
        courses: pathway.pathway_courses?.length || 0,
        required_courses: pathway.pathway_courses?.filter((pc: any) => pc.is_required).length || 0,
        optional_courses: pathway.pathway_courses?.filter((pc: any) => !pc.is_required).length || 0,
        total_duration: pathway.pathway_courses?.reduce((acc: number, pc: any) => 
          acc + (pc.course?.estimated_duration || 0), 0) || 0
      }
    };

    return NextResponse.json(pathwayWithCounts);

  } catch (error: any) {
    console.error('Pathway GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Parse request body
    const body = await req.json();
    const {
      title,
      description,
      slug,
      program_id,
      estimated_duration_hours,
      difficulty_level,
      sort_order,
      is_featured,
      cover_image_url,
      target_roles,
      prerequisites,
      learning_objectives,
      skills_covered,
      skills_gained
    } = body;

    // Validate required fields
    if (!title || !description) {
      return NextResponse.json(
        { error: 'Title and description are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Check if slug already exists (excluding current pathway)
    if (slug) {
      const { data: existingPathway } = await adminClient
        .from('learning_paths')
        .select('id')
        .eq('slug', slug.trim())
        .neq('id', id)
        .single();

      if (existingPathway) {
        return NextResponse.json(
          { error: 'A pathway with this slug already exists' },
          { status: 400 }
        );
      }
    }

    // Update pathway
    const { data: pathway, error: pathwayError } = await adminClient
      .from('learning_paths')
      .update({
        title: title.trim(),
        description: description.trim(),
        slug: slug?.trim(),
        program_id,
        estimated_duration_hours: estimated_duration_hours || null,
        difficulty_level: difficulty_level || 1,
        sort_order: sort_order || 0,
        is_featured: is_featured || false,
        cover_image_url,
        target_roles: target_roles || [],
        prerequisites: prerequisites || [],
        learning_objectives: learning_objectives || [],
        skills_covered: skills_covered || [],
        skills_gained: skills_gained || [],
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        program:programs!learning_paths_program_id_fkey(
          id,
          name,
          industry
        ),
        created_by_user:users!learning_paths_created_by_fkey(
          id,
          full_name,
          email
        )
      `)
      .single();

    if (pathwayError) {
      console.error('Error updating pathway:', pathwayError);
      return NextResponse.json(
        { error: pathwayError.message || 'Failed to update pathway' },
        { status: pathwayError.code === 'PGRST116' ? 404 : 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Pathway updated successfully',
      pathway
    });

  } catch (error: any) {
    console.error('Pathway update API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Check if pathway has courses
    const { data: pathwayCourses } = await adminClient
      .from('pathway_courses')
      .select('id')
      .eq('pathway_id', id);

    if (pathwayCourses && pathwayCourses.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete pathway with assigned courses. Please remove all courses first.' },
        { status: 400 }
      );
    }

    // Delete pathway
    const { error: deleteError } = await adminClient
      .from('learning_paths')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting pathway:', deleteError);
      return NextResponse.json(
        { error: deleteError.message || 'Failed to delete pathway' },
        { status: deleteError.code === 'PGRST116' ? 404 : 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Pathway deleted successfully'
    });

  } catch (error: any) {
    console.error('Pathway delete API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
