'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { PageTitle } from '@/components/page-title';

export default function Home() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get user role and redirect to appropriate dashboard
    const redirectToDashboard = async () => {
      try {
        const response = await fetch('/api/auth/user');
        const authData = await response.json();

        if (authData.success && authData.user) {
          const userData = authData.user;

          // Check if user has a specific context preference
          const currentContext = userData.currentContext;

          if (userData.isPlatformAdmin) {
            // Platform admins default to individual dashboard but can switch
            if (currentContext?.type === 'organization' && currentContext.organization_id) {
              // Find the organization slug for the current context
              const contextEmployment = userData.employmentRelationships?.find(
                (emp: any) => emp.organization_id === currentContext.organization_id
              );
              const orgSlug = contextEmployment?.organization_slug || 'default';
              router.push(`/org/${orgSlug}`);
            } else {
              router.push('/individual');
            }
          } else if (currentContext?.type === 'organization' && currentContext.organization_id) {
            // User has explicitly chosen organization context
            const contextEmployment = userData.employmentRelationships?.find(
              (emp: any) => emp.organization_id === currentContext.organization_id
            );
            const orgSlug = contextEmployment?.organization_slug || 'default';
            router.push(`/org/${orgSlug}`);
          } else {
            // Default to individual dashboard for all users
            router.push('/individual');
          }
        } else {
          router.push('/login');
        }
      } catch (err) {
        console.error('Redirect error:', err);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    redirectToDashboard();
  }, [router]);

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <PageTitle title="Luna Skills Platform" />
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <h2 className="mt-4 text-xl font-medium text-gray-900">Welcome to Luna Platform</h2>
          <p className="mt-2 text-gray-500">Redirecting you to your dashboard...</p>
        </div>
      </div>
    );
  }

  return null;
}
