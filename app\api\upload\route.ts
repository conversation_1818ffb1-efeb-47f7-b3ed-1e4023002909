import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/types/database.types';

export async function POST(request: Request) {
  try {
    // Create a Supabase client using auth helpers
    const cookieStore = cookies();
    const supabase = createServerComponentClient<Database>({ cookies: () => cookieStore });
    
    // Get the current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized - Not logged in' },
        { status: 401 }
      );
    }
    
    const user = session.user;
    
    // Parse the form data from the request
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const bpoId = formData.get('bpoId') as string;
    
    // Validate inputs
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }
    
    if (!bpoId) {
      return NextResponse.json(
        { error: 'No BPO ID provided' },
        { status: 400 }
      );
    }
    
    // Check if user has permission to edit this BPO
    const { data: teamMember, error: teamError } = await supabase
      .from('bpo_teams')
      .select('bpo_id')
      .eq('user_id', user.id)
      .eq('bpo_id', bpoId)
      .single();
      
    if (teamError || !teamMember) {
      return NextResponse.json(
        { error: 'Unauthorized - Not a member of this BPO' },
        { status: 403 }
      );
    }
    
    // Create admin client with service role to bypass RLS policies
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    // Generate a unique filename
    const timestamp = new Date().getTime();
    const fileExt = file.name.split('.').pop();
    const fileName = `logo-${bpoId}-${timestamp}.${fileExt}`;
    const filePath = `logos/${fileName}`;
    
    // Convert File to ArrayBuffer for upload
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = new Uint8Array(arrayBuffer);
    
    console.log('Server: Attempting to upload with admin client...');
    
    // Upload the file using admin client
    const { data: uploadData, error: uploadError } = await adminClient.storage
      .from('company-logos')
      .upload(filePath, fileBuffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: file.type,
      });
      
    if (uploadError) {
      console.error('Server upload error:', uploadError);
      return NextResponse.json(
        { error: `Upload failed: ${uploadError.message}` },
        { status: 500 }
      );
    }
    
    // Get the public URL
    const { data: urlData } = adminClient.storage
      .from('company-logos')
      .getPublicUrl(filePath);
      
    if (!urlData || !urlData.publicUrl) {
      return NextResponse.json(
        { error: 'Failed to get public URL' },
        { status: 500 }
      );
    }
    
    console.log('Server: File uploaded successfully, updating organization record...');

    // Update the organization record with the new logo URL using admin client
    const { error: updateError } = await adminClient
      .from('organizations')
      .update({
        logo_url: urlData.publicUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', bpoId);
      
    if (updateError) {
      console.error('Server DB update error:', updateError);
      return NextResponse.json(
        { error: `Failed to update profile: ${updateError.message}` },
        { status: 500 }
      );
    }
    
    console.log('Server: BPO record updated successfully');
    
    // Return the URL to the client
    return NextResponse.json({ 
      success: true,
      url: urlData.publicUrl
    });
    
  } catch (error) {
    console.error('Error in upload API route:', error);
    return NextResponse.json(
      { error: 'Server error processing upload' },
      { status: 500 }
    );
  }
} 