"use client"

import React, { useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Loader2 } from "lucide-react"
import { StaffSelector } from "./staff-selector"

const departmentFormSchema = z.object({
  name: z.string().min(1, "Department name is required"),
  description: z.string().optional(),
  department_head_id: z.string().optional(),
  staff_members: z.array(z.string()).default([]),
})

type DepartmentFormData = z.infer<typeof departmentFormSchema>

interface Department {
  id: string
  name: string
  description: string | null
  department_head_id?: string | null
}

interface DepartmentFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  department?: Department | null
  onSubmit: (data: DepartmentFormData) => Promise<void>
  loading?: boolean
  orgSlug: string
}

export function DepartmentForm({
  open,
  onOpenChange,
  department,
  onSubmit,
  loading = false,
  orgSlug,
}: DepartmentFormProps) {
  const form = useForm<DepartmentFormData>({
    resolver: zodResolver(departmentFormSchema),
    defaultValues: {
      name: "",
      description: "",
      department_head_id: undefined,
      staff_members: [],
    },
  })

  const isEdit = !!department

  // Reset form when department changes
  useEffect(() => {
    if (department) {
      form.reset({
        name: department.name,
        description: department.description || "",
        department_head_id: department.department_head_id || undefined,
        staff_members: [],
      })
    } else {
      form.reset({
        name: "",
        description: "",
        department_head_id: undefined,
        staff_members: [],
      })
    }
  }, [department, form])

  const handleFormSubmit = async (data: DepartmentFormData) => {
    await onSubmit(data)
    if (!isEdit) {
      form.reset()
    }
  }

  const handleClose = () => {
    form.reset()
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? "Edit Department" : "Create New Department"}
          </DialogTitle>
          <DialogDescription>
            {isEdit
              ? "Update the department information and staff assignments."
              : "Create a new department and assign staff members."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter department name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter department description (optional)"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <StaffSelector
                  orgSlug={orgSlug}
                  selectedStaff={form.watch("staff_members")}
                  onStaffChange={(staffIds) => form.setValue("staff_members", staffIds)}
                  selectedHead={form.watch("department_head_id")}
                  onHeadChange={(headId) => form.setValue("department_head_id", headId)}
                  excludeDepartmentId={department?.id}
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEdit ? "Update Department" : "Create Department"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
