'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Plus,
  FileText,
  Edit,
  Trash2,
  Play,
  Eye,
  Copy,
  Save,
  X,
  Code,
  Brain
} from 'lucide-react';

type PromptTemplate = {
  id: string;
  name: string;
  description: string;
  assessment_type: string;
  category: string;
  system_prompt: string;
  default_parameters: Record<string, any>;
  variable_definitions: Array<{
    name: string;
    type: string;
    description: string;
    default_value: any;
  }>;
  version: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
};

const ASSESSMENT_CATEGORIES = [
  { value: 'communication_skills', label: 'Communication Skills' },
  { value: 'cognitive_ability', label: 'Cognitive Ability' },
  { value: 'technical_skills', label: 'Technical Skills' },
  { value: 'emotional_intelligence', label: 'Emotional Intelligence' },
  { value: 'industry_knowledge', label: 'Industry Knowledge' },
  { value: 'stress_resilience', label: 'Stress Resilience' }
];

export default function PromptTemplatesPage() {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [testingPrompt, setTestingPrompt] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/ai-prompt-templates');
      
      if (!response.ok) {
        throw new Error('Failed to fetch prompt templates');
      }
      
      const data = await response.json();
      setTemplates(data.templates || []);
    } catch (error: any) {
      console.error('Error fetching templates:', error);
      toast({
        title: "Error",
        description: error.message || 'Failed to fetch prompt templates',
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = () => {
    const newTemplate: PromptTemplate = {
      id: '',
      name: '',
      description: '',
      assessment_type: '',
      category: 'communication_skills',
      system_prompt: '',
      default_parameters: {},
      variable_definitions: [],
      version: 1,
      is_active: true,
      created_at: '',
      updated_at: ''
    };
    setEditingTemplate(newTemplate);
    setIsCreating(true);
  };

  const handleSaveTemplate = async (template: PromptTemplate) => {
    try {
      const url = isCreating 
        ? '/api/admin/ai-prompt-templates'
        : `/api/admin/ai-prompt-templates/${template.id}`;
      
      const method = isCreating ? 'POST' : 'PUT';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(template)
      });

      if (!response.ok) {
        throw new Error('Failed to save prompt template');
      }

      const data = await response.json();
      
      toast({
        title: "Success",
        description: `Prompt template ${isCreating ? 'created' : 'updated'} successfully`,
      });

      setEditingTemplate(null);
      setIsCreating(false);
      fetchTemplates();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || 'Failed to save prompt template',
        variant: "destructive",
      });
    }
  };

  const handleTestPrompt = async (template: PromptTemplate) => {
    try {
      setTestingPrompt(template.id);
      
      const response = await fetch('/api/admin/ai/generate-assessment/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          assessmentType: template.category,
          questionCount: 3,
          systemPrompt: template.system_prompt,
          parameters: template.default_parameters
        })
      });

      if (!response.ok) {
        throw new Error('Failed to test prompt');
      }

      const data = await response.json();
      
      toast({
        title: "Test Successful",
        description: `Generated ${data.test_result.questions_generated} questions with quality score ${data.test_result.quality_score}`,
      });
    } catch (error: any) {
      toast({
        title: "Test Failed",
        description: error.message || 'Failed to test prompt template',
        variant: "destructive",
      });
    } finally {
      setTestingPrompt(null);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      communication_skills: 'bg-blue-100 text-blue-800',
      cognitive_ability: 'bg-purple-100 text-purple-800',
      technical_skills: 'bg-green-100 text-green-800',
      emotional_intelligence: 'bg-pink-100 text-pink-800',
      industry_knowledge: 'bg-orange-100 text-orange-800',
      stress_resilience: 'bg-red-100 text-red-800'
    } as const;
    
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="px-[50px] pt-8 pb-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-2">Loading prompt templates...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="px-[50px] pt-8 pb-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Prompt Templates</h1>
          <p className="text-muted-foreground">
            Manage system prompts for AI assessment generation
          </p>
        </div>
        <Button onClick={handleCreateTemplate}>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </div>

      {/* Template Editor Modal */}
      {editingTemplate && (
        <Card className="border-2 border-blue-200">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5" />
                {isCreating ? 'Create New Template' : 'Edit Template'}
              </CardTitle>
              <Button variant="outline" size="sm" onClick={() => {
                setEditingTemplate(null);
                setIsCreating(false);
              }}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Template Name</Label>
                <Input
                  id="name"
                  value={editingTemplate.name}
                  onChange={(e) => setEditingTemplate({
                    ...editingTemplate,
                    name: e.target.value
                  })}
                  placeholder="Enter template name"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={editingTemplate.category}
                  onValueChange={(value) => setEditingTemplate({
                    ...editingTemplate,
                    category: value
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {ASSESSMENT_CATEGORIES.map((cat) => (
                      <SelectItem key={cat.value} value={cat.value}>
                        {cat.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={editingTemplate.description}
                onChange={(e) => setEditingTemplate({
                  ...editingTemplate,
                  description: e.target.value
                })}
                placeholder="Brief description of this template"
              />
            </div>

            <div>
              <Label htmlFor="system_prompt">System Prompt</Label>
              <Textarea
                id="system_prompt"
                value={editingTemplate.system_prompt}
                onChange={(e) => setEditingTemplate({
                  ...editingTemplate,
                  system_prompt: e.target.value
                })}
                placeholder="Enter the system prompt for AI generation..."
                rows={12}
                className="font-mono text-sm"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Use variables like {{question_count}}, {{assessment_type}}, {{subcategories}} in your prompt
              </p>
            </div>

            <div className="flex items-center gap-2">
              <Button onClick={() => handleSaveTemplate(editingTemplate)}>
                <Save className="h-4 w-4 mr-2" />
                {isCreating ? 'Create Template' : 'Save Changes'}
              </Button>
              <Button variant="outline" onClick={() => handleTestPrompt(editingTemplate)}>
                <Play className="h-4 w-4 mr-2" />
                Test Prompt
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Templates List */}
      {templates.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Prompt Templates</h3>
            <p className="text-muted-foreground mb-4">
              Create your first prompt template to start generating AI assessments.
            </p>
            <Button onClick={handleCreateTemplate}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Template
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {templates.map((template) => (
            <Card key={template.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {template.name}
                    </CardTitle>
                    <CardDescription>{template.description}</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={template.is_active ? "default" : "secondary"}>
                      {template.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                    <Badge className={getCategoryColor(template.category)}>
                      {template.category.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Version {template.version} • Updated {new Date(template.updated_at).toLocaleDateString()}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => handleTestPrompt(template)}
                      disabled={testingPrompt === template.id}
                    >
                      <Play className="h-3 w-3 mr-1" />
                      {testingPrompt === template.id ? 'Testing...' : 'Test'}
                    </Button>
                    <Button variant="outline" size="sm">
                      <Eye className="h-3 w-3 mr-1" />
                      View
                    </Button>
                    <Button variant="outline" size="sm">
                      <Copy className="h-3 w-3 mr-1" />
                      Clone
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setEditingTemplate(template)}
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
