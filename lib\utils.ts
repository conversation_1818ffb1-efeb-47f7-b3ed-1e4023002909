import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// =============================================================================
// ERROR HANDLING UTILITIES
// =============================================================================

/**
 * Standard error types for the application
 */
export enum ErrorType {
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  DATABASE = 'DATABASE',
  NETWORK = 'NETWORK',
  FILE_UPLOAD = 'FILE_UPLOAD',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMIT = 'RATE_LIMIT',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN = 'UNKNOWN'
}

/**
 * Standard error interface
 */
export interface AppError {
  type: ErrorType;
  message: string;
  userMessage: string;
  code?: string;
  details?: any;
  timestamp: string;
  userId?: string;
  requestId?: string;
}

/**
 * Create a standardized error object
 */
export function createError(
  type: ErrorType,
  message: string,
  userMessage?: string,
  details?: any
): AppError {
  return {
    type,
    message,
    userMessage: userMessage || getUserFriendlyMessage(type, message),
    details,
    timestamp: new Date().toISOString(),
    requestId: generateRequestId()
  };
}

/**
 * Generate a unique request ID for error tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get user-friendly error messages based on error type
 */
function getUserFriendlyMessage(type: ErrorType, originalMessage: string): string {
  switch (type) {
    case ErrorType.AUTHENTICATION:
      return 'Please log in to continue.';
    case ErrorType.AUTHORIZATION:
      return 'You don\'t have permission to perform this action.';
    case ErrorType.VALIDATION:
      return 'Please check your input and try again.';
    case ErrorType.DATABASE:
      return 'We\'re experiencing technical difficulties. Please try again later.';
    case ErrorType.NETWORK:
      return 'Connection problem. Please check your internet and try again.';
    case ErrorType.FILE_UPLOAD:
      return 'File upload failed. Please try again with a different file.';
    case ErrorType.NOT_FOUND:
      return 'The requested resource was not found.';
    case ErrorType.RATE_LIMIT:
      return 'Too many requests. Please wait a moment and try again.';
    case ErrorType.SERVER_ERROR:
      return 'Server error occurred. Our team has been notified.';
    default:
      return 'An unexpected error occurred. Please try again.';
  }
}

/**
 * Log error to console with structured format
 */
export function logError(error: AppError, context?: string): void {
  const logData = {
    ...error,
    context,
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    url: typeof window !== 'undefined' ? window.location.href : 'server'
  };

  console.error(`[${error.type}] ${error.message}`, logData);

  // In production, you would send this to an error monitoring service
  // Example: Sentry, LogRocket, DataDog, etc.
  if (process.env.NODE_ENV === 'production') {
    // sendToErrorMonitoring(logData);
  }
}

/**
 * Handle Supabase errors and convert to AppError
 */
export function handleSupabaseError(error: any, context?: string): AppError {
  let errorType = ErrorType.DATABASE;
  let userMessage = '';

  // Handle specific Supabase error codes
  if (error?.code) {
    switch (error.code) {
      case 'PGRST301':
      case '42501':
        errorType = ErrorType.AUTHORIZATION;
        userMessage = 'You don\'t have permission to access this data.';
        break;
      case '23505':
        errorType = ErrorType.VALIDATION;
        userMessage = 'This record already exists.';
        break;
      case '23503':
        errorType = ErrorType.VALIDATION;
        userMessage = 'Cannot delete this record because it\'s being used elsewhere.';
        break;
      case 'PGRST116':
        errorType = ErrorType.NOT_FOUND;
        userMessage = 'The requested data was not found.';
        break;
      default:
        errorType = ErrorType.DATABASE;
        userMessage = 'Database error occurred. Please try again.';
    }
  }

  const appError = createError(
    errorType,
    `Supabase error: ${error?.message || 'Unknown database error'}`,
    userMessage,
    { supabaseError: error, context }
  );

  logError(appError, context);
  return appError;
}

/**
 * Handle API fetch errors
 */
export function handleFetchError(error: any, context?: string): AppError {
  let errorType = ErrorType.NETWORK;
  let userMessage = '';

  if (error instanceof TypeError && error.message.includes('fetch')) {
    errorType = ErrorType.NETWORK;
    userMessage = 'Network connection failed. Please check your internet connection.';
  } else if (error?.status) {
    switch (error.status) {
      case 401:
        errorType = ErrorType.AUTHENTICATION;
        userMessage = 'Please log in to continue.';
        break;
      case 403:
        errorType = ErrorType.AUTHORIZATION;
        userMessage = 'You don\'t have permission to perform this action.';
        break;
      case 404:
        errorType = ErrorType.NOT_FOUND;
        userMessage = 'The requested resource was not found.';
        break;
      case 429:
        errorType = ErrorType.RATE_LIMIT;
        userMessage = 'Too many requests. Please wait and try again.';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        errorType = ErrorType.SERVER_ERROR;
        userMessage = 'Server error occurred. Please try again later.';
        break;
      default:
        errorType = ErrorType.UNKNOWN;
        userMessage = 'An unexpected error occurred.';
    }
  }

  const appError = createError(
    errorType,
    `Fetch error: ${error?.message || 'Unknown network error'}`,
    userMessage,
    { fetchError: error, context }
  );

  logError(appError, context);
  return appError;
}
