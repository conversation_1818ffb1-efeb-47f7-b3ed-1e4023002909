import "./globals.css"
import { Inter } from "next/font/google"
import { ThemeProvider } from "@/components/theme-provider"
import { AppInitializer } from "@/components/app-initializer"
import { Toaster } from "sonner"
import { <PERSON>AuthProvider, CurrentContextProvider } from "@/hooks/use-luna-auth"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Luna Skills Platform",
  description: "Empowering individuals and organizations with comprehensive skills development, training, and assessment solutions",
  keywords: "skills development, training platform, assessments, professional development, Luna",
  authors: [{ name: "Luna Platform" }],
  creator: "Luna Platform",
  publisher: "Luna Platform",
  icons: {
    icon: "/Luna_Favicon_Caps.jpg",
    shortcut: "/Luna_Favicon_Caps.jpg",
    apple: "/Luna_Favicon_Caps.jpg",
  },
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://luna.app'),
  openGraph: {
    title: "Luna Skills Platform",
    description: "Empowering individuals and organizations with comprehensive skills development, training, and assessment solutions",
    url: "/",
    siteName: "Luna Skills Platform",
    type: "website",
    images: [
      {
        url: "/Luna_Logo_Dark.PNG",
        width: 1200,
        height: 630,
        alt: "Luna Skills Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Luna Skills Platform",
    description: "Empowering individuals and organizations with comprehensive skills development, training, and assessment solutions",
    images: ["/Luna_Logo_Dark.PNG"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/Luna_favicon.PNG" type="image/png" />
        {/* Development: Disable caching */}
        {process.env.NODE_ENV === 'development' && (
          <>
            <meta httpEquiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
            <meta httpEquiv="Pragma" content="no-cache" />
            <meta httpEquiv="Expires" content="0" />
          </>
        )}
      </head>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <LunaAuthProvider>
            <CurrentContextProvider>
              <AppInitializer />
              {children}
              <Toaster />
            </CurrentContextProvider>
          </LunaAuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
