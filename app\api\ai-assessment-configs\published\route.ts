import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { createClient } from '@/lib/supabase-server';

/**
 * Get published AI assessment configurations for users
 * GET /api/ai-assessment-configs/published
 */
export async function GET(req: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const supabase = createClient();

    // Fetch published AI assessment configurations
    const { data: configs, error } = await supabase
      .from('ai_assessment_configs')
      .select(`
        id,
        name,
        description,
        category,
        question_count,
        duration_minutes,
        passing_score,
        retake_limit,
        retake_fee,
        certification_eligible,
        subcategories,
        difficulty_distribution,
        status,
        created_at,
        updated_at
      `)
      .eq('status', 'published')
      .order('category', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching published AI assessment configs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch AI assessment configurations' },
        { status: 500 }
      );
    }

    // Get statistics
    const totalConfigs = configs?.length || 0;
    const configsByCategory = configs?.reduce((acc, config) => {
      acc[config.category] = (acc[config.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return NextResponse.json({
      configs: configs || [],
      statistics: {
        total: totalConfigs,
        by_category: configsByCategory
      }
    });

  } catch (error: any) {
    console.error('Published AI assessment configs fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch AI assessment configurations' },
      { status: 500 }
    );
  }
}
