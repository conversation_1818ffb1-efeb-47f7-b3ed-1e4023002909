'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Clock, AlertCircle, Check, X } from 'lucide-react';
import { <PERSON>, CardContent, <PERSON>Footer, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { AssessmentQuestion } from '@/types/assessment';

interface TypingTestProps {
  question: AssessmentQuestion;
  onComplete: (answer: string, stats: TypingTestStats) => void;
  timeLimit?: number;
}

export interface TypingTestStats {
  wpm: number;
  accuracy: number;
  completionTime: number;
  errors: number;
  grammarErrors: string[];
}

export function TypingTest({ question, onComplete, timeLimit = 300 }: TypingTestProps) {
  const [input, setInput] = useState('');
  const [started, setStarted] = useState(false);
  const [completed, setCompleted] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime, setStartTime] = useState(0);
  const [stats, setStats] = useState<TypingTestStats | null>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const isMounted = useRef(true);
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const targetText = question.correct_answer || '';

  // Cleanup function to prevent setState after unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
    };
  }, []);

  // Start timer when user begins typing
  useEffect(() => {
    if (started && !completed) {
      // Clear any existing timer first
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
      }
      
      // Use requestAnimationFrame for smoother updates
      let lastUpdateTime = Date.now();
      
      // Start a timer to track elapsed time
      timerIntervalRef.current = setInterval(() => {
        if (isMounted.current) {
          // Ensure we're updating based on actual time passed
          const now = Date.now();
          const delta = Math.floor((now - lastUpdateTime) / 1000);
          
          if (delta >= 1) {
            setElapsedTime(prev => prev + delta);
            lastUpdateTime = now;
          }
        }
      }, 500); // Check more frequently for more accurate updates
      
      return () => {
        if (timerIntervalRef.current) {
          clearInterval(timerIntervalRef.current);
        }
      };
    }
  }, [started, completed]);

  // Auto-focus the textarea when starting
  useEffect(() => {
    if (started && textAreaRef.current) {
      textAreaRef.current.focus();
    }
  }, [started]);

  const handleStart = () => {
    // Ensure we record the exact time we're starting
    const now = Date.now();
    setStarted(true);
    setStartTime(now);
    setElapsedTime(0);
    
    // Force focus on textarea
    if (textAreaRef.current) {
      textAreaRef.current.focus();
    }
    
    // We don't need to manually start the timer here
    // The useEffect with [started, completed] dependencies will handle it
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    
    // If this is the first input, start the timer
    if (!started && e.target.value.length > 0) {
      handleStart();
    }
  };

  const handleSubmit = () => {
    if (!started || completed) return;
    
    // Stop the timer
    if (timerIntervalRef.current) {
      clearInterval(timerIntervalRef.current);
    }
    
    // Calculate completion time - ensure at least 1 second
    const completionTime = Math.max(1, elapsedTime); 
    
    // Calculate words per minute
    const inputWords = input.trim().split(/\s+/);
    const words = inputWords.length;
    const minutes = completionTime / 60; 
    // Avoid division by zero and cap unrealistic WPM
    const calculatedWPM = words / (minutes || 1); 
    const wpm = Math.min(Math.round(calculatedWPM), 300); // Cap at 300 WPM which is a realistic upper limit
    
    // Calculate accuracy
    const targetWords = targetText.split(/\s+/);
    
    let correctWords = 0;
    let totalErrors = 0;
    
    // Compare each word for accuracy
    for (let i = 0; i < Math.min(targetWords.length, inputWords.length); i++) {
      if (targetWords[i] === inputWords[i]) {
        correctWords++;
      } else {
        totalErrors++;
      }
    }
    
    // Count missing or extra words as errors
    totalErrors += Math.abs(targetWords.length - inputWords.length);
    
    const accuracy = targetWords.length > 0 
      ? Math.round((correctWords / targetWords.length) * 100)
      : 0;
    
    // Basic grammar checking
    const grammarErrors: string[] = [];
    
    // Check for capitalization issues
    const sentences = input.split(/[.!?]+\s+/);
    for (const sentence of sentences) {
      if (sentence.length > 0 && sentence[0] !== sentence[0].toUpperCase()) {
        grammarErrors.push('Missing capitalization at the beginning of a sentence');
        break;
      }
    }
    
    // Check for double spaces
    if (input.includes('  ')) {
      grammarErrors.push('Double spaces detected');
    }
    
    // Check for missing periods at end of sentences
    if (!/[.!?]$/.test(input.trim()) && input.trim().length > 0) {
      grammarErrors.push('Missing punctuation at the end of text');
    }
    
    const typingStats: TypingTestStats = {
      wpm,
      accuracy,
      completionTime,
      errors: totalErrors,
      grammarErrors,
    };
    
    setStats(typingStats);
    setCompleted(true);
    onComplete(input, typingStats);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Calculate word counts
  const inputWordCount = input.trim() ? input.trim().split(/\s+/).length : 0;
  const targetWordCount = targetText.trim() ? targetText.trim().split(/\s+/).length : 0;

  return (
    <Card className="w-full">
      <CardHeader className="relative">
        <CardTitle>Typing Test</CardTitle>
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Type the text exactly as shown below
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="font-mono">
              {formatTime(elapsedTime)}
              {!started && <span className="text-muted-foreground"> (Timer starts when you begin typing)</span>}
            </span>
          </div>
        </div>
        {started && !completed && (
          <div className="mt-2 text-sm text-muted-foreground text-right">
            Time elapsed: {formatTime(elapsedTime)}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Question text display */}
        {question.question_text && (
          <div className="bg-white p-4 rounded-md text-md font-medium border-b mb-2">
            <h3 className="text-lg font-semibold mb-2">Question:</h3>
            <p>{question.question_text}</p>
          </div>
        )}
        
        {/* Target text display */}
        <div className="bg-muted p-4 rounded-md text-sm font-medium border">
          {targetText}
        </div>
        
        {!completed ? (
          <div className="space-y-2">
            <textarea
              ref={textAreaRef}
              className="w-full h-32 p-3 border rounded-md focus:ring-2 focus:ring-primary"
              placeholder="Start typing here..."
              value={input}
              onChange={handleChange}
              disabled={completed}
            />
            
            {started && (
              <div className="flex justify-between text-sm">
                <div>
                  Words: {inputWordCount} / {targetWordCount}
                </div>
                <div>
                  Progress: {Math.min(100, Math.round((inputWordCount / targetWordCount) * 100))}%
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-muted rounded-md p-3">
                <div className="text-sm text-muted-foreground">Typing Speed</div>
                <div className="text-2xl font-bold">{stats?.wpm} WPM</div>
              </div>
              <div className="bg-muted rounded-md p-3">
                <div className="text-sm text-muted-foreground">Accuracy</div>
                <div className="text-2xl font-bold">{stats?.accuracy}%</div>
              </div>
            </div>
            
            {stats?.grammarErrors && stats.grammarErrors.length > 0 && (
              <div className="border border-amber-200 bg-amber-50 rounded-md p-3">
                <h4 className="font-medium text-amber-800 mb-2">Grammar Issues</h4>
                <ul className="text-sm text-amber-700 space-y-1">
                  {stats.grammarErrors.map((error, i) => (
                    <li key={i} className="flex items-start gap-2">
                      <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                      <span>{error}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="border rounded-md">
              <div className="p-3 border-b bg-muted">
                <h4 className="font-medium">Your Submission</h4>
              </div>
              <div className="p-3 text-sm whitespace-pre-wrap">
                {input}
              </div>
            </div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-end">
        {!started ? (
          <Button onClick={handleStart}>Start Typing Test</Button>
        ) : !completed ? (
          <Button onClick={handleSubmit}>
            Submit
          </Button>
        ) : (
          <div className="flex gap-2">
            <Badge variant={stats?.accuracy && stats.accuracy >= 90 ? "success" : "destructive"}>
              {stats?.accuracy && stats.accuracy >= 90 ? "Passed" : "Needs Improvement"}
            </Badge>
          </div>
        )}
      </CardFooter>
    </Card>
  );
} 