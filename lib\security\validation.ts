/**
 * Input Validation and Sanitization Module
 * <PERSON><PERSON> request validation and input sanitization
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-error-handler';

/**
 * Sanitize string input to prevent XSS and injection attacks
 */
function sanitizeString(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .replace(/script/gi, '') // Remove script tags
    .trim();
}

/**
 * Sanitize object recursively
 */
function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }
  
  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = sanitizeString(key);
      sanitized[sanitizedKey] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

/**
 * Input sanitization middleware
 */
export function withInputSanitization(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async function inputSanitizationMiddleware(
    request: NextRequest
  ): Promise<NextResponse> {
    try {
      // Skip sanitization for certain content types
      const contentType = request.headers.get('content-type') || '';
      
      // Skip for file uploads and binary data
      if (contentType.includes('multipart/form-data') || 
          contentType.includes('application/octet-stream') ||
          contentType.includes('image/') ||
          contentType.includes('video/') ||
          contentType.includes('audio/')) {
        return handler(request);
      }
      
      // For JSON requests, sanitize the body
      if (contentType.includes('application/json')) {
        try {
          const body = await request.json();
          const sanitizedBody = sanitizeObject(body);
          
          // Create new request with sanitized body
          const sanitizedRequest = new NextRequest(request.url, {
            method: request.method,
            headers: request.headers,
            body: JSON.stringify(sanitizedBody)
          });
          
          return handler(sanitizedRequest);
        } catch (error) {
          // If JSON parsing fails, continue with original request
          return handler(request);
        }
      }
      
      // For other requests, continue without modification
      return handler(request);
      
    } catch (error) {
      return handleApiError(error, 'Input Sanitization Error');
    }
  };
}

/**
 * Request size limit middleware
 */
export function withRequestSizeLimit(maxSizeBytes: number = 1024 * 1024) { // 1MB default
  return function requestSizeLimitMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        // Check Content-Length header
        const contentLength = request.headers.get('content-length');
        
        if (contentLength) {
          const size = parseInt(contentLength, 10);
          
          if (size > maxSizeBytes) {
            return NextResponse.json(
              {
                error: 'Payload Too Large',
                message: `Request size ${size} bytes exceeds limit of ${maxSizeBytes} bytes`,
                maxSize: maxSizeBytes
              },
              { status: 413 }
            );
          }
        }
        
        return handler(request);
        
      } catch (error) {
        return handleApiError(error, 'Request Size Limit Error');
      }
    };
  };
}

/**
 * Validate request method
 */
export function withMethodValidation(allowedMethods: string[]) {
  return function methodValidationMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      if (!allowedMethods.includes(request.method)) {
        return NextResponse.json(
          {
            error: 'Method Not Allowed',
            message: `Method ${request.method} is not allowed for this endpoint`,
            allowedMethods
          },
          { 
            status: 405,
            headers: {
              'Allow': allowedMethods.join(', ')
            }
          }
        );
      }
      
      return handler(request);
    };
  };
}

/**
 * Validate required headers
 */
export function withHeaderValidation(requiredHeaders: string[]) {
  return function headerValidationMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      const missingHeaders: string[] = [];
      
      for (const header of requiredHeaders) {
        if (!request.headers.get(header)) {
          missingHeaders.push(header);
        }
      }
      
      if (missingHeaders.length > 0) {
        return NextResponse.json(
          {
            error: 'Missing Required Headers',
            message: 'One or more required headers are missing',
            missingHeaders
          },
          { status: 400 }
        );
      }
      
      return handler(request);
    };
  };
}

/**
 * Basic request validation
 */
export interface RequestValidationConfig {
  maxSize?: number;
  allowedMethods?: string[];
  requiredHeaders?: string[];
  sanitizeInput?: boolean;
}

/**
 * Comprehensive request validation middleware
 */
export function withRequestValidation(config: RequestValidationConfig = {}) {
  return function requestValidationMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    let validatedHandler = handler;
    
    // Apply validations in reverse order (last applied = first executed)
    if (config.sanitizeInput !== false) {
      validatedHandler = withInputSanitization(validatedHandler);
    }
    
    if (config.requiredHeaders && config.requiredHeaders.length > 0) {
      validatedHandler = withHeaderValidation(config.requiredHeaders)(validatedHandler);
    }
    
    if (config.allowedMethods && config.allowedMethods.length > 0) {
      validatedHandler = withMethodValidation(config.allowedMethods)(validatedHandler);
    }
    
    if (config.maxSize) {
      validatedHandler = withRequestSizeLimit(config.maxSize)(validatedHandler);
    }
    
    return validatedHandler;
  };
}

/**
 * Validate JSON schema (basic implementation)
 */
export function validateJsonSchema(data: any, schema: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Basic type checking
  if (schema.type) {
    const actualType = Array.isArray(data) ? 'array' : typeof data;
    if (actualType !== schema.type) {
      errors.push(`Expected type ${schema.type}, got ${actualType}`);
    }
  }
  
  // Required fields
  if (schema.required && Array.isArray(schema.required)) {
    for (const field of schema.required) {
      if (!(field in data)) {
        errors.push(`Missing required field: ${field}`);
      }
    }
  }
  
  // Properties validation
  if (schema.properties && typeof data === 'object' && data !== null) {
    for (const [key, value] of Object.entries(data)) {
      if (schema.properties[key]) {
        const fieldValidation = validateJsonSchema(value, schema.properties[key]);
        errors.push(...fieldValidation.errors.map(err => `${key}: ${err}`));
      }
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * JSON schema validation middleware
 */
export function withJsonSchemaValidation(schema: any) {
  return function jsonSchemaValidationMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        const contentType = request.headers.get('content-type') || '';
        
        if (contentType.includes('application/json')) {
          const body = await request.json();
          const validation = validateJsonSchema(body, schema);
          
          if (!validation.valid) {
            return NextResponse.json(
              {
                error: 'Validation Error',
                message: 'Request body does not match required schema',
                errors: validation.errors
              },
              { status: 400 }
            );
          }
          
          // Create new request with validated body
          const validatedRequest = new NextRequest(request.url, {
            method: request.method,
            headers: request.headers,
            body: JSON.stringify(body)
          });
          
          return handler(validatedRequest);
        }
        
        return handler(request);
        
      } catch (error) {
        return handleApiError(error, 'JSON Schema Validation Error');
      }
    };
  };
}
