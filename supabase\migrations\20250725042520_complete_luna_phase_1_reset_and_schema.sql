-- =============================================================================
-- LUNA PHASE 1: COMPLETE DATABASE RESET AND SCHEMA
-- This migration will completely reset the database and apply the new Luna schema
-- =============================================================================

-- =============================================================================
-- STEP 1: COMPLETE DATABASE RESET
-- =============================================================================

-- Drop all existing tables in reverse dependency order
DROP TABLE IF EXISTS user_training_data CASCADE;
DROP TABLE IF EXISTS user_contexts CASCADE;
DROP TABLE IF EXISTS team_memberships CASCADE;
DROP TABLE IF EXISTS teams CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;
DROP TABLE IF EXISTS individuals CASCADE;
DROP TABLE IF EXISTS learning_paths CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop any remaining old tables
DROP TABLE IF EXISTS prospects CASCADE;
DROP TABLE IF EXISTS organization_memberships CASCADE;
DROP TABLE IF EXISTS bpo_companies CASCADE;
DROP TABLE IF EXISTS training_data CASCADE;
DROP TABLE IF EXISTS modules CASCADE;
DROP TABLE IF EXISTS lessons CASCADE;
DROP TABLE IF EXISTS activities CASCADE;

-- Drop existing types
DROP TYPE IF EXISTS training_status CASCADE;
DROP TYPE IF EXISTS learning_status CASCADE;
DROP TYPE IF EXISTS profile_visibility CASCADE;
DROP TYPE IF EXISTS org_status CASCADE;
DROP TYPE IF EXISTS context_type CASCADE;
DROP TYPE IF EXISTS membership_status CASCADE;
DROP TYPE IF EXISTS team_member_role CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;

-- Drop existing functions
DROP FUNCTION IF EXISTS validate_luna_schema() CASCADE;
DROP FUNCTION IF EXISTS invite_to_team(UUID, UUID, VARCHAR, team_member_role, TEXT) CASCADE;
DROP FUNCTION IF EXISTS create_team(UUID, UUID, VARCHAR, TEXT) CASCADE;
DROP FUNCTION IF EXISTS get_user_available_contexts(UUID) CASCADE;
DROP FUNCTION IF EXISTS switch_user_context(UUID, context_type, UUID) CASCADE;
DROP FUNCTION IF EXISTS generate_unique_slug(TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Drop any existing policies
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public')
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(r.policyname) || ' ON ' || quote_ident(r.schemaname) || '.' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
END $$;

-- Drop any existing triggers
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (SELECT trigger_name, event_object_table FROM information_schema.triggers WHERE trigger_schema = 'public')
    LOOP
        EXECUTE 'DROP TRIGGER IF EXISTS ' || quote_ident(r.trigger_name) || ' ON ' || quote_ident(r.event_object_table) || ' CASCADE';
    END LOOP;
END $$;

-- =============================================================================
-- STEP 2: CREATE NEW LUNA SCHEMA
-- =============================================================================

-- =============================================================================
-- ENUM TYPES
-- =============================================================================

-- User roles for individual-first design
CREATE TYPE user_role AS ENUM (
  'platform_admin',    -- Luna platform administrators
  'individual',        -- Standalone individual users (default)
  'org_owner',        -- Organization owners
  'org_admin',        -- Organization administrators
  'org_member'        -- Organization team members
);

-- Team member roles within teams
CREATE TYPE team_member_role AS ENUM (
  'owner',            -- Team owner (creator)
  'admin',            -- Team administrator
  'manager',          -- Team manager
  'member',           -- Regular team member
  'viewer'            -- Read-only access
);

-- Membership status for team memberships
CREATE TYPE membership_status AS ENUM (
  'pending',          -- Invitation sent, not accepted
  'active',           -- Active team member
  'inactive',         -- Temporarily inactive
  'removed'           -- Removed from team
);

-- Context types for user context switching
CREATE TYPE context_type AS ENUM (
  'individual',       -- Individual user context
  'team'              -- Team collaboration context
);

-- Organization status
CREATE TYPE org_status AS ENUM (
  'active',           -- Active organization
  'suspended',        -- Temporarily suspended
  'trial',            -- Trial period
  'cancelled'         -- Cancelled subscription
);

-- Profile visibility settings
CREATE TYPE profile_visibility AS ENUM (
  'private',          -- Only visible to user
  'team',             -- Visible to team members
  'organization',     -- Visible to organization members
  'public'            -- Publicly visible
);

-- Learning status for individuals
CREATE TYPE learning_status AS ENUM (
  'not_started',      -- Haven't started learning
  'in_progress',      -- Currently learning
  'completed',        -- Completed learning path
  'paused'            -- Temporarily paused
);

-- Training status for legacy compatibility
CREATE TYPE training_status AS ENUM (
  'not_started',
  'in_progress',
  'completed',
  'paused'
);

-- =============================================================================
-- CORE USER TABLES
-- =============================================================================

-- Enhanced Individual-First Users
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Authentication & Identity
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'individual',

  -- Profile Information
  avatar_url TEXT,
  bio TEXT,
  timezone VARCHAR(50) DEFAULT 'UTC',

  -- Individual Account Data
  personal_skills JSONB DEFAULT '[]',
  learning_preferences JSONB DEFAULT '{}',
  industry_interests TEXT[] DEFAULT '{}',
  career_goals JSONB DEFAULT '{}',

  -- Privacy & Visibility
  profile_visibility profile_visibility DEFAULT 'private',
  searchable_by_teams BOOLEAN DEFAULT false,
  allow_team_invitations BOOLEAN DEFAULT true,

  -- Status & Metadata
  status VARCHAR(20) DEFAULT 'active',
  last_active_at TIMESTAMPTZ,
  email_verified BOOLEAN DEFAULT false,

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_searchable ON users(searchable_by_teams) WHERE searchable_by_teams = true;

-- Individual Learning Profiles
CREATE TABLE individuals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

  -- Extended Profile Information
  contact_info JSONB DEFAULT '{}',
  education JSONB[] DEFAULT '{}',
  experience JSONB[] DEFAULT '{}',
  skills JSONB[] DEFAULT '{}',
  certifications JSONB[] DEFAULT '{}',

  -- Media Assets
  profile_image_url TEXT,
  intro_video_url TEXT,
  resume_url TEXT,
  portfolio_url TEXT,

  -- Learning Status & Progress
  learning_status learning_status DEFAULT 'not_started',
  current_learning_path_id UUID,
  total_learning_hours DECIMAL(10,2) DEFAULT 0,
  completed_courses INTEGER DEFAULT 0,

  -- Skills & Career Data
  skill_assessments JSONB DEFAULT '{}',
  career_interests JSONB DEFAULT '{}',
  salary_expectations JSONB DEFAULT '{}',

  -- Preferences
  learning_style JSONB DEFAULT '{}',
  availability JSONB DEFAULT '{}',
  notification_preferences JSONB DEFAULT '{}',

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  UNIQUE(user_id)
);

-- Indexes
CREATE INDEX idx_individuals_user_id ON individuals(user_id);
CREATE INDEX idx_individuals_learning_status ON individuals(learning_status);
CREATE INDEX idx_individuals_current_path ON individuals(current_learning_path_id);

-- =============================================================================
-- ORGANIZATION & TEAM TABLES
-- =============================================================================

-- Multi-Tenant Organizations
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Basic Information
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  industry VARCHAR(100),
  size_range VARCHAR(50),

  -- Branding & Customization
  logo_url TEXT,
  website_url TEXT,
  branding_config JSONB DEFAULT '{}',

  -- Multi-Tenancy Settings
  subdomain VARCHAR(50) UNIQUE,
  custom_domain VARCHAR(255),

  -- Subscription & Limits
  subscription_tier VARCHAR(50) DEFAULT 'basic',
  max_teams INTEGER DEFAULT 10,
  max_members INTEGER DEFAULT 100,
  features_enabled JSONB DEFAULT '{}',

  -- Ownership & Management
  created_by UUID NOT NULL REFERENCES users(id),
  status org_status DEFAULT 'active',

  -- Settings
  settings JSONB DEFAULT '{}',

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  CONSTRAINT valid_slug CHECK (slug ~* '^[a-z0-9-]+$'),
  CONSTRAINT valid_subdomain CHECK (subdomain IS NULL OR subdomain ~* '^[a-z0-9-]+$')
);

-- Indexes
CREATE INDEX idx_organizations_slug ON organizations(slug);
CREATE INDEX idx_organizations_subdomain ON organizations(subdomain);
CREATE INDEX idx_organizations_created_by ON organizations(created_by);
CREATE INDEX idx_organizations_status ON organizations(status);

-- Core Collaboration Entity
CREATE TABLE teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,

  -- Team Information
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) NOT NULL,
  description TEXT,

  -- Team Settings
  is_public BOOLEAN DEFAULT false,
  max_members INTEGER DEFAULT 50,

  -- Learning & Goals
  team_goals JSONB DEFAULT '{}',
  shared_learning_paths JSONB DEFAULT '[]',
  team_skills_focus JSONB DEFAULT '[]',

  -- Collaboration Features
  enable_chat BOOLEAN DEFAULT true,
  enable_file_sharing BOOLEAN DEFAULT true,
  enable_progress_sharing BOOLEAN DEFAULT true,

  -- Management
  created_by UUID NOT NULL REFERENCES users(id),
  status VARCHAR(20) DEFAULT 'active',

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  UNIQUE(organization_id, slug),
  CONSTRAINT valid_team_slug CHECK (slug ~* '^[a-z0-9-]+$')
);

-- Indexes
CREATE INDEX idx_teams_organization_id ON teams(organization_id);
CREATE INDEX idx_teams_slug ON teams(organization_id, slug);
CREATE INDEX idx_teams_created_by ON teams(created_by);
CREATE INDEX idx_teams_status ON teams(status);
CREATE INDEX idx_teams_public ON teams(is_public) WHERE is_public = true;

-- Team Membership Bridge
CREATE TABLE team_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,

  -- Membership Details
  role team_member_role NOT NULL DEFAULT 'member',
  status membership_status DEFAULT 'pending',

  -- Permissions & Settings
  permissions JSONB DEFAULT '{}',
  notification_settings JSONB DEFAULT '{}',

  -- Invitation Tracking
  invited_by UUID REFERENCES users(id),
  invited_at TIMESTAMPTZ,
  invitation_message TEXT,

  -- Membership Lifecycle
  joined_at TIMESTAMPTZ,
  left_at TIMESTAMPTZ,
  removed_by UUID REFERENCES users(id),
  removal_reason TEXT,

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  UNIQUE(user_id, team_id)
);

-- Indexes
CREATE INDEX idx_team_memberships_user_id ON team_memberships(user_id);
CREATE INDEX idx_team_memberships_team_id ON team_memberships(team_id);
CREATE INDEX idx_team_memberships_status ON team_memberships(status);
CREATE INDEX idx_team_memberships_role ON team_memberships(role);
CREATE INDEX idx_team_memberships_active ON team_memberships(user_id, team_id)
  WHERE status = 'active';

-- =============================================================================
-- CONTEXT MANAGEMENT TABLES
-- =============================================================================

-- Context Switching System
CREATE TABLE user_contexts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

  -- Current Active Context
  active_context context_type NOT NULL DEFAULT 'individual',
  active_team_id UUID REFERENCES teams(id),
  active_organization_id UUID REFERENCES organizations(id),

  -- Context History & Preferences
  recent_contexts JSONB DEFAULT '[]',
  preferred_context context_type DEFAULT 'individual',

  -- Session Management
  last_context_switch TIMESTAMPTZ DEFAULT NOW(),
  session_data JSONB DEFAULT '{}',

  -- Context-Specific Settings
  individual_settings JSONB DEFAULT '{}',
  team_settings JSONB DEFAULT '{}',

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  UNIQUE(user_id),
  CONSTRAINT valid_team_context CHECK (
    (active_context = 'team' AND active_team_id IS NOT NULL) OR
    (active_context = 'individual' AND active_team_id IS NULL)
  )
);

-- Indexes
CREATE INDEX idx_user_contexts_user_id ON user_contexts(user_id);
CREATE INDEX idx_user_contexts_active_team ON user_contexts(active_team_id);
CREATE INDEX idx_user_contexts_context_type ON user_contexts(active_context);

-- Context-Aware Training Data
CREATE TABLE user_training_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,

  -- Context Information
  context_type context_type NOT NULL DEFAULT 'individual',
  team_id UUID REFERENCES teams(id),
  organization_id UUID REFERENCES organizations(id),

  -- Training Progress
  training_status training_status DEFAULT 'not_started',
  current_module_id UUID,
  current_lesson_id UUID,

  -- Progress Tracking
  modules_completed JSONB DEFAULT '[]',
  lessons_completed JSONB DEFAULT '[]',
  activities_completed JSONB DEFAULT '[]',
  assessments_completed JSONB DEFAULT '[]',

  -- Performance Metrics
  total_time_spent INTEGER DEFAULT 0, -- in minutes
  completion_percentage DECIMAL(5,2) DEFAULT 0,
  average_score DECIMAL(5,2),

  -- Learning Path Data
  learning_path_id UUID,
  path_progress JSONB DEFAULT '{}',
  custom_goals JSONB DEFAULT '{}',

  -- Skills Development
  skills_before JSONB DEFAULT '{}',
  skills_after JSONB DEFAULT '{}',
  skills_improvement JSONB DEFAULT '{}',

  -- Data Continuity
  inherited_from_context UUID, -- Reference to previous context data
  data_migration_log JSONB DEFAULT '[]',

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  CONSTRAINT valid_context_data CHECK (
    (context_type = 'team' AND team_id IS NOT NULL) OR
    (context_type = 'individual' AND team_id IS NULL)
  )
);

-- Indexes
CREATE INDEX idx_training_data_user_id ON user_training_data(user_id);
CREATE INDEX idx_training_data_context ON user_training_data(context_type, team_id);
CREATE INDEX idx_training_data_status ON user_training_data(training_status);
CREATE INDEX idx_training_data_team ON user_training_data(team_id);
CREATE INDEX idx_training_data_path ON user_training_data(learning_path_id);

-- =============================================================================
-- LEARNING CONTENT TABLES
-- =============================================================================

-- Structured Learning Journeys
CREATE TABLE learning_paths (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

  -- Basic Information
  title VARCHAR(255) NOT NULL,
  description TEXT,
  slug VARCHAR(100) UNIQUE NOT NULL,

  -- Content Structure
  modules_sequence JSONB NOT NULL DEFAULT '[]',
  estimated_duration_hours INTEGER,
  difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),

  -- Targeting & Prerequisites
  target_roles JSONB DEFAULT '[]',
  prerequisites JSONB DEFAULT '[]',
  learning_objectives JSONB DEFAULT '[]',

  -- Skills & Outcomes
  skills_covered JSONB DEFAULT '[]',
  skills_gained JSONB DEFAULT '[]',
  competencies JSONB DEFAULT '[]',

  -- Visibility & Access
  is_public BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  organization_id UUID REFERENCES organizations(id),
  team_id UUID REFERENCES teams(id),

  -- Effectiveness Metrics
  completion_rate DECIMAL(5,2) DEFAULT 0,
  average_rating DECIMAL(3,2),
  total_enrollments INTEGER DEFAULT 0,

  -- Status & Metadata
  status VARCHAR(20) DEFAULT 'active',
  tags JSONB DEFAULT '[]',

  -- Audit Trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_learning_paths_slug ON learning_paths(slug);
CREATE INDEX idx_learning_paths_created_by ON learning_paths(created_by);
CREATE INDEX idx_learning_paths_organization ON learning_paths(organization_id);
CREATE INDEX idx_learning_paths_team ON learning_paths(team_id);
CREATE INDEX idx_learning_paths_public ON learning_paths(is_public) WHERE is_public = true;
CREATE INDEX idx_learning_paths_status ON learning_paths(status);

-- =============================================================================
-- DATABASE FUNCTIONS
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to generate unique slugs
CREATE OR REPLACE FUNCTION generate_unique_slug(base_text TEXT, table_name TEXT, column_name TEXT DEFAULT 'slug')
RETURNS TEXT AS $$
DECLARE
  slug TEXT;
  counter INTEGER := 0;
  final_slug TEXT;
BEGIN
  -- Create base slug
  slug := lower(regexp_replace(base_text, '[^a-zA-Z0-9\s]', '', 'g'));
  slug := regexp_replace(slug, '\s+', '-', 'g');
  slug := trim(both '-' from slug);

  -- Ensure uniqueness
  final_slug := slug;
  WHILE EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = table_name
    AND column_name = final_slug
  ) LOOP
    counter := counter + 1;
    final_slug := slug || '-' || counter;
  END LOOP;

  RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- Function to switch user context
CREATE OR REPLACE FUNCTION switch_user_context(
  p_user_id UUID,
  p_context_type context_type,
  p_team_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  v_organization_id UUID;
BEGIN
  -- Validate team membership if switching to team context
  IF p_context_type = 'team' THEN
    IF p_team_id IS NULL THEN
      RAISE EXCEPTION 'Team ID required for team context';
    END IF;

    -- Check if user is member of the team
    IF NOT EXISTS (
      SELECT 1 FROM team_memberships
      WHERE user_id = p_user_id
      AND team_id = p_team_id
      AND status = 'active'
    ) THEN
      RAISE EXCEPTION 'User is not an active member of this team';
    END IF;

    -- Get organization ID
    SELECT organization_id INTO v_organization_id
    FROM teams WHERE id = p_team_id;
  END IF;

  -- Update user context
  INSERT INTO user_contexts (user_id, active_context, active_team_id, active_organization_id)
  VALUES (p_user_id, p_context_type, p_team_id, v_organization_id)
  ON CONFLICT (user_id)
  DO UPDATE SET
    active_context = p_context_type,
    active_team_id = p_team_id,
    active_organization_id = v_organization_id,
    last_context_switch = NOW(),
    recent_contexts = jsonb_set(
      COALESCE(user_contexts.recent_contexts, '[]'::jsonb),
      '{0}',
      jsonb_build_object(
        'context_type', user_contexts.active_context,
        'team_id', user_contexts.active_team_id,
        'switched_at', user_contexts.last_context_switch
      )
    );

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's available contexts
CREATE OR REPLACE FUNCTION get_user_available_contexts(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT jsonb_build_object(
    'individual', jsonb_build_object(
      'type', 'individual',
      'name', 'Personal Learning',
      'available', true
    ),
    'teams', COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'type', 'team',
          'team_id', t.id,
          'team_name', t.name,
          'team_slug', t.slug,
          'organization_name', o.name,
          'organization_slug', o.slug,
          'role', tm.role,
          'available', tm.status = 'active'
        )
      ) FILTER (WHERE t.id IS NOT NULL),
      '[]'::jsonb
    )
  ) INTO result
  FROM team_memberships tm
  LEFT JOIN teams t ON tm.team_id = t.id
  LEFT JOIN organizations o ON t.organization_id = o.id
  WHERE tm.user_id = p_user_id;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a new team
CREATE OR REPLACE FUNCTION create_team(
  p_organization_id UUID,
  p_creator_id UUID,
  p_name VARCHAR(255),
  p_description TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_team_id UUID;
  v_slug VARCHAR(100);
BEGIN
  -- Generate unique slug
  v_slug := generate_unique_slug(p_name, 'teams', 'slug');

  -- Create team
  INSERT INTO teams (
    organization_id, name, slug, description, created_by
  ) VALUES (
    p_organization_id, p_name, v_slug, p_description, p_creator_id
  ) RETURNING id INTO v_team_id;

  -- Add creator as team owner
  INSERT INTO team_memberships (
    user_id, team_id, role, status, joined_at
  ) VALUES (
    p_creator_id, v_team_id, 'owner', 'active', NOW()
  );

  RETURN v_team_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to invite user to team
CREATE OR REPLACE FUNCTION invite_to_team(
  p_team_id UUID,
  p_inviter_id UUID,
  p_invitee_email VARCHAR(255),
  p_role team_member_role DEFAULT 'member',
  p_message TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_invitee_id UUID;
  v_membership_id UUID;
BEGIN
  -- Get invitee user ID
  SELECT id INTO v_invitee_id FROM users WHERE email = p_invitee_email;

  IF v_invitee_id IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', p_invitee_email;
  END IF;

  -- Check if already a member
  IF EXISTS (
    SELECT 1 FROM team_memberships
    WHERE user_id = v_invitee_id AND team_id = p_team_id
  ) THEN
    RAISE EXCEPTION 'User is already a member of this team';
  END IF;

  -- Create invitation
  INSERT INTO team_memberships (
    user_id, team_id, role, status, invited_by, invited_at, invitation_message
  ) VALUES (
    v_invitee_id, p_team_id, p_role, 'pending', p_inviter_id, NOW(), p_message
  ) RETURNING id INTO v_membership_id;

  RETURN v_membership_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate schema integrity
CREATE OR REPLACE FUNCTION validate_luna_schema()
RETURNS TABLE(check_name TEXT, status TEXT, details TEXT) AS $$
BEGIN
  -- Check if all required tables exist
  RETURN QUERY
  SELECT
    'Required Tables'::TEXT,
    CASE WHEN COUNT(*) = 8 THEN 'PASS' ELSE 'FAIL' END::TEXT,
    'Found ' || COUNT(*) || ' of 8 required tables'::TEXT
  FROM information_schema.tables
  WHERE table_name IN (
    'users', 'individuals', 'organizations', 'teams',
    'team_memberships', 'user_contexts', 'user_training_data', 'learning_paths'
  );

  -- Check if all RLS policies are enabled
  RETURN QUERY
  SELECT
    'RLS Policies'::TEXT,
    CASE WHEN COUNT(*) >= 8 THEN 'PASS' ELSE 'FAIL' END::TEXT,
    'Found ' || COUNT(*) || ' RLS policies'::TEXT
  FROM pg_policies
  WHERE schemaname = 'public';

  -- Check if all triggers are created
  RETURN QUERY
  SELECT
    'Update Triggers'::TEXT,
    CASE WHEN COUNT(*) >= 8 THEN 'PASS' ELSE 'FAIL' END::TEXT,
    'Found ' || COUNT(*) || ' update triggers'::TEXT
  FROM information_schema.triggers
  WHERE trigger_name LIKE '%updated_at%';

END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE individuals ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_contexts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_training_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "users_select_own" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "users_update_own" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "users_insert_own" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- Individuals table policies
CREATE POLICY "individuals_manage_own" ON individuals FOR ALL USING (user_id = auth.uid());

-- Organizations table policies
CREATE POLICY "organizations_select_members" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT DISTINCT t.organization_id
      FROM team_memberships tm
      JOIN teams t ON tm.team_id = t.id
      WHERE tm.user_id = auth.uid() AND tm.status = 'active'
    )
  );

-- Teams table policies
CREATE POLICY "teams_select_members" ON teams
  FOR SELECT USING (
    id IN (
      SELECT team_id FROM team_memberships
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Team memberships policies
CREATE POLICY "memberships_select_involved" ON team_memberships
  FOR SELECT USING (
    user_id = auth.uid() OR
    team_id IN (
      SELECT team_id FROM team_memberships
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- User contexts policies
CREATE POLICY "contexts_manage_own" ON user_contexts FOR ALL USING (user_id = auth.uid());

-- Training data policies
CREATE POLICY "training_data_manage_own" ON user_training_data FOR ALL USING (user_id = auth.uid());

-- Learning paths policies
CREATE POLICY "learning_paths_select_public" ON learning_paths FOR SELECT USING (is_public = true);
CREATE POLICY "learning_paths_manage_own" ON learning_paths FOR ALL USING (created_by = auth.uid());

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================================================

-- Add updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_individuals_updated_at
  BEFORE UPDATE ON individuals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organizations_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at
  BEFORE UPDATE ON teams
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_memberships_updated_at
  BEFORE UPDATE ON team_memberships
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_contexts_updated_at
  BEFORE UPDATE ON user_contexts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_training_data_updated_at
  BEFORE UPDATE ON user_training_data
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_paths_updated_at
  BEFORE UPDATE ON learning_paths
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- INITIAL DATA & SETUP
-- =============================================================================

-- Insert default learning paths
INSERT INTO learning_paths (
  id, title, description, slug, modules_sequence,
  estimated_duration_hours, difficulty_level, is_public, status
) VALUES
(
  gen_random_uuid(),
  'Getting Started with Luna',
  'Introduction to Luna platform and basic skills assessment',
  'getting-started-luna',
  '[]'::jsonb,
  2,
  1,
  true,
  'active'
);

-- =============================================================================
-- DEPLOYMENT VALIDATION
-- =============================================================================

-- Validate the deployed schema
SELECT * FROM validate_luna_schema();

-- =============================================================================
-- LUNA PHASE 1 DATABASE SCHEMA DEPLOYMENT COMPLETE
-- =============================================================================