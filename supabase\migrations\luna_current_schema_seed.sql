-- Luna Current Schema Seed Data
-- Seed data for the current employment-based architecture

-- Insert organizations
INSERT INTO organizations (
  id, name, slug, description, industry, size_range, 
  subscription_tier, branding_config, subdomain, website_url
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  'TechCorp Solutions',
  'techcorp-solutions',
  'Leading technology solutions provider',
  'Technology',
  'large',
  'enterprise',
  '{"primary_color": "#2563eb"}',
  'techcorp',
  'https://techcorp.com'
),
(
  '*************-2222-2222-************',
  'Creative Agency Inc',
  'creative-agency',
  'Full-service creative and marketing agency',
  'Marketing',
  'medium',
  'professional',
  '{"primary_color": "#7c3aed"}',
  'creative',
  'https://creativeagency.com'
),
(
  '*************-3333-3333-************',
  'StartupHub',
  'startuphub',
  'Innovation and startup incubator',
  'Consulting',
  'small',
  'basic',
  '{"primary_color": "#059669"}',
  'startup',
  'https://startuphub.com'
)
ON CONFLICT (id) DO NOTHING;

-- Insert departments for each organization
INSERT INTO departments (
  id, organization_id, name, description
) VALUES 
-- TechCorp departments
(
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111',
  'Engineering',
  'Software development and technical operations'
),
(
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '11111111-1111-1111-1111-111111111111',
  'Product Management',
  'Product strategy and roadmap planning'
),
-- Creative Agency departments
(
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  '*************-2222-2222-************',
  'Design',
  'Creative design and visual communications'
),
(
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  '*************-2222-2222-************',
  'Marketing',
  'Digital marketing and campaign management'
),
-- StartupHub departments
(
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  '*************-3333-3333-************',
  'Operations',
  'Business operations and administration'
),
(
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  '*************-3333-3333-************',
  'Consulting',
  'Business consulting and advisory services'
)
ON CONFLICT (id) DO NOTHING;

-- Get existing user IDs from auth users (we'll use the first few for employment relationships)
-- Note: This assumes users already exist from the auth sync

-- Insert employment relationships
-- We'll assign the first few users to different organizations with different roles
INSERT INTO employment_relationships (
  id, user_id, organization_id, department_id, role, status, job_title, hire_date, joined_at
) 
SELECT 
  gen_random_uuid(),
  u.id,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid,
  'organization_admin'::employment_role,
  'active'::employment_status,
  'Chief Technology Officer',
  CURRENT_DATE - INTERVAL '6 months',
  NOW() - INTERVAL '6 months'
FROM users u 
WHERE u.role = 'individual' 
LIMIT 1
ON CONFLICT (user_id, organization_id) DO NOTHING;

INSERT INTO employment_relationships (
  id, user_id, organization_id, department_id, role, status, job_title, hire_date, joined_at
) 
SELECT 
  gen_random_uuid(),
  u.id,
  '*************-2222-2222-************'::uuid,
  'cccccccc-cccc-cccc-cccc-cccccccccccc'::uuid,
  'organization_admin'::employment_role,
  'active'::employment_status,
  'Creative Director',
  CURRENT_DATE - INTERVAL '4 months',
  NOW() - INTERVAL '4 months'
FROM users u 
WHERE u.role = 'individual' 
AND u.id NOT IN (
  SELECT user_id FROM employment_relationships WHERE role = 'organization_admin'
)
LIMIT 1
ON CONFLICT (user_id, organization_id) DO NOTHING;

INSERT INTO employment_relationships (
  id, user_id, organization_id, department_id, role, status, job_title, hire_date, joined_at
) 
SELECT 
  gen_random_uuid(),
  u.id,
  '*************-3333-3333-************'::uuid,
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'::uuid,
  'organization_admin'::employment_role,
  'active'::employment_status,
  'Operations Manager',
  CURRENT_DATE - INTERVAL '3 months',
  NOW() - INTERVAL '3 months'
FROM users u 
WHERE u.role = 'individual' 
AND u.id NOT IN (
  SELECT user_id FROM employment_relationships WHERE role = 'organization_admin'
)
LIMIT 1
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Add some department admins and staff members
INSERT INTO employment_relationships (
  id, user_id, organization_id, department_id, role, status, job_title, hire_date, joined_at
) 
SELECT 
  gen_random_uuid(),
  u.id,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb'::uuid,
  'department_admin'::employment_role,
  'active'::employment_status,
  'Senior Product Manager',
  CURRENT_DATE - INTERVAL '2 months',
  NOW() - INTERVAL '2 months'
FROM users u 
WHERE u.role = 'individual' 
AND u.id NOT IN (
  SELECT user_id FROM employment_relationships
)
LIMIT 1
ON CONFLICT (user_id, organization_id) DO NOTHING;

INSERT INTO employment_relationships (
  id, user_id, organization_id, department_id, role, status, job_title, hire_date, joined_at
) 
SELECT 
  gen_random_uuid(),
  u.id,
  '*************-2222-2222-************'::uuid,
  'dddddddd-dddd-dddd-dddd-dddddddddddd'::uuid,
  'department_admin'::employment_role,
  'active'::employment_status,
  'Marketing Manager',
  CURRENT_DATE - INTERVAL '1 month',
  NOW() - INTERVAL '1 month'
FROM users u 
WHERE u.role = 'individual' 
AND u.id NOT IN (
  SELECT user_id FROM employment_relationships
)
LIMIT 1
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Add some staff members
INSERT INTO employment_relationships (
  id, user_id, organization_id, department_id, role, status, job_title, hire_date, joined_at
) 
SELECT 
  gen_random_uuid(),
  u.id,
  '11111111-1111-1111-1111-111111111111'::uuid,
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'::uuid,
  'staff_member'::employment_role,
  'active'::employment_status,
  'Software Engineer',
  CURRENT_DATE - INTERVAL '1 month',
  NOW() - INTERVAL '1 month'
FROM users u 
WHERE u.role = 'individual' 
AND u.id NOT IN (
  SELECT user_id FROM employment_relationships
)
LIMIT 3
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Create user contexts for employed users
INSERT INTO user_contexts (
  id, user_id, active_context, organization_id, department_id, employment_id
)
SELECT 
  gen_random_uuid(),
  er.user_id,
  'organization'::context_type,
  er.organization_id,
  er.department_id,
  er.id
FROM employment_relationships er
WHERE er.status = 'active'
ON CONFLICT (user_id) DO NOTHING;

-- Create individual contexts for users without employment
INSERT INTO user_contexts (
  id, user_id, active_context
)
SELECT 
  gen_random_uuid(),
  u.id,
  'individual'::context_type
FROM users u
WHERE u.id NOT IN (SELECT user_id FROM user_contexts)
AND u.role = 'individual'
ON CONFLICT (user_id) DO NOTHING;
