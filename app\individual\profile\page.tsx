import { ProfilePage } from "@/components/profile-page"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { redirect } from "next/navigation"
import { Database } from "@/types/supabase"

interface Module {
  id: string
  title: string
  progress: number
  status: string
}

// Must match the interface in profile-page.tsx
interface Certificate {
  id: string
  module_title: string
  issue_date: string
  certificate_url: string
}

interface Skill {
  name: string
  level: string
  proficiency: number
}

interface Badge {
  id: string
  title: string
  description: string
  badge_type: string
}

// Define custom interfaces for tables not yet in database schema
interface BadgeData {
  id: string;
  title: string;
  description: string;
  badge_type: string;
}

interface CallPracticeSession {
  id: string;
  duration_minutes: number;
  score: number;
}

// Extend ProspectProfile interface to match component requirements
interface ProspectProfile {
  id: string;
  contact_info: any;
  education: any[];
  experience: any[];
  skills: any[];
  intro_video_url: string | null;
  resume_url: string | null;
  profile_visibility: boolean;
  training_status: string;
}

export default async function Profile() {
  const supabase = createServerComponentClient<Database>({ cookies })
  
  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()
  if (!session) {
    redirect("/login")
  }
  
  // Get the user data
  const { data: userData, error: userError } = await supabase
    .from("users")
    .select("id, role, full_name, email, avatar_url")
    .eq("id", session.user.id)
    .single()
  
  if (userError || !userData || (userData.role !== "individual" && userData.role !== "platform_admin")) {
    redirect("/unauthorized")
  }
  
  // Also fetch the user data from auth to get avatar if available
  const { data: { user: authUser } } = await supabase.auth.getUser()
  
  console.log("User data:", {
    id: userData.id,
    role: userData.role,
    avatar_url: userData.avatar_url,
    auth_avatar: authUser?.user_metadata?.avatar_url
  })
  
  // Get the individual's profile
  const { data: individualData, error: individualError } = await supabase
    .from("individuals")
    .select(`
      id,
      contact_info,
      education,
      experience,
      skills,
      certifications,
      profile_image_url,
      intro_video_url,
      resume_url,
      learning_status
    `)
    .eq("user_id", userData.id)
    .single()

  if (individualError) {
    console.log("Individual profile not found, this is normal for users without individual profiles")
  }
  
  // Get training progress statistics
  const individualId = individualData?.id
  
  // Prepare profile data
  const profileData = {
    user: {
      id: userData.id,
      full_name: userData.full_name || "",
      email: userData.email || "",
      role: userData.role,
      // Use user table avatar or fall back to auth metadata avatar
      avatar_url: userData.avatar_url || authUser?.user_metadata?.avatar_url || null
    },
    prospect: individualData ? {
      id: individualData.id,
      contact_info: individualData.contact_info || {},
      education: Array.isArray(individualData.education) ? individualData.education : [],
      experience: Array.isArray(individualData.experience) ? individualData.experience : [],
      skills: Array.isArray(individualData.skills) ? individualData.skills : [],
      certifications: Array.isArray(individualData.certifications) ? individualData.certifications : [],
      intro_video_url: individualData.intro_video_url,
      resume_url: individualData.resume_url,
      profile_visibility: true, // Default to visible for individuals
      training_status: individualData.learning_status || "not_started"
    } : null,
    training: {
      progress: 0,
      aiCallHours: 0,
      avgScore: 0,
      totalCalls: 0,
      modules: [] as Module[],
      certificates: [] as Certificate[],
      badges: [] as Badge[],
      skills: [] as Skill[]
    }
  };
  
  if (individualId) {
    try {
      // Get training data for this individual
      const { data: trainingData } = await supabase
        .from("user_training_data")
        .select(`
          id,
          training_type,
          training_status,
          progress_percentage,
          completion_date,
          metadata
        `)
        .eq("user_id", userData.id)
        .eq("context_type", "individual")

      // Process training data for Luna platform
      if (trainingData && trainingData.length > 0) {
        // Calculate basic training statistics
        const completedTraining = trainingData.filter(t => t.training_status === 'completed')
        const inProgressTraining = trainingData.filter(t => t.training_status === 'in_progress')

        profileData.training.progress = trainingData.length > 0
          ? Math.round(completedTraining.length / trainingData.length * 100)
          : 0

        // Create simple module data from training records
        const moduleData: Module[] = trainingData.map(training => ({
          id: training.id,
          title: training.training_type || 'Training Module',
          progress: training.progress_percentage || 0,
          status: training.training_status || 'not_started'
        }))

        profileData.training.modules = moduleData
      }

      // For now, use empty arrays for certificates and badges since we don't have those tables yet
      profileData.training.certificates = []
      profileData.training.badges = []

      profileData.training.skills = []
      
    } catch (error: any) {
      console.error("Error fetching profile data:", error.message || error)
    }
  }
  
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <ProfilePage data={profileData} />
    </div>
  )
} 