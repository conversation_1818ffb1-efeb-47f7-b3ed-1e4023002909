import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { assessmentSessionService } from '@/lib/services/assessment-session';
import { createClient } from '@/lib/supabase-server';

/**
 * Create new assessment session
 * POST /api/assessments/sessions
 */
export async function POST(req: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const body = await req.json();
    const { configId } = body;

    if (!configId) {
      return NextResponse.json(
        { error: 'Assessment configuration ID is required' },
        { status: 400 }
      );
    }

    // Create session
    const result = await assessmentSessionService.createSession(
      authResult.user.id,
      configId
    );

    return NextResponse.json({
      success: true,
      session: result.session,
      requires_payment: result.requiresPayment,
      payment_amount: result.paymentAmount,
      message: result.message
    });

  } catch (error: any) {
    console.error('Session creation error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create assessment session' },
      { status: 500 }
    );
  }
}

/**
 * Get user's assessment sessions
 * GET /api/assessments/sessions
 */
export async function GET(req: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { searchParams } = new URL(req.url);
    const configId = searchParams.get('configId');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    const supabase = createClient();

    // Build query
    let query = supabase
      .from('ai_assessment_sessions')
      .select(`
        *,
        config:ai_assessment_configs(
          id,
          name,
          description,
          category,
          duration_minutes,
          passing_score
        )
      `)
      .eq('user_id', authResult.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (configId) {
      query = query.eq('ai_assessment_config_id', configId);
    }

    if (status) {
      query = query.eq('status', status);
    }

    const { data: sessions, error } = await query;

    if (error) {
      console.error('Error fetching sessions:', error);
      return NextResponse.json(
        { error: 'Failed to fetch assessment sessions' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('ai_assessment_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', authResult.user.id);

    if (configId) {
      countQuery = countQuery.eq('ai_assessment_config_id', configId);
    }

    if (status) {
      countQuery = countQuery.eq('status', status);
    }

    const { count } = await countQuery;

    return NextResponse.json({
      sessions: sessions || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        has_more: (count || 0) > offset + limit
      }
    });

  } catch (error: any) {
    console.error('Sessions fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch assessment sessions' },
      { status: 500 }
    );
  }
}
