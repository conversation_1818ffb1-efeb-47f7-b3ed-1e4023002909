/**
 * Question Generation Pipeline Service
 * Orchestrates the complete flow from user request to generated questions in database
 */

import { createClient } from '@/lib/supabase-server';
import { togetherAI } from '@/lib/ai/together-client';
import { promptTemplateService } from '@/lib/services/prompt-template';
import type { PromptCompilationContext } from '@/lib/services/prompt-template';
import type { AssessmentGenerationRequest, GeneratedQuestion } from '@/lib/ai/together-client';

export interface PipelineRequest {
  userId: string;
  assessmentConfigId: string;
  sessionId?: string;
  customParameters?: Record<string, any>;
  priority?: 'low' | 'normal' | 'high';
}

export interface PipelineResult {
  success: boolean;
  sessionId?: string;
  questionsGenerated: number;
  qualityScore: number;
  generationTime: number;
  tokensUsed: number;
  error?: string;
  metadata: {
    pipelineId: string;
    templateUsed: string;
    modelUsed: string;
    timestamp: string;
    stages: PipelineStage[];
  };
}

export interface PipelineStage {
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: number;
  endTime?: number;
  duration?: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface AssessmentConfig {
  id: string;
  name: string;
  prompt_template_id: string;
  question_count: number;
  difficulty_distribution: Record<string, number>;
  question_types: string[];
  subcategories: string[];
  duration_minutes: number;
  passing_score: number;
  category: string;
  ai_model_name: string;
  temperature: number;
  max_tokens: number;
  prompt_template: {
    id: string;
    name: string;
    system_prompt: string;
    default_parameters: Record<string, any>;
  };
}

export class QuestionGenerationPipeline {
  private supabase = createClient();
  private stages: PipelineStage[] = [];
  private pipelineId: string;

  constructor() {
    this.pipelineId = `pipeline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Execute the complete question generation pipeline
   */
  async execute(request: PipelineRequest): Promise<PipelineResult> {
    const startTime = Date.now();
    this.stages = [];

    try {
      console.log(`Starting question generation pipeline ${this.pipelineId} for user ${request.userId}`);

      // Stage 1: Load Assessment Configuration
      const config = await this.executeStage('load_config', async () => {
        return await this.loadAssessmentConfig(request.assessmentConfigId);
      });

      // Stage 2: Create or Validate Session
      const sessionId = await this.executeStage('create_session', async () => {
        return request.sessionId || await this.createAssessmentSession(request.userId, config);
      });

      // Stage 3: Compile Prompt Template
      const compiledPrompt = await this.executeStage('compile_prompt', async () => {
        return await this.compilePromptTemplate(config, request.customParameters);
      });

      // Stage 4: Generate Questions with AI
      const generationResult = await this.executeStage('generate_questions', async () => {
        return await this.generateQuestionsWithAI(config, compiledPrompt);
      });

      // Stage 5: Validate and Process Questions
      const validatedQuestions = await this.executeStage('validate_questions', async () => {
        return await this.validateAndProcessQuestions(generationResult.questions, config);
      });

      // Stage 6: Store Questions in Database
      await this.executeStage('store_questions', async () => {
        return await this.storeQuestionsInDatabase(sessionId, validatedQuestions);
      });

      // Stage 7: Update Session Status
      await this.executeStage('update_session', async () => {
        return await this.updateSessionWithResults(sessionId, validatedQuestions, generationResult.metadata);
      });

      const totalTime = Date.now() - startTime;

      console.log(`Pipeline ${this.pipelineId} completed successfully in ${totalTime}ms`);

      return {
        success: true,
        sessionId,
        questionsGenerated: validatedQuestions.length,
        qualityScore: generationResult.metadata.quality_score,
        generationTime: totalTime,
        tokensUsed: generationResult.metadata.tokens_used,
        metadata: {
          pipelineId: this.pipelineId,
          templateUsed: config.prompt_template.name,
          modelUsed: config.ai_model_name,
          timestamp: new Date().toISOString(),
          stages: this.stages
        }
      };

    } catch (error) {
      const totalTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`Pipeline ${this.pipelineId} failed after ${totalTime}ms:`, errorMessage);

      return {
        success: false,
        questionsGenerated: 0,
        qualityScore: 0,
        generationTime: totalTime,
        tokensUsed: 0,
        error: errorMessage,
        metadata: {
          pipelineId: this.pipelineId,
          templateUsed: '',
          modelUsed: '',
          timestamp: new Date().toISOString(),
          stages: this.stages
        }
      };
    }
  }

  /**
   * Execute a pipeline stage with error handling and timing
   */
  private async executeStage<T>(stageName: string, stageFunction: () => Promise<T>): Promise<T> {
    const stage: PipelineStage = {
      name: stageName,
      status: 'running',
      startTime: Date.now()
    };

    this.stages.push(stage);
    console.log(`Pipeline ${this.pipelineId}: Starting stage ${stageName}`);

    try {
      const result = await stageFunction();
      
      stage.status = 'completed';
      stage.endTime = Date.now();
      stage.duration = stage.endTime - stage.startTime;
      
      console.log(`Pipeline ${this.pipelineId}: Stage ${stageName} completed in ${stage.duration}ms`);
      
      return result;
    } catch (error) {
      stage.status = 'failed';
      stage.endTime = Date.now();
      stage.duration = stage.endTime - stage.startTime;
      stage.error = error instanceof Error ? error.message : 'Unknown error';
      
      console.error(`Pipeline ${this.pipelineId}: Stage ${stageName} failed after ${stage.duration}ms:`, stage.error);
      
      throw error;
    }
  }

  /**
   * Load assessment configuration with prompt template
   */
  private async loadAssessmentConfig(configId: string): Promise<AssessmentConfig> {
    const { data, error } = await this.supabase
      .from('ai_assessment_configs')
      .select(`
        *,
        prompt_template:ai_prompt_templates(
          id,
          name,
          system_prompt,
          default_parameters
        )
      `)
      .eq('id', configId)
      .eq('status', 'published')
      .single();

    if (error || !data) {
      throw new Error(`Assessment configuration not found: ${configId}`);
    }

    if (!data.prompt_template) {
      throw new Error('Prompt template not found for assessment configuration');
    }

    return data as AssessmentConfig;
  }

  /**
   * Create new assessment session
   */
  private async createAssessmentSession(userId: string, config: AssessmentConfig): Promise<string> {
    // Check user's attempt count
    const { count } = await this.supabase
      .from('ai_assessment_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('ai_assessment_config_id', config.id);

    const attemptNumber = (count || 0) + 1;

    // Create session
    const { data, error } = await this.supabase
      .from('ai_assessment_sessions')
      .insert({
        user_id: userId,
        ai_assessment_config_id: config.id,
        attempt_number: attemptNumber,
        status: 'in_progress',
        total_questions: config.question_count,
        questions_answered: 0,
        time_spent: 0,
        payment_required: false,
        payment_status: 'not_required'
      })
      .select('id')
      .single();

    if (error || !data) {
      throw new Error('Failed to create assessment session');
    }

    return data.id;
  }

  /**
   * Compile prompt template with assessment parameters
   */
  private async compilePromptTemplate(
    config: AssessmentConfig,
    customParameters?: Record<string, any>
  ) {
    const context: PromptCompilationContext = {
      assessmentType: config.category,
      questionCount: config.question_count,
      difficultyDistribution: config.difficulty_distribution,
      subcategories: config.subcategories,
      questionTypes: config.question_types,
      duration: config.duration_minutes,
      passingScore: config.passing_score,
      targetRole: 'BPO Call Center Agent',
      industryFocus: 'Business Process Outsourcing',
      customParameters: customParameters || {}
    };

    return await promptTemplateService.compilePrompt(config.prompt_template_id, context);
  }

  /**
   * Generate questions using AI
   */
  private async generateQuestionsWithAI(config: AssessmentConfig, compiledPrompt: any) {
    const generationRequest: AssessmentGenerationRequest = {
      assessmentType: config.category,
      questionCount: config.question_count,
      difficultyDistribution: config.difficulty_distribution,
      subcategories: config.subcategories,
      questionTypes: config.question_types,
      systemPrompt: compiledPrompt.systemPrompt,
      parameters: compiledPrompt.metadata.parametersApplied
    };

    return await togetherAI.generateAssessmentQuestions(generationRequest);
  }

  /**
   * Validate and process generated questions
   */
  private async validateAndProcessQuestions(
    questions: GeneratedQuestion[],
    config: AssessmentConfig
  ): Promise<GeneratedQuestion[]> {
    // Additional validation beyond what's in the AI client
    const validQuestions = questions.filter(question => {
      // Check question length
      if (question.question_text.length < 10 || question.question_text.length > 1000) {
        console.warn(`Question filtered: invalid length (${question.question_text.length} chars)`);
        return false;
      }

      // Check for inappropriate content (basic check)
      const inappropriateWords = ['inappropriate', 'offensive']; // Extend this list
      if (inappropriateWords.some(word => question.question_text.toLowerCase().includes(word))) {
        console.warn('Question filtered: potentially inappropriate content');
        return false;
      }

      // Ensure subcategory is valid
      if (!config.subcategories.includes(question.subcategory)) {
        console.warn(`Question filtered: invalid subcategory (${question.subcategory})`);
        return false;
      }

      return true;
    });

    if (validQuestions.length < config.question_count * 0.8) {
      throw new Error(`Too many questions filtered out. Generated ${validQuestions.length} valid questions, expected at least ${Math.floor(config.question_count * 0.8)}`);
    }

    return validQuestions.slice(0, config.question_count);
  }

  /**
   * Store questions in database
   */
  private async storeQuestionsInDatabase(sessionId: string, questions: GeneratedQuestion[]): Promise<void> {
    const questionsToInsert = questions.map((question, index) => ({
      assessment_session_id: sessionId,
      question_number: index + 1,
      question_type: question.question_type,
      question_text: question.question_text,
      question_context: question.question_context,
      answer_options: question.answer_options,
      correct_answer: question.correct_answer,
      explanation: question.explanation,
      subcategory: question.subcategory,
      difficulty_level: question.difficulty,
      competency_focus: question.subcategory,
      points: question.points,
      estimated_time: question.estimated_time,
      ai_confidence_score: question.ai_confidence_score
    }));

    const { error } = await this.supabase
      .from('ai_generated_assessment_questions')
      .insert(questionsToInsert);

    if (error) {
      throw new Error(`Failed to store questions in database: ${error.message}`);
    }
  }

  /**
   * Update session with generation results
   */
  private async updateSessionWithResults(
    sessionId: string,
    questions: GeneratedQuestion[],
    metadata: any
  ): Promise<void> {
    const { error } = await this.supabase
      .from('ai_assessment_sessions')
      .update({
        generation_metadata: metadata,
        total_questions: questions.length,
        generation_timestamp: new Date().toISOString()
      })
      .eq('id', sessionId);

    if (error) {
      throw new Error(`Failed to update session: ${error.message}`);
    }
  }
}

// Export singleton instance
export const questionGenerationPipeline = new QuestionGenerationPipeline();
