'use client';

import React, { useState } from 'react';
import { ChevronDown, User, Building2, Check, ChevronsUpDown } from 'lucide-react';
import { useLunaAuth, useCurrentContext } from '@/hooks/use-luna-auth';
import { useRouter } from 'next/navigation';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { SidebarMenuButton } from "@/components/ui/sidebar"

interface ContextSwitcherProps {
  className?: string;
}

export function ContextSwitcher({ className = '' }: ContextSwitcherProps) {
  const { user, switchContext } = useLunaAuth();
  const { context, availableEmployments } = useCurrentContext();
  const [switching, setSwitching] = useState(false);
  const router = useRouter();

  if (!user) return null;

  // Don't show context switcher for users who can't switch contexts
  const canSwitchToIndividual = user.role === 'individual' || user.isPlatformAdmin;
  const hasOrganizations = availableEmployments && availableEmployments.length > 0;

  if (!canSwitchToIndividual && !hasOrganizations) {
    return null;
  }

  const handleContextSwitch = async (
    contextType: 'individual' | 'organization',
    organizationId?: string
  ) => {
    setSwitching(true);

    try {
      await switchContext(contextType, organizationId);

      // Force navigation after context switch
      if (contextType === 'individual') {
        router.push('/individual');
      } else if (contextType === 'organization' && organizationId) {
        // Get organization slug from employment relationships
        const employment = availableEmployments?.find(emp => emp.organization_id === organizationId);
        const orgSlug = employment?.organization_slug || 'default';
        router.push(`/org/${orgSlug}`);
      }
    } catch (error) {
      console.error('Context switch failed:', error);
    } finally {
      setSwitching(false);
    }
  };

  const currentContextDisplay = () => {
    if (context?.type === 'organization') {
      const employment = availableEmployments?.find(e => e.organization_id === context.organization_id);
      return {
        icon: <Building2 className="h-4 w-4" />,
        label: employment?.organization_name || 'Organization',
        subtitle: 'Organization Mode'
      };
    } else {
      return {
        icon: <User className="h-4 w-4" />,
        label: user.full_name || 'Individual',
        subtitle: 'Individual Mode'
      };
    }
  };

  const currentDisplay = currentContextDisplay();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarMenuButton
          size="lg"
          className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground bg-sidebar-accent hover:bg-sidebar-accent/80"
          disabled={switching}
        >
          <div className="flex size-9 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            {React.cloneElement(currentDisplay.icon, { className: "h-5 w-5" })}
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold text-base">{currentDisplay.label}</span>
            <span className="truncate text-sm text-muted-foreground">{currentDisplay.subtitle}</span>
          </div>
          <ChevronsUpDown className="ml-auto h-5 w-5" />
        </SidebarMenuButton>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
        align="start"
        side="bottom"
        sideOffset={4}
      >
        <DropdownMenuLabel className="text-xs text-muted-foreground">
          Switch Context
        </DropdownMenuLabel>
        
        {/* Individual Context Option */}
        {canSwitchToIndividual && (
          <DropdownMenuItem
            onClick={() => handleContextSwitch('individual')}
            className="gap-2 p-2"
          >
            <div className="flex size-6 items-center justify-center rounded-sm border">
              <User className="size-4 shrink-0" />
            </div>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">Individual Mode</span>
              <span className="truncate text-xs">Personal learning and profile</span>
            </div>
            {context?.type === 'individual' && (
              <Check className="size-4" />
            )}
          </DropdownMenuItem>
        )}

        {/* Organization Context Options */}
        {hasOrganizations && (
          <>
            {canSwitchToIndividual && <DropdownMenuSeparator />}
            <DropdownMenuLabel className="text-xs text-muted-foreground">
              Organizations
            </DropdownMenuLabel>
            {availableEmployments?.map((employment) => (
              <DropdownMenuItem
                key={employment.organization_id}
                onClick={() => handleContextSwitch('organization', employment.organization_id)}
                className="gap-2 p-2"
              >
                <div className="flex size-6 items-center justify-center rounded-sm border">
                  <Building2 className="size-4 shrink-0" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">{employment.organization_name}</span>
                  <span className="truncate text-xs capitalize">{employment.employment_role} access</span>
                </div>
                {context?.type === 'organization' && context.organization_id === employment.organization_id && (
                  <Check className="size-4" />
                )}
              </DropdownMenuItem>
            ))}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
