import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { createClient } from '@/lib/supabase-server';

/**
 * Get all assessments
 * GET /api/admin/assessments
 */
export async function GET(req: NextRequest) {
  try {
    // Temporarily remove auth for testing
    const supabase = createClient();

    // Fetch assessments
    const { data: assessments, error } = await supabase
      .from('assessments')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching assessments:', error);
      return NextResponse.json(
        { error: 'Failed to fetch assessments', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      assessments: assessments || []
    });

  } catch (error: any) {
    console.error('Assessments fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch assessments' },
      { status: 500 }
    );
  }
}

/**
 * Create new assessment
 * POST /api/admin/assessments
 */
export async function POST(req: NextRequest) {
  try {
    // Temporarily remove auth for testing
    const body = await req.json();
    const {
      name,
      instructions,
      passing_score,
      duration_minutes,
      question_count,
      price,
      question_types,
      system_prompt,
      status = 'draft'
    } = body;

    // Validate required fields
    if (!name || !system_prompt) {
      return NextResponse.json(
        { error: 'Name and system prompt are required' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Create assessment
    const { data: assessment, error } = await supabase
      .from('assessments')
      .insert({
        name,
        instructions,
        passing_score: passing_score || 70,
        duration_minutes: duration_minutes || 30,
        question_count: question_count || 10,
        price: price || 0,
        question_types: question_types || ['multiple_choice'],
        system_prompt,
        status,
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating assessment:', error);
      return NextResponse.json(
        { error: 'Failed to create assessment', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      assessment,
      message: 'Assessment created successfully'
    });

  } catch (error: any) {
    console.error('Assessment creation error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create assessment' },
      { status: 500 }
    );
  }
}
