import { ReactNode } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>r, <PERSON>R<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export interface AssessmentCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  duration: string;
  status?: "not-started" | "completed" | "in-progress";
  whatItChecks: string;
  whatToExpect: string;
  category: "technical" | "soft-skills" | "both";
  onClick?: () => void;
}

export function AssessmentCard({ 
  title, 
  description, 
  icon, 
  duration, 
  status = "not-started",
  whatItChecks,
  whatToExpect,
  category,
  onClick
}: AssessmentCardProps) {
  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
              {icon}
            </div>
            <CardTitle className="text-lg">{title}</CardTitle>
          </div>
          {status === "completed" && (
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
              <CircleCheck className="mr-1 h-3 w-3" /> Completed
            </Badge>
          )}
          {status === "in-progress" && (
            <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">In Progress</Badge>
          )}
        </div>
        <CardDescription className="mt-2">{description}</CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="flex flex-col gap-3">
          <div>
            <h4 className="text-sm font-medium">What it checks:</h4>
            <p className="text-sm text-muted-foreground">{whatItChecks}</p>
          </div>
          <div>
            <h4 className="text-sm font-medium">What to expect:</h4>
            <p className="text-sm text-muted-foreground">{whatToExpect}</p>
          </div>
          <div className="flex items-center gap-2 mt-1">
            <Timer className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Time needed: {duration}</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        {status === "completed" ? (
          <Button variant="outline" className="w-full" onClick={onClick}>
            View Results
          </Button>
        ) : status === "in-progress" ? (
          <Button className="w-full" onClick={onClick}>
            Continue Assessment
          </Button>
        ) : (
          <Button className="w-full" onClick={onClick}>
            Start Assessment <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </CardFooter>
    </Card>
  );
} 