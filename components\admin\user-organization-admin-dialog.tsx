"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { Loader2, Building2, Crown, UserCheck } from "lucide-react"

interface User {
  id: string
  email: string
  full_name: string
  role: string
}

interface Organization {
  id: string
  name: string
  slug: string
}

interface OrganizationMembership {
  id: string
  organization_id: string
  organization_name: string
  role: string
  status: string
}

interface UserOrganizationAdminDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  user: User | null
  onSuccess: () => void
}

export function UserOrganizationAdminDialog({
  open,
  onOpenChange,
  user,
  onSuccess
}: UserOrganizationAdminDialogProps) {
  const [loading, setLoading] = useState(false)
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [userMemberships, setUserMemberships] = useState<OrganizationMembership[]>([])
  const [selectedOrganization, setSelectedOrganization] = useState<string>("")
  const [selectedRole, setSelectedRole] = useState<string>("member")

  // Fetch organizations and user memberships
  useEffect(() => {
    if (open && user) {
      fetchOrganizations()
      fetchUserMemberships()
    }
  }, [open, user])

  const fetchOrganizations = async () => {
    try {
      const response = await fetch('/api/admin/organizations')
      if (!response.ok) throw new Error('Failed to fetch organizations')
      const data = await response.json()
      setOrganizations(data)
    } catch (error) {
      console.error('Error fetching organizations:', error)
      toast.error('Failed to load organizations')
    }
  }

  const fetchUserMemberships = async () => {
    if (!user) return
    
    try {
      const response = await fetch(`/api/admin/users/${user.id}/memberships`)
      if (!response.ok) throw new Error('Failed to fetch user memberships')
      const data = await response.json()
      setUserMemberships(data)
    } catch (error) {
      console.error('Error fetching user memberships:', error)
      toast.error('Failed to load user memberships')
    }
  }

  const handleAddMembership = async () => {
    if (!user || !selectedOrganization || !selectedRole) {
      toast.error('Please select an organization and role')
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/users/${user.id}/memberships`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organization_id: selectedOrganization,
          role: selectedRole,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to add organization membership')
      }

      toast.success('Organization membership added successfully')
      setSelectedOrganization("")
      setSelectedRole("member")
      fetchUserMemberships()
      onSuccess()
    } catch (error: any) {
      console.error('Error adding membership:', error)
      toast.error(error.message || 'Failed to add organization membership')
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveMembership = async (membershipId: string) => {
    if (!confirm('Are you sure you want to remove this organization membership?')) {
      return
    }

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/users/${user?.id}/memberships/${membershipId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to remove organization membership')
      }

      toast.success('Organization membership removed successfully')
      fetchUserMemberships()
      onSuccess()
    } catch (error: any) {
      console.error('Error removing membership:', error)
      toast.error(error.message || 'Failed to remove organization membership')
    } finally {
      setLoading(false)
    }
  }

  const handleChangeRole = async (membershipId: string, newRole: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/users/${user?.id}/memberships/${membershipId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ role: newRole }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update membership role')
      }

      toast.success('Membership role updated successfully')
      fetchUserMemberships()
      onSuccess()
    } catch (error: any) {
      console.error('Error updating role:', error)
      toast.error(error.message || 'Failed to update membership role')
    } finally {
      setLoading(false)
    }
  }

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      owner: { label: 'Owner', variant: 'default' as const, icon: Crown },
      admin: { label: 'Admin', variant: 'secondary' as const, icon: UserCheck },
      manager: { label: 'Manager', variant: 'outline' as const, icon: UserCheck },
      member: { label: 'Member', variant: 'outline' as const, icon: UserCheck },
      viewer: { label: 'Viewer', variant: 'outline' as const, icon: UserCheck },
    }
    
    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.member
    const Icon = config.icon
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  const availableOrganizations = organizations.filter(org => 
    !userMemberships.some(membership => membership.organization_id === org.id)
  )

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Organization Access</DialogTitle>
          <DialogDescription>
            Manage <strong>{user.full_name}</strong>'s organization memberships and admin roles
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Memberships */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Current Organization Memberships</h4>
            {userMemberships.length === 0 ? (
              <div className="text-sm text-muted-foreground p-4 border rounded-lg text-center">
                No organization memberships found
              </div>
            ) : (
              <div className="space-y-2">
                {userMemberships.map((membership) => (
                  <div key={membership.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Building2 className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">{membership.organization_name}</div>
                        <div className="text-sm text-muted-foreground">
                          Status: {membership.status}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Select
                        value={membership.role}
                        onValueChange={(newRole) => handleChangeRole(membership.id, newRole)}
                        disabled={loading}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="owner">Owner</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                          <SelectItem value="manager">Manager</SelectItem>
                          <SelectItem value="member">Member</SelectItem>
                          <SelectItem value="viewer">Viewer</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemoveMembership(membership.id)}
                        disabled={loading}
                      >
                        Remove
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Add New Membership */}
          {availableOrganizations.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Add Organization Membership</h4>
              <div className="grid grid-cols-2 gap-3">
                <Select value={selectedOrganization} onValueChange={setSelectedOrganization}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select organization" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableOrganizations.map((org) => (
                      <SelectItem key={org.id} value={org.id}>
                        {org.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedRole} onValueChange={setSelectedRole}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="owner">Owner</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="member">Member</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={handleAddMembership} 
                disabled={loading || !selectedOrganization}
                className="w-full"
              >
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Add Membership
              </Button>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
