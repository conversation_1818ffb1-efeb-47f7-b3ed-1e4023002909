'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  Line,
  LineChart,
  Pie,
  PieChart,
  Cell,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend,
} from 'recharts';
import {
  Users,
  Briefcase,
  Calendar,
  ArrowRight,
  TrendingUp,
  TrendingDown,
  User<PERSON><PERSON><PERSON>,
  Clock,
  User,
  CheckCircle2,
  XCircle,
  FileText,
  AlertTriangle,
  Building2,
  Target,
  Award,
  Activity,
  DollarSign,
  UserPlus,
  Eye,
  MoreHorizontal,
  ArrowUpRight,
  ArrowDownRight,
} from 'lucide-react';

// Mock data for charts and KPIs
const mockStats = {
  totalEmployees: 247,
  activeVacancies: 12,
  totalCandidates: 89,
  upcomingInterviews: 23,
  departmentCount: 8,
  avgTimeToHire: 18,
  employeeRetention: 94.2,
  recruitmentBudget: 125000,
};

const mockHiringTrend = [
  { month: 'Jan', hires: 8, applications: 45 },
  { month: 'Feb', hires: 12, applications: 52 },
  { month: 'Mar', hires: 15, applications: 68 },
  { month: 'Apr', hires: 10, applications: 41 },
  { month: 'May', hires: 18, applications: 73 },
  { month: 'Jun', hires: 14, applications: 59 },
];

const mockDepartmentData = [
  { name: 'Engineering', employees: 89, vacancies: 5, color: '#3b82f6' },
  { name: 'Sales', employees: 45, vacancies: 3, color: '#10b981' },
  { name: 'Marketing', employees: 32, vacancies: 2, color: '#f59e0b' },
  { name: 'HR', employees: 18, vacancies: 1, color: '#ef4444' },
  { name: 'Finance', employees: 24, vacancies: 1, color: '#8b5cf6' },
  { name: 'Operations', employees: 39, vacancies: 0, color: '#06b6d4' },
];

const mockRecruitmentFunnel = [
  { stage: 'Applications', count: 234, color: '#3b82f6' },
  { stage: 'Screening', count: 89, color: '#10b981' },
  { stage: 'Interviews', count: 45, color: '#f59e0b' },
  { stage: 'Final Round', count: 23, color: '#ef4444' },
  { stage: 'Offers', count: 12, color: '#8b5cf6' },
];

const chartConfig = {
  hires: {
    label: "Hires",
    color: "hsl(var(--chart-1))",
  },
  applications: {
    label: "Applications",
    color: "hsl(var(--chart-2))",
  },
} satisfies ChartConfig;

interface OrganizationData {
  id: string;
  name: string;
  slug: string;
  description?: string;
}

export default function OrganizationDashboard() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [organization, setOrganization] = useState<OrganizationData | null>(null);

  const orgSlug = params.orgSlug as string;

  useEffect(() => {
    const fetchOrganizationData = async () => {
      try {
        // Fetch organization data based on slug
        const { data: orgData, error } = await supabase
          .from('organizations')
          .select('*')
          .eq('slug', orgSlug)
          .single();

        if (error) {
          console.error('Error fetching organization:', error);
          // For now, use mock data if database query fails
          setOrganization({
            id: '1',
            name: 'TechCorp Solutions',
            slug: orgSlug,
            description: 'Leading technology solutions provider'
          });
        } else {
          setOrganization(orgData);
        }
      } catch (error) {
        console.error('Error:', error);
        // Use mock data as fallback
        setOrganization({
          id: '1',
          name: 'TechCorp Solutions',
          slug: orgSlug,
          description: 'Leading technology solutions provider'
        });
      } finally {
        setLoading(false);
      }
    };

    if (orgSlug) {
      fetchOrganizationData();
    }
  }, [orgSlug, router]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        <span className="ml-3 text-lg">Loading dashboard...</span>
      </div>
    );
  }

  if (!organization) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Organization Not Found</AlertTitle>
        <AlertDescription>
          The organization you're looking for doesn't exist or you don't have access to it.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Organization Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening at {organization.name}.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View Reports
          </Button>
          <Button size="sm">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Employee
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalEmployees}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +12% from last month
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Vacancies</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.activeVacancies}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-orange-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +3 new this week
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Candidates</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalCandidates}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-blue-600 flex items-center">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                +23 this week
              </span>
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Interviews</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.upcomingInterviews}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-purple-600 flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                5 today
              </span>
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
