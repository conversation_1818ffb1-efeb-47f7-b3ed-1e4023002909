'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function CreateUserPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main users page which has the create functionality
    router.push('/admin/users');
  }, [router]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h2 className="text-lg font-semibold">Redirecting...</h2>
        <p className="text-gray-600">Taking you to the user management page.</p>
      </div>
    </div>
  );
}
