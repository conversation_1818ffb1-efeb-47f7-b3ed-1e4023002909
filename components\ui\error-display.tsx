"use client";

import React from 'react';
import { AlertTriangle, RefreshCw, Home, ChevronLeft, Info, AlertCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AppError, ErrorType } from '@/lib/utils';
import { useRouter } from 'next/navigation';

/**
 * Props for error display components
 */
export interface ErrorDisplayProps {
  error: AppError;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  className?: string;
}

/**
 * Inline error alert component
 */
export function ErrorAlert({ 
  error, 
  onRetry, 
  onDismiss, 
  showDetails = false,
  className 
}: ErrorDisplayProps) {
  const getAlertVariant = (errorType: ErrorType) => {
    switch (errorType) {
      case ErrorType.AUTHENTICATION:
      case ErrorType.AUTHORIZATION:
        return 'destructive';
      case ErrorType.VALIDATION:
        return 'destructive';
      case ErrorType.NOT_FOUND:
        return 'default';
      default:
        return 'destructive';
    }
  };

  const getIcon = (errorType: ErrorType) => {
    switch (errorType) {
      case ErrorType.AUTHENTICATION:
      case ErrorType.AUTHORIZATION:
        return <XCircle className="h-4 w-4" />;
      case ErrorType.VALIDATION:
        return <AlertCircle className="h-4 w-4" />;
      case ErrorType.NOT_FOUND:
        return <Info className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  return (
    <Alert variant={getAlertVariant(error.type)} className={className}>
      {getIcon(error.type)}
      <AlertTitle>{getErrorTitle(error.type)}</AlertTitle>
      <AlertDescription className="mt-2">
        <div className="space-y-2">
          <p>{error.userMessage}</p>
          
          {showDetails && error.details && (
            <details className="text-xs opacity-75">
              <summary className="cursor-pointer">Technical Details</summary>
              <pre className="mt-1 whitespace-pre-wrap">
                {JSON.stringify(error.details, null, 2)}
              </pre>
            </details>
          )}
          
          <div className="flex gap-2 mt-3">
            {onRetry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                className="h-8"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Try Again
              </Button>
            )}
            {onDismiss && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onDismiss}
                className="h-8"
              >
                Dismiss
              </Button>
            )}
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
}

/**
 * Full-page error display component
 */
export function ErrorPage({ 
  error, 
  onRetry, 
  showDetails = false,
  className 
}: ErrorDisplayProps) {
  const router = useRouter();

  const getErrorIcon = (errorType: ErrorType) => {
    switch (errorType) {
      case ErrorType.NOT_FOUND:
        return <Info className="h-16 w-16 text-blue-500" />;
      case ErrorType.AUTHENTICATION:
      case ErrorType.AUTHORIZATION:
        return <XCircle className="h-16 w-16 text-red-500" />;
      default:
        return <AlertTriangle className="h-16 w-16 text-amber-500" />;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center min-h-[60vh] p-4 ${className}`}>
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getErrorIcon(error.type)}
          </div>
          <CardTitle className="text-xl">
            {getErrorTitle(error.type)}
          </CardTitle>
          <CardDescription>
            {error.userMessage}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {showDetails && error.details && (
            <details className="text-xs text-muted-foreground">
              <summary className="cursor-pointer mb-2">Technical Details</summary>
              <div className="bg-muted p-2 rounded text-xs">
                <p><strong>Error ID:</strong> {error.requestId}</p>
                <p><strong>Time:</strong> {new Date(error.timestamp).toLocaleString()}</p>
                <p><strong>Type:</strong> {error.type}</p>
                {error.code && <p><strong>Code:</strong> {error.code}</p>}
                <pre className="mt-2 whitespace-pre-wrap">
                  {JSON.stringify(error.details, null, 2)}
                </pre>
              </div>
            </details>
          )}
          
          <div className="flex flex-col gap-2">
            {onRetry && (
              <Button onClick={onRetry} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}
            
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => router.back()}
                className="flex-1"
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => router.push('/')}
                className="flex-1"
              >
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Compact error message for forms and small spaces
 */
export function ErrorMessage({ 
  error, 
  onDismiss,
  className 
}: ErrorDisplayProps) {
  return (
    <div className={`flex items-center gap-2 text-sm text-destructive ${className}`}>
      <AlertCircle className="h-4 w-4 flex-shrink-0" />
      <span className="flex-1">{error.userMessage}</span>
      {onDismiss && (
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onDismiss}
          className="h-6 w-6 p-0"
        >
          <XCircle className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}

/**
 * Loading state with error fallback
 */
export function LoadingWithError({ 
  isLoading, 
  error, 
  onRetry,
  children,
  loadingText = "Loading...",
  className 
}: {
  isLoading: boolean;
  error: AppError | null;
  onRetry?: () => void;
  children: React.ReactNode;
  loadingText?: string;
  className?: string;
}) {
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>{loadingText}</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorAlert 
        error={error} 
        onRetry={onRetry}
        className={className}
      />
    );
  }

  return <>{children}</>;
}

/**
 * Get error title based on error type
 */
function getErrorTitle(errorType: ErrorType): string {
  switch (errorType) {
    case ErrorType.AUTHENTICATION:
      return 'Authentication Required';
    case ErrorType.AUTHORIZATION:
      return 'Access Denied';
    case ErrorType.VALIDATION:
      return 'Invalid Input';
    case ErrorType.DATABASE:
      return 'Database Error';
    case ErrorType.NETWORK:
      return 'Connection Error';
    case ErrorType.FILE_UPLOAD:
      return 'Upload Failed';
    case ErrorType.NOT_FOUND:
      return 'Not Found';
    case ErrorType.RATE_LIMIT:
      return 'Rate Limited';
    case ErrorType.SERVER_ERROR:
      return 'Server Error';
    default:
      return 'Something went wrong';
  }
}
