/**
 * Performance Optimization Hooks
 * React hooks for performance monitoring and optimization
 */

"use client"

import * as React from "react"
import { performanceMonitor } from "@/lib/performance-monitor"
import { trainingCache, userCache, apiCache } from "@/lib/cache-manager"

// =============================================================================
// PERFORMANCE MONITORING HOOKS
// =============================================================================

/**
 * Hook to monitor component performance
 */
export function useComponentPerformance(componentName: string) {
  const renderCount = React.useRef(0)
  const mountTime = React.useRef(Date.now())
  
  React.useEffect(() => {
    renderCount.current += 1
    
    // Log excessive re-renders
    if (renderCount.current > 10) {
      console.warn(`Component ${componentName} has rendered ${renderCount.current} times`)
    }
    
    // Measure mount time on first render
    if (renderCount.current === 1) {
      const mountDuration = Date.now() - mountTime.current
      performanceMonitor.recordCustomMetric(
        `component-mount-${componentName}`,
        mountDuration,
        'ms'
      )
    }
  })
  
  return {
    renderCount: renderCount.current,
    measureRender: React.useCallback((operationName: string) => {
      return performanceMonitor.startTiming(`${componentName}-${operationName}`)
    }, [componentName])
  }
}

/**
 * Hook to measure API call performance
 */
export function useApiPerformance() {
  const [metrics, setMetrics] = React.useState<Record<string, number>>({})
  
  const measureApiCall = React.useCallback(async <T,>(
    apiName: string,
    apiCall: () => Promise<T>,
    useCache = true
  ): Promise<T> => {
    const cacheKey = `api-${apiName}`
    
    // Check cache first if enabled
    if (useCache) {
      const cachedResult = await apiCache.get(cacheKey)
      if (cachedResult) {
        setMetrics(prev => ({ ...prev, [apiName]: 0 })) // Cache hit = 0ms
        return cachedResult
      }
    }
    
    const startTime = Date.now()
    
    try {
      const result = await apiCall()
      const duration = Date.now() - startTime
      
      // Record performance metric
      performanceMonitor.recordCustomMetric(`api-${apiName}`, duration, 'ms')
      setMetrics(prev => ({ ...prev, [apiName]: duration }))
      
      // Cache successful result
      if (useCache && result) {
        apiCache.set(cacheKey, result, 2 * 60 * 1000) // 2 minutes
      }
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      performanceMonitor.recordCustomMetric(`api-${apiName}-error`, duration, 'ms')
      setMetrics(prev => ({ ...prev, [`${apiName}-error`]: duration }))
      throw error
    }
  }, [])
  
  return { measureApiCall, metrics }
}

/**
 * Hook to optimize expensive computations
 */
export function useOptimizedComputation<T>(
  computation: () => T,
  dependencies: React.DependencyList,
  options: {
    cache?: boolean
    cacheKey?: string
    cacheTtl?: number
    measurePerformance?: boolean
    computationName?: string
  } = {}
): T {
  const {
    cache = true,
    cacheKey,
    cacheTtl = 5 * 60 * 1000, // 5 minutes
    measurePerformance = true,
    computationName = 'computation'
  } = options
  
  const memoizedResult = React.useMemo(() => {
    const key = cacheKey || `computation-${JSON.stringify(dependencies)}`
    
    // Check cache first
    if (cache) {
      const cached = userCache.get(key)
      if (cached) {
        return cached
      }
    }
    
    // Measure computation time
    const startTime = measurePerformance ? Date.now() : 0
    const result = computation()
    
    if (measurePerformance) {
      const duration = Date.now() - startTime
      performanceMonitor.recordCustomMetric(
        `computation-${computationName}`,
        duration,
        'ms'
      )
      
      // Warn about slow computations
      if (duration > 100) {
        console.warn(`Slow computation detected: ${computationName} took ${duration}ms`)
      }
    }
    
    // Cache result
    if (cache) {
      userCache.set(key, result, cacheTtl)
    }
    
    return result
  }, dependencies)
  
  return memoizedResult
}

/**
 * Hook to debounce expensive operations
 */
export function useDebouncedOperation<T extends (...args: any[]) => any>(
  operation: T,
  delay: number = 300,
  options: {
    leading?: boolean
    trailing?: boolean
    maxWait?: number
  } = {}
): [T, { cancel: () => void; flush: () => void; pending: boolean }] {
  const { leading = false, trailing = true, maxWait } = options
  
  const [pending, setPending] = React.useState(false)
  const timeoutRef = React.useRef<NodeJS.Timeout>()
  const maxTimeoutRef = React.useRef<NodeJS.Timeout>()
  const lastCallTimeRef = React.useRef<number>()
  const lastInvokeTimeRef = React.useRef<number>(0)
  const argsRef = React.useRef<Parameters<T>>()
  const resultRef = React.useRef<ReturnType<T>>()
  
  const invokeFunc = React.useCallback(() => {
    const args = argsRef.current!
    const result = operation(...args)
    lastInvokeTimeRef.current = Date.now()
    resultRef.current = result
    setPending(false)
    return result
  }, [operation])
  
  const leadingEdge = React.useCallback(() => {
    lastInvokeTimeRef.current = Date.now()
    setPending(true)
    return leading ? invokeFunc() : resultRef.current
  }, [leading, invokeFunc])
  
  const remainingWait = React.useCallback((time: number) => {
    const timeSinceLastCall = time - (lastCallTimeRef.current || 0)
    const timeSinceLastInvoke = time - lastInvokeTimeRef.current
    const timeWaiting = delay - timeSinceLastCall
    
    return maxWait !== undefined
      ? Math.min(timeWaiting, maxWait - timeSinceLastInvoke)
      : timeWaiting
  }, [delay, maxWait])
  
  const shouldInvoke = React.useCallback((time: number) => {
    const timeSinceLastCall = time - (lastCallTimeRef.current || 0)
    const timeSinceLastInvoke = time - lastInvokeTimeRef.current
    
    return (
      lastCallTimeRef.current === undefined ||
      timeSinceLastCall >= delay ||
      timeSinceLastCall < 0 ||
      (maxWait !== undefined && timeSinceLastInvoke >= maxWait)
    )
  }, [delay, maxWait])
  
  const trailingEdge = React.useCallback(() => {
    timeoutRef.current = undefined
    if (trailing && argsRef.current) {
      return invokeFunc()
    }
    argsRef.current = undefined
    setPending(false)
    return resultRef.current
  }, [trailing, invokeFunc])
  
  const timerExpired = React.useCallback(() => {
    const time = Date.now()
    if (shouldInvoke(time)) {
      return trailingEdge()
    }
    const remaining = remainingWait(time)
    timeoutRef.current = setTimeout(timerExpired, remaining)
  }, [shouldInvoke, trailingEdge, remainingWait])
  
  const cancel = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = undefined
    }
    if (maxTimeoutRef.current) {
      clearTimeout(maxTimeoutRef.current)
      maxTimeoutRef.current = undefined
    }
    lastInvokeTimeRef.current = 0
    lastCallTimeRef.current = undefined
    argsRef.current = undefined
    setPending(false)
  }, [])
  
  const flush = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = undefined
      return trailingEdge()
    }
    return resultRef.current
  }, [trailingEdge])
  
  const debouncedFunc = React.useCallback(
    (...args: Parameters<T>) => {
      const time = Date.now()
      const isInvoking = shouldInvoke(time)
      
      lastCallTimeRef.current = time
      argsRef.current = args
      
      if (isInvoking) {
        if (!timeoutRef.current) {
          return leadingEdge()
        }
        if (maxWait !== undefined) {
          timeoutRef.current = setTimeout(timerExpired, delay)
          maxTimeoutRef.current = setTimeout(trailingEdge, maxWait)
          return leading ? invokeFunc() : resultRef.current
        }
      }
      
      if (!timeoutRef.current) {
        timeoutRef.current = setTimeout(timerExpired, delay)
      }
      
      return resultRef.current
    },
    [shouldInvoke, leadingEdge, delay, maxWait, leading, invokeFunc, timerExpired, trailingEdge]
  ) as T
  
  return [debouncedFunc, { cancel, flush, pending }]
}

/**
 * Hook to monitor memory usage
 */
export function useMemoryMonitor() {
  const [memoryInfo, setMemoryInfo] = React.useState<{
    used: number
    total: number
    limit: number
  } | null>(null)
  
  React.useEffect(() => {
    if (!('memory' in performance)) {
      return
    }
    
    const updateMemoryInfo = () => {
      const memory = (performance as any).memory
      setMemoryInfo({
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        limit: memory.jsHeapSizeLimit
      })
    }
    
    updateMemoryInfo()
    const interval = setInterval(updateMemoryInfo, 5000) // Every 5 seconds
    
    return () => clearInterval(interval)
  }, [])
  
  return memoryInfo
}

/**
 * Hook to optimize list rendering with virtualization
 */
export function useVirtualizedList<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const [scrollTop, setScrollTop] = React.useState(0)
  
  const visibleRange = React.useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )
    
    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, overscan, items.length])
  
  const visibleItems = React.useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1)
  }, [items, visibleRange])
  
  const totalHeight = items.length * itemHeight
  const offsetY = visibleRange.startIndex * itemHeight
  
  return {
    visibleItems,
    totalHeight,
    offsetY,
    setScrollTop,
    visibleRange
  }
}

/**
 * Hook to preload critical resources
 */
export function useResourcePreloader() {
  const preloadImage = React.useCallback((src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = reject
      img.src = src
    })
  }, [])
  
  const preloadScript = React.useCallback((src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.onload = () => resolve()
      script.onerror = reject
      script.src = src
      document.head.appendChild(script)
    })
  }, [])
  
  const preloadStylesheet = React.useCallback((href: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.onload = () => resolve()
      link.onerror = reject
      link.href = href
      document.head.appendChild(link)
    })
  }, [])
  
  return {
    preloadImage,
    preloadScript,
    preloadStylesheet
  }
}

/**
 * Hook to get performance summary
 */
export function usePerformanceSummary() {
  const [summary, setSummary] = React.useState(performanceMonitor.getSummary())

  React.useEffect(() => {
    // DISABLED: Frequent updates were causing lag
    // Only enable in production with explicit flag
    if (process.env.NODE_ENV === 'production' && process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_SUMMARY === 'true') {
      const interval = setInterval(() => {
        setSummary(performanceMonitor.getSummary())
      }, 60000) // Reduced from 10s to 1 minute

      return () => clearInterval(interval)
    }
  }, [])

  return summary
}
