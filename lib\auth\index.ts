/**
 * Authentication Module
 * Centralized exports for the Luna authentication system
 */

// Types
export type { 
  AuthUser, 
  AuthResult, 
  EmploymentRelationship, 
  UserContext 
} from './types';

// Core authentication functions
export { 
  getAuthenticatedUser, 
  getServerAuthUser 
} from './core';

// Authentication utilities
export {
  createAuthErrorResponse,
  isPlatformAdmin,
  isOrganizationAdmin,
  isDepartmentAdmin,
  hasEmployment,
  getUserOrganizationIds,
  getUserDepartmentIds,
  hasOrganizationAccess,
  hasDepartmentAccess,
  getUserRoleInOrganization,
  canManageOrganization,
  canManageDepartment,
  isActiveUser,
  getUserDisplayName,
  getCurrentOrganization,
  getCurrentDepartment
} from './utils';

// Authentication middleware
export {
  requirePlatformAdmin,
  requireOrganizationAdmin,
  requireDepartmentAdmin,
  withAuth,
  withAuthProtection,
  withAdminProtection,
  withOrgAdminProtection,
  withDeptAdminProtection
} from './middleware';
