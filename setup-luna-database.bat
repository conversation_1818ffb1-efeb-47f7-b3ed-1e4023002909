@echo off
echo ===============================================================================
echo LUNA DATABASE RESET AND SETUP SCRIPT
echo ===============================================================================
echo.
echo This script will:
echo 1. Reset your current Supabase database
echo 2. Create the new Luna Phase 1 schema
echo 3. Set up all tables, functions, and security policies
echo 4. Validate the deployment
echo.
echo WARNING: This will DELETE ALL existing data in your database!
echo.
set /p confirm="Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 1
)

echo.
echo ===============================================================================
echo STEP 1: CHECKING ENVIRONMENT VARIABLES
echo ===============================================================================

if "%NEXT_PUBLIC_SUPABASE_URL%"=="" (
    echo ERROR: NEXT_PUBLIC_SUPABASE_URL is not set
    echo Please set your Supabase URL in .env.local
    pause
    exit /b 1
)

if "%SUPABASE_SERVICE_ROLE_KEY%"=="" (
    echo ERROR: SUPABASE_SERVICE_ROLE_KEY is not set
    echo Please set your Supabase service role key in .env.local
    pause
    exit /b 1
)

echo ✓ Environment variables are set

echo.
echo ===============================================================================
echo STEP 2: INSTALLING DEPENDENCIES (if needed)
echo ===============================================================================

where psql >nul 2>nul
if %errorlevel% neq 0 (
    echo WARNING: psql not found. Installing via npm...
    npm install -g @supabase/cli
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install Supabase CLI
        echo Please install PostgreSQL client tools manually
        pause
        exit /b 1
    )
)

echo ✓ Database tools are available

echo.
echo ===============================================================================
echo STEP 3: BACKING UP CURRENT DATABASE (optional)
echo ===============================================================================

set /p backup="Do you want to create a backup first? (y/N): "
if /i "%backup%"=="y" (
    echo Creating backup...
    set backup_file=luna_backup_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql
    set backup_file=%backup_file: =0%
    
    echo Backup would be saved as: %backup_file%
    echo Note: You'll need to implement backup logic based on your Supabase setup
    echo For now, please use Supabase dashboard to create a backup if needed
    pause
)

echo.
echo ===============================================================================
echo STEP 4: EXECUTING DATABASE RESET AND SETUP
echo ===============================================================================

echo Executing main schema setup...
echo.

echo Running reset-and-setup-luna-database.sql...
echo Note: You'll need to execute this in your Supabase SQL editor or via psql

echo.
echo ===============================================================================
echo STEP 5: NEXT STEPS
echo ===============================================================================

echo.
echo To complete the database setup:
echo.
echo 1. Open your Supabase dashboard
echo 2. Go to SQL Editor
echo 3. Copy and paste the contents of 'reset-and-setup-luna-database.sql'
echo 4. Execute the script
echo 5. Copy and paste the contents of 'luna-database-functions-and-policies.sql'
echo 6. Execute the second script
echo 7. Verify the schema validation results
echo.
echo Alternatively, if you have psql configured:
echo.
echo psql "postgresql://postgres:[password]@[host]:[port]/postgres" -f reset-and-setup-luna-database.sql
echo psql "postgresql://postgres:[password]@[host]:[port]/postgres" -f luna-database-functions-and-policies.sql
echo.
echo Replace [password], [host], and [port] with your Supabase connection details
echo.

echo ===============================================================================
echo SETUP SCRIPT COMPLETE
echo ===============================================================================
echo.
echo The database schema files have been created:
echo - reset-and-setup-luna-database.sql (main schema)
echo - luna-database-functions-and-policies.sql (functions and security)
echo.
echo Please execute these files in your Supabase database to complete the setup.
echo.
pause
