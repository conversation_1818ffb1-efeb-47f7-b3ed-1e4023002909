import { Suspense } from "react"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { FilesPage as FilesPageComponent } from "@/components/files-page"
import { Database } from "@/types/database.types"

export const metadata = {
  title: "Files & Certificates | Luna Platform",
  description: "Manage your files and certificates",
}

// Force dynamic rendering for this page due to authentication requirements
export const dynamic = 'force-dynamic'

async function FilesData() {
  try {
    console.log("🚀 FilesData component starting")
    const supabase = createServerComponentClient<Database>({ cookies })

    // Check if user is authenticated using getUser
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    console.log("👤 Auth check:", { userId: user?.id, error: userError?.message })

    if (userError) {
      console.error("Authentication error:", userError)
      redirect("/login")
    }
    if (!user) {
      console.log("❌ No user found, redirecting to login")
      redirect("/login")
    }
    
    // Get the user data from the users table
    const { data: userData, error: userDataError } = await supabase
      .from("users")
      .select("id, role")
      .eq("id", user.id)
      .single()

    console.log("👥 User data check:", { userData, error: userDataError?.message })

    if (userDataError) {
      console.error("User data error:", userDataError)
      redirect("/unauthorized")
    }
    if (!userData || (userData.role !== "individual" && userData.role !== "platform_admin")) {
      console.log("❌ User does not have access, redirecting")
      redirect("/unauthorized")
    }

    // Get the individual's profile (optional for files page)
    let individualData = null
    try {
      const { data, error } = await supabase
        .from("individuals")
        .select("id")
        .eq("user_id", userData.id)
        .single()

      if (!error) {
        individualData = data
      }
    } catch (error) {
      console.log("Individual profile not found, continuing without it")
    }

    // For now, provide mock files data since Luna doesn't have a files system yet
    console.log("Providing mock files for user:", userData.id)

    const mockFiles = [
      {
        id: 'file-1',
        file_name: 'Resume.pdf',
        file_type: 'document',
        file_url: '#',
        file_size: 245760,
        upload_date: new Date().toISOString(),
        module_title: '',
        module_description: '',
        issue_date: new Date().toISOString(),
        expiry_date: '',
        verification_code: '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        mime_type: 'application/pdf',
        original_filename: 'Resume.pdf',
        title: 'Resume'
      },
      {
        id: 'file-2',
        file_name: 'Certificate.pdf',
        file_type: 'certificate',
        file_url: '#',
        file_size: 156432,
        upload_date: new Date().toISOString(),
        module_title: '',
        module_description: '',
        issue_date: new Date().toISOString(),
        expiry_date: '',
        verification_code: 'CERT-2024-001',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        mime_type: 'application/pdf',
        original_filename: 'Certificate.pdf',
        title: 'Training Certificate'
      }
    ]

    return (
      <FilesPageComponent
        files={mockFiles}
        prospectId={individualData?.id || userData.id}
      />
    )
  } catch (error) {
    console.error("Unexpected error:", error)
    return <div>An unexpected error occurred. Please try again later.</div>
  }
}

export default function FilesAndCertificatesPageWrapper() {
  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <Suspense fallback={<div>Loading files...</div>}>
        <FilesData />
      </Suspense>
    </div>
  )
}