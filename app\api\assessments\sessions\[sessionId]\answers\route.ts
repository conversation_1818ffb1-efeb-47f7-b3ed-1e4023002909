import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { assessmentSessionService } from '@/lib/services/assessment-session';

/**
 * Submit answer for assessment question
 * POST /api/assessments/sessions/[sessionId]/answers
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { sessionId } = params;
    const body = await req.json();
    const { questionId, userAnswer, responseTime = 0 } = body;

    if (!questionId || userAnswer === undefined) {
      return NextResponse.json(
        { error: 'Question ID and user answer are required' },
        { status: 400 }
      );
    }

    // Get session to verify ownership and status
    const sessionData = await assessmentSessionService.getSessionWithQuestions(sessionId);

    if (!sessionData) {
      return NextResponse.json(
        { error: 'Assessment session not found' },
        { status: 404 }
      );
    }

    // Verify user owns this session
    if (sessionData.session.user_id !== authResult.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Check if session is in progress
    if (sessionData.session.status !== 'in_progress') {
      return NextResponse.json(
        { error: 'Assessment session is not active' },
        { status: 400 }
      );
    }

    // Submit the answer
    const result = await assessmentSessionService.submitAnswer(
      sessionId,
      questionId,
      userAnswer,
      responseTime
    );

    // Get updated session data
    const updatedSessionData = await assessmentSessionService.getSessionWithQuestions(sessionId);

    return NextResponse.json({
      success: true,
      is_correct: result.isCorrect,
      explanation: result.explanation,
      session_progress: {
        questions_answered: updatedSessionData?.session.questions_answered || 0,
        total_questions: updatedSessionData?.session.total_questions || 0,
        current_score: updatedSessionData?.session.raw_score || 0,
        status: updatedSessionData?.session.status
      }
    });

  } catch (error: any) {
    console.error('Answer submission error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to submit answer' },
      { status: 500 }
    );
  }
}

/**
 * Get all answers for a session (for review)
 * GET /api/assessments/sessions/[sessionId]/answers
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { sessionId } = params;

    // Get session with questions
    const sessionData = await assessmentSessionService.getSessionWithQuestions(sessionId);

    if (!sessionData) {
      return NextResponse.json(
        { error: 'Assessment session not found' },
        { status: 404 }
      );
    }

    // Verify user owns this session
    if (sessionData.session.user_id !== authResult.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Only allow viewing answers if session is completed
    if (sessionData.session.status !== 'completed') {
      return NextResponse.json(
        { error: 'Assessment must be completed to view answers' },
        { status: 400 }
      );
    }

    // Return questions with user answers
    const questionsWithAnswers = sessionData.questions.map(question => ({
      id: question.id,
      question_number: question.question_number,
      question_type: question.question_type,
      question_text: question.question_text,
      question_context: question.question_context,
      answer_options: question.answer_options,
      user_answer: question.user_answer,
      correct_answer: question.correct_answer,
      is_correct: question.is_correct,
      explanation: question.explanation,
      subcategory: question.subcategory,
      difficulty_level: question.difficulty_level,
      points: question.points,
      response_time: question.response_time
    }));

    return NextResponse.json({
      session: {
        id: sessionData.session.id,
        status: sessionData.session.status,
        raw_score: sessionData.session.raw_score,
        competency_scores: sessionData.session.competency_scores,
        overall_competency_level: sessionData.session.overall_competency_level,
        passed: sessionData.session.passed,
        time_spent: sessionData.session.time_spent,
        completed_at: sessionData.session.completed_at
      },
      questions: questionsWithAnswers,
      config: {
        name: sessionData.config.name,
        passing_score: sessionData.config.passing_score,
        certification_eligible: sessionData.config.certification_eligible
      }
    });

  } catch (error: any) {
    console.error('Answers fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch answers' },
      { status: 500 }
    );
  }
}
