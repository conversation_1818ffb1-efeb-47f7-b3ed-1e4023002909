/**
 * Consolidated File Upload API
 * Handles all file upload types: documents, images, media, logos
 * Replaces multiple scattered upload endpoints
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser, createAuthErrorResponse } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-server';
import { withApiErrorHandler, createApiSuccessResponse } from '@/lib/api-error-handler';
import { withUploadSecurity } from '@/lib/security/enhanced-middleware';
import { ValidationSchemas } from '@/lib/security/validation-schemas';
import { v4 as uuidv4 } from 'uuid';

// File type configurations
const FILE_CONFIGS = {
  avatar: {
    bucket: 'profile-images',
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    profileColumn: 'avatar_url'
  },
  resume: {
    bucket: 'resumes',
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    profileColumn: 'resume_url'
  },
  document: {
    bucket: 'documents',
    maxSize: 25 * 1024 * 1024, // 25MB
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'],
    profileColumn: null
  },
  logo: {
    bucket: 'company-logos',
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp'],
    profileColumn: 'logo_url'
  },
  media: {
    bucket: 'course-media',
    maxSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: ['video/mp4', 'video/webm', 'audio/mpeg', 'audio/wav', 'image/jpeg', 'image/png'],
    profileColumn: null
  }
};

export const POST = withUploadSecurity(
  async (req: NextRequest, user: any) => {
    console.log('📤 Consolidated Upload API - Starting upload process...');
    console.log('✅ User authenticated:', { id: user.id, email: user.email });

    // Step 2: Parse and validate form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const fileType = formData.get('type') as string || 'document';
    const entityId = formData.get('entityId') as string; // For organization logos, etc.

    if (!file) {
      throw new Error('No file provided');
    }

    // Validate upload parameters
    const uploadParams = ValidationSchemas.FileUpload.upload.parse({
      type: fileType,
      entityId: entityId || undefined
    });

    // Validate file metadata
    const fileMetadata = ValidationSchemas.FileUpload.fileMetadata.parse({
      name: file.name,
      size: file.size,
      type: file.type
    });

    // Step 3: Validate file type and size
    const config = FILE_CONFIGS[fileType as keyof typeof FILE_CONFIGS];
    if (!config) {
      throw new Error(`Invalid file type: ${fileType}`);
    }

    if (file.size > config.maxSize) {
      throw new Error(`File too large. Maximum size: ${config.maxSize / (1024 * 1024)}MB`);
    }

    if (!config.allowedTypes.includes(file.type)) {
      throw new Error(`Invalid file format. Allowed: ${config.allowedTypes.join(', ')}`);
    }

    console.log('✅ File validation passed:', {
      name: file.name,
      size: file.size,
      type: file.type,
      uploadType: fileType
    });

    // Step 4: Generate unique filename
    const timestamp = Date.now();
    const fileExt = file.name.split('.').pop();
    const uniqueId = uuidv4();
    const fileName = `${uniqueId}-${timestamp}.${fileExt}`;
    
    // Create path based on file type and user/entity
    let filePath: string;
    if (fileType === 'logo' && entityId) {
      filePath = `logos/${entityId}/${fileName}`;
    } else {
      filePath = `${user.id}/${fileName}`;
    }

    console.log('📁 Generated file path:', filePath);

    // Step 5: Upload to Supabase Storage
    const adminClient = createAdminClient();
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = new Uint8Array(arrayBuffer);

    const { data: uploadData, error: uploadError } = await adminClient.storage
      .from(config.bucket)
      .upload(filePath, fileBuffer, {
        cacheControl: '3600',
        upsert: true,
        contentType: file.type,
      });

    if (uploadError) {
      console.error('❌ Upload failed:', uploadError);
      throw new Error(`Upload failed: ${uploadError.message}`);
    }

    console.log('✅ File uploaded successfully:', uploadData);

    // Step 6: Get public URL
    const { data: publicUrlData } = adminClient.storage
      .from(config.bucket)
      .getPublicUrl(filePath);

    const publicUrl = publicUrlData.publicUrl;

    // Step 7: Save file record to database
    const fileRecord = {
      id: uniqueId,
      user_id: user.id,
      file_name: file.name,
      file_type: fileType,
      file_size: file.size,
      mime_type: file.type,
      storage_path: filePath,
      public_url: publicUrl,
      bucket_name: config.bucket,
      entity_id: entityId || null,
      created_at: new Date().toISOString()
    };

    const { data: dbData, error: dbError } = await adminClient
      .from('files')
      .insert(fileRecord)
      .select()
      .single();

    if (dbError) {
      console.error('❌ Database insert failed:', dbError);
      // Clean up uploaded file
      await adminClient.storage.from(config.bucket).remove([filePath]);
      throw new Error(`Database error: ${dbError.message}`);
    }

    console.log('✅ File record saved to database:', dbData);

    // Step 8: Update profile if needed
    if (config.profileColumn && fileType !== 'logo') {
      await updateUserProfile(adminClient, user.id, config.profileColumn, publicUrl);
    }

    return createApiSuccessResponse({
      message: 'File uploaded successfully',
      file: {
        id: uniqueId,
        name: file.name,
        size: file.size,
        type: file.type,
        url: publicUrl,
        uploadType: fileType
      }
    });
  }
);

/**
 * Update user profile with file URL
 */
async function updateUserProfile(
  adminClient: any,
  userId: string,
  column: string,
  url: string
) {
  try {
    // Try to update prospects table first
    const { error: prospectError } = await adminClient
      .from('prospects')
      .update({ [column]: url })
      .eq('user_id', userId);

    if (prospectError) {
      console.warn('Could not update prospects table:', prospectError.message);
    }

    // Also try to update users table if it has the column
    if (column === 'avatar_url') {
      const { error: userError } = await adminClient
        .from('users')
        .update({ [column]: url })
        .eq('id', userId);

      if (userError) {
        console.warn('Could not update users table:', userError.message);
      }
    }
  } catch (error) {
    console.warn('Profile update failed:', error);
  }
}

/**
 * GET /api/upload/consolidated/config
 * Get upload configuration
 */
export async function GET(req: NextRequest) {
  return withApiErrorHandler(async () => {
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return createApiSuccessResponse({
      message: 'Upload configuration',
      fileTypes: Object.keys(FILE_CONFIGS),
      configs: Object.entries(FILE_CONFIGS).reduce((acc, [key, config]) => {
        acc[key] = {
          maxSize: `${config.maxSize / (1024 * 1024)}MB`,
          allowedTypes: config.allowedTypes,
          bucket: config.bucket
        };
        return acc;
      }, {} as Record<string, any>)
    });
  }, 'GET /api/upload/consolidated/config');
}
