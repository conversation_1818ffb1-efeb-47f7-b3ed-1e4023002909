'use client';

import { useState, useEffect, useMemo, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { createBrowserClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import {
  Building2,
  Users,
  Plus,
  Search,
  MoreVertical,
  Edit,
  Trash2,
  Filter,
  Download,
  TrendingUp,
  Activity,
  Crown,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Settings,
  AlertTriangle,
  UserCheck,
  Target
} from 'lucide-react';

// Define types based on our database schema
interface Department {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  organization_id: string;
  department_head_id: string | null;
  created_at: string | null;
  updated_at: string | null;
  department_head?: {
    id: string;
    full_name: string;
    email: string;
  };
  _count?: {
    employees: number;
  };
}

interface DepartmentStats {
  totalDepartments: number;
  totalStaff: number;
  departmentsWithHeads: number;
  averageTeamSize: number;
}

interface User {
  id: string;
  full_name: string;
  email: string;
}

export default function DepartmentsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);

  const supabase = createBrowserClient();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [headFilter, setHeadFilter] = useState('all');
  const [sizeFilter, setSizeFilter] = useState('all');
  const [sortField, setSortField] = useState<keyof Department>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    department_head_id: ''
  });

  const orgSlug = params.orgSlug as string;

  // Calculate stats
  const stats = useMemo((): DepartmentStats => {
    const totalDepartments = departments.length;
    const totalStaff = departments.reduce((sum, dept) => sum + (dept._count?.employees || 0), 0);
    const departmentsWithHeads = departments.filter(dept => dept.department_head_id).length;
    const averageTeamSize = totalDepartments > 0 ? Math.round(totalStaff / totalDepartments) : 0;

    return {
      totalDepartments,
      totalStaff,
      departmentsWithHeads,
      averageTeamSize
    };
  }, [departments]);

  // Filter and sort departments
  const filteredDepartments = useMemo(() => {
    let filtered = departments.filter(dept => {
      const matchesSearch = searchQuery === '' ||
        dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (dept.description && dept.description.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesHead = headFilter === 'all' ||
        (headFilter === 'with_head' && dept.department_head_id) ||
        (headFilter === 'no_head' && !dept.department_head_id);

      const staffCount = dept._count?.employees || 0;
      const matchesSize = sizeFilter === 'all' ||
        (sizeFilter === 'small' && staffCount <= 5) ||
        (sizeFilter === 'medium' && staffCount > 5 && staffCount <= 15) ||
        (sizeFilter === 'large' && staffCount > 15);

      return matchesSearch && matchesHead && matchesSize;
    });

    // Sort departments
    filtered.sort((a, b) => {
      let aValue: any = a[sortField];
      let bValue: any = b[sortField];

      if (sortField === 'employees') {
        aValue = a._count?.employees || 0;
        bValue = b._count?.employees || 0;
      }

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [departments, searchQuery, headFilter, sizeFilter, sortField, sortDirection]);

  // Sorting functions
  const handleSort = useCallback((field: keyof Department) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  }, [sortField, sortDirection]);

  const getSortIcon = useCallback((field: keyof Department) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortDirection === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  }, [sortField, sortDirection]);

  useEffect(() => {
    fetchDepartments();
    fetchUsers();
  }, [orgSlug]);

  const fetchDepartments = async () => {
    try {
      setLoading(true);
      // First get the organization ID from slug
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError);
        toast({
          title: "Error",
          description: "Failed to load organization data.",
          variant: "destructive",
        });
        return;
      }

      // Fetch departments for this organization
      const { data, error } = await supabase
        .from('departments')
        .select(`
          *,
          department_head:users!departments_department_head_id_fkey (
            id,
            full_name,
            email
          )
        `)
        .eq('organization_id', orgData.id)
        .order('name');

      if (error) {
        console.error('Error fetching departments:', error);
        // For development: use mock data if departments table doesn't exist or is empty
        const mockDepartments: Department[] = [
          {
            id: '1',
            name: 'Engineering',
            slug: 'engineering',
            description: 'Software development and technical operations',
            organization_id: orgData.id,
            department_head_id: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            _count: { employees: 12 }
          },
          {
            id: '2',
            name: 'Marketing',
            slug: 'marketing',
            description: 'Brand management and customer acquisition',
            organization_id: orgData.id,
            department_head_id: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            _count: { employees: 8 }
          },
          {
            id: '3',
            name: 'Human Resources',
            slug: 'human-resources',
            description: 'People operations and talent management',
            organization_id: orgData.id,
            department_head_id: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            _count: { employees: 6 }
          }
        ];
        setDepartments(mockDepartments);
        return;
      }

      // Get employee counts for each department and add slugs
      const departmentsWithCounts = await Promise.all(
        (data || []).map(async (dept) => {
          const { data: employments, error: empError } = await supabase
            .from('employment_relationships')
            .select('id')
            .eq('department_id', dept.id)
            .eq('status', 'active');

          if (empError) {
            console.error('Error fetching employee count for department:', dept.name, empError);
          }

          return {
            ...dept,
            slug: dept.slug || dept.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
            _count: {
              employees: employments?.length || 0
            }
          };
        })
      );

      setDepartments(departmentsWithCounts);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast({
        title: "Error",
        description: "Failed to load departments.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      // Get organization users for department head selection
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError);
        return;
      }

      // Get users who have employment relationships with this organization
      // Use the specific relationship: user_id -> users.id
      const { data, error } = await supabase
        .from('employment_relationships')
        .select(`
          users!employment_relationships_user_id_fkey (
            id,
            full_name,
            email
          )
        `)
        .eq('organization_id', orgData.id)
        .in('status', ['active'])
        .in('role', ['organization_admin', 'department_admin', 'staff_member']);

      if (error) {
        console.error('Error fetching organization users:', error);
        return;
      }

      // Extract unique users (in case someone has multiple employment relationships)
      const uniqueUsers = data?.reduce((acc, rel) => {
        const user = rel.users;
        if (user && !acc.find(u => u.id === user.id)) {
          acc.push(user);
        }
        return acc;
      }, [] as User[]) || [];

      setUsers(uniqueUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      setUsers([]);
    }
  };

  const handleCreateDepartment = async () => {
    try {
      // Get organization ID
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError) throw orgError;

      const { error } = await supabase
        .from('departments')
        .insert({
          name: formData.name,
          description: formData.description || null,
          department_head_id: formData.department_head_id || null,
          organization_id: orgData.id
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Department created successfully.",
      });

      setIsCreateDialogOpen(false);
      setFormData({ name: '', description: '', department_head_id: '' });
      fetchDepartments();
    } catch (error) {
      console.error('Error creating department:', error);
      toast({
        title: "Error",
        description: "Failed to create department.",
        variant: "destructive",
      });
    }
  };

  const handleEditDepartment = async () => {
    if (!selectedDepartment) return;

    try {
      const { error } = await supabase
        .from('departments')
        .update({
          name: formData.name,
          description: formData.description || null,
          department_head_id: formData.department_head_id || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedDepartment.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Department updated successfully.",
      });

      setIsEditDialogOpen(false);
      setSelectedDepartment(null);
      setFormData({ name: '', description: '', department_head_id: '' });
      fetchDepartments();
    } catch (error) {
      console.error('Error updating department:', error);
      toast({
        title: "Error",
        description: "Failed to update department.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteDepartment = async (departmentId: string) => {
    if (!confirm('Are you sure you want to delete this department?')) return;

    try {
      const { error } = await supabase
        .from('departments')
        .delete()
        .eq('id', departmentId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Department deleted successfully.",
      });

      fetchDepartments();
    } catch (error) {
      console.error('Error deleting department:', error);
      toast({
        title: "Error",
        description: "Failed to delete department.",
        variant: "destructive",
      });
    }
  };

  const openEditDialog = (department: Department) => {
    setSelectedDepartment(department);
    setFormData({
      name: department.name,
      description: department.description || '',
      department_head_id: department.department_head_id || ''
    });
    setIsEditDialogOpen(true);
  };

  if (loading && departments.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading departments...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Departments</h1>
          <p className="text-muted-foreground">
            Manage your organization's departments and team structure
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Department
          </Button>
        </div>
      </div>

      {/* Create Department Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Department</DialogTitle>
              <DialogDescription>
                Add a new department to your organization.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Department Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter department name"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Enter department description"
                />
              </div>
              <div>
                <Label htmlFor="head">Department Head</Label>
                <Select
                  value={formData.department_head_id}
                  onValueChange={(value) => setFormData({ ...formData, department_head_id: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select department head" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No department head</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.full_name} ({user.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateDepartment}>Create Department</Button>
            </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Departments</div>
              <div className="text-3xl font-bold mt-1">{stats.totalDepartments}</div>
              <p className="text-xs text-muted-foreground">
                {stats.departmentsWithHeads} with heads assigned
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
              <Users className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Staff</div>
              <div className="text-3xl font-bold mt-1">{stats.totalStaff}</div>
              <p className="text-xs text-muted-foreground">
                Across all departments
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/20">
              <UserCheck className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Heads Assigned</div>
              <div className="text-3xl font-bold mt-1">{stats.departmentsWithHeads}</div>
              <p className="text-xs text-muted-foreground">
                {Math.round((stats.departmentsWithHeads / Math.max(stats.totalDepartments, 1)) * 100)}% coverage
              </p>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg shadow-orange-500/20">
              <Target className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Avg Team Size</div>
              <div className="text-3xl font-bold mt-1">{stats.averageTeamSize}</div>
              <p className="text-xs text-muted-foreground">
                Staff per department
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-2 flex-1">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search departments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>

          <Select value={headFilter} onValueChange={setHeadFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Head Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              <SelectItem value="with_head">With Head</SelectItem>
              <SelectItem value="no_head">No Head</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sizeFilter} onValueChange={setSizeFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Team Size" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Sizes</SelectItem>
              <SelectItem value="small">Small (1-5)</SelectItem>
              <SelectItem value="medium">Medium (6-15)</SelectItem>
              <SelectItem value="large">Large (16+)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-muted-foreground">
          {filteredDepartments.length} of {departments.length} departments
        </div>
      </div>

      {/* Departments Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[250px]">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Department
                    {getSortIcon('name')}
                  </button>
                </TableHead>
                <TableHead>
                  <button
                    onClick={() => handleSort('department_head_id')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Department Head
                    {getSortIcon('department_head_id')}
                  </button>
                </TableHead>
                <TableHead>
                  <button
                    onClick={() => handleSort('employees' as keyof Department)}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Staff Count
                    {getSortIcon('employees' as keyof Department)}
                  </button>
                </TableHead>
                <TableHead>
                  <button
                    onClick={() => handleSort('created_at')}
                    className="flex items-center gap-2 hover:text-foreground transition-colors"
                  >
                    Created
                    {getSortIcon('created_at')}
                  </button>
                </TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center h-24 text-muted-foreground">
                    Loading departments...
                  </TableCell>
                </TableRow>
              ) : filteredDepartments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center h-24">
                    <div className="flex flex-col items-center justify-center py-8">
                      <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Departments Found</h3>
                      <p className="text-muted-foreground text-center mb-4">
                        {searchQuery || headFilter !== 'all' || sizeFilter !== 'all'
                          ? 'No departments match your current filters'
                          : 'No departments found'}
                      </p>
                      {!searchQuery && headFilter === 'all' && sizeFilter === 'all' && (
                        <Button onClick={() => setIsCreateDialogOpen(true)}>
                          <Plus className="mr-2 h-4 w-4" />
                          Create First Department
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredDepartments.map((department) => (
                  <TableRow
                    key={department.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => router.push(`/org/${orgSlug}/departments/${department.slug}/staff`)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-sm font-semibold">
                          {department.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="font-medium">{department.name}</div>
                          {department.description && (
                            <div className="text-sm text-muted-foreground">
                              {department.description}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {department.department_head ? (
                        <div className="flex items-center gap-2">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {department.department_head.full_name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm">{department.department_head.full_name}</span>
                        </div>
                      ) : (
                        <span className="text-sm text-muted-foreground">No head assigned</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          (department._count?.employees || 0) > 10 ? 'default' :
                          (department._count?.employees || 0) > 5 ? 'secondary' :
                          'outline'
                        }
                      >
                        <Users className="h-3 w-3 mr-1" />
                        {department._count?.employees || 0}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {department.created_at ? (
                        new Date(department.created_at).toLocaleDateString()
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Manage Department</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              router.push(`/org/${orgSlug}/departments/${department.slug}/staff`);
                            }}
                          >
                            <Users className="h-4 w-4 mr-2" />
                            <span>View Staff</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              openEditDialog(department);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            <span>Edit Department</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-red-600 focus:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteDepartment(department.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            <span>Delete Department</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Department Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Department</DialogTitle>
            <DialogDescription>
              Update department information.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Department Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter department name"
              />
            </div>
            <div>
              <Label htmlFor="edit-description">Description</Label>
              <Input
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Enter department description"
              />
            </div>
            <div>
              <Label htmlFor="edit-head">Department Head</Label>
              <Select
                value={formData.department_head_id}
                onValueChange={(value) => setFormData({ ...formData, department_head_id: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select department head" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No department head</SelectItem>
                  {users.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.full_name} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditDepartment}>Update Department</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
