"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { Loader2, AlertTriangle, CheckCircle, Clock, XCircle } from "lucide-react"

interface Organization {
  id: string
  name: string
  status: string
}

interface OrganizationStatusDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  organization: Organization | null
  onSuccess: () => void
}

const statusOptions = [
  {
    value: "active",
    label: "Active",
    description: "Organization is fully operational",
    icon: CheckCircle,
    color: "bg-green-500"
  },
  {
    value: "trial",
    label: "Trial",
    description: "Organization is in trial period",
    icon: Clock,
    color: "bg-blue-500"
  },
  {
    value: "suspended",
    label: "Suspended",
    description: "Organization access is temporarily suspended",
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    color: "bg-yellow-500"
  },
  {
    value: "cancelled",
    label: "Cancelled",
    description: "Organization subscription is cancelled",
    icon: XCircle,
    color: "bg-red-500"
  }
]

const getStatusBadge = (status: string) => {
  const statusConfig = statusOptions.find(s => s.value === status)
  if (!statusConfig) return null

  const Icon = statusConfig.icon
  
  return (
    <Badge variant="outline" className="flex items-center gap-1">
      <div className={`w-2 h-2 rounded-full ${statusConfig.color}`} />
      {statusConfig.label}
    </Badge>
  )
}

export function OrganizationStatusDialog({
  open,
  onOpenChange,
  organization,
  onSuccess
}: OrganizationStatusDialogProps) {
  const [loading, setLoading] = useState(false)
  const [selectedStatus, setSelectedStatus] = useState<string>("")

  // Set initial status when dialog opens
  useState(() => {
    if (organization && open) {
      setSelectedStatus(organization.status)
    }
  })

  const handleStatusUpdate = async () => {
    if (!organization || !selectedStatus) return

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/organizations/${organization.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: selectedStatus }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update organization status')
      }

      toast.success('Organization status updated successfully')
      onSuccess()
      onOpenChange(false)
    } catch (error: any) {
      console.error('Error updating organization status:', error)
      toast.error(error.message || 'Failed to update organization status')
    } finally {
      setLoading(false)
    }
  }

  if (!organization) return null

  const currentStatus = statusOptions.find(s => s.value === organization.status)
  const newStatus = statusOptions.find(s => s.value === selectedStatus)
  const hasChanged = selectedStatus !== organization.status

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Update Organization Status</DialogTitle>
          <DialogDescription>
            Change the status for <strong>{organization.name}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Current Status</label>
            <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              {currentStatus && (
                <>
                  <currentStatus.icon className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">{currentStatus.label}</div>
                    <div className="text-sm text-muted-foreground">
                      {currentStatus.description}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* New Status Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">New Status</label>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => {
                  const Icon = status.icon
                  return (
                    <SelectItem key={status.value} value={status.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${status.color}`} />
                        <span>{status.label}</span>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
            
            {newStatus && (
              <div className="text-sm text-muted-foreground p-2 bg-muted/50 rounded">
                {newStatus.description}
              </div>
            )}
          </div>

          {/* Status Change Preview */}
          {hasChanged && (
            <div className="p-4 border rounded-lg bg-blue-50 dark:bg-blue-950/20">
              <div className="flex items-center gap-2 text-sm">
                <span className="font-medium">Status will change from:</span>
                {getStatusBadge(organization.status)}
                <span>→</span>
                {getStatusBadge(selectedStatus)}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleStatusUpdate} 
            disabled={loading || !hasChanged}
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
