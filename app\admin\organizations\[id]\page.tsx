'use client';

import { useState, useEffect, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { createBrowserClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import {
  Building2,
  Users,
  ArrowLeft,
  Globe,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Target,
  Activity,
  TrendingUp,
  Award,
  BookO<PERSON>,
  Crown,
  User,
  Settings,
  Edit,
  Trash2
} from 'lucide-react';

// Define types
interface Organization {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  website: string | null;
  industry: string | null;
  size: string | null;
  created_at: string | null;
  updated_at: string | null;
}

interface Department {
  id: string;
  name: string;
  description: string | null;
  created_at: string | null;
  staff_count: number;
  department_head?: {
    full_name: string;
    email: string;
  };
}

interface StaffMember {
  id: string;
  full_name: string;
  email: string;
  job_title: string | null;
  role: string;
  status: string;
  hire_date: string | null;
  department_name: string;
  avatar_url?: string;
}

interface OrganizationStats {
  totalEmployees: number;
  totalDepartments: number;
  activeEmployees: number;
  organizationAdmins: number;
  departmentAdmins: number;
  staffMembers: number;
}

export default function OrganizationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);

  const supabase = createBrowserClient();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [activeTab, setActiveTab] = useState('overview');

  const organizationId = params.id as string;

  // Calculate stats
  const stats = useMemo((): OrganizationStats => {
    const totalEmployees = staffMembers.length;
    const totalDepartments = departments.length;
    const activeEmployees = staffMembers.filter(staff => staff.status === 'active').length;
    const organizationAdmins = staffMembers.filter(staff => staff.role === 'organization_admin').length;
    const departmentAdmins = staffMembers.filter(staff => staff.role === 'department_admin').length;
    const staffMembersCount = staffMembers.filter(staff => staff.role === 'staff_member').length;

    return {
      totalEmployees,
      totalDepartments,
      activeEmployees,
      organizationAdmins,
      departmentAdmins,
      staffMembers: staffMembersCount
    };
  }, [staffMembers, departments]);

  useEffect(() => {
    fetchOrganizationDetails();
  }, [organizationId]);

  const fetchOrganizationDetails = async () => {
    try {
      setLoading(true);

      // Fetch organization details
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', organizationId)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError);
        toast({
          title: "Error",
          description: "Failed to load organization details.",
          variant: "destructive",
        });
        return;
      }

      setOrganization(orgData);

      // Fetch departments
      const { data: deptData, error: deptError } = await supabase
        .from('departments')
        .select(`
          id,
          name,
          description,
          created_at,
          department_head_id,
          users:users!departments_department_head_id_fkey (
            full_name,
            email
          )
        `)
        .eq('organization_id', organizationId);

      if (deptError) {
        console.error('Error fetching departments:', deptError);
      } else {
        // Get staff count for each department
        const departmentsWithCounts = await Promise.all(
          (deptData || []).map(async (dept) => {
            const { data: employments, error: empError } = await supabase
              .from('employment_relationships')
              .select('id')
              .eq('department_id', dept.id)
              .eq('status', 'active');

            return {
              ...dept,
              staff_count: employments?.length || 0,
              department_head: dept.users || undefined
            };
          })
        );
        setDepartments(departmentsWithCounts);
      }

      // Fetch staff members
      const { data: employments, error: empError } = await supabase
        .from('employment_relationships')
        .select(`
          id,
          user_id,
          department_id,
          role,
          status,
          job_title,
          hire_date,
          users!employment_relationships_user_id_fkey (
            id,
            full_name,
            email
          ),
          departments!employment_relationships_department_id_fkey (
            name
          )
        `)
        .eq('organization_id', organizationId);

      if (empError) {
        console.error('Error fetching staff:', empError);
      } else {
        const staffData = (employments || []).map(emp => ({
          id: emp.users?.id || '',
          full_name: emp.users?.full_name || '',
          email: emp.users?.email || '',
          job_title: emp.job_title,
          role: emp.role,
          status: emp.status,
          hire_date: emp.hire_date,
          department_name: emp.departments?.name || 'Unknown Department'
        }));
        setStaffMembers(staffData);
      }

    } catch (error) {
      console.error('Error fetching organization details:', error);
      toast({
        title: "Error",
        description: "Failed to load organization details.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading organization details...</p>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Organization Not Found</h3>
          <p className="text-muted-foreground mb-4">The requested organization could not be found.</p>
          <Button onClick={() => router.push('/admin/organizations')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 p-[50px]">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/admin/organizations')}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{organization.name}</h1>
            <p className="text-muted-foreground">
              Organization details and management
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Edit className="mr-2 h-4 w-4" />
            Edit Organization
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Organization Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Description</Label>
              <p className="mt-1">{organization.description || 'No description provided'}</p>
            </div>
            {organization.website && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Website</Label>
                <div className="mt-1 flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-600" />
                  <a
                    href={organization.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {organization.website.replace(/^https?:\/\//, '')}
                  </a>
                </div>
              </div>
            )}
            {organization.industry && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Industry</Label>
                <div className="mt-1">
                  <Badge variant="secondary">{organization.industry}</Badge>
                </div>
              </div>
            )}
            {organization.size && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Organization Size</Label>
                <div className="mt-1">
                  <Badge variant="outline">{organization.size}</Badge>
                </div>
              </div>
            )}
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Created</Label>
              <div className="mt-1 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{organization.created_at ? new Date(organization.created_at).toLocaleDateString() : 'Unknown'}</span>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Organization ID</Label>
              <p className="mt-1 font-mono text-sm text-muted-foreground">{organization.id}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
              <Users className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Employees</div>
              <div className="text-3xl font-bold mt-1">{stats.totalEmployees}</div>
            </div>
          </div>
        </div>

        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Departments</div>
              <div className="text-3xl font-bold mt-1">{stats.totalDepartments}</div>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/20">
              <Activity className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Staff</div>
              <div className="text-3xl font-bold mt-1">{stats.activeEmployees}</div>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg shadow-orange-500/20">
              <Crown className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Org Admins</div>
              <div className="text-3xl font-bold mt-1">{stats.organizationAdmins}</div>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-teal-500 to-cyan-600 text-white shadow-lg shadow-teal-500/20">
              <Target className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Dept Heads</div>
              <div className="text-3xl font-bold mt-1">{stats.departmentAdmins}</div>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-pink-500 to-rose-600 text-white shadow-lg shadow-pink-500/20">
              <User className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Staff Members</div>
              <div className="text-3xl font-bold mt-1">{stats.staffMembers}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="departments">Departments ({stats.totalDepartments})</TabsTrigger>
          <TabsTrigger value="staff">Staff ({stats.totalEmployees})</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Departments */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Departments</CardTitle>
                <CardDescription>Latest departments in this organization</CardDescription>
              </CardHeader>
              <CardContent>
                {departments.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">No departments found</p>
                ) : (
                  <div className="space-y-3">
                    {departments.slice(0, 5).map((dept) => (
                      <div key={dept.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div>
                          <div className="font-medium">{dept.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {dept.staff_count} staff members
                          </div>
                        </div>
                        <Badge variant="outline">{dept.staff_count}</Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Staff */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Staff</CardTitle>
                <CardDescription>Latest staff members in this organization</CardDescription>
              </CardHeader>
              <CardContent>
                {staffMembers.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">No staff members found</p>
                ) : (
                  <div className="space-y-3">
                    {staffMembers.slice(0, 5).map((staff) => (
                      <div key={staff.id} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={staff.avatar_url} alt={staff.full_name} />
                          <AvatarFallback className="text-xs">
                            {staff.full_name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{staff.full_name}</div>
                          <div className="text-sm text-muted-foreground truncate">
                            {staff.job_title} • {staff.department_name}
                          </div>
                        </div>
                        <Badge
                          variant={staff.role === 'organization_admin' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {staff.role === 'organization_admin' ? 'Admin' :
                           staff.role === 'department_admin' ? 'Head' : 'Staff'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="departments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Departments</CardTitle>
              <CardDescription>All departments in this organization</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Department</TableHead>
                    <TableHead>Department Head</TableHead>
                    <TableHead>Staff Count</TableHead>
                    <TableHead>Created</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {departments.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center h-24 text-muted-foreground">
                        No departments found
                      </TableCell>
                    </TableRow>
                  ) : (
                    departments.map((dept) => (
                      <TableRow key={dept.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{dept.name}</div>
                            {dept.description && (
                              <div className="text-sm text-muted-foreground">{dept.description}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {dept.department_head ? (
                            <div>
                              <div className="font-medium">{dept.department_head.full_name}</div>
                              <div className="text-sm text-muted-foreground">{dept.department_head.email}</div>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">No head assigned</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            <Users className="h-3 w-3 mr-1" />
                            {dept.staff_count}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {dept.created_at ? new Date(dept.created_at).toLocaleDateString() : '-'}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="staff" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Staff Members</CardTitle>
              <CardDescription>All staff members in this organization</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Staff Member</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Hire Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {staffMembers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center h-24 text-muted-foreground">
                        No staff members found
                      </TableCell>
                    </TableRow>
                  ) : (
                    staffMembers.map((staff) => (
                      <TableRow key={staff.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={staff.avatar_url} alt={staff.full_name} />
                              <AvatarFallback className="text-xs">
                                {staff.full_name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{staff.full_name}</div>
                              <div className="text-sm text-muted-foreground">{staff.email}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <Badge
                              variant={
                                staff.role === 'organization_admin' ? 'default' :
                                staff.role === 'department_admin' ? 'secondary' :
                                'outline'
                              }
                              className="mb-1"
                            >
                              {staff.role === 'organization_admin' && <Crown className="h-3 w-3 mr-1" />}
                              {staff.role === 'department_admin' && <Target className="h-3 w-3 mr-1" />}
                              {staff.role === 'staff_member' && <User className="h-3 w-3 mr-1" />}
                              {staff.role === 'organization_admin' ? 'Org Admin' :
                               staff.role === 'department_admin' ? 'Dept Head' : 'Staff'}
                            </Badge>
                            {staff.job_title && (
                              <div className="text-sm text-muted-foreground">{staff.job_title}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{staff.department_name}</span>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={staff.status === 'active' ? 'default' : 'secondary'}
                            className={staff.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                          >
                            {staff.status === 'active' ? 'Active' :
                             staff.status === 'on_leave' ? 'On Leave' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {staff.hire_date ? new Date(staff.hire_date).toLocaleDateString() : '-'}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
