/**
 * Security Headers Module
 * Handles HTTP security headers for API responses
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-error-handler';

/**
 * Generate unique request ID for tracking
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Get comprehensive security headers
 */
function getSecurityHeaders(): Record<string, string> {
  return {
    // Content Security Policy
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.openai.com https://*.supabase.co wss://*.supabase.co",
      "media-src 'self' blob:",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'none'",
      "upgrade-insecure-requests"
    ].join('; '),
    
    // HTTP Strict Transport Security
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    
    // Prevent MIME type sniffing
    'X-Content-Type-Options': 'nosniff',
    
    // Prevent clickjacking
    'X-Frame-Options': 'DENY',
    
    // XSS Protection
    'X-XSS-Protection': '1; mode=block',
    
    // Referrer Policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Permissions Policy
    'Permissions-Policy': [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()',
      'magnetometer=()',
      'accelerometer=()',
      'gyroscope=()'
    ].join(', '),
    
    // Remove server information
    'Server': 'Luna-Skills-Platform',
    
    // API specific headers
    'X-API-Version': '1.0',
    'X-Request-ID': generateRequestId()
  };
}

/**
 * Security headers middleware
 */
export function withSecurityHeaders(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async function securityHeadersMiddleware(
    request: NextRequest
  ): Promise<NextResponse> {
    try {
      // Execute the handler first
      const response = await handler(request);
      
      // Get security headers
      const securityHeaders = getSecurityHeaders();
      
      // Apply security headers
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      
      return response;
      
    } catch (error) {
      return handleApiError(error, 'Security Headers Middleware Error');
    }
  };
}

/**
 * CORS headers configuration
 */
export interface CorsConfig {
  origin?: string | string[] | boolean;
  methods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
}

/**
 * Default CORS configuration
 */
const DEFAULT_CORS_CONFIG: CorsConfig = {
  origin: process.env.NODE_ENV === 'development' ? true : false,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'X-API-Key'
  ],
  exposedHeaders: [
    'X-RateLimit-Limit',
    'X-RateLimit-Remaining',
    'X-RateLimit-Reset',
    'X-Request-ID'
  ],
  credentials: true,
  maxAge: 86400 // 24 hours
};

/**
 * CORS middleware
 */
export function withCors(config: CorsConfig = DEFAULT_CORS_CONFIG) {
  return function corsMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      // Handle preflight requests
      if (request.method === 'OPTIONS') {
        return handlePreflightRequest(request, config);
      }

      try {
        // Execute handler
        const response = await handler(request);
        
        // Add CORS headers
        addCorsHeaders(response, request, config);
        
        return response;
        
      } catch (error) {
        const errorResponse = handleApiError(error, 'CORS Middleware Error');
        addCorsHeaders(errorResponse, request, config);
        return errorResponse;
      }
    };
  };
}

/**
 * Handle preflight CORS requests
 */
function handlePreflightRequest(
  request: NextRequest, 
  config: CorsConfig
): NextResponse {
  const response = new NextResponse(null, { status: 204 });
  addCorsHeaders(response, request, config);
  return response;
}

/**
 * Add CORS headers to response
 */
function addCorsHeaders(
  response: NextResponse, 
  request: NextRequest, 
  config: CorsConfig
): void {
  const origin = request.headers.get('origin');
  
  // Handle origin
  if (config.origin === true) {
    response.headers.set('Access-Control-Allow-Origin', origin || '*');
  } else if (config.origin === false) {
    // No CORS
  } else if (typeof config.origin === 'string') {
    response.headers.set('Access-Control-Allow-Origin', config.origin);
  } else if (Array.isArray(config.origin) && origin) {
    if (config.origin.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }
  }
  
  // Handle methods
  if (config.methods) {
    response.headers.set('Access-Control-Allow-Methods', config.methods.join(', '));
  }
  
  // Handle allowed headers
  if (config.allowedHeaders) {
    response.headers.set('Access-Control-Allow-Headers', config.allowedHeaders.join(', '));
  }
  
  // Handle exposed headers
  if (config.exposedHeaders) {
    response.headers.set('Access-Control-Expose-Headers', config.exposedHeaders.join(', '));
  }
  
  // Handle credentials
  if (config.credentials) {
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  }
  
  // Handle max age
  if (config.maxAge) {
    response.headers.set('Access-Control-Max-Age', config.maxAge.toString());
  }
}

/**
 * Security headers for development
 */
export function getDevSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Server': 'Luna-Skills-Platform-Dev',
    'X-API-Version': '1.0-dev',
    'X-Request-ID': generateRequestId()
  };
}

/**
 * Apply minimal security headers for development
 */
export function withDevSecurityHeaders(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async function devSecurityHeadersMiddleware(
    request: NextRequest
  ): Promise<NextResponse> {
    try {
      const response = await handler(request);
      const headers = getDevSecurityHeaders();
      
      Object.entries(headers).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      
      return response;
    } catch (error) {
      return handleApiError(error, 'Dev Security Headers Error');
    }
  };
}
