import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase'
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth-utils'

// File type configurations for course content
const COURSE_CONTENT_CONFIGS = {
  video: {
    bucket: 'course-media',
    maxSize: 500 * 1024 * 1024, // 500MB
    allowedTypes: ['video/mp4', 'video/webm', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv'],
    folder: 'videos'
  },
  document: {
    bucket: 'course-media',
    maxSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    folder: 'documents'
  },
  presentation: {
    bucket: 'course-media',
    maxSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/pdf'],
    folder: 'presentations'
  },
  image: {
    bucket: 'course-media',
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    folder: 'images'
  }
}

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin()
    if (!authResult.user) {
      return createAuthErrorResponse(authResult)
    }

    // Parse form data
    const formData = await req.formData()
    const file = formData.get('file') as File
    const folder = formData.get('folder') as string || 'course-media'
    const contentType = formData.get('contentType') as string

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    // Determine file type based on MIME type
    let fileTypeConfig
    if (file.type.startsWith('video/')) {
      fileTypeConfig = COURSE_CONTENT_CONFIGS.video
    } else if (file.type.startsWith('image/')) {
      fileTypeConfig = COURSE_CONTENT_CONFIGS.image
    } else if (file.type === 'application/pdf' && contentType === 'presentation') {
      fileTypeConfig = COURSE_CONTENT_CONFIGS.presentation
    } else if (file.type === 'application/pdf' || file.type.includes('document')) {
      fileTypeConfig = COURSE_CONTENT_CONFIGS.document
    } else if (file.type.includes('presentation')) {
      fileTypeConfig = COURSE_CONTENT_CONFIGS.presentation
    } else {
      return NextResponse.json(
        { error: 'Unsupported file type' },
        { status: 400 }
      )
    }

    // Validate file size
    if (file.size > fileTypeConfig.maxSize) {
      return NextResponse.json(
        { error: `File size exceeds maximum allowed size of ${fileTypeConfig.maxSize / (1024 * 1024)}MB` },
        { status: 400 }
      )
    }

    // Validate file type
    if (!fileTypeConfig.allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'File type not allowed' },
        { status: 400 }
      )
    }

    // Generate unique filename
    const timestamp = Date.now()
    const fileExt = file.name.split('.').pop()
    const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_')
    const uniqueFileName = `${timestamp}_${sanitizedName}`
    const filePath = `${fileTypeConfig.folder}/${uniqueFileName}`

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const fileBuffer = new Uint8Array(arrayBuffer)

    // Upload to Supabase Storage
    const adminClient = createAdminClient()
    const { data: uploadData, error: uploadError } = await adminClient.storage
      .from(fileTypeConfig.bucket)
      .upload(filePath, fileBuffer, {
        cacheControl: '3600',
        upsert: false,
        contentType: file.type
      })

    if (uploadError) {
      console.error('Upload error:', uploadError)
      return NextResponse.json(
        { error: 'Failed to upload file', details: uploadError.message },
        { status: 500 }
      )
    }

    // Get public URL
    const { data: publicUrlData } = adminClient.storage
      .from(fileTypeConfig.bucket)
      .getPublicUrl(filePath)

    if (!publicUrlData?.publicUrl) {
      return NextResponse.json(
        { error: 'Failed to get public URL' },
        { status: 500 }
      )
    }

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      file: {
        name: file.name,
        originalName: file.name,
        size: file.size,
        type: file.type,
        url: publicUrlData.publicUrl,
        path: filePath,
        bucket: fileTypeConfig.bucket,
        uploadedAt: new Date().toISOString()
      }
    })

  } catch (error: any) {
    console.error('Upload API error:', error)
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to check upload service status
export async function GET() {
  return NextResponse.json({
    status: 'operational',
    supportedTypes: {
      video: COURSE_CONTENT_CONFIGS.video.allowedTypes,
      document: COURSE_CONTENT_CONFIGS.document.allowedTypes,
      presentation: COURSE_CONTENT_CONFIGS.presentation.allowedTypes,
      image: COURSE_CONTENT_CONFIGS.image.allowedTypes
    },
    maxSizes: {
      video: `${COURSE_CONTENT_CONFIGS.video.maxSize / (1024 * 1024)}MB`,
      document: `${COURSE_CONTENT_CONFIGS.document.maxSize / (1024 * 1024)}MB`,
      presentation: `${COURSE_CONTENT_CONFIGS.presentation.maxSize / (1024 * 1024)}MB`,
      image: `${COURSE_CONTENT_CONFIGS.image.maxSize / (1024 * 1024)}MB`
    }
  })
}
