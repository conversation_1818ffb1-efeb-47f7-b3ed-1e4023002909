import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const { accessToken, userId, metadata } = await request.json();
    
    if (!accessToken || !userId) {
      return NextResponse.json(
        { error: 'Missing required information' },
        { status: 400 }
      );
    }
    
    // Create admin client to create necessary records
    const adminClient = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    console.log('Processing invitation acceptance for user:', userId);
    console.log('Metadata:', metadata);
    
    // Get user data to confirm it exists in auth.users
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId);
    
    if (userError || !userData?.user) {
      console.error('Failed to get user data:', userError);
      return NextResponse.json(
        { error: 'Failed to verify user' },
        { status: 500 }
      );
    }
    
    const user = userData.user;
    console.log('User from auth system:', user.email);
    
    // Check if the user already exists in the users table
    const { data: existingUser, error: existingUserError } = await adminClient
      .from('users')
      .select('id, is_placeholder')
      .eq('id', user.id)
      .maybeSingle();
      
    if (existingUserError) {
      console.error('Error checking existing user:', existingUserError);
    }
    
    // Check if we're dealing with a placeholder scenario
    let placeholderUserId = metadata?.placeholder_user_id;
    
    // Handle the placeholder user migration if needed
    if (placeholderUserId) {
      console.log('Found placeholder user ID:', placeholderUserId);
      
      // Check if there's an invitation-tracking placeholder record
      const { data: placeholderUser, error: placeholderError } = await adminClient
        .from('users')
        .select('id, is_placeholder')
        .eq('id', placeholderUserId)
        .eq('is_placeholder', true)
        .maybeSingle();
      
      if (placeholderError) {
        console.error('Error checking placeholder user:', placeholderError);
      }
      
      if (placeholderUser) {
        console.log('Found placeholder user record, migrating team membership');
        
        // Update any bpo_teams entry that used the placeholder user ID
        const { error: teamUpdateError } = await adminClient
          .from('bpo_teams')
          .update({
            user_id: user.id,  // The real user ID
            is_placeholder: false,
            accepted_at: new Date().toISOString()
          })
          .eq('user_id', placeholderUserId);
        
        if (teamUpdateError) {
          console.error('Error updating team membership from placeholder:', teamUpdateError);
        }
        
        // Delete the placeholder user record
        const { error: deleteError } = await adminClient
          .from('users')
          .delete()
          .eq('id', placeholderUserId)
          .eq('is_placeholder', true);
        
        if (deleteError) {
          console.error('Error deleting placeholder user:', deleteError);
        }
      }
    }
    
    // If user doesn't exist in users table, create them
    if (!existingUser) {
      console.log('Creating new user record in users table');
      // Create a new user record in the users table
      const { error: insertUserError } = await adminClient
        .from('users')
        .insert({
          id: user.id,
          email: user.email || '',
          role: 'bpo_team',
          first_name: user.user_metadata?.first_name || '',
          last_name: user.user_metadata?.last_name || ''
        });
        
      if (insertUserError) {
        console.error('Failed to create user record:', insertUserError);
        return NextResponse.json(
          { error: 'Failed to create user record' },
          { status: 500 }
        );
      }
    }
    
    // Check if we need to add the user to an organization
    if (metadata?.organization_id && metadata?.role) {
      const organizationId = metadata.organization_id;
      const role = metadata.role;

      // Check if the user is already employed by this organization
      const { data: existingEmployment, error: employmentCheckError } = await adminClient
        .from('employment_relationships')
        .select('id')
        .eq('user_id', user.id)
        .eq('organization_id', organizationId)
        .maybeSingle();
        
      if (employmentCheckError) {
        console.error('Error checking existing employment:', employmentCheckError);
      }

      if (!existingEmployment) {
        console.log('Adding user to organization:', organizationId, 'with role:', role);
        // Add the user to the organization
        const { error: employmentInsertError } = await adminClient
          .from('employment_relationships')
          .insert({
            organization_id: organizationId,
            user_id: user.id,
            role: role,
            status: 'active',
            joined_at: new Date().toISOString()
          });
          
        if (employmentInsertError) {
          console.error('Failed to add user to organization:', employmentInsertError);
          return NextResponse.json(
            { error: 'Failed to add user to organization' },
            { status: 500 }
          );
        }
      } else {
        console.log('User already employed, updating status to active');
        // User already exists in the organization, just update the status
        const { error: updateError } = await adminClient
          .from('employment_relationships')
          .update({
            status: 'active',
            joined_at: new Date().toISOString()
          })
          .eq('user_id', user.id)
          .eq('organization_id', organizationId);

        if (updateError) {
          console.error('Failed to update employment record:', updateError);
        }
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'User and employment relationship setup successful'
    });
    
  } catch (error: any) {
    console.error('Error in invitation callback:', error);
    return NextResponse.json(
      { error: error.message || 'Server error processing invitation callback' },
      { status: 500 }
    );
  }
} 