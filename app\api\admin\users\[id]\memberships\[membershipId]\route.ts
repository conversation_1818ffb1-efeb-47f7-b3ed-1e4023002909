import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; membershipId: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    const { id, membershipId } = await params;
    
    // Parse request body
    const body = await req.json();
    const { role, status } = body;

    // Validate role if provided
    if (role) {
      const validRoles = ['organization_admin', 'department_admin', 'staff_member'];
      if (!validRoles.includes(role)) {
        return NextResponse.json(
          { error: 'Invalid role specified. Must be organization_admin, department_admin, or staff_member' },
          { status: 400 }
        );
      }
    }

    // Validate status if provided
    if (status) {
      const validStatuses = ['active', 'inactive', 'terminated'];
      if (!validStatuses.includes(status)) {
        return NextResponse.json(
          { error: 'Invalid status specified. Must be active, inactive, or terminated' },
          { status: 400 }
        );
      }
    }
    
    // Create admin client
    const adminClient = createAdminClient();

    // Update the membership
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (role) updateData.role = role;
    if (status) updateData.status = status;

    const { data: employment, error: employmentError } = await adminClient
      .from('employment_relationships')
      .update(updateData)
      .eq('id', membershipId)
      .eq('user_id', id) // Ensure the employment belongs to the specified user
      .select()
      .single();

    if (employmentError) {
      console.error('Error updating employment relationship:', employmentError);
      return NextResponse.json(
        { error: employmentError.message || 'Failed to update employment relationship' },
        { status: 500 }
      );
    }

    if (!employment) {
      return NextResponse.json(
        { error: 'Employment relationship not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(employment);
  } catch (error: any) {
    console.error('User membership PATCH API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string; membershipId: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    const { id, membershipId } = await params;
    
    // Create admin client
    const adminClient = createAdminClient();

    // Delete the employment relationship
    const { error: employmentError } = await adminClient
      .from('employment_relationships')
      .delete()
      .eq('id', membershipId)
      .eq('user_id', id); // Ensure the employment belongs to the specified user

    if (employmentError) {
      console.error('Error deleting employment relationship:', employmentError);
      return NextResponse.json(
        { error: employmentError.message || 'Failed to delete employment relationship' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('User membership DELETE API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
