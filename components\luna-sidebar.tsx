"use client"

import {
  <PERSON><PERSON>,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  useSidebar,
} from "@/components/ui/sidebar"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  ChevronDown,
  LogOut,
  PanelLeft,
  Building2,
  User,
} from "lucide-react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { useLunaAuth, useCurrentContext } from "@/hooks/use-luna-auth"
import { useNavigation } from "@/hooks/use-navigation"
import { type NavGroup, type NavItem } from "@/lib/navigation-config"

// Helper function to get user initials
function getUserInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}




interface LunaSidebarProps {
  className?: string
}

export function LunaSidebar({ className }: LunaSidebarProps) {
  const { state, open, setOpen } = useSidebar()
  const { user, signOut, loading, switchContext } = useLunaAuth()
  const { context, availableEmployments } = useCurrentContext()
  const { navGroups, isLoading: navLoading } = useNavigation()
  const pathname = usePathname()
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [contextSwitcherOpen, setContextSwitcherOpen] = useState(false)
  const [sidebarMode, setSidebarMode] = useState<"hover" | "persistent">("persistent")

  useEffect(() => {
    setMounted(true)
    // Set initial sidebar state based on mode
    if (sidebarMode === "hover") {
      setOpen(false)
    }
  }, [sidebarMode, setOpen])

  // Prevent hydration mismatch by checking mounted state
  if (!mounted) {
    return (
      <Sidebar className="border-r border-gray-200" collapsible="icon" variant="sidebar">
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </Sidebar>
    )
  }

  // Navigation groups are now provided by the useNavigation hook
  // Use fallback navigation when loading
  const fallbackNavGroups = [
    {
      label: "Main Menu",
      items: [
        { title: "Dashboard", url: "/individual", icon: () => null },
        { title: "Profile", url: "/individual/profile", icon: () => null }
      ]
    }
  ]

  // Get user initials for avatar
  const getUserInitials = (name: string): string => {
    if (!name) return 'U'
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const handleContextSwitch = async (
    contextType: 'individual' | 'organization',
    organizationId?: string
  ) => {
    setContextSwitcherOpen(false)
    try {
      if (switchContext) {
        await switchContext(contextType, organizationId)

        // Force navigation after context switch
        if (contextType === 'individual') {
          router.push('/individual')
        } else if (contextType === 'organization' && organizationId) {
          // Get organization slug from employment relationships
          const employment = availableEmployments?.find(emp => emp.organization_id === organizationId)
          const orgSlug = employment?.organization_slug || 'default'
          router.push(`/org/${orgSlug}`)
        }
      } else {
        console.error('switchContext function not available')
      }
    } catch (error) {
      console.error('Context switch failed:', error)
    }
  }

  const handleSignOut = async () => {
    try {
      if (signOut) {
        await signOut()
      } else {
        console.error('signOut function not available')
      }
    } catch (error) {
      console.error('Sign out failed:', error)
    }
  }

  // Check user permissions for context switching with fallbacks
  const canSwitchToIndividual = user?.role === 'individual' || user?.isPlatformAdmin || loading
  const hasOrganizations = availableEmployments && availableEmployments.length > 0

  // Fallback user data when loading
  const displayUser = user || { full_name: 'Loading...', email: '<EMAIL>' }

  const toggleSidebarMode = () => {
    if (sidebarMode === "persistent") {
      setSidebarMode("hover")
      setOpen(false)
    } else {
      setSidebarMode("persistent")
      setOpen(true)
    }
  }

  return (
    <Sidebar
      className={`border-r border-gray-200 ${className}`}
      style={{ backgroundColor: '#ffffff !important' }}
      collapsible="icon"
      variant="sidebar"
      onMouseEnter={() => sidebarMode === "hover" && setOpen(true)}
      onMouseLeave={() => sidebarMode === "hover" && setOpen(false)}
    >
      <SidebarHeader className="py-3 group-data-[collapsible=icon]:px-0 px-4">
        <div className="flex items-center justify-start group-data-[collapsible=icon]:justify-center w-full">
          <Link href="/" className="flex items-center gap-2">
            <img
              src="/Luna_Logo_Final.png"
              alt="Luna"
              className="h-8 w-auto flex-shrink-0 group-data-[collapsible=icon]:hidden"
            />
            <img
              src="/Luna_Favicon_Caps.jpg"
              alt="Luna"
              className="w-8 h-8 rounded flex-shrink-0 hidden group-data-[collapsible=icon]:block"
            />
          </Link>
        </div>

        {/* Context Switcher */}
        {(canSwitchToIndividual || hasOrganizations) && (
          <div className="mt-3">
            <div className="border-t mb-3 -mx-4" style={{ borderColor: '#e2e8f0' }}></div>
            <DropdownMenu open={contextSwitcherOpen} onOpenChange={setContextSwitcherOpen}>
            <DropdownMenuTrigger asChild>
              <button className="w-full flex items-center gap-2 px-2 py-1.5 text-left hover:bg-gray-50 rounded border border-gray-200 bg-white transition-colors group-data-[collapsible=icon]:w-8 group-data-[collapsible=icon]:h-8 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:mx-auto group-data-[collapsible=icon]:border-0 group-data-[collapsible=icon]:bg-transparent">
                <div className="w-6 h-6 bg-gray-600 rounded flex items-center justify-center flex-shrink-0 text-white font-medium text-xs group-data-[collapsible=icon]:w-8 group-data-[collapsible=icon]:h-8">
                  {context?.type === 'organization'
                    ? <Building2 className="w-3 h-3" />
                    : getUserInitials(displayUser.full_name || displayUser.email || 'U')}
                </div>
                <div className="flex-1 min-w-0 group-data-[collapsible=icon]:hidden">
                  <div className="text-sm font-medium truncate" style={{ color: '#051237c2' }}>
                    {context?.type === 'organization'
                      ? availableEmployments?.find(e => e.organization_id === context.organization_id)?.organization_name || 'Organization'
                      : displayUser.full_name || displayUser.email}
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {context?.type === 'organization' ? 'Organization Mode' : 'Individual Mode'}
                  </div>
                </div>
                <ChevronDown className="w-3 h-3 text-gray-400 flex-shrink-0 group-data-[collapsible=icon]:hidden" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-[var(--radix-dropdown-menu-trigger-width)] p-0 border border-gray-200 shadow-lg rounded">
              <div className="p-1">
                {/* Individual Mode Option */}
                {canSwitchToIndividual && (
                  <DropdownMenuItem
                    className="p-2 rounded hover:bg-gray-50 focus:bg-gray-50 cursor-pointer"
                    onClick={() => handleContextSwitch('individual')}
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center text-white">
                        <User className="w-3 h-3" />
                      </div>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium" style={{ color: '#051237c2' }}>
                          Individual Mode
                        </span>
                        <span className="text-xs text-gray-500">Personal learning and profile</span>
                      </div>
                      {context?.type === 'individual' && (
                        <div className="ml-auto w-2 h-2 bg-blue-600 rounded-full"></div>
                      )}
                    </div>
                  </DropdownMenuItem>
                )}

                {/* Organization Contexts */}
                {hasOrganizations && (
                  <>
                    <div className="px-2 py-1 border-t border-gray-100 mt-1">
                      <div className="text-xs font-medium text-gray-400 mb-2">Organizations</div>
                      {availableEmployments.map((employment) => (
                        <DropdownMenuItem
                          key={employment.organization_id}
                          className="p-2 rounded hover:bg-gray-50 focus:bg-gray-50 mb-1 cursor-pointer"
                          onClick={() => handleContextSwitch('organization', employment.organization_id)}
                        >
                          <div className="flex items-center gap-2">
                            <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center text-white">
                              <Building2 className="w-3 h-3" />
                            </div>
                            <div className="flex flex-col">
                              <span className="text-sm font-medium" style={{ color: '#051237c2' }}>
                                {employment.organization_name}
                              </span>
                              <span className="text-xs text-gray-500 capitalize">
                                {employment.employment_role} access
                              </span>
                            </div>
                            {context?.type === 'organization' && context.organization_id === employment.organization_id && (
                              <div className="ml-auto w-2 h-2 bg-blue-600 rounded-full"></div>
                            )}
                          </div>
                        </DropdownMenuItem>
                      ))}

                      <div className="mt-2 px-0">
                        <button className="text-xs text-gray-400 hover:text-gray-600 transition-colors">
                          Create new company <span className="text-blue-600 underline hover:text-blue-700">Click here</span>
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
          </div>
        )}
      </SidebarHeader>

      <SidebarContent className="py-2 group-data-[collapsible=icon]:px-0 px-4">
        {(navLoading || !navGroups.length ? fallbackNavGroups : navGroups).map((group, groupIndex) => (
          <div key={group.label}>
            {groupIndex > 0 && (
              <div className="border-t mx-4 my-2 hidden group-data-[collapsible=icon]:block" style={{ borderColor: '#f0f0f0' }}></div>
            )}
            <SidebarGroup>
              <SidebarGroupLabel className="text-xs font-medium mb-1 px-0 group-data-[collapsible=icon]:hidden flex items-center gap-2" style={{ color: '#05123752' }}>
                <span>{group.label}</span>
                <div className="flex-1 border-t" style={{ borderColor: '#f0f0f0' }}></div>
              </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-0">
                {group.items.map((item) => {
                  const isActive = pathname === item.url ||
                    (item.url !== '/' && pathname.startsWith(item.url))

                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        className={`w-full justify-start gap-3 px-2 py-1.5 text-gray-600 hover:bg-gray-50 rounded transition-colors group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:w-8 group-data-[collapsible=icon]:h-8 group-data-[collapsible=icon]:p-0 group-data-[collapsible=icon]:mx-auto group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:items-center ${
                          isActive ? '' : ''
                        }`}
                        tooltip={state === "collapsed" ? item.title : undefined}
                      >
                        <Link
                          href={item.url}
                          className="flex items-center gap-3 w-full group-data-[collapsible=icon]:justify-center hover:text-[#3076ff]"
                        >
                          <item.icon
                            className="w-10 h-10 flex-shrink-0 hover:text-[#3076ff] transition-colors"
                            style={{ color: isActive ? '#3076ff' : '#051237c2' }}
                          />
                          <span
                            className="text-sm font-normal group-data-[collapsible=icon]:hidden hover:text-[#3076ff] transition-colors"
                            style={{
                              fontSize: '14px',
                              fontWeight: 400,
                              color: isActive ? '#3076ff' : '#051237c2'
                            }}
                          >
                            {item.title}
                          </span>
                          {item.badge !== undefined && item.badge > 0 && (
                            <Badge
                              variant="secondary"
                              className="ml-auto text-xs group-data-[collapsible=icon]:hidden"
                            >
                              {item.badge}
                            </Badge>
                          )}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )
                })}
              </SidebarMenu>
            </SidebarGroupContent>
            </SidebarGroup>
          </div>
        ))}
      </SidebarContent>

      <SidebarFooter className="px-4 py-4">
        {/* Keep footer minimal to match design */}
      </SidebarFooter>
    </Sidebar>
  )
}
