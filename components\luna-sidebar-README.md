# 🌙 Luna Sidebar - Enhanced Navigation Component

## Overview

The new Luna Sidebar is a unified, context-aware navigation component that addresses the critical issues identified in the UI audit. It consolidates multiple sidebar implementations into a single, maintainable solution.

## ✨ Key Features

### 🔄 Context-Aware Navigation
- **Individual Mode**: Personal learning, career development, and profile management
- **Organization Mode**: Team management, organizational training, and analytics
- **Admin Mode**: Platform administration, user management, and system settings

### 🎨 Design System Integration
- Uses Luna's design tokens and CSS custom properties
- Consistent with existing UI components
- Proper dark/light theme support
- Accessible color contrast ratios

### 📱 Mobile-First Design
- Responsive sidebar behavior
- Touch-friendly interaction targets (44px minimum)
- Proper mobile overlay patterns
- Optimized for small screens

### ♿ Enhanced Accessibility
- Proper ARIA labels and descriptions
- Keyboard navigation support
- Screen reader announcements
- Focus management

## 🚀 Quick Start

### 1. Basic Usage

```tsx
import { LunaLayout } from "@/components/luna-layout"

export default function MyPage() {
  return (
    <LunaLayout>
      <div className="p-6">
        <h1>My Page Content</h1>
        {/* Your page content here */}
      </div>
    </LunaLayout>
  )
}
```

### 2. Using the HOC Pattern

```tsx
import { withLunaLayout } from "@/components/luna-layout"

function MyPageComponent() {
  return (
    <div className="p-6">
      <h1>My Page Content</h1>
      {/* Your page content here */}
    </div>
  )
}

export default withLunaLayout(MyPageComponent)
```

### 3. Direct Sidebar Usage

```tsx
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { LunaSidebar } from "@/components/luna-sidebar"

export default function CustomLayout({ children }) {
  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <LunaSidebar />
        <SidebarInset className="flex-1">
          {children}
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}
```

## 🔧 Configuration

### Navigation Items

The sidebar automatically adapts its navigation based on the current context:

- **Individual Context**: Learning, Career, Support sections
- **Organization Context**: Overview, Team Management, L&D sections  
- **Admin Context**: Platform Management, User Management, Content Management sections

### Customizing Navigation

To modify navigation items, edit the respective arrays in `luna-sidebar.tsx`:

```tsx
const individualNavGroups: NavGroup[] = [
  {
    label: "Learning",
    items: [
      {
        title: "Dashboard",
        url: "/individual",
        icon: Home,
      },
      // Add more items...
    ],
  },
  // Add more groups...
]
```

### Adding Badges

Add notification badges to navigation items:

```tsx
{
  title: "Assessments",
  url: "/individual/assessments",
  icon: ClipboardCheck,
  badge: 3, // Shows a badge with count
}
```

## 🎯 Migration Guide

### From Existing Sidebar Components

1. **Remove old sidebar imports**:
   ```tsx
   // Remove these
   import { AppSidebar } from "@/components/app-sidebar"
   import { AdminDashboardLayout } from "@/components/admin-dashboard-layout"
   import { IndividualSidebarLayout } from "@/components/layout/individual-sidebar-layout"
   ```

2. **Replace with LunaLayout**:
   ```tsx
   // Add this
   import { LunaLayout } from "@/components/luna-layout"
   ```

3. **Update page structure**:
   ```tsx
   // Before
   export default function MyPage() {
     return (
       <IndividualSidebarLayout>
         <div>Content</div>
       </IndividualSidebarLayout>
     )
   }

   // After
   export default function MyPage() {
     return (
       <LunaLayout>
         <div>Content</div>
       </LunaLayout>
     )
   }
   ```

### Route Updates

Ensure your routes match the expected patterns:

- Individual: `/individual/*`
- Organization: `/organization/*` 
- Admin: `/admin/*`

## 🎨 Theming

The sidebar uses Luna's design token system:

```css
/* Sidebar-specific tokens */
--sidebar-background
--sidebar-foreground  
--sidebar-border
--sidebar-accent
--sidebar-primary
```

### Custom Styling

Override styles using CSS custom properties:

```tsx
<LunaSidebar 
  className="custom-sidebar"
  style={{
    '--sidebar-width': '280px',
    '--sidebar-width-icon': '64px'
  }}
/>
```

## 🔍 Troubleshooting

### Common Issues

1. **Context not switching**: Ensure `useLunaAuth` and `useCurrentContext` hooks are properly configured
2. **Navigation not updating**: Check that routes match the expected patterns
3. **Styling issues**: Verify design tokens are loaded and CSS custom properties are available

### Debug Mode

Enable debug logging:

```tsx
// Add to your page component
useEffect(() => {
  console.log('Current context:', context)
  console.log('Available employments:', availableEmployments)
}, [context, availableEmployments])
```

## 📊 Performance

### Bundle Impact
- **Reduced bundle size**: Eliminates duplicate sidebar implementations
- **Tree shaking**: Only loads required navigation items
- **Lazy loading**: Context-specific navigation loaded on demand

### Optimization Tips
- Use the HOC pattern for better component reuse
- Implement proper loading states for context switching
- Cache navigation state where appropriate

## 🧪 Testing

### Unit Tests
```bash
npm test -- luna-sidebar
```

### Integration Tests
```bash
npm test -- luna-layout
```

### Accessibility Tests
```bash
npm run test:a11y
```

## 📝 Contributing

When adding new navigation items:

1. Add to the appropriate navigation group
2. Include proper TypeScript types
3. Add accessibility attributes
4. Test across all contexts
5. Update this documentation

## 🔗 Related Components

- `@/components/ui/sidebar` - Base sidebar primitives
- `@/hooks/use-luna-auth` - Authentication and user context
- `@/components/luna/context-switcher` - Context switching logic
- `@/components/ui/breadcrumb` - Navigation breadcrumbs
