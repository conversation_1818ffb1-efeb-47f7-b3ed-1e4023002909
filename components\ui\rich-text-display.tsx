'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import { useState, useEffect } from 'react';
import { Extension } from '@tiptap/core';

// Extend the global Commands interface to include our custom commands
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    indent: {
      indent: () => ReturnType;
      outdent: () => ReturnType;
    }
  }
}

// Custom extension for indentation - Same as in rich-text-editor.tsx
const IndentExtension = Extension.create({
  name: 'indent',
  addGlobalAttributes() {
    return [
      {
        types: ['paragraph', 'heading', 'blockquote'],
        attributes: {
          indent: {
            default: 0,
            renderHTML: attributes => {
              if (!attributes.indent) {
                return {};
              }
              return {
                style: `margin-left: ${attributes.indent * 2}em`,
              };
            },
            parseHTML: element => {
              const style = element.getAttribute('style') || '';
              const marginLeft = style.match(/margin-left: (\d+)em/);
              if (marginLeft) {
                return parseInt(marginLeft[1], 10) / 2;
              }
              return 0;
            },
          },
        },
      },
      {
        // Handle lists properly to keep bullets/numbers with text
        types: ['bulletList', 'orderedList'],
        attributes: {
          indent: {
            default: 0,
            renderHTML: attributes => {
              if (!attributes.indent) {
                return {};
              }
              return {
                class: `indent-${attributes.indent}`,
                style: `margin-left: ${attributes.indent * 2}em`,
              };
            },
            parseHTML: element => {
              // Check for our custom class first
              const className = element.getAttribute('class') || '';
              const indentClass = className.match(/indent-(\d+)/);
              if (indentClass) {
                return parseInt(indentClass[1], 10);
              }
              
              // Fall back to style-based parsing
              const style = element.getAttribute('style') || '';
              const marginLeft = style.match(/margin-left: (\d+)em/);
              if (marginLeft) {
                return parseInt(marginLeft[1], 10) / 2;
              }
              return 0;
            },
          },
        },
      },
    ];
  },
});

interface RichTextDisplayProps {
  content: string;
  className?: string;
}

export function RichTextDisplay({ content, className = '' }: RichTextDisplayProps) {
  const [isMounted, setIsMounted] = useState(false);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        bulletList: {
          HTMLAttributes: {
            class: 'list-disc pl-6 my-2',
          },
        },
        orderedList: {
          HTMLAttributes: {
            class: 'list-decimal pl-6 my-2',
          },
        },
        listItem: {
          HTMLAttributes: {
            class: 'pl-1 my-1',
          },
        },
        code: {
          HTMLAttributes: {
            class: 'bg-muted px-1.5 py-0.5 rounded font-mono text-sm',
          },
        },
      }),
      Link.configure({
        openOnClick: true,
        HTMLAttributes: {
          class: 'text-primary underline underline-offset-4 hover:text-primary/80 transition-colors',
          target: '_blank',
          rel: 'noopener noreferrer',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-md max-w-full my-4',
        },
      }),
      IndentExtension,
    ],
    content,
    editable: false,
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose dark:prose-invert prose-ul:list-disc prose-ol:list-decimal max-w-none focus:outline-none',
      },
    },
  });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content, false);
    }
  }, [editor, content]);

  if (!isMounted) {
    return null;
  }

  return (
    <div className={className}>
      <EditorContent editor={editor} />
    </div>
  );
} 