import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    const { id } = await params;
    
    // Create admin client
    const adminClient = createAdminClient();
    
    // Get user's employment relationships (Luna schema)
    const { data: employments, error: employmentsError } = await adminClient
      .from('employment_relationships')
      .select(`
        id,
        organization_id,
        department_id,
        role,
        job_title,
        status,
        start_date,
        created_at,
        updated_at,
        organizations!inner(
          id,
          name
        ),
        departments!inner(
          id,
          name
        )
      `)
      .eq('user_id', id)
      .order('created_at', { ascending: false });

    if (employmentsError) {
      console.error('Error fetching user employment relationships:', employmentsError);
      return NextResponse.json(
        { error: employmentsError.message || 'Failed to fetch user employment relationships' },
        { status: 500 }
      );
    }

    // Format the response
    const formattedEmployments = (employments || []).map((employment: any) => ({
      id: employment.id,
      organization_id: employment.organization_id,
      organization_name: employment.organizations.name,
      department_id: employment.department_id,
      department_name: employment.departments.name,
      role: employment.role,
      job_title: employment.job_title,
      status: employment.status,
      start_date: employment.start_date,
      created_at: employment.created_at,
      updated_at: employment.updated_at,
    }));
    
    return NextResponse.json(formattedEmployments);
  } catch (error: any) {
    console.error('User memberships GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    const { id } = await params;
    
    // Parse request body
    const body = await req.json();
    const { organization_id, department_id, role, job_title } = body;

    // Validate required fields
    if (!organization_id || !department_id || !role) {
      return NextResponse.json(
        { error: 'Organization ID, department ID, and role are required' },
        { status: 400 }
      );
    }

    // Validate role
    const validRoles = ['organization_admin', 'department_admin', 'staff_member'];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role specified. Must be organization_admin, department_admin, or staff_member' },
        { status: 400 }
      );
    }
    
    // Create admin client
    const adminClient = createAdminClient();

    // Check if employment relationship already exists
    const { data: existingEmployment, error: checkError } = await adminClient
      .from('employment_relationships')
      .select('id')
      .eq('user_id', id)
      .eq('organization_id', organization_id)
      .eq('department_id', department_id)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking existing employment:', checkError);
      return NextResponse.json(
        { error: 'Failed to check existing employment relationship' },
        { status: 500 }
      );
    }

    if (existingEmployment) {
      return NextResponse.json(
        { error: 'User already has an employment relationship in this department' },
        { status: 400 }
      );
    }

    // Create the employment relationship
    const { data: employment, error: employmentError } = await adminClient
      .from('employment_relationships')
      .insert({
        user_id: id,
        organization_id,
        department_id,
        role,
        job_title: job_title || 'Employee',
        status: 'active',
        start_date: new Date().toISOString(),
        created_by: authResult.user.id,
      })
      .select()
      .single();

    if (employmentError) {
      console.error('Error creating employment relationship:', employmentError);
      return NextResponse.json(
        { error: employmentError.message || 'Failed to create employment relationship' },
        { status: 500 }
      );
    }

    return NextResponse.json(employment);
  } catch (error: any) {
    console.error('User memberships POST API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
