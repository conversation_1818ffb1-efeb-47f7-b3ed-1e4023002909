/**
 * Layout Components
 * Standardized layout patterns and containers for consistent UI structure
 */

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { ArrowLeft, MoreHorizontal, Plus, Filter, Search } from "lucide-react"

// TypeScript interfaces
export interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  backButton?: {
    href?: string
    onClick?: () => void
    label?: string
  }
  actions?: React.ReactNode
  className?: string
}

export interface SectionHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  actions?: React.ReactNode
  className?: string
  variant?: 'default' | 'compact'
}

export interface GridLayoutProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4 | 5 | 6
  gap?: 'sm' | 'md' | 'lg'
  className?: string
  responsive?: boolean
}

export interface ListLayoutProps {
  children: React.ReactNode
  gap?: 'sm' | 'md' | 'lg'
  className?: string
  dividers?: boolean
}

export interface CardGridProps {
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  gap?: 'sm' | 'md' | 'lg'
  className?: string
  minCardWidth?: string
}

export interface EmptyStateProps {
  title: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  action?: {
    label: string
    onClick?: () => void
    href?: string
  }
  className?: string
}

export interface LoadingStateProps {
  title?: string
  description?: string
  className?: string
  variant?: 'spinner' | 'skeleton' | 'pulse'
}

/**
 * Page Header Component
 */
export const PageHeader = React.forwardRef<
  HTMLDivElement,
  PageHeaderProps
>(({
  title,
  description,
  children,
  backButton,
  actions,
  className,
  ...props
}, ref) => {
  return (
    <div ref={ref} className={cn("space-y-4", className)} {...props}>
      <div className="flex items-start justify-between gap-4">
        <div className="flex items-start gap-4 min-w-0 flex-1">
          {backButton && (
            <Button
              variant="ghost"
              size="sm"
              className="mt-1 flex-shrink-0"
              onClick={backButton.onClick}
              asChild={!!backButton.href}
            >
              {backButton.href ? (
                <a href={backButton.href}>
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  {backButton.label || 'Back'}
                </a>
              ) : (
                <>
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  {backButton.label || 'Back'}
                </>
              )}
            </Button>
          )}
          <div className="min-w-0 flex-1">
            <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
            {description && (
              <p className="text-muted-foreground mt-1">{description}</p>
            )}
          </div>
        </div>
        {actions && (
          <div className="flex items-center gap-2 flex-shrink-0">
            {actions}
          </div>
        )}
      </div>
      {children}
    </div>
  )
})

PageHeader.displayName = "PageHeader"

/**
 * Section Header Component
 */
export const SectionHeader = React.forwardRef<
  HTMLDivElement,
  SectionHeaderProps
>(({
  title,
  description,
  children,
  actions,
  className,
  variant = 'default',
  ...props
}, ref) => {
  return (
    <div ref={ref} className={cn("space-y-2", className)} {...props}>
      <div className="flex items-start justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className={cn(
            "font-semibold tracking-tight",
            variant === 'compact' ? "text-lg" : "text-xl"
          )}>
            {title}
          </h2>
          {description && (
            <p className={cn(
              "text-muted-foreground",
              variant === 'compact' ? "text-sm mt-0.5" : "text-base mt-1"
            )}>
              {description}
            </p>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-2 flex-shrink-0">
            {actions}
          </div>
        )}
      </div>
      {children}
    </div>
  )
})

SectionHeader.displayName = "SectionHeader"

/**
 * Grid Layout Component
 */
export const GridLayout = React.forwardRef<
  HTMLDivElement,
  GridLayoutProps
>(({
  children,
  columns = 3,
  gap = 'md',
  className,
  responsive = true,
  ...props
}, ref) => {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-6',
    lg: 'gap-8'
  }
  
  const columnClasses = responsive ? {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6'
  } : {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6'
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "grid",
        columnClasses[columns],
        gapClasses[gap],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
})

GridLayout.displayName = "GridLayout"

/**
 * List Layout Component
 */
export const ListLayout = React.forwardRef<
  HTMLDivElement,
  ListLayoutProps
>(({
  children,
  gap = 'md',
  className,
  dividers = false,
  ...props
}, ref) => {
  const gapClasses = {
    sm: 'space-y-2',
    md: 'space-y-4',
    lg: 'space-y-6'
  }
  
  if (dividers) {
    const childrenArray = React.Children.toArray(children)
    return (
      <div ref={ref} className={cn("space-y-0", className)} {...props}>
        {childrenArray.map((child, index) => (
          <React.Fragment key={index}>
            {child}
            {index < childrenArray.length - 1 && (
              <Separator className="my-4" />
            )}
          </React.Fragment>
        ))}
      </div>
    )
  }
  
  return (
    <div
      ref={ref}
      className={cn(gapClasses[gap], className)}
      {...props}
    >
      {children}
    </div>
  )
})

ListLayout.displayName = "ListLayout"

/**
 * Card Grid Component
 */
export const CardGrid = React.forwardRef<
  HTMLDivElement,
  CardGridProps
>(({
  children,
  columns = 3,
  gap = 'md',
  className,
  minCardWidth = "320px",
  ...props
}, ref) => {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-6',
    lg: 'gap-8'
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "grid auto-fit-grid",
        gapClasses[gap],
        className
      )}
      style={{
        gridTemplateColumns: `repeat(auto-fit, minmax(${minCardWidth}, 1fr))`
      }}
      {...props}
    >
      {children}
    </div>
  )
})

CardGrid.displayName = "CardGrid"

/**
 * Empty State Component
 */
export const EmptyState = React.forwardRef<
  HTMLDivElement,
  EmptyStateProps
>(({
  title,
  description,
  icon: Icon,
  action,
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-col items-center justify-center text-center py-12 px-4",
        className
      )}
      {...props}
    >
      {Icon && (
        <div className="mb-4 p-3 rounded-full bg-muted">
          <Icon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      {description && (
        <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
      )}
      {action && (
        <Button
          onClick={action.onClick}
          asChild={!!action.href}
        >
          {action.href ? (
            <a href={action.href}>{action.label}</a>
          ) : (
            action.label
          )}
        </Button>
      )}
    </div>
  )
})

EmptyState.displayName = "EmptyState"

/**
 * Loading State Component
 */
export const LoadingState = React.forwardRef<
  HTMLDivElement,
  LoadingStateProps
>(({
  title = "Loading...",
  description,
  className,
  variant = 'spinner',
  ...props
}, ref) => {
  if (variant === 'skeleton') {
    return (
      <div ref={ref} className={cn("space-y-4", className)} {...props}>
        <div className="space-y-2">
          <div className="h-4 bg-muted rounded animate-pulse" />
          <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="h-32 bg-muted rounded animate-pulse" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-2/3" />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }
  
  if (variant === 'pulse') {
    return (
      <div
        ref={ref}
        className={cn(
          "flex flex-col items-center justify-center py-12 px-4 animate-pulse",
          className
        )}
        {...props}
      >
        <div className="w-12 h-12 bg-muted rounded-full mb-4" />
        <div className="h-6 bg-muted rounded w-32 mb-2" />
        {description && (
          <div className="h-4 bg-muted rounded w-48" />
        )}
      </div>
    )
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-col items-center justify-center py-12 px-4",
        className
      )}
      {...props}
    >
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4" />
      <h3 className="text-lg font-semibold mb-2">{title}</h3>
      {description && (
        <p className="text-muted-foreground text-center max-w-md">{description}</p>
      )}
    </div>
  )
})

LoadingState.displayName = "LoadingState"

/**
 * Content Container Component
 */
export const ContentContainer = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
    padding?: 'none' | 'sm' | 'md' | 'lg'
  }
>(({
  children,
  maxWidth = 'full',
  padding = 'md',
  className,
  ...props
}, ref) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    '2xl': 'max-w-7xl',
    full: 'max-w-full'
  }
  
  const paddingClasses = {
    none: '',
    sm: 'px-4 py-4',
    md: 'px-6 py-6',
    lg: 'px-8 py-8'
  }
  
  return (
    <div
      ref={ref}
      className={cn(
        "mx-auto w-full",
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
})

ContentContainer.displayName = "ContentContainer"
