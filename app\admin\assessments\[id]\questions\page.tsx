"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { 
  Edit, Plus, Search, AlertCircle, Trash2, 
  FileText, ArrowLeft, Check, X, FileQuestion
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from '@/components/ui/skeleton';
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { getAssessmentById } from '@/lib/api/assessments';
import { adminGetAssessmentQuestions, adminCreateQuestion, adminUpdateQuestion, adminDeleteQuestion } from '@/app/actions/assessment-actions';
import { Assessment, AssessmentQuestion, QuestionType, AssessmentQuestionInsert } from '@/types/assessment';

export default function AssessmentQuestionsPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap the params Promise
  const resolvedParams = React.use(params);
  const assessmentId = resolvedParams.id;
  
  const router = useRouter();
  
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [questions, setQuestions] = useState<AssessmentQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState<AssessmentQuestion | null>(null);
  
  // New question state
  const [newQuestion, setNewQuestion] = useState<AssessmentQuestionInsert>({
    assessment_id: assessmentId,
    question_text: '',
    question_type: 'multiple_choice',
    options: [],
    correct_answer: null,
    points: 10,
    time_limit: null,
  });

  // For multiple choice options
  const [newOption, setNewOption] = useState<string>('');
  const [editingOptionIndex, setEditingOptionIndex] = useState<number | null>(null);

  useEffect(() => {
    async function loadData() {
      try {
        // Load assessment details
        const assessmentData = await getAssessmentById(assessmentId);
        if (!assessmentData) {
          throw new Error('Assessment not found');
        }
        setAssessment(assessmentData);
        
        // Load questions
        const questionsData = await adminGetAssessmentQuestions(assessmentId);
        setQuestions(questionsData);
      } catch (err) {
        console.error('Error loading assessment questions:', err);
        setError('Failed to load assessment questions. Please try again later.');
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [assessmentId]);

  // Handle search and filtering
  const filteredQuestions = questions.filter(question => 
    question.question_text.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Question type specific rendering
  const renderQuestionTypeFields = (questionType: QuestionType, isEdit = false, questionData?: AssessmentQuestion) => {
    const data = isEdit && questionData ? questionData : newQuestion;
    
    switch (questionType) {
      case 'multiple_choice':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="options">Options</Label>
              <div className="mt-2 space-y-2">
                {Array.isArray(data.options) && data.options.map((option, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input 
                      type="text" 
                      value={editingOptionIndex === index ? newOption : option as string} 
                      onChange={(e) => {
                        if (editingOptionIndex === index) {
                          setNewOption(e.target.value);
                        } else {
                          const updatedOptions = [...(data.options as string[] || [])];
                          updatedOptions[index] = e.target.value;
                          if (isEdit && questionData) {
                            setCurrentQuestion({
                              ...questionData,
                              options: updatedOptions
                            });
                          } else {
                            setNewQuestion({
                              ...newQuestion,
                              options: updatedOptions
                            });
                          }
                        }
                      }}
                      onFocus={() => {
                        if (editingOptionIndex !== index) {
                          setEditingOptionIndex(index);
                          setNewOption(option as string);
                        }
                      }}
                      onBlur={() => {
                        if (editingOptionIndex === index) {
                          const updatedOptions = [...(data.options as string[] || [])];
                          updatedOptions[index] = newOption;
                          if (isEdit && questionData) {
                            setCurrentQuestion({
                              ...questionData,
                              options: updatedOptions
                            });
                          } else {
                            setNewQuestion({
                              ...newQuestion,
                              options: updatedOptions
                            });
                          }
                          setEditingOptionIndex(null);
                        }
                      }}
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => {
                        const updatedOptions = [...(data.options as string[] || [])];
                        updatedOptions.splice(index, 1);
                        if (isEdit && questionData) {
                          setCurrentQuestion({
                            ...questionData,
                            options: updatedOptions,
                            // Reset correct answer if it was this option
                            correct_answer: data.correct_answer === option ? null : data.correct_answer
                          });
                        } else {
                          setNewQuestion({
                            ...newQuestion,
                            options: updatedOptions,
                            // Reset correct answer if it was this option
                            correct_answer: newQuestion.correct_answer === option ? null : newQuestion.correct_answer
                          });
                        }
                      }}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant={data.correct_answer === option ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (isEdit && questionData) {
                          setCurrentQuestion({
                            ...questionData,
                            correct_answer: option as string
                          });
                        } else {
                          setNewQuestion({
                            ...newQuestion,
                            correct_answer: option as string
                          });
                        }
                      }}
                    >
                      {data.correct_answer === option ? (
                        <Check className="h-4 w-4 mr-1" />
                      ) : null}
                      Correct
                    </Button>
                  </div>
                ))}
              </div>
              
              <div className="flex items-center gap-2 mt-2">
                <Input 
                  type="text" 
                  placeholder="Add new option" 
                  value={newOption}
                  onChange={(e) => setNewOption(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && newOption.trim()) {
                      const updatedOptions = [...(data.options as string[] || []), newOption];
                      if (isEdit && questionData) {
                        setCurrentQuestion({
                          ...questionData,
                          options: updatedOptions
                        });
                      } else {
                        setNewQuestion({
                          ...newQuestion,
                          options: updatedOptions
                        });
                      }
                      setNewOption('');
                      e.preventDefault();
                    }
                  }}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    if (newOption.trim()) {
                      const updatedOptions = [...(data.options as string[] || []), newOption];
                      if (isEdit && questionData) {
                        setCurrentQuestion({
                          ...questionData,
                          options: updatedOptions
                        });
                      } else {
                        setNewQuestion({
                          ...newQuestion,
                          options: updatedOptions
                        });
                      }
                      setNewOption('');
                    }
                  }}
                >
                  Add
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="points">Points</Label>
              <Input
                id="points"
                type="number"
                value={data.points}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0;
                  if (isEdit && questionData) {
                    setCurrentQuestion({
                      ...questionData,
                      points: value
                    });
                  } else {
                    setNewQuestion({
                      ...newQuestion,
                      points: value
                    });
                  }
                }}
              />
            </div>
          </div>
        );
        
      case 'text_input':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="time_limit">Time Limit (seconds)</Label>
              <Input
                id="time_limit"
                type="number"
                placeholder="e.g., 600 (10 minutes)"
                value={data.time_limit || ''}
                onChange={(e) => {
                  const value = e.target.value ? parseInt(e.target.value) : null;
                  if (isEdit && questionData) {
                    setCurrentQuestion({
                      ...questionData,
                      time_limit: value
                    });
                  } else {
                    setNewQuestion({
                      ...newQuestion,
                      time_limit: value
                    });
                  }
                }}
              />
              <p className="text-sm text-muted-foreground mt-1">
                Leave empty for no time limit
              </p>
            </div>
            
            <div>
              <Label htmlFor="points">Points</Label>
              <Input
                id="points"
                type="number"
                value={data.points}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0;
                  if (isEdit && questionData) {
                    setCurrentQuestion({
                      ...questionData,
                      points: value
                    });
                  } else {
                    setNewQuestion({
                      ...newQuestion,
                      points: value
                    });
                  }
                }}
              />
            </div>
          </div>
        );
        
      case 'typing_test':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="correct_answer">Target Text to Type</Label>
              <Textarea
                id="correct_answer"
                placeholder="Enter the text that the user needs to type"
                value={data.correct_answer || ''}
                onChange={(e) => {
                  if (isEdit && questionData) {
                    setCurrentQuestion({
                      ...questionData,
                      correct_answer: e.target.value
                    });
                  } else {
                    setNewQuestion({
                      ...newQuestion,
                      correct_answer: e.target.value
                    });
                  }
                }}
                className="min-h-[150px]"
              />
            </div>
            
            <div>
              <Label htmlFor="time_limit">Time Limit (seconds)</Label>
              <Input
                id="time_limit"
                type="number"
                placeholder="e.g., 300 (5 minutes)"
                value={data.time_limit || ''}
                onChange={(e) => {
                  const value = e.target.value ? parseInt(e.target.value) : null;
                  if (isEdit && questionData) {
                    setCurrentQuestion({
                      ...questionData,
                      time_limit: value
                    });
                  } else {
                    setNewQuestion({
                      ...newQuestion,
                      time_limit: value
                    });
                  }
                }}
              />
            </div>
            
            <div>
              <Label htmlFor="points">Points</Label>
              <Input
                id="points"
                type="number"
                value={data.points}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0;
                  if (isEdit && questionData) {
                    setCurrentQuestion({
                      ...questionData,
                      points: value
                    });
                  } else {
                    setNewQuestion({
                      ...newQuestion,
                      points: value
                    });
                  }
                }}
              />
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };

  // Form submission handlers
  const handleCreateQuestion = async () => {
    setIsSubmitting(true);
    try {
      // Validate form
      if (!newQuestion.question_text) {
        throw new Error('Question text is required');
      }
      
      if (newQuestion.question_type === 'multiple_choice') {
        if (!Array.isArray(newQuestion.options) || newQuestion.options.length < 2) {
          throw new Error('Multiple choice questions must have at least 2 options');
        }
        if (!newQuestion.correct_answer) {
          throw new Error('Please select a correct answer');
        }
      }
      
      if (newQuestion.question_type === 'typing_test' && !newQuestion.correct_answer) {
        throw new Error('Target text is required for typing tests');
      }

      const created = await adminCreateQuestion(newQuestion);
      setQuestions(prev => [...prev, created]);
      
      // Reset form
      setNewQuestion({
        assessment_id: assessmentId,
        question_text: '',
        question_type: 'multiple_choice',
        options: [],
        correct_answer: null,
        points: 10,
        time_limit: null,
      });
      setNewOption('');
      
      setCreateDialogOpen(false);
    } catch (err: any) {
      console.error('Failed to create question:', err);
      setError(err.message || 'Failed to create question');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateQuestion = async () => {
    if (!currentQuestion) return;
    
    setIsSubmitting(true);
    try {
      // Validate form
      if (!currentQuestion.question_text) {
        throw new Error('Question text is required');
      }
      
      if (currentQuestion.question_type === 'multiple_choice') {
        if (!Array.isArray(currentQuestion.options) || currentQuestion.options.length < 2) {
          throw new Error('Multiple choice questions must have at least 2 options');
        }
        if (!currentQuestion.correct_answer) {
          throw new Error('Please select a correct answer');
        }
      }
      
      if (currentQuestion.question_type === 'typing_test' && !currentQuestion.correct_answer) {
        throw new Error('Target text is required for typing tests');
      }

      const updated = await adminUpdateQuestion(currentQuestion.id, currentQuestion);
      setQuestions(prev => 
        prev.map(question => 
          question.id === updated.id ? updated : question
        )
      );
      
      setEditDialogOpen(false);
    } catch (err: any) {
      console.error('Failed to update question:', err);
      setError(err.message || 'Failed to update question');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteQuestion = async () => {
    if (!currentQuestion) return;
    
    setIsSubmitting(true);
    try {
      await adminDeleteQuestion(currentQuestion.id, assessmentId);
      setQuestions(prev => 
        prev.filter(question => question.id !== currentQuestion.id)
      );
      
      setDeleteDialogOpen(false);
    } catch (err: any) {
      console.error('Failed to delete question:', err);
      setError(err.message || 'Failed to delete question');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render question card
  const renderQuestionCard = (question: AssessmentQuestion) => {
    return (
      <Card key={question.id} className="mb-4">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-start">
            <div className="space-y-1">
              <CardTitle className="text-lg">{question.question_text}</CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {question.question_type === 'multiple_choice' ? 'Multiple Choice' : 
                   question.question_type === 'text_input' ? 'Long Answer' : 
                   'Typing Test'}
                </Badge>
                <Badge variant="secondary">{question.points} points</Badge>
                {question.time_limit && (
                  <Badge variant="outline">{Math.floor(question.time_limit / 60)}m {question.time_limit % 60}s</Badge>
                )}
              </div>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => {
                  setCurrentQuestion(question);
                  setEditDialogOpen(true);
                }}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon"
                onClick={() => {
                  setCurrentQuestion(question);
                  setDeleteDialogOpen(true);
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {question.question_type === 'multiple_choice' && (
            <div className="mt-2 space-y-2">
              {Array.isArray(question.options) && question.options.map((option, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className={`px-3 py-2 rounded-md w-full ${question.correct_answer === option ? 'bg-green-50 border border-green-200' : 'bg-gray-50 border'}`}>
                    {option as string}
                    {question.correct_answer === option && (
                      <span className="text-green-600 font-medium ml-2">✓ Correct</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {question.question_type === 'typing_test' && (
            <div className="mt-2 p-3 bg-gray-50 border rounded-md text-sm">
              <p className="font-medium mb-1">Target Text:</p>
              <p className="whitespace-pre-wrap">{question.correct_answer}</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" disabled>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-8 w-64" />
        </div>
        
        <div className="flex justify-between items-center">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        
        <Skeleton className="h-[calc(100vh-250px)] w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!assessment) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Assessment Not Found</AlertTitle>
          <AlertDescription>The assessment you're looking for doesn't exist or has been removed.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="outline" size="icon" onClick={() => router.push('/admin/assessments')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{assessment.title} - Questions</h1>
          <p className="text-muted-foreground">
            Manage questions for this assessment
          </p>
        </div>
      </div>
      
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2 w-full max-w-sm">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search questions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-9"
          />
        </div>

        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Question
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Question</DialogTitle>
              <DialogDescription>
                Create a new question for this assessment.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div>
                <Label htmlFor="question_type">Question Type</Label>
                <Select
                  value={newQuestion.question_type}
                  onValueChange={(value: QuestionType) => {
                    setNewQuestion({
                      ...newQuestion,
                      question_type: value,
                      // Reset options and correct answer when changing type
                      options: value === 'multiple_choice' ? [] : null,
                      correct_answer: null
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select question type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                    <SelectItem value="text_input">Long Answer</SelectItem>
                    <SelectItem value="typing_test">Typing Test</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="question_text">Question Text</Label>
                <Textarea
                  id="question_text"
                  placeholder="Enter your question here"
                  value={newQuestion.question_text}
                  onChange={(e) => setNewQuestion({
                    ...newQuestion,
                    question_text: e.target.value
                  })}
                  className="min-h-[100px]"
                />
              </div>
              
              {/* Render question type specific fields */}
              {renderQuestionTypeFields(newQuestion.question_type)}
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateQuestion} disabled={isSubmitting}>
                {isSubmitting ? 'Creating...' : 'Create Question'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <Separator />
      
      {filteredQuestions.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12">
          <FileQuestion className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">No Questions Found</h3>
          <p className="text-muted-foreground mt-1">
            {searchQuery 
              ? `No questions match "${searchQuery}"`
              : "This assessment doesn't have any questions yet. Add one to get started."
            }
          </p>
          <Button
            className="mt-4"
            onClick={() => setCreateDialogOpen(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add First Question
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">
              {filteredQuestions.length} {filteredQuestions.length === 1 ? 'Question' : 'Questions'}
            </h2>
          </div>
          
          <div className="space-y-4">
            {filteredQuestions.map(renderQuestionCard)}
          </div>
        </div>
      )}
      
      {/* Edit Question Dialog */}
      {currentQuestion && (
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Question</DialogTitle>
              <DialogDescription>
                Make changes to this question.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div>
                <Label htmlFor="edit_question_type">Question Type</Label>
                <Select
                  value={currentQuestion.question_type}
                  onValueChange={(value: QuestionType) => {
                    setCurrentQuestion({
                      ...currentQuestion,
                      question_type: value,
                      // Reset options and correct answer when changing type
                      options: value === 'multiple_choice' ? [] : null,
                      correct_answer: null
                    });
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select question type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                    <SelectItem value="text_input">Long Answer</SelectItem>
                    <SelectItem value="typing_test">Typing Test</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit_question_text">Question Text</Label>
                <Textarea
                  id="edit_question_text"
                  placeholder="Enter your question here"
                  value={currentQuestion.question_text}
                  onChange={(e) => setCurrentQuestion({
                    ...currentQuestion,
                    question_text: e.target.value
                  })}
                  className="min-h-[100px]"
                />
              </div>
              
              {/* Render question type specific fields */}
              {renderQuestionTypeFields(currentQuestion.question_type, true, currentQuestion)}
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateQuestion} disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      
      {/* Delete Question Dialog */}
      {currentQuestion && (
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Question</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this question? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            
            <div className="py-4">
              <p className="font-medium">{currentQuestion.question_text}</p>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDeleteQuestion}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Deleting...' : 'Delete Question'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
} 