-- =============================================================================
-- LUNA DATABASE SEEDING EXECUTION SCRIPT
-- =============================================================================
-- This script executes the comprehensive seeding for Luna's employment-based
-- dual-context architecture. Run this after the schema is properly set up.
-- =============================================================================

-- Check if we're connected to the right database
SELECT 'Connected to database:' as info, current_database() as database_name;

-- Execute the comprehensive seeding script
\i clear-and-reseed.sql

-- Final verification
SELECT 'Seeding execution completed at:' as info, NOW() as timestamp;
