/**
 * AI Service Configuration
 * Centralized configuration for all AI services (Together.ai, ElevenLabs, Pinecone)
 */

export interface AIServiceConfig {
  together: {
    apiKey: string;
    baseUrl: string;
    defaultModel: string;
    timeout: number;
    retryAttempts: number;
  };
  elevenlabs: {
    apiKey: string;
    baseUrl: string;
    defaultVoice: string;
    timeout: number;
  };
  pinecone: {
    apiKey: string;
    environment: string;
    indexName: string;
    dimension: number;
  };
}

export const AI_MODELS = {
  // Together.ai models for different use cases
  TOGETHER: {
    LLAMA_70B: 'meta-llama/Llama-2-70b-chat-hf',
    LLAMA_13B: 'meta-llama/Llama-2-13b-chat-hf',
    LLAMA_7B: 'meta-llama/Llama-2-7b-chat-hf',
    MISTRAL_7B: 'mistralai/Mistral-7B-Instruct-v0.1',
    CODE_LLAMA: 'codellama/CodeLlama-34b-Instruct-hf'
  }
} as const;

export const DEFAULT_AI_CONFIG: AIServiceConfig = {
  together: {
    apiKey: process.env.TOGETHER_AI_API_KEY || '',
    baseUrl: 'https://api.together.xyz',
    defaultModel: AI_MODELS.TOGETHER.LLAMA_70B,
    timeout: 60000, // 60 seconds
    retryAttempts: 3
  },
  elevenlabs: {
    apiKey: process.env.ELEVENLABS_API_KEY || '',
    baseUrl: 'https://api.elevenlabs.io',
    defaultVoice: 'rachel', // Professional female voice
    timeout: 30000 // 30 seconds
  },
  pinecone: {
    apiKey: process.env.PINECONE_API_KEY || '',
    environment: process.env.PINECONE_ENVIRONMENT || 'us-west1-gcp',
    indexName: process.env.PINECONE_INDEX_NAME || 'luna-assessments',
    dimension: 1536 // OpenAI embedding dimension
  }
};

// Assessment-specific AI configurations
export const ASSESSMENT_AI_CONFIG = {
  // Model selection based on assessment type
  MODEL_SELECTION: {
    communication_skills: AI_MODELS.TOGETHER.LLAMA_70B,
    cognitive_ability: AI_MODELS.TOGETHER.LLAMA_70B,
    technical_skills: AI_MODELS.TOGETHER.LLAMA_13B,
    emotional_intelligence: AI_MODELS.TOGETHER.LLAMA_70B,
    industry_knowledge: AI_MODELS.TOGETHER.LLAMA_13B,
    stress_resilience: AI_MODELS.TOGETHER.LLAMA_13B
  },
  
  // Generation parameters by assessment type
  GENERATION_PARAMS: {
    communication_skills: {
      temperature: 0.7,
      max_tokens: 4000,
      top_p: 0.9,
      repetition_penalty: 1.1
    },
    cognitive_ability: {
      temperature: 0.6,
      max_tokens: 3000,
      top_p: 0.8,
      repetition_penalty: 1.2
    },
    technical_skills: {
      temperature: 0.5,
      max_tokens: 2500,
      top_p: 0.8,
      repetition_penalty: 1.1
    },
    emotional_intelligence: {
      temperature: 0.8,
      max_tokens: 3500,
      top_p: 0.9,
      repetition_penalty: 1.0
    },
    industry_knowledge: {
      temperature: 0.4,
      max_tokens: 2000,
      top_p: 0.7,
      repetition_penalty: 1.2
    },
    stress_resilience: {
      temperature: 0.7,
      max_tokens: 3000,
      top_p: 0.9,
      repetition_penalty: 1.1
    }
  }
} as const;

// Voice configuration for different assessment types
export const VOICE_CONFIG = {
  ASSESSMENT_VOICES: {
    instructions: 'rachel', // Clear, professional voice for instructions
    questions: 'josh', // Neutral voice for questions
    feedback: 'bella' // Warm voice for feedback
  },
  
  VOICE_SETTINGS: {
    stability: 0.75,
    similarity_boost: 0.75,
    style: 0.0,
    use_speaker_boost: true
  }
} as const;

// Validation functions
export function validateAIConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!DEFAULT_AI_CONFIG.together.apiKey) {
    errors.push('TOGETHER_AI_API_KEY environment variable is required');
  }
  
  if (!DEFAULT_AI_CONFIG.elevenlabs.apiKey) {
    errors.push('ELEVENLABS_API_KEY environment variable is required');
  }
  
  if (!DEFAULT_AI_CONFIG.pinecone.apiKey) {
    errors.push('PINECONE_API_KEY environment variable is required');
  }
  
  if (!DEFAULT_AI_CONFIG.pinecone.environment) {
    errors.push('PINECONE_ENVIRONMENT environment variable is required');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Get model for specific assessment type
export function getModelForAssessment(assessmentType: string): string {
  return ASSESSMENT_AI_CONFIG.MODEL_SELECTION[assessmentType as keyof typeof ASSESSMENT_AI_CONFIG.MODEL_SELECTION] 
    || AI_MODELS.TOGETHER.LLAMA_70B;
}

// Get generation parameters for assessment type
export function getGenerationParams(assessmentType: string) {
  return ASSESSMENT_AI_CONFIG.GENERATION_PARAMS[assessmentType as keyof typeof ASSESSMENT_AI_CONFIG.GENERATION_PARAMS] 
    || ASSESSMENT_AI_CONFIG.GENERATION_PARAMS.communication_skills;
}

// Export configuration
export { DEFAULT_AI_CONFIG as aiConfig };
