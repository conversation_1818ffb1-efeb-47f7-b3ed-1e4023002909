"use client"

import React, { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Edit, Save, X, Plus, Trash2 } from 'lucide-react'
import { toast } from "sonner"

interface Course {
  id: string
  name: string
  description: string
  slug: string
  level: string
  estimated_duration: number
  status: string
  price: number
  cover_image_url?: string
  preview_video_url?: string
  meta_description?: string
  tags: string[]
  learning_objectives: string[]
  target_audience?: string
  instructor_id?: string
  instructor_bio?: string
  enrollment_count: number
  completion_rate: number
  average_rating: number
  course_complexity: string
  certification_available: boolean
  is_standalone: boolean
  required_software: string[]
  hardware_requirements?: string
  language: string
  accessibility_features: string[]
  prerequisite_courses: string[]
  created_at: string
  updated_at: string
}

interface CourseBasicInfoSectionProps {
  course: Course
  onUpdate: () => void
}

export function CourseBasicInfoSection({ course, onUpdate }: CourseBasicInfoSectionProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: course.name,
    description: course.description,
    level: course.level,
    estimated_duration: course.estimated_duration?.toString() || '',
    status: course.status,
    price: course.price?.toString() || '',
    cover_image_url: course.cover_image_url || '',
    preview_video_url: course.preview_video_url || '',
    meta_description: course.meta_description || '',
    target_audience: course.target_audience || '',
    instructor_bio: course.instructor_bio || '',
    course_complexity: course.course_complexity,
    certification_available: course.certification_available,
    is_standalone: course.is_standalone,
    hardware_requirements: course.hardware_requirements || '',
    language: course.language,
    tags: course.tags.join(', '),
    learning_objectives: course.learning_objectives.join('\n'),
    required_software: course.required_software.join(', '),
    accessibility_features: course.accessibility_features.join(', ')
  })

  // Memoized change handlers to prevent re-renders that cause focus loss
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: e.target.value }))
  }, [])

  const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, description: e.target.value }))
  }, [])

  const handleLevelChange = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, level: value }))
  }, [])

  const handleDurationChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, estimated_duration: e.target.value }))
  }, [])

  const handleStatusChange = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, status: value }))
  }, [])

  const handlePriceChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, price: e.target.value }))
  }, [])

  const handleCoverImageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, cover_image_url: e.target.value }))
  }, [])

  const handlePreviewVideoChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, preview_video_url: e.target.value }))
  }, [])

  const handleMetaDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, meta_description: e.target.value }))
  }, [])

  const handleTargetAudienceChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, target_audience: e.target.value }))
  }, [])

  const handleInstructorBioChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, instructor_bio: e.target.value }))
  }, [])

  const handleComplexityChange = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, course_complexity: value }))
  }, [])

  const handleCertificationChange = useCallback((checked: boolean) => {
    setFormData(prev => ({ ...prev, certification_available: checked }))
  }, [])

  const handleStandaloneChange = useCallback((checked: boolean) => {
    setFormData(prev => ({ ...prev, is_standalone: checked }))
  }, [])

  const handleHardwareRequirementsChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, hardware_requirements: e.target.value }))
  }, [])

  const handleLanguageChange = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, language: value }))
  }, [])

  const handleTagsChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, tags: e.target.value }))
  }, [])

  const handleLearningObjectivesChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, learning_objectives: e.target.value }))
  }, [])

  const handleRequiredSoftwareChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, required_software: e.target.value }))
  }, [])

  const handleAccessibilityFeaturesChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, accessibility_features: e.target.value }))
  }, [])

  const handleSave = async () => {
    try {
      setLoading(true)

      const payload = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        level: formData.level,
        estimated_duration: formData.estimated_duration ? parseInt(formData.estimated_duration) : null,
        status: formData.status,
        price: formData.price ? parseFloat(formData.price) : null,
        cover_image_url: formData.cover_image_url?.trim() || null,
        preview_video_url: formData.preview_video_url?.trim() || null,
        meta_description: formData.meta_description?.trim() || null,
        target_audience: formData.target_audience?.trim() || null,
        instructor_bio: formData.instructor_bio?.trim() || null,
        course_complexity: formData.course_complexity,
        certification_available: formData.certification_available,
        is_standalone: formData.is_standalone,
        hardware_requirements: formData.hardware_requirements?.trim() || null,
        language: formData.language,
        tags: formData.tags 
          ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
          : [],
        learning_objectives: formData.learning_objectives 
          ? formData.learning_objectives.split('\n').map(obj => obj.trim()).filter(obj => obj.length > 0)
          : [],
        required_software: formData.required_software 
          ? formData.required_software.split(',').map(sw => sw.trim()).filter(sw => sw.length > 0)
          : [],
        accessibility_features: formData.accessibility_features 
          ? formData.accessibility_features.split(',').map(feat => feat.trim()).filter(feat => feat.length > 0)
          : []
      }

      const response = await fetch(`/api/admin/courses/${course.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update course')
      }

      toast.success('Course updated successfully')
      onUpdate()
      
    } catch (error: any) {
      console.error('Error updating course:', error)
      toast.error(error.message || 'Failed to update course')
    } finally {
      setLoading(false)
    }
  }



  const formatPrice = (price: number | null) => {
    if (price === null) return 'Free'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const formatDuration = (minutes: number | null) => {
    if (!minutes) return 'Not set'
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <Button
          size="sm"
          onClick={handleSave}
          disabled={loading}
        >
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
        <div className="space-y-4">
              {/* Basic Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Course Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={handleNameChange}
                    placeholder="Enter course name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Select value={formData.level} onValueChange={handleLevelChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={handleDescriptionChange}
                  placeholder="Describe what students will learn in this course"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <Input
                    id="duration"
                    type="number"
                    value={formData.estimated_duration}
                    onChange={handleDurationChange}
                    placeholder="120"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="price">Price ($)</Label>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={handlePriceChange}
                    placeholder="99.99"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={handleStatusChange}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="review">Under Review</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Configuration Switches */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Course Complexity</Label>
                    <p className="text-sm text-muted-foreground">Basic or Advanced features</p>
                  </div>
                  <Select value={formData.course_complexity} onValueChange={handleComplexityChange}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="basic">Basic</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Certification</Label>
                    <p className="text-sm text-muted-foreground">Offers completion certificate</p>
                  </div>
                  <Switch
                    checked={formData.certification_available}
                    onCheckedChange={handleCertificationChange}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Standalone</Label>
                    <p className="text-sm text-muted-foreground">Can be purchased individually</p>
                  </div>
                  <Switch
                    checked={formData.is_standalone}
                    onCheckedChange={handleStandaloneChange}
                  />
                </div>
              </div>
            </div>
    </div>
  )
}
