-- Disable RLS policies to allow manual data deletion
-- Run this to remove policy restrictions

-- Disable <PERSON><PERSON> on all tables
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE organizations DISABLE ROW LEVEL SECURITY;
ALTER TABLE departments DISABLE ROW LEVEL SECURITY;
ALTER TABLE employment_relationships DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_contexts DISABLE ROW LEVEL SECURITY;
ALTER TABLE individuals DISABLE ROW LEVEL SECURITY;
ALTER TABLE employment_invitations DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies (if any)
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Platform admins can manage all users" ON users;
DROP POLICY IF EXISTS "Organization admins can view org members" ON users;

DROP POLICY IF EXISTS "Organizations are viewable by members" ON organizations;
DROP POLICY IF EXISTS "Organization admins can update org" ON organizations;
DROP POLICY IF EXISTS "Platform admins can manage organizations" ON organizations;

DROP POLICY IF EXISTS "Departments viewable by org members" ON departments;
DROP POLICY IF EXISTS "Organization admins can manage departments" ON departments;
DROP POLICY IF EXISTS "Platform admins can manage departments" ON departments;

DROP POLICY IF EXISTS "Employment relationships viewable by user and org" ON employment_relationships;
DROP POLICY IF EXISTS "Organization admins can manage employment" ON employment_relationships;
DROP POLICY IF EXISTS "Platform admins can manage employment" ON employment_relationships;

DROP POLICY IF EXISTS "Users can manage own context" ON user_contexts;
DROP POLICY IF EXISTS "Organization admins can view member contexts" ON user_contexts;
DROP POLICY IF EXISTS "Platform admins can manage contexts" ON user_contexts;

DROP POLICY IF EXISTS "Users can view own individual profile" ON individuals;
DROP POLICY IF EXISTS "Users can update own individual profile" ON individuals;
DROP POLICY IF EXISTS "Platform admins can manage individuals" ON individuals;

DROP POLICY IF EXISTS "Invitations viewable by org admins" ON employment_invitations;
DROP POLICY IF EXISTS "Organization admins can manage invitations" ON employment_invitations;
DROP POLICY IF EXISTS "Platform admins can manage invitations" ON employment_invitations;

-- Confirm RLS is disabled
SELECT 
  schemaname,
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('users', 'organizations', 'departments', 'employment_relationships', 'user_contexts', 'individuals', 'employment_invitations')
ORDER BY tablename;

-- Show message
SELECT 'RLS policies disabled - you can now delete records manually' as status;
