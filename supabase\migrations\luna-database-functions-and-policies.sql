-- =============================================================================
-- LUNA DATABASE FUNCTIONS AND SECURITY POLICIES
-- Part 2 of the Luna database setup
-- =============================================================================

-- Function to switch user context
CREATE OR REPLACE FUNCTION switch_user_context(
  p_user_id UUID,
  p_context_type context_type,
  p_team_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  v_organization_id UUID;
BEGIN
  -- Validate team membership if switching to team context
  IF p_context_type = 'team' THEN
    IF p_team_id IS NULL THEN
      RAISE EXCEPTION 'Team ID required for team context';
    END IF;
    
    -- Check if user is member of the team
    IF NOT EXISTS (
      SELECT 1 FROM team_memberships 
      WHERE user_id = p_user_id 
      AND team_id = p_team_id 
      AND status = 'active'
    ) THEN
      RAISE EXCEPTION 'User is not an active member of this team';
    END IF;
    
    -- Get organization ID
    SELECT organization_id INTO v_organization_id
    FROM teams WHERE id = p_team_id;
  END IF;
  
  -- Update user context
  INSERT INTO user_contexts (user_id, active_context, active_team_id, active_organization_id)
  VALUES (p_user_id, p_context_type, p_team_id, v_organization_id)
  ON CONFLICT (user_id) 
  DO UPDATE SET
    active_context = p_context_type,
    active_team_id = p_team_id,
    active_organization_id = v_organization_id,
    last_context_switch = NOW(),
    recent_contexts = jsonb_set(
      COALESCE(user_contexts.recent_contexts, '[]'::jsonb),
      '{0}',
      jsonb_build_object(
        'context_type', user_contexts.active_context,
        'team_id', user_contexts.active_team_id,
        'switched_at', user_contexts.last_context_switch
      )
    );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's available contexts
CREATE OR REPLACE FUNCTION get_user_available_contexts(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  SELECT jsonb_build_object(
    'individual', jsonb_build_object(
      'type', 'individual',
      'name', 'Personal Learning',
      'available', true
    ),
    'teams', COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'type', 'team',
          'team_id', t.id,
          'team_name', t.name,
          'team_slug', t.slug,
          'organization_name', o.name,
          'organization_slug', o.slug,
          'role', tm.role,
          'available', tm.status = 'active'
        )
      ) FILTER (WHERE t.id IS NOT NULL),
      '[]'::jsonb
    )
  ) INTO result
  FROM team_memberships tm
  LEFT JOIN teams t ON tm.team_id = t.id
  LEFT JOIN organizations o ON t.organization_id = o.id
  WHERE tm.user_id = p_user_id;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create a new team
CREATE OR REPLACE FUNCTION create_team(
  p_organization_id UUID,
  p_creator_id UUID,
  p_name VARCHAR(255),
  p_description TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_team_id UUID;
  v_slug VARCHAR(100);
BEGIN
  -- Generate unique slug
  v_slug := generate_unique_slug(p_name, 'teams', 'slug');
  
  -- Create team
  INSERT INTO teams (
    organization_id, name, slug, description, created_by
  ) VALUES (
    p_organization_id, p_name, v_slug, p_description, p_creator_id
  ) RETURNING id INTO v_team_id;
  
  -- Add creator as team owner
  INSERT INTO team_memberships (
    user_id, team_id, role, status, joined_at
  ) VALUES (
    p_creator_id, v_team_id, 'owner', 'active', NOW()
  );
  
  RETURN v_team_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to invite user to team
CREATE OR REPLACE FUNCTION invite_to_team(
  p_team_id UUID,
  p_inviter_id UUID,
  p_invitee_email VARCHAR(255),
  p_role team_member_role DEFAULT 'member',
  p_message TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_invitee_id UUID;
  v_membership_id UUID;
BEGIN
  -- Get invitee user ID
  SELECT id INTO v_invitee_id FROM users WHERE email = p_invitee_email;
  
  IF v_invitee_id IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', p_invitee_email;
  END IF;
  
  -- Check if already a member
  IF EXISTS (
    SELECT 1 FROM team_memberships 
    WHERE user_id = v_invitee_id AND team_id = p_team_id
  ) THEN
    RAISE EXCEPTION 'User is already a member of this team';
  END IF;
  
  -- Create invitation
  INSERT INTO team_memberships (
    user_id, team_id, role, status, invited_by, invited_at, invitation_message
  ) VALUES (
    v_invitee_id, p_team_id, p_role, 'pending', p_inviter_id, NOW(), p_message
  ) RETURNING id INTO v_membership_id;
  
  RETURN v_membership_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate schema integrity
CREATE OR REPLACE FUNCTION validate_luna_schema()
RETURNS TABLE(check_name TEXT, status TEXT, details TEXT) AS $$
BEGIN
  -- Check if all required tables exist
  RETURN QUERY
  SELECT 
    'Required Tables'::TEXT,
    CASE WHEN COUNT(*) = 8 THEN 'PASS' ELSE 'FAIL' END::TEXT,
    'Found ' || COUNT(*) || ' of 8 required tables'::TEXT
  FROM information_schema.tables 
  WHERE table_name IN (
    'users', 'individuals', 'organizations', 'teams', 
    'team_memberships', 'user_contexts', 'user_training_data', 'learning_paths'
  );
  
  -- Check if all RLS policies are enabled
  RETURN QUERY
  SELECT 
    'RLS Policies'::TEXT,
    CASE WHEN COUNT(*) >= 8 THEN 'PASS' ELSE 'FAIL' END::TEXT,
    'Found ' || COUNT(*) || ' RLS policies'::TEXT
  FROM pg_policies 
  WHERE schemaname = 'public';
  
  -- Check if all triggers are created
  RETURN QUERY
  SELECT 
    'Update Triggers'::TEXT,
    CASE WHEN COUNT(*) >= 8 THEN 'PASS' ELSE 'FAIL' END::TEXT,
    'Found ' || COUNT(*) || ' update triggers'::TEXT
  FROM information_schema.triggers 
  WHERE trigger_name LIKE '%updated_at%';
  
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Users can view their own data
CREATE POLICY "users_select_own" ON users
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own data
CREATE POLICY "users_update_own" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own data (registration)
CREATE POLICY "users_insert_own" ON users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Team members can view basic info of other team members
CREATE POLICY "users_select_team_members" ON users
  FOR SELECT USING (
    id IN (
      SELECT DISTINCT tm2.user_id
      FROM team_memberships tm1
      JOIN team_memberships tm2 ON tm1.team_id = tm2.team_id
      WHERE tm1.user_id = auth.uid()
      AND tm1.status = 'active'
      AND tm2.status = 'active'
    )
  );

-- Enable RLS on individuals table
ALTER TABLE individuals ENABLE ROW LEVEL SECURITY;

-- Users can manage their own individual profile
CREATE POLICY "individuals_manage_own" ON individuals
  FOR ALL USING (user_id = auth.uid());

-- Enable RLS on organizations table
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Organization members can view their organization
CREATE POLICY "organizations_select_members" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT DISTINCT t.organization_id
      FROM team_memberships tm
      JOIN teams t ON tm.team_id = t.id
      WHERE tm.user_id = auth.uid() AND tm.status = 'active'
    )
  );

-- Organization owners can update their organization
CREATE POLICY "organizations_update_owners" ON organizations
  FOR UPDATE USING (created_by = auth.uid());

-- Enable RLS on teams table
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

-- Team members can view their teams
CREATE POLICY "teams_select_members" ON teams
  FOR SELECT USING (
    id IN (
      SELECT team_id FROM team_memberships 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Team owners/admins can update teams
CREATE POLICY "teams_update_owners_admins" ON teams
  FOR UPDATE USING (
    id IN (
      SELECT team_id FROM team_memberships
      WHERE user_id = auth.uid()
      AND status = 'active'
      AND role IN ('owner', 'admin')
    )
  );

-- Enable RLS on team_memberships table
ALTER TABLE team_memberships ENABLE ROW LEVEL SECURITY;

-- Users can view memberships they're involved in
CREATE POLICY "memberships_select_involved" ON team_memberships
  FOR SELECT USING (
    user_id = auth.uid() OR
    team_id IN (
      SELECT team_id FROM team_memberships
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Team owners/admins can manage memberships
CREATE POLICY "memberships_manage_owners_admins" ON team_memberships
  FOR ALL USING (
    team_id IN (
      SELECT team_id FROM team_memberships
      WHERE user_id = auth.uid()
      AND status = 'active'
      AND role IN ('owner', 'admin')
    )
  );

-- Enable RLS on user_contexts table
ALTER TABLE user_contexts ENABLE ROW LEVEL SECURITY;

-- Users can manage their own context
CREATE POLICY "contexts_manage_own" ON user_contexts
  FOR ALL USING (user_id = auth.uid());

-- Enable RLS on user_training_data table
ALTER TABLE user_training_data ENABLE ROW LEVEL SECURITY;

-- Users can access their own training data
CREATE POLICY "training_data_select_own" ON user_training_data
  FOR SELECT USING (user_id = auth.uid());

-- Users can update their own training data
CREATE POLICY "training_data_update_own" ON user_training_data
  FOR UPDATE USING (user_id = auth.uid());

-- Users can insert their own training data
CREATE POLICY "training_data_insert_own" ON user_training_data
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Team members can view team training data
CREATE POLICY "training_data_select_team" ON user_training_data
  FOR SELECT USING (
    context_type = 'team' AND
    team_id IN (
      SELECT team_id FROM team_memberships
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Enable RLS on learning_paths table
ALTER TABLE learning_paths ENABLE ROW LEVEL SECURITY;

-- Users can view public learning paths
CREATE POLICY "learning_paths_select_public" ON learning_paths
  FOR SELECT USING (is_public = true);

-- Users can view their own learning paths
CREATE POLICY "learning_paths_select_own" ON learning_paths
  FOR SELECT USING (created_by = auth.uid());

-- Team members can view team learning paths
CREATE POLICY "learning_paths_select_team" ON learning_paths
  FOR SELECT USING (
    team_id IN (
      SELECT team_id FROM team_memberships
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Users can manage their own learning paths
CREATE POLICY "learning_paths_manage_own" ON learning_paths
  FOR ALL USING (created_by = auth.uid());

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================================================

-- Add updated_at triggers to all tables
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_individuals_updated_at
  BEFORE UPDATE ON individuals
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_organizations_updated_at
  BEFORE UPDATE ON organizations
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_teams_updated_at
  BEFORE UPDATE ON teams
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_team_memberships_updated_at
  BEFORE UPDATE ON team_memberships
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_contexts_updated_at
  BEFORE UPDATE ON user_contexts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_training_data_updated_at
  BEFORE UPDATE ON user_training_data
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_learning_paths_updated_at
  BEFORE UPDATE ON learning_paths
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- INITIAL DATA & SETUP
-- =============================================================================

-- Insert default learning paths for individual users
INSERT INTO learning_paths (
  id, title, description, slug, modules_sequence,
  estimated_duration_hours, difficulty_level, is_public, status
) VALUES
(
  gen_random_uuid(),
  'Getting Started with Luna',
  'Introduction to Luna platform and basic skills assessment',
  'getting-started-luna',
  '[]'::jsonb,
  2,
  1,
  true,
  'active'
),
(
  gen_random_uuid(),
  'Professional Development Fundamentals',
  'Core professional skills for career advancement',
  'professional-development-fundamentals',
  '[]'::jsonb,
  20,
  2,
  true,
  'active'
);

-- =============================================================================
-- SCHEMA VALIDATION
-- =============================================================================

-- Validate the deployed schema
SELECT * FROM validate_luna_schema();

-- =============================================================================
-- DEPLOYMENT COMPLETE
-- Luna Phase 1 Database Schema Successfully Deployed
-- =============================================================================
