import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-admin';

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const program_id = searchParams.get('program_id');
    const difficulty = searchParams.get('difficulty');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Create admin client
    const adminClient = createAdminClient();

    // Build query
    let query = adminClient
      .from('learning_paths')
      .select(`
        *,
        program:programs!learning_paths_program_id_fkey(
          id,
          name,
          industry
        ),
        created_by_user:users!learning_paths_created_by_fkey(
          id,
          full_name,
          email
        ),
        pathway_courses(
          id,
          sequence_order,
          is_required,
          course:courses(
            id,
            name,
            level,
            estimated_duration,
            status
          )
        )
      `)
      .order('sort_order', { ascending: true });

    // Apply filters
    if (program_id && program_id !== 'all') {
      query = query.eq('program_id', program_id);
    }

    if (difficulty && difficulty !== 'all') {
      query = query.eq('difficulty_level', parseInt(difficulty));
    }

    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: pathways, error: pathwaysError } = await query;

    if (pathwaysError) {
      console.error('Error fetching pathways:', pathwaysError);
      return NextResponse.json(
        { error: pathwaysError.message || 'Failed to fetch pathways' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = adminClient
      .from('learning_paths')
      .select('*', { count: 'exact', head: true });

    if (program_id && program_id !== 'all') {
      countQuery = countQuery.eq('program_id', program_id);
    }

    if (difficulty && difficulty !== 'all') {
      countQuery = countQuery.eq('difficulty_level', parseInt(difficulty));
    }

    if (search) {
      countQuery = countQuery.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting pathways:', countError);
    }

    // Add course counts to pathways
    const pathwaysWithCounts = (pathways || []).map((pathway: any) => ({
      ...pathway,
      _count: {
        courses: pathway.pathway_courses?.length || 0,
        required_courses: pathway.pathway_courses?.filter((pc: any) => pc.is_required).length || 0,
        optional_courses: pathway.pathway_courses?.filter((pc: any) => !pc.is_required).length || 0
      }
    }));

    return NextResponse.json({
      pathways: pathwaysWithCounts,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error: any) {
    console.error('Pathways GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Parse request body
    const body = await req.json();
    const {
      title,
      description,
      slug,
      program_id,
      estimated_duration_hours,
      difficulty_level,
      sort_order,
      is_featured,
      cover_image_url,
      target_roles,
      prerequisites,
      learning_objectives,
      skills_covered,
      skills_gained
    } = body;

    // Validate required fields
    if (!title || !description || !program_id) {
      return NextResponse.json(
        { error: 'Title, description, and program are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Check if slug already exists
    if (slug) {
      const { data: existingPathway } = await adminClient
        .from('learning_paths')
        .select('id')
        .eq('slug', slug.trim())
        .single();

      if (existingPathway) {
        return NextResponse.json(
          { error: 'A pathway with this slug already exists' },
          { status: 400 }
        );
      }
    }

    // Verify program exists
    const { data: program, error: programError } = await adminClient
      .from('programs')
      .select('id')
      .eq('id', program_id)
      .single();

    if (programError || !program) {
      return NextResponse.json(
        { error: 'Program not found' },
        { status: 404 }
      );
    }

    // Create pathway
    const { data: pathway, error: pathwayError } = await adminClient
      .from('learning_paths')
      .insert({
        title: title.trim(),
        description: description.trim(),
        slug: slug?.trim() || title.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
        program_id,
        estimated_duration_hours: estimated_duration_hours || null,
        difficulty_level: difficulty_level || 1,
        sort_order: sort_order || 0,
        is_featured: is_featured || false,
        cover_image_url,
        target_roles: target_roles || [],
        prerequisites: prerequisites || [],
        learning_objectives: learning_objectives || [],
        skills_covered: skills_covered || [],
        skills_gained: skills_gained || [],
        created_by: authResult.user.id
      })
      .select(`
        *,
        program:programs!learning_paths_program_id_fkey(
          id,
          name,
          industry
        ),
        created_by_user:users!learning_paths_created_by_fkey(
          id,
          full_name,
          email
        )
      `)
      .single();

    if (pathwayError) {
      console.error('Error creating pathway:', pathwayError);
      return NextResponse.json(
        { error: pathwayError.message || 'Failed to create pathway' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Pathway created successfully',
      pathway
    });

  } catch (error: any) {
    console.error('Pathway creation API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
