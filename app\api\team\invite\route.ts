import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';

export async function POST(request: Request) {
  try {
    // Get the current user's session
    const cookieStore = cookies();
    const supabase = createServerComponentClient<Database>({ cookies: () => cookieStore });
    
    // Get the current user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      return NextResponse.json(
        { error: 'Unauthorized - Not logged in' },
        { status: 401 }
      );
    }
    
    // Parse request body
    const { email, role, bpoId } = await request.json();
    
    if (!email || !role || !bpoId) {
      return NextResponse.json(
        { error: 'Missing required fields: email, role, or bpoId' },
        { status: 400 }
      );
    }
    
    // Check if the current user has permission to invite to this BPO
    const { data: teamMember, error: teamError } = await supabase
      .from('bpo_teams')
      .select('role')
      .eq('user_id', session.user.id)
      .eq('bpo_id', bpoId)
      .single();
    
    if (teamError || !teamMember) {
      return NextResponse.json(
        { error: 'Unauthorized - Not a member of this BPO' },
        { status: 403 }
      );
    }
    
    // Only admins can invite users
    if (teamMember.role !== 'admin' && teamMember.role !== 'owner') {
      return NextResponse.json(
        { error: 'Unauthorized - Only admins can invite team members' },
        { status: 403 }
      );
    }
    
    // Create admin client to send the invitation
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    // Check if a user with this email already exists
    const { data: existingUser, error: userError } = await adminClient
      .from('users')
      .select('id')
      .eq('email', email)
      .maybeSingle();
    
    let userId;
    
    if (existingUser) {
      userId = existingUser.id;
      
      // Check if user is already employed by this organization
      const { data: existingEmployment } = await adminClient
        .from('employment_relationships')
        .select('id')
        .eq('organization_id', bpoId)
        .eq('user_id', userId)
        .maybeSingle();
        
      if (existingEmployment) {
        return NextResponse.json(
          { error: 'User is already employed by this organization' },
          { status: 400 }
        );
      }

      // Add existing user to the organization
      const { data: newEmployment, error: insertError } = await adminClient
        .from('employment_relationships')
        .insert({
          organization_id: bpoId,
          user_id: userId,
          role: role,
          status: 'invited',
          joined_at: new Date().toISOString(),
          created_by: session.user.id
        })
        .select('id')
        .single();
      
      if (insertError) {
        console.error('Error adding team member:', insertError);
        return NextResponse.json(
          { error: `Failed to add team member: ${insertError.message || 'Unknown error'}` },
          { status: 500 }
        );
      }
      
      // Get BPO info for the email
      const { data: bpoData } = await adminClient
        .from('bpos')
        .select('name')
        .eq('id', bpoId)
        .single();
      
      // Send invitation email via Supabase Auth
      const { data: inviteData, error: inviteError } = await adminClient.auth.admin.inviteUserByEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_BASE_URL}/login?invited=true&bpo=${bpoId}`,
        data: {
          bpo_id: bpoId,
          role: role,
          team_member_id: newTeamMember.id,
          bpo_name: bpoData?.name || 'BPO Company'
        }
      });
      
      if (inviteError) {
        console.error('Error sending invitation:', inviteError);
        return NextResponse.json(
          { error: `Failed to send invitation: ${inviteError.message}` },
          { status: 500 }
        );
      }
      
      return NextResponse.json({
        success: true,
        message: `Invitation sent to ${email}`,
        teamMemberId: newTeamMember.id
      });
      
    } else {
      // User doesn't exist yet, send invitation to sign up
      
      // Get BPO info for the email
      const { data: bpoData } = await adminClient
        .from('bpos')
        .select('name')
        .eq('id', bpoId)
        .single();
      
      // Instead of creating a placeholder user, we'll create a record in a new invitations table
      const { data: invitation, error: invitationError } = await adminClient
        .from('bpo_invitations')
        .insert({
          bpo_id: bpoId,
          email: email,
          role: role,
          invited_at: new Date().toISOString(),
          invited_by: session.user.id,
          status: 'pending'
        })
        .select('id')
        .single();
      
      if (invitationError) {
        console.error('Error creating invitation record:', invitationError);
        console.log('Invitation error details:', JSON.stringify(invitationError));
        
        // If table doesn't exist, create it (first time setup)
        if (invitationError.message && invitationError.message.includes('relation "bpo_invitations" does not exist')) {
          console.log('Missing bpo_invitations table. Please ensure the table is created using:');
          console.log('1. Run node scripts/create_invitations_table.js');
          console.log('2. Or manually create the table in Supabase dashboard');
          
          return NextResponse.json(
            { 
              error: 'The invitations table needs to be created',
              details: 'Please run the setup script or contact the administrator' 
            },
            { status: 500 }
          );
        } else {
          return NextResponse.json(
            { error: `Failed to create invitation: ${invitationError.message || 'Unknown error'}` },
            { status: 500 }
          );
        }
      }
      
      // Send invitation email via Supabase Auth
      const { data: inviteData, error: inviteError } = await adminClient.auth.admin.inviteUserByEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_BASE_URL}/login?invited=true&bpo=${bpoId}`,
        data: {
          bpo_id: bpoId,
          role: role,
          bpo_name: bpoData?.name || 'BPO Company'
        }
      });
      
      if (inviteError) {
        console.error('Error sending invitation:', inviteError);
        return NextResponse.json(
          { error: `Failed to send invitation: ${inviteError.message || 'Unknown error'}` },
          { status: 500 }
        );
      }
      
      return NextResponse.json({
        success: true,
        message: `Invitation sent to ${email}`,
        email: email
      });
    }
    
  } catch (error: any) {
    console.error('Error in team invite API:', error);
    return NextResponse.json(
      { error: error.message || 'Server error processing invitation' },
      { status: 500 }
    );
  }
}