'use client';

import { useState, useEffect, useMemo } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { createBrowserClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { 
  Users, 
  Search, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Filter,
  Download,
  TrendingUp,
  Activity,
  UserCheck,
  Target,
  ArrowLeft,
  Grid3X3,
  List,
  Plus,
  Mail,
  Phone,
  Calendar,
  Award,
  BookOpen,
  Code,
  Briefcase
} from 'lucide-react';

// Define types
interface StaffMember {
  id: string;
  full_name: string;
  slug: string;
  email: string;
  job_title: string | null;
  hire_date: string | null;
  status: 'active' | 'inactive' | 'on_leave';
  performance_score: number;
  training_progress: number;
  skills: string[];
  recent_activity: string;
  avatar_url?: string;
}

interface Department {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  department_head?: {
    full_name: string;
    email: string;
  };
}

interface StaffStats {
  totalStaff: number;
  activeStaff: number;
  averagePerformance: number;
  trainingCompletion: number;
}

export default function DepartmentStaffPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [department, setDepartment] = useState<Department | null>(null);
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);

  const orgSlug = params.orgSlug as string;
  const departmentSlug = params.departmentSlug as string;

  const supabase = createBrowserClient();

  useEffect(() => {
    fetchDepartmentData();
    fetchStaffMembers();
  }, [orgSlug, departmentSlug]);

  const fetchDepartmentData = async () => {
    try {
      // Get organization ID first
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError);
        return;
      }

      // Find department by slug
      const { data: departmentsData, error: deptError } = await supabase
        .from('departments')
        .select(`
          *,
          department_head:users!departments_department_head_id_fkey (
            full_name,
            email
          )
        `)
        .eq('organization_id', orgData.id);

      if (deptError) {
        console.error('Error fetching departments:', deptError);
        return;
      }

      // Find the department that matches the slug
      const department = departmentsData?.find(dept => {
        const deptSlug = dept.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        return deptSlug === departmentSlug;
      });

      if (department) {
        setDepartment({
          ...department,
          slug: departmentSlug
        });
      }
    } catch (error) {
      console.error('Error fetching department:', error);
    }
  };

  const fetchStaffMembers = async () => {
    try {
      setLoading(true);
      console.log('🚀 Starting staff fetch for:', { orgSlug, departmentSlug });

      // Step 1: Get organization by slug (same as debug)
      const { data: organizations, error: orgError } = await supabase
        .from('organizations')
        .select('id, name, slug')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('❌ Organization query failed:', orgError);
        return;
      }
      console.log('✅ Organization found:', organizations);

      // Step 2: Get all departments for this organization (same as debug)
      const { data: departments, error: deptError } = await supabase
        .from('departments')
        .select('id, name, organization_id')
        .eq('organization_id', organizations.id);

      if (deptError) {
        console.error('❌ Departments query failed:', deptError);
        return;
      }
      console.log('✅ Departments found:', departments?.length || 0);

      // Step 3: Find the specific department by slug
      const department = departments?.find(dept => {
        const deptSlug = dept.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        console.log(`🔍 Comparing: "${deptSlug}" === "${departmentSlug}"`);
        return deptSlug === departmentSlug;
      });

      if (!department) {
        console.error('❌ Department not found. Available departments:');
        departments?.forEach(d => {
          const slug = d.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
          console.log(`   - ${d.name} -> ${slug}`);
        });
        setStaffMembers([]);
        return;
      }
      console.log('✅ Department found:', department);

      // Step 4: Get employment relationships for this department (same as debug)
      console.log('🔍 Querying employment relationships for department ID:', department.id);
      const { data: employments, error: empError } = await supabase
        .from('employment_relationships')
        .select('id, user_id, department_id, organization_id, role, status, job_title')
        .eq('department_id', department.id);

      if (empError) {
        console.error('❌ Employment relationships query failed:', empError);
        console.error('❌ Department ID that failed:', department.id);
        console.error('❌ Full error details:', JSON.stringify(empError, null, 2));
        setStaffMembers([]);
        return;
      }
      console.log('✅ Employment relationships found:', employments?.length || 0);
      console.log('✅ Employment data:', employments);

      if (!employments || employments.length === 0) {
        console.log('ℹ️ No employment relationships found for this department');
        setStaffMembers([]);
        return;
      }

      // Step 5: Get users and individuals data for these employment relationships
      const userIds = employments.map(emp => emp.user_id);
      console.log('🔍 User IDs to fetch:', userIds);

      if (userIds.length === 0) {
        console.log('ℹ️ No user IDs to fetch');
        setStaffMembers([]);
        return;
      }

      // Get basic user info (name, email)
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, full_name, email')
        .in('id', userIds);

      if (usersError) {
        console.error('❌ Users query failed:', usersError);
        console.error('❌ User IDs that failed:', userIds);
        console.error('❌ Full error details:', JSON.stringify(usersError, null, 2));
        setStaffMembers([]);
        return;
      }
      console.log('✅ Users found:', users?.length || 0);

      // Get individuals data (training-related info, skills, profile image)
      const { data: individuals, error: individualsError } = await supabase
        .from('individuals')
        .select('user_id, skills, profile_image_url, learning_status, completed_courses, total_learning_hours')
        .in('user_id', userIds);

      if (individualsError) {
        console.error('❌ Individuals query failed:', individualsError);
        console.error('❌ Full error details:', JSON.stringify(individualsError, null, 2));
        // Don't return here, continue without individuals data
      }
      console.log('✅ Individuals found:', individuals?.length || 0);

      // Step 6: Build staff members combining users and individuals data
      const staffMembers: StaffMember[] = employments.map(emp => {
        const user = users?.find(u => u.id === emp.user_id);
        if (!user) {
          console.warn('⚠️ User not found for employment:', emp.user_id);
          return null;
        }

        const individual = individuals?.find(ind => ind.user_id === emp.user_id);
        const userSlug = user.full_name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

        // Extract skills from individuals table (JSON array)
        let skills: string[] = ['General Skills'];
        if (individual?.skills && Array.isArray(individual.skills)) {
          skills = individual.skills.map(skill =>
            typeof skill === 'string' ? skill : JSON.stringify(skill)
          );
        }

        // Calculate training progress from individuals data
        const trainingProgress = individual?.total_learning_hours
          ? Math.min(Math.floor((individual.total_learning_hours / 100) * 100), 100)
          : Math.floor(Math.random() * 40) + 60;

        // Calculate performance score based on completed courses
        const performanceScore = individual?.completed_courses
          ? Math.min(Math.floor((individual.completed_courses / 10) * 100), 100)
          : Math.floor(Math.random() * 30) + 70;

        return {
          id: user.id,
          full_name: user.full_name,
          slug: userSlug,
          email: user.email,
          job_title: emp.job_title || (emp.role === 'department_admin' ? 'Department Head' : 'Staff Member'),
          hire_date: emp.hire_date,
          status: emp.status as 'active' | 'inactive' | 'on_leave',
          performance_score: performanceScore,
          training_progress: trainingProgress,
          skills: skills,
          recent_activity: individual?.learning_status === 'active' ? 'Currently learning' : 'Recent platform activity',
          avatar_url: individual?.profile_image_url || null
        };
      }).filter(Boolean) as StaffMember[];

      console.log('✅ Final staff members:', staffMembers.length);
      setStaffMembers(staffMembers);

    } catch (error) {
      console.error('💥 Unexpected error in fetchStaffMembers:', error);
      setStaffMembers([]);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading staff members...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{department?.name} Staff</h1>
          <p className="text-muted-foreground">
            {department?.description}
          </p>
        </div>
        <Button onClick={() => router.push(`/org/${orgSlug}/departments`)}>
          Back to Departments
        </Button>
      </div>

      {staffMembers.length === 0 ? (
        // Empty State
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
              <Users className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2">No Staff Members Yet</h3>
            <p className="text-gray-500 dark:text-gray-400 text-center mb-6 max-w-sm">
              This department doesn't have any staff members assigned yet. Get started by adding your first team member.
            </p>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Staff Member
            </Button>
          </CardContent>
        </Card>
      ) : (
        // Staff Cards Grid
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {staffMembers.map((member) => (
            <Card
              key={member.id}
              className="p-6 cursor-pointer hover:shadow-lg transition-all duration-200 relative"
              onClick={() => router.push(`/org/${orgSlug}/departments/${departmentSlug}/staff/${member.slug}/profile`)}
            >
              {member.job_title === 'Department Head' && (
                <div className="absolute top-2 right-2">
                  <Badge variant="default" className="bg-blue-600 text-white text-xs">
                    Department Head
                  </Badge>
                </div>
              )}

              <div className="flex items-center gap-3 mb-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={member.avatar_url} alt={member.full_name} />
                  <AvatarFallback className="bg-blue-500 text-white">
                    {member.full_name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold">{member.full_name}</h3>
                  <p className="text-sm text-muted-foreground">{member.job_title}</p>
                  {member.hire_date && (
                    <p className="text-xs text-muted-foreground">
                      Joined {new Date(member.hire_date).toLocaleDateString()}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Performance:</span>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${member.performance_score}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{member.performance_score}%</span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Training:</span>
                  <div className="flex items-center gap-2">
                    <div className="w-16 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${member.training_progress}%` }}
                      ></div>
                    </div>
                    <span className="text-sm font-medium">{member.training_progress}%</span>
                  </div>
                </div>

                <div>
                  <span className="text-sm font-medium mb-2 block">Skills:</span>
                  <div className="flex flex-wrap gap-1">
                    {member.skills.slice(0, 3).map((skill) => (
                      <Badge key={skill} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {member.skills.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{member.skills.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <p className="text-xs text-muted-foreground">
                    📈 {member.recent_activity}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
