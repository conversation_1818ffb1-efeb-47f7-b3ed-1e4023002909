"use client"

import { useState, useEffect, memo, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Loader2, Upload, X } from "lucide-react"

const courseSchema = z.object({
  name: z.string().min(1, "Course name is required"),
  description: z.string().min(1, "Description is required"),
  price: z.string().optional(),
  level: z.enum(['beginner', 'intermediate', 'advanced']).default('beginner'),
  estimated_duration: z.string().optional(),
  status: z.enum(['draft', 'review', 'published', 'archived']).default('draft'),
  target_audience: z.string().optional(),
  learning_objectives: z.string().optional(),
  tags: z.string().optional(),
})

type CourseFormData = z.infer<typeof courseSchema>

interface Course {
  id: string
  name: string
  description: string
  price: number | null
  level: string
  estimated_duration: number | null
  status: string
  target_audience: string | null
  learning_objectives: string[]
  tags: string[]
  slug: string | null
  preview_video_url: string | null
  cover_image_url: string | null
  created_at: string
  updated_at: string
}

interface CourseFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  course?: Course | null
  onSuccess: () => void
}

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'review', label: 'Under Review' },
  { value: 'published', label: 'Published' },
  { value: 'archived', label: 'Archived' }
]

const levelOptions = [
  { value: 'beginner', label: 'Beginner' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'advanced', label: 'Advanced' }
]

export const CourseFormDialog = memo(function CourseFormDialog({
  open,
  onOpenChange,
  course,
  onSuccess
}: CourseFormDialogProps) {
  const [loading, setLoading] = useState(false)
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null)
  const [previewVideoFile, setPreviewVideoFile] = useState<File | null>(null)
  const isEditing = !!course

  const form = useForm<CourseFormData>({
    resolver: zodResolver(courseSchema),
    defaultValues: {
      name: "",
      description: "",
      price: "",
      level: "beginner",
      estimated_duration: "",
      status: "draft",
      target_audience: "",
      learning_objectives: "",
      tags: "",
    },
  })

  // Reset form when course changes
  useEffect(() => {
    if (course) {
      form.reset({
        name: course.name,
        description: course.description,
        price: course.price?.toString() || "",
        level: course.level as any,
        estimated_duration: course.estimated_duration?.toString() || "",
        status: course.status as any,
        target_audience: course.target_audience || "",
        learning_objectives: course.learning_objectives?.join('\n') || "",
        tags: course.tags?.join(', ') || "",
      })
    } else {
      form.reset({
        name: "",
        description: "",
        price: "",
        level: "beginner",
        estimated_duration: "",
        status: "draft",
        target_audience: "",
        learning_objectives: "",
        tags: "",
      })
    }
    setCoverImageFile(null)
    setPreviewVideoFile(null)
  }, [course, form])

  // Cleanup when dialog closes
  useEffect(() => {
    if (!open) {
      setLoading(false)
      setCoverImageFile(null)
      setPreviewVideoFile(null)
    }
  }, [open])

  const onSubmit = async (data: CourseFormData) => {
    setLoading(true)
    try {
      const url = isEditing 
        ? `/api/admin/courses/${course.id}`
        : '/api/admin/courses'
      
      const method = isEditing ? 'PUT' : 'POST'
      
      // Process form data
      const payload = {
        name: data.name.trim(),
        description: data.description.trim(),
        price: data.price ? parseFloat(data.price) : null,
        level: data.level,
        estimated_duration: data.estimated_duration ? parseInt(data.estimated_duration) : null,
        status: data.status,
        target_audience: data.target_audience?.trim() || null,
        learning_objectives: data.learning_objectives 
          ? data.learning_objectives.split('\n').map(obj => obj.trim()).filter(obj => obj.length > 0)
          : [],
        tags: data.tags 
          ? data.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
          : [],
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save course')
      }

      const result = await response.json()

      // TODO: Handle file uploads for cover image and preview video
      // This will be implemented when we add the media upload functionality

      toast.success(
        isEditing
          ? 'Course updated successfully'
          : 'Course created successfully'
      )

      // Close dialog first to prevent state conflicts
      onOpenChange(false)

      // Reset form state
      form.reset()
      setCoverImageFile(null)
      setPreviewVideoFile(null)

      // Trigger parent refresh after dialog is closed
      setTimeout(() => {
        onSuccess()
      }, 100)

    } catch (error: any) {
      console.error('Error saving course:', error)
      toast.error(error.message || 'Failed to save course')
    } finally {
      setLoading(false)
    }
  }

  const handleFileSelect = (file: File, type: 'cover' | 'video') => {
    if (type === 'cover') {
      setCoverImageFile(file)
    } else {
      setPreviewVideoFile(file)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Course' : 'Create New Course'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the course details below.'
              : 'Fill in the details to create a new course.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Basic Information</h4>
              
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Course Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter course name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description *</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe what students will learn in this course"
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="level"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Difficulty Level</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select level" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {levelOptions.map((level) => (
                            <SelectItem key={level.value} value={level.value}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {statusOptions.map((status) => (
                            <SelectItem key={status.value} value={status.value}>
                              {status.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price (USD)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.01"
                          placeholder="0.00" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Leave empty for free course
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="estimated_duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration (hours)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="10" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Estimated completion time
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Additional Details */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Additional Details</h4>

              <FormField
                control={form.control}
                name="target_audience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Audience</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Beginners in customer service, BPO professionals"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Who is this course designed for?
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="learning_objectives"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Learning Objectives</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter each objective on a new line&#10;e.g., Understand customer service principles&#10;Learn effective communication techniques"
                        rows={4}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      What will students achieve? (One per line)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="customer service, communication, BPO, soft skills"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Comma-separated tags for better discoverability
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Media Upload Section - Placeholder for future implementation */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Media (Coming Soon)</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">Cover Image</label>
                  <div className="border-2 border-dashed border-muted rounded-lg p-4 text-center text-muted-foreground">
                    <Upload className="h-6 w-6 mx-auto mb-2" />
                    <p className="text-sm">Upload coming soon</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-muted-foreground">Preview Video</label>
                  <div className="border-2 border-dashed border-muted rounded-lg p-4 text-center text-muted-foreground">
                    <Upload className="h-6 w-6 mx-auto mb-2" />
                    <p className="text-sm">Upload coming soon</p>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? 'Update Course' : 'Create Course'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
})
