import { NextRequest, NextResponse } from 'next/server';
import { createApiClient } from '@/lib/supabase-server';

/**
 * GET /api/org/[orgSlug]/staff
 * Get all staff members for an organization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orgSlug: string }> }
) {
  try {
    const { orgSlug } = await params;
    const supabase = await createApiClient();

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Get all staff members for this organization
    const { data: staff, error: staffError } = await supabase
      .from('employment_relationships')
      .select(`
        id,
        user_id,
        role,
        job_title,
        status,
        department_id,
        users!employment_relationships_user_id_fkey(
          id,
          full_name,
          email,
          avatar_url
        ),
        departments(
          id,
          name
        )
      `)
      .eq('organization_id', organization.id)
      .eq('status', 'active');

    if (staffError) {
      console.error('Error fetching staff:', staffError);
      return NextResponse.json(
        { error: 'Failed to fetch staff members' },
        { status: 500 }
      );
    }

    // Format the response
    const formattedStaff = (staff || []).map(member => ({
      id: member.user_id,
      employment_id: member.id,
      full_name: member.users.full_name,
      email: member.users.email,
      avatar_url: member.users.avatar_url,
      role: member.role,
      job_title: member.job_title,
      status: member.status,
      current_department_id: member.department_id,
      current_department_name: member.departments?.name || 'No Department',
    }));

    return NextResponse.json(formattedStaff);
  } catch (error: any) {
    console.error('Staff GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
