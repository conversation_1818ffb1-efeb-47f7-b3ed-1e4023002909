import { createAdminClient, supabase } from '@/lib/supabase';
import {
  Assessment,
  AssessmentCompletion,
  AssessmentCompletionInsert,
  AssessmentCompletionUpdate,
  AssessmentInsert,
  AssessmentQuestion,
  AssessmentStats,
  AssessmentUpdate,
  AssessmentWithStatus
} from '@/types/assessment';

// Get all assessments
export async function getAllAssessments(): Promise<Assessment[]> {
  const { data, error } = await supabase
    .from('assessments')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Error fetching assessments: ${error.message}`);
  }

  return data;
}

// Get assessment by ID
export async function getAssessmentById(id: string): Promise<Assessment | null> {
  const { data, error } = await supabase
    .from('assessments')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // Record not found
      return null;
    }
    throw new Error(`Error fetching assessment: ${error.message}`);
  }

  return data;
}

// Create assessment (admin only)
export async function createAssessment(assessment: AssessmentInsert): Promise<Assessment> {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessments')
    .insert(assessment)
    .select()
    .single();

  if (error) {
    throw new Error(`Error creating assessment: ${error.message}`);
  }

  return data;
}

// Update assessment (admin only)
export async function updateAssessment(id: string, assessment: AssessmentUpdate): Promise<Assessment> {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessments')
    .update(assessment)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Error updating assessment: ${error.message}`);
  }

  return data;
}

// Toggle assessment visibility (admin only)
export async function toggleAssessmentVisibility(id: string, isActive: boolean): Promise<Assessment> {
  const adminClient = createAdminClient();
  
  const { data, error } = await adminClient
    .from('assessments')
    .update({ is_active: isActive })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Error toggling assessment visibility: ${error.message}`);
  }

  return data;
}

// Delete assessment (admin only)
export async function deleteAssessment(id: string): Promise<void> {
  const adminClient = createAdminClient();
  
  const { error } = await adminClient
    .from('assessments')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Error deleting assessment: ${error.message}`);
  }
}

// Get assessment statistics (admin only)
export async function getAssessmentStats(): Promise<AssessmentStats> {
  const { data: assessments, error: assessmentsError } = await supabase
    .from('assessments')
    .select('*');

  if (assessmentsError) {
    throw new Error(`Error fetching assessment stats: ${assessmentsError.message}`);
  }

  const { data: completions, error: completionsError } = await supabase
    .from('assessment_completions')
    .select('*')
    .eq('status', 'completed');

  if (completionsError) {
    throw new Error(`Error fetching assessment completions: ${completionsError.message}`);
  }

  // Calculate statistics
  const total = assessments.length;
  const active = assessments.filter(a => a.is_active).length;
  const inactive = total - active;
  const technical = assessments.filter(a => a.category === 'Technical').length;
  const softSkills = assessments.filter(a => a.category === 'Soft Skills').length;
  const totalCompletions = completions.length;
  
  // Calculate average score from completed assessments
  const totalScores = completions.reduce((sum, completion) => sum + (completion.score || 0), 0);
  const avgScore = totalCompletions > 0 ? Math.round(totalScores / totalCompletions) : 0;

  return {
    total,
    active,
    inactive,
    technical,
    softSkills,
    totalCompletions,
    avgScore
  };
}

// Get assessments for prospect with completion status
export async function getAssessmentsForProspect(userId: string): Promise<AssessmentWithStatus[]> {
  console.log(`Fetching assessments for prospect ${userId}`);
  
  // First get all module assessment IDs to exclude them
  const { data: moduleAssessments, error: moduleAssessmentsError } = await supabase
    .from('module_assessments')
    .select('id');

  if (moduleAssessmentsError) {
    console.error(`Error fetching module assessments: ${moduleAssessmentsError.message}`);
    // Continue anyway - we'll just use other filtering methods
  }

  // Create a set of module assessment IDs for fast lookups
  const moduleAssessmentIds = new Set(moduleAssessments?.map(ma => ma.id) || []);
  console.log(`Found ${moduleAssessmentIds.size} module assessments to exclude`);
  
  // Get all assessments from the assessments table
  const { data: assessments, error: assessmentsError } = await supabase
    .from('assessments')
    .select('*')
    .order('created_at', { ascending: false });

  if (assessmentsError) {
    console.error(`Error fetching assessments: ${assessmentsError.message}`);
    throw new Error(`Error fetching assessments: ${assessmentsError.message}`);
  }

  console.log(`Found ${assessments?.length || 0} assessments from assessments table`);

  // Then get completion status for this user
  const { data: completions, error: completionsError } = await supabase
    .from('assessment_completions')
    .select('*')
    .eq('user_id', userId);

  if (completionsError) {
    console.error(`Error fetching assessment completions: ${completionsError.message}`);
    throw new Error(`Error fetching assessment completions: ${completionsError.message}`);
  }

  console.log(`Found ${completions?.length || 0} assessment completions for user`);

  // Filter out any assessments that have the same ID as a module assessment
  const filteredAssessments = assessments.filter(assessment => !moduleAssessmentIds.has(assessment.id));
  console.log(`After filtering out module assessments: ${filteredAssessments.length} assessments remaining`);

  // Additional safety filter to ensure we only have valid assessments
  const validAssessments = filteredAssessments.filter(assessment => 
    assessment.title && 
    assessment.description && 
    assessment.category && 
    assessment.passing_score !== undefined
  );

  console.log(`Filtered to ${validAssessments.length} valid assessments`);

  // Merge data to include completion status
  const assessmentsWithStatus: AssessmentWithStatus[] = validAssessments.map(assessment => {
    const completion = completions.find(c => c.assessment_id === assessment.id);
    
    if (!completion) {
      return {
        ...assessment,
        status: 'not_started'
      };
    }

    return {
      ...assessment,
      status: completion.status === 'completed' ? 'completed' : 'in_progress',
      score: completion.score,
      completion_time: completion.completion_time,
      completed_at: completion.completed_at
    };
  });

  console.log(`Returning ${assessmentsWithStatus.length} assessments with status`);
  return assessmentsWithStatus;
}

// Get assessment with completion status
export async function getAssessmentWithStatus(assessmentId: string, userId: string): Promise<AssessmentWithStatus | null> {
  // Get the assessment
  const { data: assessment, error: assessmentError } = await supabase
    .from('assessments')
    .select('*')
    .eq('id', assessmentId)
    .single();

  if (assessmentError) {
    if (assessmentError.code === 'PGRST116') {
      return null;
    }
    throw new Error(`Error fetching assessment: ${assessmentError.message}`);
  }

  // Get the completion status
  const { data: completion, error: completionError } = await supabase
    .from('assessment_completions')
    .select('*')
    .eq('assessment_id', assessmentId)
    .eq('user_id', userId)
    .maybeSingle();

  if (completionError) {
    throw new Error(`Error fetching assessment completion: ${completionError.message}`);
  }

  if (!completion) {
    return {
      ...assessment,
      status: 'not_started'
    };
  }

  return {
    ...assessment,
    status: completion.status === 'completed' ? 'completed' : 'in_progress',
    score: completion.score,
    completion_time: completion.completion_time,
    completed_at: completion.completed_at
  };
}

// Start assessment for a prospect
export async function startAssessment(
  assessmentId: string, 
  userId: string
): Promise<AssessmentCompletion> {
  // Check if there's already a record
  const { data: existingCompletion } = await supabase
    .from('assessment_completions')
    .select('*')
    .eq('assessment_id', assessmentId)
    .eq('user_id', userId)
    .maybeSingle();

  // If there's already a completed assessment, return it
  if (existingCompletion?.status === 'completed') {
    return existingCompletion;
  }
  
  // If there's an in-progress assessment, return it
  if (existingCompletion?.status === 'started') {
    return existingCompletion;
  }

  // Otherwise create a new record
  const newCompletion: AssessmentCompletionInsert = {
    assessment_id: assessmentId,
    user_id: userId,
    status: 'started'
  };

  const { data, error } = await supabase
    .from('assessment_completions')
    .insert(newCompletion)
    .select()
    .single();

  if (error) {
    throw new Error(`Error starting assessment: ${error.message}`);
  }

  return data;
}

// Complete assessment for a prospect
export async function completeAssessment(
  assessmentId: string,
  userId: string,
  score: number,
  completionTime: number
): Promise<AssessmentCompletion> {
  const completionData: AssessmentCompletionUpdate = {
    status: 'completed',
    score,
    completion_time: completionTime,
    completed_at: new Date().toISOString()
  };

  const { data, error } = await supabase
    .from('assessment_completions')
    .update(completionData)
    .eq('assessment_id', assessmentId)
    .eq('user_id', userId)
    .select()
    .single();

  if (error) {
    throw new Error(`Error completing assessment: ${error.message}`);
  }

  // Update the assessment stats
  await updateAssessmentStats(assessmentId);

  return data;
}

// Abandon assessment
export async function abandonAssessment(
  assessmentId: string,
  userId: string
): Promise<void> {
  const { error } = await supabase
    .from('assessment_completions')
    .update({ status: 'abandoned' })
    .eq('assessment_id', assessmentId)
    .eq('user_id', userId);

  if (error) {
    throw new Error(`Error abandoning assessment: ${error.message}`);
  }
}

// Get assessment questions
export async function getAssessmentQuestions(assessmentId: string): Promise<AssessmentQuestion[]> {
  const { data, error } = await supabase
    .from('assessment_questions')
    .select('*')
    .eq('assessment_id', assessmentId)
    .order('created_at', { ascending: true });

  if (error) {
    throw new Error(`Error fetching assessment questions: ${error.message}`);
  }

  return data;
}

// Update assessment stats based on completions
async function updateAssessmentStats(assessmentId: string): Promise<void> {
  const adminClient = createAdminClient();
  
  // Get all completions for this assessment
  const { data: completions, error: completionsError } = await adminClient
    .from('assessment_completions')
    .select('*')
    .eq('assessment_id', assessmentId)
    .eq('status', 'completed');

  if (completionsError) {
    throw new Error(`Error fetching completions: ${completionsError.message}`);
  }

  const completionCount = completions.length;
  
  // Calculate average score
  const totalScore = completions.reduce((sum, completion) => sum + (completion.score || 0), 0);
  const avgScore = completionCount > 0 ? Math.round(totalScore / completionCount) : 0;

  // Update the assessment
  const { error: updateError } = await adminClient
    .from('assessments')
    .update({
      completions: completionCount,
      avg_score: avgScore
    })
    .eq('id', assessmentId);

  if (updateError) {
    throw new Error(`Error updating assessment stats: ${updateError.message}`);
  }
} 