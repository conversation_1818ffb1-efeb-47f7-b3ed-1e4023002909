"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { 
  ChevronRight, ChevronDown, Play, CheckCircle, 
  BookOpen, Lock, Clock, Menu, X, FileText, ChevronLeft,
  FileQuestion, Video
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface Lesson {
  id: string;
  title: string;
  description?: string | null;
  duration_minutes?: number | null;
  order_index: number;
  is_quiz?: boolean;
  lesson_type?: string;
  total_questions?: number;
  progress?: {
    status: 'not_started' | 'in_progress' | 'completed';
    progressPercentage: number;
  };
}

interface ModuleSidebarProps {
  moduleId: string;
  moduleName: string;
  currentLessonId?: string;
  lessons: Lesson[];
  className?: string;
}

export function ModuleSidebar({ 
  moduleId, 
  moduleName, 
  currentLessonId, 
  lessons,
  className 
}: ModuleSidebarProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  
  // Close mobile sidebar when navigating to a new lesson
  useEffect(() => {
    setMobileSidebarOpen(false);
  }, [currentLessonId]);
  
  // Close mobile sidebar on larger screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setMobileSidebarOpen(false);
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  // Get the current lesson index
  const currentIndex = lessons.findIndex(lesson => lesson.id === currentLessonId);
  
  // Determine next and previous lessons
  const previousLesson = currentIndex > 0 ? lessons[currentIndex - 1] : null;
  const nextLesson = currentIndex < lessons.length - 1 ? lessons[currentIndex + 1] : null;

  return (
    <>
      {/* Mobile menu toggle with fixed positioning */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button 
          variant="outline" 
          size="icon" 
          onClick={() => setMobileSidebarOpen(true)}
          className="bg-background/80 backdrop-blur-sm shadow-md rounded-full h-10 w-10"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </div>
      
      {/* Mobile overlay with transition */}
      {mobileSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden backdrop-blur-sm transition-opacity duration-300"
          onClick={() => setMobileSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar with improved transitions */}
      <aside 
        className={cn(
          "fixed top-0 left-0 z-50 h-full bg-card border-r flex flex-col lg:relative lg:z-0 transition-all duration-300 ease-in-out shadow-lg lg:shadow-none",
          sidebarOpen ? "w-80 sm:w-96 md:w-[350px]" : "w-16",
          mobileSidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          className
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header with module name */}
          <div className="p-4 border-b flex items-center justify-between bg-card/90 backdrop-blur-sm sticky top-0 z-10">
            {sidebarOpen ? (
              <div className="flex-1 min-w-0">
                <h2 className="font-semibold truncate text-primary">{moduleName}</h2>
                <p className="text-xs text-muted-foreground mt-0.5">
                  {lessons.length} {lessons.length === 1 ? 'lesson' : 'lessons'}
                </p>
              </div>
            ) : (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="w-8 h-8 flex items-center justify-center rounded-full bg-primary/10 text-primary font-semibold text-sm">
                M
              </span>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p>{moduleName}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            
            <div className="flex items-center gap-2">
              {/* Mobile close button */}
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => setMobileSidebarOpen(false)}
                className="lg:hidden text-muted-foreground hover:text-foreground"
              >
                <X className="h-5 w-5" />
                <span className="sr-only">Close menu</span>
              </Button>
              
              {/* Toggle sidebar width button (desktop only) */}
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="hidden lg:flex text-muted-foreground hover:text-foreground"
                aria-label={sidebarOpen ? "Collapse sidebar" : "Expand sidebar"}
              >
                <ChevronLeft className={cn("h-5 w-5 transition-transform", !sidebarOpen && "rotate-180")} />
              </Button>
            </div>
          </div>
          
          {/* Lessons list */}
          <ScrollArea className="flex-1">
            <div className="px-2 py-3">
              {lessons.map((lesson, index) => {
                const status = lesson.progress?.status || 'not_started';
                const isCompleted = status === 'completed';
                const isInProgress = status === 'in_progress';
                const isCurrent = lesson.id === currentLessonId;
                const progressPercentage = lesson.progress?.progressPercentage || 0;
                
                // Icon based on status
                const StatusIcon = isCompleted
                  ? CheckCircle
                  : isInProgress
                    ? Video
                    : lesson.is_quiz
                      ? FileQuestion
                      : Video;
                
                return (
                  <Link 
                    key={lesson.id} 
                    href={`/prospect/training/${moduleId}/lessons/${lesson.id}`}
                    className={cn(
                      "flex items-start gap-3 p-2.5 rounded-lg transition-all mb-2",
                      isCurrent 
                        ? "bg-primary/10 text-primary ring-1 ring-primary/20" 
                        : "hover:bg-muted/80",
                      !sidebarOpen && "justify-center"
                    )}
                  >
                    {!sidebarOpen ? (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                    <div className={cn(
                      "flex items-center justify-center rounded-md w-7 h-7 shrink-0",
                      isCompleted ? "text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400" :
                      isInProgress ? "text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400" :
                              isCurrent ? "text-primary bg-primary/10" :
                              lesson.is_quiz ? "text-purple-600 bg-purple-100 dark:bg-purple-900/30 dark:text-purple-400" :
                              "text-amber-600 bg-amber-100 dark:bg-amber-900/30 dark:text-amber-400"
                            )}>
                              {isCurrent ? <Play className="h-4 w-4" /> : 
                               lesson.is_quiz ? <FileQuestion className="h-4 w-4" /> :
                               isCompleted ? <CheckCircle className="h-4 w-4" /> :
                               <Video className="h-4 w-4" />}
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right">
                            <p>{index + 1}. {lesson.title}</p>
                            {lesson.is_quiz && (
                              <p className="text-xs text-purple-600">Quiz</p>
                            )}
                            {lesson.duration_minutes && (
                              <p className="text-xs text-muted-foreground">{lesson.duration_minutes} min</p>
                            )}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    ) : (
                      <>
                        <div className={cn(
                          "flex items-center justify-center rounded-md w-7 h-7 shrink-0 mt-0.5",
                          isCompleted ? "text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400" :
                          isInProgress ? "text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400" :
                          isCurrent ? "text-primary bg-primary/10" :
                          lesson.is_quiz ? "text-purple-600 bg-purple-100 dark:bg-purple-900/30 dark:text-purple-400" :
                      "text-amber-600 bg-amber-100 dark:bg-amber-900/30 dark:text-amber-400"
                    )}>
                          {isCurrent ? <Play className="h-4 w-4" /> : 
                           lesson.is_quiz ? <FileQuestion className="h-4 w-4" /> :
                           isCompleted ? <CheckCircle className="h-4 w-4" /> :
                           <Video className="h-4 w-4" />}
                    </div>
                    
                      <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between gap-2">
                            <div className="font-medium text-sm leading-tight pr-2 line-clamp-2">
                            {index + 1}. {lesson.title}
                          </div>
                          {isCompleted && (
                              <Badge variant="outline" className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 border-green-200 dark:border-green-800 shrink-0 mt-0.5">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              <span className="text-xs">Done</span>
                            </Badge>
                          )}
                        </div>
                        
                          <div className="flex items-center text-xs text-muted-foreground mt-1">
                            {lesson.duration_minutes && (
                              <div className="flex items-center mr-3">
                            <Clock className="h-3 w-3 mr-1" />
                            {lesson.duration_minutes} min
                          </div>
                        )}
                        
                            {lesson.is_quiz && (
                              <div className="flex items-center mr-3">
                                <FileQuestion className="h-3 w-3 mr-1" />
                                Quiz
                                {lesson.total_questions ? ` (${lesson.total_questions})` : ''}
                              </div>
                            )}
                            
                            {isInProgress && (
                              <Badge variant="secondary" className="h-5 text-xs px-1.5 bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 border-none">
                                In progress
                              </Badge>
                            )}
                          </div>
                          
                          {(isInProgress || isCompleted) && (
                            <div className="mt-2">
                              <Progress 
                                value={progressPercentage} 
                                className={cn(
                                  "h-1.5 rounded-full", 
                                  isCompleted 
                                    ? "bg-green-100 dark:bg-green-900/20 [&>div]:bg-green-500 dark:[&>div]:bg-green-400" 
                                    : "bg-blue-100 dark:bg-blue-900/20 [&>div]:bg-blue-500 dark:[&>div]:bg-blue-400"
                                )}
                              />
                          </div>
                        )}
                      </div>
                      </>
                    )}
                  </Link>
                );
              })}
            </div>
          </ScrollArea>
          
          {/* Navigation buttons for mobile */}
          <div className="p-4 border-t bg-card/90 backdrop-blur-sm lg:hidden">
            <div className="flex items-center justify-between gap-2">
              {previousLesson ? (
                <Button 
                  variant="outline" 
                  size="sm" 
                  asChild
                  className="flex-1"
                >
                  <Link href={`/prospect/training/${moduleId}/lessons/${previousLesson.id}`}>
                    <ChevronLeft className="h-4 w-4 mr-1.5" />
                    Previous
                  </Link>
                </Button>
              ) : (
                <Button variant="outline" size="sm" disabled className="flex-1">
                  <ChevronLeft className="h-4 w-4 mr-1.5" />
                  Previous
                </Button>
              )}
              
              <Button 
                variant="outline" 
                size="sm" 
                asChild
                className="flex-1"
              >
                <Link href={`/prospect/training/${moduleId}`}>
                  <BookOpen className="h-4 w-4 mr-1.5" />
                  Module
                </Link>
              </Button>
              
              {nextLesson ? (
                <Button 
                  variant="default" 
                  size="sm" 
                  asChild
                  className="flex-1"
                >
                  <Link href={`/prospect/training/${moduleId}/lessons/${nextLesson.id}`}>
                    Next
                    <ChevronRight className="h-4 w-4 ml-1.5" />
                  </Link>
                </Button>
              ) : (
                <Button variant="default" size="sm" disabled className="flex-1">
                  Next
                  <ChevronRight className="h-4 w-4 ml-1.5" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </aside>
    </>
  );
} 