/** @type {import('next').NextConfig} */
import path from 'path'

const nextConfig = {
  // Build configuration - ESLint and TypeScript checking disabled for faster builds
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // OPTIMIZED: Enable Next.js image optimization for better performance
  images: {
    // Enable optimization for better performance and smaller bundle sizes
    unoptimized: false,
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // Add domains for external images if needed
    domains: [],
    // Enable remote patterns for Supabase storage
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
      },
    ],
  },

  // OPTIMIZED: Reduce server external packages for better bundling
  serverExternalPackages: [],

  experimental: {
    serverActions: {
      bodySizeLimit: '10mb', // Reduced from 50mb for better performance
    },
    // Performance optimizations
    optimizeCss: true,
    optimizeServerReact: true,
    // Enable additional optimizations
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  // Performance optimizations
  compress: true,
  poweredByHeader: false,

  // OPTIMIZED: Enhanced bundle optimization
  webpack: (config, { isServer, dev }) => {
    // Optimize imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(process.cwd()),
    }

    // Production optimizations
    if (!dev) {
      // Tree shaking optimization
      config.optimization = {
        ...config.optimization,
        usedExports: true,
        sideEffects: false,
      }
    }

    // Server-side fallbacks
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    return config
  },

  // OPTIMIZED: Enhanced headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300', // 5 minutes instead of 1 year
          },
        ],
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300', // 5 minutes instead of 1 year
          },
        ],
      },
    ]
  },
}

export default nextConfig
