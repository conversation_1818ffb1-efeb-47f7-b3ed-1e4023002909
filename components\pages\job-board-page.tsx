"use client"

import {
  <PERSON>riefcase,
  Building,
  Clock,
  DollarSign,
  Filter,
  MapPin,
  Search,
  Star,
  Calendar,
  ArrowUpDown,
} from "lucide-react"
import { useState, useEffect } from "react"
import { useTheme } from "next-themes"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"

interface JobProps {
  id: string
  title: string
  company: string
  companyLogo: string | null
  location: string
  jobType: string
  salary: string
  postedDate: string
  description: string
  skills: string[]
  isNew: boolean
  deadline: string | null
}

export function JobBoardPage({ jobs = [] }: { jobs?: JobProps[] }) {
  const [searchTerm, setSearchTerm] = useState("")
  const [jobTypeFilter, setJobTypeFilter] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("relevance")
  const [mounted, setMounted] = useState(false)
  const { theme } = useTheme()
  const [isLoading, setIsLoading] = useState(true)

  // Simulate loading state
  useEffect(() => {
    setMounted(true)
    const timer = setTimeout(() => setIsLoading(false), 800)
    return () => clearTimeout(timer)
  }, [])

  // Sort jobs based on selected option
  const sortJobs = (jobs: JobProps[]) => {
    switch (sortBy) {
      case "recent":
        return [...jobs].sort((a, b) => {
          const dateA = a.postedDate.replace("Posted ", "").replace(" days ago", "")
          const dateB = b.postedDate.replace("Posted ", "").replace(" days ago", "")
          return parseInt(dateA) - parseInt(dateB)
        })
      case "salary-high":
        return [...jobs].sort((a, b) => {
          const salaryA = a.salary.includes("-")
            ? parseInt(a.salary.split("-")[1].replace(/\D/g, ""))
            : parseInt(a.salary.replace(/\D/g, "")) || 0
          const salaryB = b.salary.includes("-")
            ? parseInt(b.salary.split("-")[1].replace(/\D/g, ""))
            : parseInt(b.salary.replace(/\D/g, "")) || 0
          return salaryB - salaryA
        })
      case "salary-low":
        return [...jobs].sort((a, b) => {
          const salaryA = a.salary.includes("-")
            ? parseInt(a.salary.split("-")[0].replace(/\D/g, ""))
            : parseInt(a.salary.replace(/\D/g, "")) || 0
          const salaryB = b.salary.includes("-")
            ? parseInt(b.salary.split("-")[0].replace(/\D/g, ""))
            : parseInt(b.salary.replace(/\D/g, "")) || 0
          return salaryA - salaryB
        })
      default:
        return jobs
    }
  }

  // Filter jobs based on search term and job type
  const filteredJobs = sortJobs(jobs.filter(job => {
    // Search filter
    const searchLower = searchTerm.toLowerCase()
    const matchesSearch = searchTerm === "" ||
      job.title.toLowerCase().includes(searchLower) ||
      job.company.toLowerCase().includes(searchLower) ||
      job.description.toLowerCase().includes(searchLower) ||
      job.skills.some(skill => skill.toLowerCase().includes(searchLower))

    // Job type filter
    const matchesJobType = jobTypeFilter.length === 0 ||
      jobTypeFilter.includes(job.jobType.toLowerCase())

    return matchesSearch && matchesJobType
  }))

  // Handle job type filter change
  const handleJobTypeChange = (type: string) => {
    setJobTypeFilter(prev => {
      if (prev.includes(type)) {
        return prev.filter(t => t !== type)
      } else {
        return [...prev, type]
      }
    })
  }

  if (!mounted) return null

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Job Board</h1>
        <p className="text-gray-600">Find your next opportunity</p>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search jobs, companies, or skills..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full md:w-48">
              <ArrowUpDown className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="salary-high">Salary: High to Low</SelectItem>
              <SelectItem value="salary-low">Salary: Low to High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Job Type Filters */}
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium text-gray-700 mr-2">Job Type:</span>
          {["full-time", "part-time", "contract", "remote"].map((type) => (
            <div key={type} className="flex items-center space-x-2">
              <Checkbox
                id={type}
                checked={jobTypeFilter.includes(type)}
                onCheckedChange={() => handleJobTypeChange(type)}
              />
              <label
                htmlFor={type}
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 capitalize"
              >
                {type.replace("-", " ")}
              </label>
            </div>
          ))}
        </div>
      </div>

      {/* Job Results */}
      <div className="space-y-4">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 5 }).map((_, i) => (
            <Card key={i} className="p-6">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                  <Skeleton className="h-10 w-24" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-24" />
                </div>
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            </Card>
          ))
        ) : filteredJobs.length === 0 ? (
          <Card className="p-8 text-center">
            <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No jobs found</h3>
            <p className="text-gray-600">Try adjusting your search criteria or filters.</p>
          </Card>
        ) : (
          filteredJobs.map((job) => (
            <Card key={job.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4 flex-1">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={job.companyLogo || ""} alt={job.company} />
                      <AvatarFallback>
                        {job.company.split(" ").map(word => word[0]).join("").slice(0, 2)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-lg font-semibold text-gray-900">{job.title}</h3>
                        {job.isNew && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            New
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600 mb-2">{job.company}</p>
                      <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {job.salary}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {job.postedDate}
                        </div>
                        {job.deadline && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            Deadline: {job.deadline}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <Button>Apply Now</Button>
                </div>

                <p className="text-gray-700 mb-4 line-clamp-2">{job.description}</p>

                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline" className="capitalize">
                      {job.jobType.replace("-", " ")}
                    </Badge>
                    {job.skills.slice(0, 3).map((skill) => (
                      <Badge key={skill} variant="secondary">
                        {skill}
                      </Badge>
                    ))}
                    {job.skills.length > 3 && (
                      <Badge variant="secondary">
                        +{job.skills.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Results Summary */}
      {!isLoading && (
        <div className="mt-8 text-center text-gray-600">
          Showing {filteredJobs.length} of {jobs.length} jobs
        </div>
      )}
    </div>
  )
}
