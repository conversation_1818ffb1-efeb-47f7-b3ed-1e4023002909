import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id: courseId } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Get quiz configurations for lessons in this course
    const { data: quizConfigs, error: quizError } = await adminClient
      .from('ai_quiz_configs')
      .select(`
        *,
        lesson:lessons(
          id,
          name,
          module:modules(
            id,
            name,
            course_id
          )
        )
      `)
      .eq('lesson.module.course_id', courseId);

    if (quizError) {
      console.error('Error fetching quiz configs:', quizError);
      return NextResponse.json(
        { error: quizError.message || 'Failed to fetch quiz configurations' },
        { status: 500 }
      );
    }

    // Filter out quizzes where the lesson doesn't belong to this course
    const courseQuizzes = (quizConfigs || []).filter(quiz => 
      quiz.lesson?.module?.course_id === courseId
    );

    return NextResponse.json({
      quizzes: courseQuizzes || []
    });

  } catch (error: any) {
    console.error('Course quizzes GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
