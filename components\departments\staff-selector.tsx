"use client"

import { useState, useEffect } from "react"
import { Check, ChevronsUpDown, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Label } from "@/components/ui/label"

interface StaffMember {
  id: string
  employment_id: string
  full_name: string
  email: string
  avatar_url?: string
  role: string
  job_title?: string
  current_department_id: string
  current_department_name: string
}

interface StaffSelectorProps {
  orgSlug: string
  selectedStaff: string[]
  onStaffChange: (staffIds: string[]) => void
  selectedHead?: string
  onHeadChange: (headId?: string) => void
  excludeDepartmentId?: string
}

export function StaffSelector({
  orgSlug,
  selectedStaff,
  onStaffChange,
  selectedHead,
  onHeadChange,
  excludeDepartmentId,
}: StaffSelectorProps) {
  const [staff, setStaff] = useState<StaffMember[]>([])
  const [loading, setLoading] = useState(true)
  const [headOpen, setHeadOpen] = useState(false)
  const [staffOpen, setStaffOpen] = useState(false)

  useEffect(() => {
    fetchStaff()
  }, [orgSlug])

  const fetchStaff = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/org/${orgSlug}/staff`)
      if (!response.ok) {
        throw new Error('Failed to fetch staff')
      }
      const data = await response.json()
      setStaff(data.staff || [])
    } catch (error) {
      console.error('Error fetching staff:', error)
      setStaff([])
    } finally {
      setLoading(false)
    }
  }

  // Filter available staff (exclude current department if specified)
  const availableStaff = staff.filter(member => {
    return excludeDepartmentId ? member.current_department_id !== excludeDepartmentId : true
  })

  const selectedHeadMember = selectedHead ? staff.find(s => s.employment_id === selectedHead) : undefined
  const selectedStaffMembers = staff.filter(s => selectedStaff.includes(s.employment_id))

  const handleStaffToggle = (employmentId: string) => {
    const newSelection = selectedStaff.includes(employmentId)
      ? selectedStaff.filter(id => id !== employmentId)
      : [...selectedStaff, employmentId]
    onStaffChange(newSelection)
  }

  const removeStaffMember = (employmentId: string) => {
    onStaffChange(selectedStaff.filter(id => id !== employmentId))
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  if (loading) {
    return <div className="text-sm text-muted-foreground">Loading staff...</div>
  }

  return (
    <div className="space-y-6">
      {/* Department Head Combobox */}
      <div className="space-y-2">
        <Label>Department Head</Label>
        <Popover open={headOpen} onOpenChange={setHeadOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={headOpen}
              className="w-full justify-between"
            >
              {selectedHeadMember ? (
                <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={selectedHeadMember.avatar_url} />
                    <AvatarFallback className="text-xs">
                      {getInitials(selectedHeadMember.full_name)}
                    </AvatarFallback>
                  </Avatar>
                  <span>{selectedHeadMember.full_name}</span>
                </div>
              ) : (
                "Select department head..."
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <Command>
              <CommandInput placeholder="Search staff members..." />
              <CommandList>
                <CommandEmpty>No staff members found.</CommandEmpty>
                <CommandGroup>
                  {selectedHead && (
                    <CommandItem
                      onSelect={() => {
                        onHeadChange(undefined)
                        setHeadOpen(false)
                      }}
                    >
                      <X className="mr-2 h-4 w-4" />
                      Clear selection
                    </CommandItem>
                  )}
                  {availableStaff.map((member) => (
                    <CommandItem
                      key={member.employment_id}
                      value={`${member.full_name} ${member.email} ${member.job_title || ''}`}
                      onSelect={() => {
                        onHeadChange(member.employment_id)
                        setHeadOpen(false)
                      }}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedHead === member.employment_id ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex items-center gap-2 flex-1">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={member.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {getInitials(member.full_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium">{member.full_name}</div>
                          <div className="text-xs text-muted-foreground">{member.email}</div>
                          {member.job_title && (
                            <div className="text-xs text-muted-foreground">{member.job_title}</div>
                          )}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {member.role}
                        </Badge>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      {/* Staff Members Multi-Select Combobox */}
      <div className="space-y-2">
        <Label>Staff Members</Label>
        <Popover open={staffOpen} onOpenChange={setStaffOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={staffOpen}
              className="w-full justify-between"
            >
              {selectedStaff.length > 0 ? (
                `${selectedStaff.length} staff member${selectedStaff.length > 1 ? 's' : ''} selected`
              ) : (
                "Select staff members..."
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <Command>
              <CommandInput placeholder="Search staff members..." />
              <CommandList>
                <CommandEmpty>No staff members found.</CommandEmpty>
                <CommandGroup>
                  {availableStaff.map((member) => (
                    <CommandItem
                      key={member.employment_id}
                      value={`${member.full_name} ${member.email} ${member.job_title || ''}`}
                      onSelect={() => handleStaffToggle(member.employment_id)}
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          selectedStaff.includes(member.employment_id) ? "opacity-100" : "opacity-0"
                        )}
                      />
                      <div className="flex items-center gap-2 flex-1">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={member.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {getInitials(member.full_name)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="font-medium">{member.full_name}</div>
                          <div className="text-xs text-muted-foreground">{member.email}</div>
                          {member.job_title && (
                            <div className="text-xs text-muted-foreground">{member.job_title}</div>
                          )}
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {member.role}
                        </Badge>
                      </div>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {/* Selected Staff Display */}
        {selectedStaffMembers.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium">Selected Staff ({selectedStaffMembers.length})</div>
            <div className="space-y-2">
              {selectedStaffMembers.map((member) => (
                <div key={member.employment_id} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={member.avatar_url} />
                    <AvatarFallback className="text-xs">
                      {getInitials(member.full_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{member.full_name}</div>
                    <div className="text-xs text-muted-foreground">{member.email}</div>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {member.role}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeStaffMember(member.employment_id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
