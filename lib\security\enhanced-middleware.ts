/**
 * Enhanced Security Middleware
 * Provides pre-configured security middleware for different types of API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { withApiSecurity, type ApiSecurityOptions } from './index';
import { RATE_LIMITS } from './rate-limiting';
import { withAuthProtection } from '@/lib/auth/middleware';
import { requirePlatformAdmin } from '@/lib/auth/middleware';
import { withApi<PERSON>rror<PERSON>and<PERSON> } from '@/lib/api-error-handler';
import { createAuthErrorResponse } from '@/lib/auth';

/**
 * Security middleware for public API endpoints
 * - Basic rate limiting
 * - Security headers
 * - Input sanitization
 */
export function withPublicSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.PUBLIC,
    maxRequestSize: 1024 * 1024, // 1MB
    ...options
  };

  return withApiSecurity(handler, securityOptions);
}

/**
 * Security middleware for authenticated API endpoints
 * - Authentication required
 * - Standard rate limiting
 * - Input validation
 * - Security headers
 */
export function withAuthenticatedSecurity(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.DEFAULT,
    maxRequestSize: 10 * 1024 * 1024, // 10MB
    ...options
  };

  return withApiSecurity(
    withAuthProtection(handler),
    securityOptions
  );
}

/**
 * Security middleware for admin API endpoints
 * - Platform admin authentication required
 * - Higher rate limits
 * - Enhanced logging
 * - Security headers
 */
export function withAdminSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.ADMIN,
    maxRequestSize: 50 * 1024 * 1024, // 50MB
    ...options
  };

  const adminHandler = async (request: NextRequest): Promise<NextResponse> => {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return handler(request);
  };

  return withApiSecurity(adminHandler, securityOptions);
}

/**
 * Security middleware for file upload endpoints
 * - Authentication required
 * - Strict rate limiting for uploads
 * - Large file size limits
 * - Skip input sanitization for multipart data
 */
export function withUploadSecurity(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.UPLOAD,
    maxRequestSize: 100 * 1024 * 1024, // 100MB
    skipInputSanitization: true, // Skip for multipart form data
    ...options
  };

  return withApiSecurity(
    withAuthProtection(handler),
    securityOptions
  );
}

/**
 * Security middleware for profile management endpoints
 * - Authentication required
 * - Profile-specific rate limiting
 * - Input validation
 * - Security headers
 */
export function withProfileSecurity(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.PROFILE,
    maxRequestSize: 5 * 1024 * 1024, // 5MB
    ...options
  };

  return withApiSecurity(
    withAuthProtection(handler),
    securityOptions
  );
}

/**
 * Security middleware for database query endpoints
 * - Authentication required
 * - Database-specific rate limiting
 * - Input validation
 * - Query sanitization
 */
export function withDatabaseSecurity(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.DATABASE,
    maxRequestSize: 2 * 1024 * 1024, // 2MB
    ...options
  };

  return withApiSecurity(
    withAuthProtection(handler),
    securityOptions
  );
}

/**
 * Security middleware for authentication endpoints
 * - Strict rate limiting to prevent brute force
 * - Enhanced logging
 * - Security headers
 */
export function withAuthSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.AUTH,
    maxRequestSize: 1024 * 1024, // 1MB
    ...options
  };

  return withApiSecurity(handler, securityOptions);
}

/**
 * Comprehensive security wrapper with error handling
 * Combines security middleware with standardized error handling
 */
export function withComprehensiveSecurity(
  handler: () => Promise<any>,
  securityType: 'public' | 'authenticated' | 'admin' | 'upload' | 'profile' | 'database' | 'auth' = 'authenticated',
  options: Partial<ApiSecurityOptions> = {}
) {
  const wrappedHandler = withApiErrorHandler(handler);

  switch (securityType) {
    case 'public':
      return withPublicSecurity(wrappedHandler, options);
    case 'admin':
      return withAdminSecurity(wrappedHandler, options);
    case 'upload':
      return withUploadSecurity(wrappedHandler as any, options);
    case 'profile':
      return withProfileSecurity(wrappedHandler as any, options);
    case 'database':
      return withDatabaseSecurity(wrappedHandler as any, options);
    case 'auth':
      return withAuthSecurity(wrappedHandler, options);
    case 'authenticated':
    default:
      return withAuthenticatedSecurity(wrappedHandler as any, options);
  }
}

/**
 * Development mode security (higher limits for testing)
 */
export function withDevelopmentSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: Partial<ApiSecurityOptions> = {}
) {
  // Only use in development
  if (process.env.NODE_ENV !== 'development') {
    throw new Error('Development security middleware should only be used in development mode');
  }

  const securityOptions: ApiSecurityOptions = {
    rateLimit: RATE_LIMITS.DEVELOPMENT,
    maxRequestSize: 100 * 1024 * 1024, // 100MB
    ...options
  };

  return withApiSecurity(handler, securityOptions);
}
