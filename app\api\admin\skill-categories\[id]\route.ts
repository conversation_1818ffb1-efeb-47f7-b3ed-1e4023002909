import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    const { data: category, error } = await supabase
      .from('skill_categories')
      .select(`
        *,
        skills(count)
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Error fetching skill category:', error);
      return NextResponse.json(
        { error: 'Failed to fetch skill category' },
        { status: 500 }
      );
    }

    if (!category) {
      return NextResponse.json(
        { error: 'Skill category not found' },
        { status: 404 }
      );
    }

    // Transform the data to include skill count
    const transformedCategory = {
      ...category,
      skill_count: Array.isArray(category.skills) ? category.skills.length : 0
    };

    return NextResponse.json(transformedCategory);

  } catch (error) {
    console.error('Unexpected error in skill category GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.color_code) {
      return NextResponse.json(
        { error: 'Name and color code are required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('skill_categories')
      .update({
        name: body.name,
        description: body.description || null,
        color_code: body.color_code,
        icon: body.icon || 'folder',
        is_active: body.is_active !== undefined ? body.is_active : true,
        sort_order: body.sort_order || 0,
      })
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating skill category:', error);
      return NextResponse.json(
        { error: 'Failed to update skill category' },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Skill category not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Unexpected error in skill category PUT:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Check if category has skills
    const { data: skills, error: skillsError } = await supabase
      .from('skills')
      .select('id')
      .eq('category_id', params.id)
      .limit(1);

    if (skillsError) {
      console.error('Error checking skills:', skillsError);
      return NextResponse.json(
        { error: 'Failed to check category usage' },
        { status: 500 }
      );
    }

    if (skills && skills.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category that contains skills' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('skill_categories')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting skill category:', error);
      return NextResponse.json(
        { error: 'Failed to delete skill category' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Unexpected error in skill category DELETE:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
