"use client"

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import styles from './topline-sidebar.module.css'
import {
  Home,
  User,
  BookOpen,
  ShoppingCart,
  Phone,
  ClipboardCheck,
  FileCheck,
  Briefcase,
  Calendar,
  Settings,
  HelpCircle,
  Pin,
  PinOff,
  ChevronLeft,
  ChevronRight,
  Menu,
  X,
  ChevronDown
} from 'lucide-react'

interface MenuItem {
  title: string
  href: string
  icon: React.ElementType
  badge?: number
}

interface MenuSection {
  title: string
  items: MenuItem[]
}

const menuSections: MenuSection[] = [
  {
    title: "Main Menu",
    items: [
      { title: "Home", href: "/individual", icon: Home },
      { title: "Community (Beta)", href: "/individual/community", icon: User },
      { title: "Topline OS", href: "/individual/topline-os", icon: Settings },
    ]
  },
  {
    title: "Outreach",
    items: [
      { title: "Leads", href: "/individual/leads", icon: User },
      { title: "Inbox", href: "/individual/inbox", icon: ClipboardCheck },
      { title: "Booking", href: "/individual/booking", icon: Calendar },
    ]
  },
  {
    title: "Sourcing",
    items: [
      { title: "Search", href: "/individual/search", icon: BookOpen },
      { title: "Shortlist", href: "/individual/shortlist", icon: FileCheck },
      { title: "Projects", href: "/individual/projects", icon: Briefcase },
    ]
  },
  {
    title: "Settings",
    items: [
      { title: "Integrations", href: "/individual/integrations", icon: Settings },
      { title: "Help & Support", href: "/individual/help", icon: HelpCircle },
      { title: "Settings", href: "/individual/settings", icon: Settings },
    ]
  }
]

interface ToplineSidebarProps {
  children?: React.ReactNode
}

export function ToplineSidebar({ children }: ToplineSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(true)
  const [isPinned, setIsPinned] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const [showContextDropdown, setShowContextDropdown] = useState(false)
  const pathname = usePathname()

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowContextDropdown(false)
    }

    if (showContextDropdown) {
      document.addEventListener('click', handleClickOutside)
      return () => document.removeEventListener('click', handleClickOutside)
    }
  }, [showContextDropdown])

  // Load pin state from localStorage
  useEffect(() => {
    const savedPinState = localStorage.getItem('topline-sidebar-pinned')
    if (savedPinState !== null) {
      setIsPinned(JSON.parse(savedPinState))
      setIsCollapsed(!JSON.parse(savedPinState))
    }
  }, [])

  // Save pin state to localStorage
  useEffect(() => {
    localStorage.setItem('topline-sidebar-pinned', JSON.stringify(isPinned))
    setIsCollapsed(!isPinned && !isHovered)
  }, [isPinned, isHovered])

  const handlePinToggle = () => {
    setIsPinned(!isPinned)
  }

  const handleTriggerToggle = () => {
    if (isPinned) {
      setIsPinned(false)
    } else {
      setIsPinned(true)
    }
  }

  const handleMouseEnter = () => {
    if (!isPinned) {
      setIsHovered(true)
    }
  }

  const handleMouseLeave = () => {
    if (!isPinned) {
      setIsHovered(false)
    }
  }

  const isExpanded = isPinned || isHovered

  return (
    <div className="flex h-screen bg-gray-50" style={{ fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif' }}>
      {/* Sidebar */}
      <div
        className={`fixed left-0 top-0 h-full bg-white transition-all duration-300 ease-in-out z-50 ${
          isExpanded ? 'w-64 shadow-xl border-r border-gray-200' : 'w-16 shadow-sm border-r border-gray-200'
        }`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        style={{ backgroundColor: '#ffffff', borderColor: '#e5e7eb' }}
      >
        {/* Header with Logo */}
        <div className="flex items-center justify-between px-4 py-4 border-b" style={{ borderColor: '#f3f4f6' }}>
          {isExpanded ? (
            <div className="flex items-center space-x-3 transition-all duration-300">
              <img
                src="https://topline.com/assets/portal/logo-94740f7e.png"
                alt="Topline"
                className="h-8 w-auto"
              />
            </div>
          ) : (
            <div className="mx-auto">
              <img
                src="https://topline.com/assets/portal/logo-sm-c6e737cc.svg"
                alt="Topline"
                className="h-8 w-8"
              />
            </div>
          )}

          {isExpanded && (
            <button
              onClick={handlePinToggle}
              className="p-1.5 hover:bg-gray-100 rounded-md transition-all duration-200"
              title={isPinned ? "Unpin sidebar" : "Pin sidebar"}
              style={{ color: '#6b7280' }}
            >
              {isPinned ? (
                <PinOff className="w-4 h-4" />
              ) : (
                <Pin className="w-4 h-4" />
              )}
            </button>
          )}
        </div>

        {/* Context Switcher */}
        {isExpanded && (
          <div className="px-4 py-3 border-b" style={{ borderColor: '#f3f4f6' }}>
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  setShowContextDropdown(!showContextDropdown)
                }}
                className="w-full flex items-center justify-between px-3 py-2 text-sm rounded-md hover:bg-gray-50 transition-colors"
                style={{ color: '#374151', backgroundColor: showContextDropdown ? '#f9fafb' : 'transparent' }}
              >
                <span className="font-medium">Individual Context</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showContextDropdown ? 'rotate-180' : ''}`} />
              </button>

              {showContextDropdown && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors" style={{ color: '#374151' }}>
                      Individual Context
                    </button>
                    <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 transition-colors" style={{ color: '#6b7280' }}>
                      Organization Context
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto py-4">
          {menuSections.map((section, sectionIndex) => (
            <div key={section.title} className={sectionIndex > 0 ? "mt-6" : ""}>
              {isExpanded && (
                <div className="px-4 mb-3">
                  <h4 className="text-xs font-semibold uppercase tracking-wider" style={{ color: '#9ca3af', fontSize: '11px' }}>
                    {section.title}
                  </h4>
                </div>
              )}
              
              <div className="space-y-1 px-2">
                {section.items.map((item) => {
                  const isActive = pathname === item.href
                  return (
                    <div key={item.title} className="relative group">
                      <Link
                        href={item.href}
                        className={`flex items-center px-3 py-2 rounded-md transition-all duration-200 group relative ${
                          isActive ? 'text-white' : 'hover:bg-gray-100'
                        } ${!isExpanded ? 'justify-center' : ''}`}
                        style={{
                          backgroundColor: isActive ? '#4f46e5' : 'transparent',
                          color: isActive ? '#ffffff' : '#374151'
                        }}
                      >
                        <item.icon
                          className="w-4 h-4 flex-shrink-0"
                          style={{
                            color: isActive ? '#ffffff' : '#6b7280'
                          }}
                        />
                        {isExpanded && (
                          <span className="ml-3 text-sm font-medium">
                            {item.title}
                          </span>
                        )}
                        {isExpanded && item.badge !== undefined && item.badge > 0 && (
                          <span
                            className="ml-auto text-xs font-medium px-2 py-0.5 rounded-full"
                            style={{
                              backgroundColor: isActive ? 'rgba(255,255,255,0.2)' : '#e5e7eb',
                              color: isActive ? '#ffffff' : '#6b7280'
                            }}
                          >
                            {item.badge}
                          </span>
                        )}
                      </Link>

                      {/* Tooltip for collapsed state */}
                      {!isExpanded && (
                        <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                          {item.title}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </nav>

        {/* Footer */}
        {isExpanded && (
          <div className="border-t p-4 mt-auto" style={{ borderColor: '#f3f4f6' }}>
            <div className="text-xs space-y-1" style={{ color: '#9ca3af' }}>
              <div className="font-semibold" style={{ color: '#6b7280' }}>Topline v1.3</div>
              <div>© 2025 Topline Technologies.</div>
              <div>All rights reserved.</div>
            </div>
          </div>
        )}
      </div>

      {/* Trigger Button - Fixed position when sidebar is collapsed */}
      {!isExpanded && (
        <button
          onClick={handleTriggerToggle}
          className="fixed top-4 left-4 z-50 p-2 bg-white border border-gray-200 rounded-md shadow-sm hover:shadow-md transition-all duration-200"
          style={{ color: '#6b7280' }}
          title="Open sidebar"
        >
          <Menu className="w-5 h-5" />
        </button>
      )}

      {/* Main Content Area */}
      <div
        className={`flex-1 transition-all duration-300 ease-in-out ${
          isExpanded ? 'ml-64' : 'ml-16'
        }`}
      >
        {/* Header with trigger when expanded */}
        <div className="flex items-center p-4 bg-white border-b border-gray-200">
          <button
            onClick={handleTriggerToggle}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors mr-4"
            style={{ color: '#6b7280' }}
            title={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
          >
            {isExpanded ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
          <h1 className="text-lg font-semibold" style={{ color: '#374151' }}>
            Dashboard
          </h1>
        </div>

        {/* Page content */}
        <div className="flex-1 p-8">
          {children}
        </div>
      </div>
    </div>
  )
}
