import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    // Parse request body
    const body = await req.json();
    const { user_id, organization_id, role } = body;
    
    // Validate required fields
    if (!user_id || !organization_id || !role) {
      return NextResponse.json(
        { error: 'User ID, organization ID, and role are required' },
        { status: 400 }
      );
    }
    
    // Validate role
    const validRoles = ['admin', 'member'];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role. Must be admin or member' },
        { status: 400 }
      );
    }
    
    // Create admin client
    const adminClient = createAdminClient();
    
    // Check if membership already exists
    const { data: existingMembership } = await adminClient
      .from('organization_memberships')
      .select('id, status')
      .eq('user_id', user_id)
      .eq('organization_id', organization_id)
      .single();
    
    if (existingMembership) {
      if (existingMembership.status === 'active') {
        return NextResponse.json(
          { error: 'User is already a member of this organization' },
          { status: 400 }
        );
      } else {
        // Reactivate existing membership
        const { data: membership, error: updateError } = await adminClient
          .from('organization_memberships')
          .update({
            role,
            status: 'active',
            updated_at: new Date().toISOString()
          })
          .eq('id', existingMembership.id)
          .select()
          .single();
        
        if (updateError) {
          console.error('Error updating organization membership:', updateError);
          return NextResponse.json(
            { error: updateError.message || 'Failed to update organization membership' },
            { status: 500 }
          );
        }
        
        return NextResponse.json(membership);
      }
    }
    
    // Create new membership
    const { data: membership, error: membershipError } = await adminClient
      .from('organization_memberships')
      .insert([
        {
          user_id,
          organization_id,
          role,
          status: 'active'
        }
      ])
      .select()
      .single();
    
    if (membershipError) {
      console.error('Error creating organization membership:', membershipError);
      return NextResponse.json(
        { error: membershipError.message || 'Failed to create organization membership' },
        { status: 500 }
      );
    }
    
    return NextResponse.json(membership);
  } catch (error: any) {
    console.error('Organization memberships POST API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }
    
    const { searchParams } = new URL(req.url);
    const organizationId = searchParams.get('organization_id');
    const userId = searchParams.get('user_id');
    
    // Create admin client
    const adminClient = createAdminClient();
    
    let query = adminClient
      .from('employment_relationships')
      .select(`
        *,
        user:users!employment_relationships_user_id_fkey(
          id,
          email,
          full_name
        ),
        department:departments!employment_relationships_department_id_fkey(
          id,
          name,
          organization_id,
          organization:organizations!departments_organization_id_fkey(
            id,
            name
          )
        )
      `)
      .order('created_at', { ascending: false });
    
    if (organizationId) {
      query = query.eq('organization_id', organizationId);
    }
    
    if (userId) {
      query = query.eq('user_id', userId);
    }
    
    const { data: employments, error: employmentsError } = await query;

    if (employmentsError) {
      console.error('Error fetching employment relationships:', employmentsError);
      return NextResponse.json(
        { error: employmentsError.message || 'Failed to fetch employment relationships' },
        { status: 500 }
      );
    }

    return NextResponse.json(employments || []);
  } catch (error: any) {
    console.error('Organization memberships GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
