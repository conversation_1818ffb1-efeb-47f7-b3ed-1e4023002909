# Topline-Style Sidebar Component

A modern, collapsible sidebar component inspired by topline.com's design and functionality.

## Features

- **🎯 Smart Collapse/Expand**: Starts collapsed (64px) and expands to 240px on hover
- **📌 Pin Toggle**: Click the pin icon to keep the sidebar permanently expanded
- **💾 Persistent State**: Remembers pin state across browser sessions
- **🎨 Modern Design**: Clean, professional styling with smooth animations
- **📱 Responsive**: Adapts to different screen sizes
- **🔍 Tooltips**: Shows menu item names when collapsed
- **⚡ Smooth Transitions**: 300ms ease-in-out animations

## Usage

```tsx
import { ToplineSidebar } from '@/components/topline-sidebar'

export default function MyPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <ToplineSidebar />
      
      {/* Your page content goes here */}
      <div className="p-8">
        <h1>Page Content</h1>
      </div>
    </div>
  )
}
```

## Behavior

1. **Default State**: Sidebar is collapsed (64px wide) showing only icons
2. **Hover Expansion**: Mouse over the sidebar to expand it to 240px
3. **Pin Toggle**: When expanded, click the pin icon to keep it open permanently
4. **Persistent Memory**: The pin state is saved to localStorage
5. **Tooltips**: When collapsed, hover over menu items to see their names

## Customization

The sidebar menu items are defined in the `menuSections` array within the component. You can modify this to add, remove, or reorganize menu items:

```tsx
const menuSections: MenuSection[] = [
  {
    title: "Main Menu",
    items: [
      { title: "Home", href: "/individual", icon: Home },
      // Add more items...
    ]
  },
  // Add more sections...
]
```

## Styling

The component uses Tailwind CSS classes and includes:
- Clean white background with subtle borders
- Blue accent colors for active states
- Smooth shadow transitions
- Professional typography
- Responsive design patterns

## Demo

Visit `/individual/test-home` to see the sidebar in action with a sample topline.com-inspired page layout.
