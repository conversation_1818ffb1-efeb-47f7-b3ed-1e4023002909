# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea
.vscode

# Performance optimization - exclude heavy files from indexing
*.log
*.cache
*.tmp
.turbo/
.swc/
*.tsbuildinfo
.eslintcache

# Large binary files
*.wasm
*.node
*.dll

# Cache directories
.cache/
cache/
tmp/
temp/
