import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { togetherAI } from '@/lib/ai/together-client';
import { validateAIConfig } from '@/lib/ai/config';

/**
 * Test AI service connections
 * POST /api/admin/ai/test-connection
 */
export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Validate AI configuration
    const configValidation = validateAIConfig();
    if (!configValidation.isValid) {
      return NextResponse.json(
        { 
          error: 'AI configuration invalid',
          details: configValidation.errors
        },
        { status: 400 }
      );
    }

    const testResults = {
      together_ai: { status: 'testing', error: null },
      elevenlabs: { status: 'testing', error: null },
      pinecone: { status: 'testing', error: null }
    };

    // Test Together.ai connection
    try {
      const togetherConnected = await togetherAI.testConnection();
      testResults.together_ai = {
        status: togetherConnected ? 'connected' : 'failed',
        error: togetherConnected ? null : 'Connection test failed'
      };
    } catch (error) {
      testResults.together_ai = {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test ElevenLabs connection (placeholder for now)
    try {
      // TODO: Implement ElevenLabs connection test
      testResults.elevenlabs = {
        status: 'not_implemented',
        error: 'ElevenLabs integration not yet implemented'
      };
    } catch (error) {
      testResults.elevenlabs = {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    // Test Pinecone connection (placeholder for now)
    try {
      // TODO: Implement Pinecone connection test
      testResults.pinecone = {
        status: 'not_implemented',
        error: 'Pinecone integration not yet implemented'
      };
    } catch (error) {
      testResults.pinecone = {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    const allConnected = Object.values(testResults).every(
      result => result.status === 'connected' || result.status === 'not_implemented'
    );

    return NextResponse.json({
      success: allConnected,
      services: testResults,
      message: allConnected 
        ? 'All implemented AI services are connected successfully'
        : 'Some AI services failed to connect'
    });

  } catch (error: any) {
    console.error('AI connection test error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to test AI connections' },
      { status: 500 }
    );
  }
}

/**
 * Get AI service status
 * GET /api/admin/ai/test-connection
 */
export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Return configuration status without testing connections
    const configValidation = validateAIConfig();
    
    return NextResponse.json({
      configuration: {
        together_ai: {
          configured: !!process.env.TOGETHER_AI_API_KEY,
          model: 'meta-llama/Llama-2-70b-chat-hf'
        },
        elevenlabs: {
          configured: !!process.env.ELEVENLABS_API_KEY,
          default_voice: 'rachel'
        },
        pinecone: {
          configured: !!process.env.PINECONE_API_KEY,
          environment: process.env.PINECONE_ENVIRONMENT || 'not_set',
          index_name: process.env.PINECONE_INDEX_NAME || 'not_set'
        }
      },
      validation: configValidation
    });

  } catch (error: any) {
    console.error('AI status check error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to check AI status' },
      { status: 500 }
    );
  }
}
