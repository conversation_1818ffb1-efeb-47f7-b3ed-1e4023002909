/**
 * Assessment Session Management Service
 * Handles session creation, state management, and user attempt tracking
 */

import { createClient } from '@/lib/supabase-server';
import { togetherAI } from '@/lib/ai/together-client';
import type { AssessmentGenerationRequest, GeneratedQuestion } from '@/lib/ai/together-client';

export interface AssessmentSession {
  id: string;
  user_id: string;
  ai_assessment_config_id: string;
  attempt_number: number;
  status: 'in_progress' | 'completed' | 'abandoned' | 'expired' | 'paused' | 'payment_pending';
  started_at: string;
  completed_at?: string;
  generation_prompt?: string;
  ai_model_used?: string;
  generation_timestamp?: string;
  generation_metadata?: Record<string, any>;
  total_questions?: number;
  questions_answered: number;
  raw_score?: number;
  competency_scores?: Record<string, number>;
  overall_competency_level?: string;
  passed?: boolean;
  time_spent: number;
  payment_required: boolean;
  payment_amount?: number;
  payment_status: string;
}

export interface AssessmentConfig {
  id: string;
  name: string;
  description: string;
  question_count: number;
  difficulty_distribution: Record<string, number>;
  question_types: string[];
  subcategories: string[];
  duration_minutes: number;
  passing_score: number;
  category: string;
  retake_limit: number;
  retake_fee: number;
  certification_eligible: boolean;
  ai_model_name: string;
  temperature: number;
  max_tokens: number;
  prompt_template: {
    id: string;
    system_prompt: string;
    default_parameters: Record<string, any>;
  };
}

export interface SessionCreationResult {
  session: AssessmentSession;
  requiresPayment: boolean;
  paymentAmount?: number;
  message: string;
}

export interface QuestionGenerationResult {
  success: boolean;
  questions: GeneratedQuestion[];
  metadata: {
    generation_id: string;
    model_used: string;
    tokens_used: number;
    generation_time: number;
    quality_score: number;
  };
  error?: string;
}

export class AssessmentSessionService {
  private supabase = createClient();

  /**
   * Create a new assessment session for a user
   */
  async createSession(
    userId: string, 
    configId: string
  ): Promise<SessionCreationResult> {
    try {
      // Get assessment configuration
      const config = await this.getAssessmentConfig(configId);
      if (!config) {
        throw new Error('Assessment configuration not found');
      }

      // Check user's attempt count
      const attemptCount = await this.getUserAttemptCount(userId, configId);
      const nextAttemptNumber = attemptCount + 1;

      // Check if payment is required
      const requiresPayment = nextAttemptNumber > config.retake_limit && config.retake_fee > 0;
      const paymentAmount = requiresPayment ? config.retake_fee : 0;

      // Create session record
      const { data: session, error } = await this.supabase
        .from('ai_assessment_sessions')
        .insert({
          user_id: userId,
          ai_assessment_config_id: configId,
          attempt_number: nextAttemptNumber,
          status: requiresPayment ? 'payment_pending' : 'in_progress',
          total_questions: config.question_count,
          questions_answered: 0,
          time_spent: 0,
          payment_required: requiresPayment,
          payment_amount: paymentAmount,
          payment_status: requiresPayment ? 'pending' : 'not_required'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating assessment session:', error);
        throw new Error('Failed to create assessment session');
      }

      // If no payment required, generate questions immediately
      if (!requiresPayment) {
        await this.generateQuestionsForSession(session.id, config);
      }

      return {
        session,
        requiresPayment,
        paymentAmount,
        message: requiresPayment 
          ? `Payment of $${paymentAmount} required for attempt ${nextAttemptNumber}`
          : 'Assessment session created successfully'
      };

    } catch (error) {
      console.error('Session creation error:', error);
      throw error;
    }
  }

  /**
   * Generate questions for an assessment session
   */
  async generateQuestionsForSession(
    sessionId: string,
    config: AssessmentConfig
  ): Promise<QuestionGenerationResult> {
    try {
      // Update session status to generating
      await this.updateSessionStatus(sessionId, 'in_progress');

      // Build generation request
      const generationRequest: AssessmentGenerationRequest = {
        assessmentType: config.category,
        questionCount: config.question_count,
        difficultyDistribution: config.difficulty_distribution,
        subcategories: config.subcategories,
        questionTypes: config.question_types,
        systemPrompt: config.prompt_template.system_prompt,
        parameters: {
          ...config.prompt_template.default_parameters,
          target_role: 'BPO Call Center Agent',
          industry_focus: 'Business Process Outsourcing',
          current_date: new Date().toISOString().split('T')[0],
          duration_minutes: config.duration_minutes,
          passing_score: config.passing_score
        }
      };

      // Generate questions using AI
      const result = await togetherAI.generateAssessmentQuestions(generationRequest);

      // Store generated questions in database
      const questionsToInsert = result.questions.map((question, index) => ({
        assessment_session_id: sessionId,
        question_number: index + 1,
        question_type: question.question_type,
        question_text: question.question_text,
        question_context: question.question_context,
        answer_options: question.answer_options,
        correct_answer: question.correct_answer,
        explanation: question.explanation,
        subcategory: question.subcategory,
        difficulty_level: question.difficulty,
        competency_focus: question.subcategory,
        points: question.points,
        estimated_time: question.estimated_time,
        ai_confidence_score: question.ai_confidence_score
      }));

      const { error: insertError } = await this.supabase
        .from('ai_generated_assessment_questions')
        .insert(questionsToInsert);

      if (insertError) {
        console.error('Error storing generated questions:', insertError);
        throw new Error('Failed to store generated questions');
      }

      // Update session with generation metadata
      await this.supabase
        .from('ai_assessment_sessions')
        .update({
          generation_prompt: generationRequest.systemPrompt,
          ai_model_used: config.ai_model_name,
          generation_timestamp: new Date().toISOString(),
          generation_metadata: result.metadata,
          total_questions: result.questions.length
        })
        .eq('id', sessionId);

      return {
        success: true,
        questions: result.questions,
        metadata: result.metadata
      };

    } catch (error) {
      console.error('Question generation error:', error);
      
      // Update session status to failed
      await this.updateSessionStatus(sessionId, 'abandoned');
      
      return {
        success: false,
        questions: [],
        metadata: {
          generation_id: '',
          model_used: '',
          tokens_used: 0,
          generation_time: 0,
          quality_score: 0
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get assessment configuration with prompt template
   */
  private async getAssessmentConfig(configId: string): Promise<AssessmentConfig | null> {
    const { data, error } = await this.supabase
      .from('ai_assessment_configs')
      .select(`
        *,
        prompt_template:ai_prompt_templates(
          id,
          system_prompt,
          default_parameters
        )
      `)
      .eq('id', configId)
      .eq('status', 'published')
      .single();

    if (error || !data) {
      console.error('Error fetching assessment config:', error);
      return null;
    }

    return data as AssessmentConfig;
  }

  /**
   * Get user's attempt count for a specific assessment
   */
  private async getUserAttemptCount(userId: string, configId: string): Promise<number> {
    const { count, error } = await this.supabase
      .from('ai_assessment_sessions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('ai_assessment_config_id', configId);

    if (error) {
      console.error('Error getting attempt count:', error);
      return 0;
    }

    return count || 0;
  }

  /**
   * Update session status
   */
  async updateSessionStatus(
    sessionId: string, 
    status: AssessmentSession['status']
  ): Promise<void> {
    const updates: any = { status };
    
    if (status === 'completed') {
      updates.completed_at = new Date().toISOString();
    }

    const { error } = await this.supabase
      .from('ai_assessment_sessions')
      .update(updates)
      .eq('id', sessionId);

    if (error) {
      console.error('Error updating session status:', error);
      throw new Error('Failed to update session status');
    }
  }

  /**
   * Get session with questions
   */
  async getSessionWithQuestions(sessionId: string): Promise<{
    session: AssessmentSession;
    questions: any[];
    config: AssessmentConfig;
  } | null> {
    // Get session
    const { data: session, error: sessionError } = await this.supabase
      .from('ai_assessment_sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    if (sessionError || !session) {
      return null;
    }

    // Get questions
    const { data: questions, error: questionsError } = await this.supabase
      .from('ai_generated_assessment_questions')
      .select('*')
      .eq('assessment_session_id', sessionId)
      .order('question_number');

    if (questionsError) {
      console.error('Error fetching questions:', questionsError);
      return null;
    }

    // Get config
    const config = await this.getAssessmentConfig(session.ai_assessment_config_id);
    if (!config) {
      return null;
    }

    return {
      session,
      questions: questions || [],
      config
    };
  }

  /**
   * Submit answer for a question
   */
  async submitAnswer(
    sessionId: string,
    questionId: string,
    userAnswer: any,
    responseTime: number
  ): Promise<{ isCorrect: boolean; explanation: string }> {
    // Get the question
    const { data: question, error } = await this.supabase
      .from('ai_generated_assessment_questions')
      .select('*')
      .eq('id', questionId)
      .eq('assessment_session_id', sessionId)
      .single();

    if (error || !question) {
      throw new Error('Question not found');
    }

    // Determine if answer is correct
    const isCorrect = this.evaluateAnswer(question, userAnswer);

    // Update question with user's answer
    await this.supabase
      .from('ai_generated_assessment_questions')
      .update({
        user_answer: userAnswer,
        is_correct: isCorrect,
        response_time: responseTime,
        answered_at: new Date().toISOString()
      })
      .eq('id', questionId);

    // Update session progress
    await this.updateSessionProgress(sessionId);

    return {
      isCorrect,
      explanation: question.explanation || ''
    };
  }

  /**
   * Evaluate if user's answer is correct
   */
  private evaluateAnswer(question: any, userAnswer: any): boolean {
    switch (question.question_type) {
      case 'multiple_choice':
      case 'single_choice':
        return userAnswer === question.correct_answer;
      
      case 'true_false':
        return Boolean(userAnswer) === Boolean(question.correct_answer);
      
      case 'text_input':
      case 'fill_in_blank':
        // Simple string comparison for now - could be enhanced with fuzzy matching
        const correctAnswer = String(question.correct_answer).toLowerCase().trim();
        const userAnswerStr = String(userAnswer).toLowerCase().trim();
        return correctAnswer === userAnswerStr;
      
      default:
        return false;
    }
  }

  /**
   * Update session progress and calculate scores
   */
  private async updateSessionProgress(sessionId: string): Promise<void> {
    // Get all questions for this session
    const { data: questions, error } = await this.supabase
      .from('ai_generated_assessment_questions')
      .select('*')
      .eq('assessment_session_id', sessionId);

    if (error || !questions) {
      return;
    }

    const answeredQuestions = questions.filter(q => q.user_answer !== null);
    const correctAnswers = answeredQuestions.filter(q => q.is_correct);
    const totalQuestions = questions.length;
    const questionsAnswered = answeredQuestions.length;

    // Calculate raw score
    const rawScore = questionsAnswered > 0 
      ? (correctAnswers.length / questionsAnswered) * 100 
      : 0;

    // Calculate competency scores by subcategory
    const competencyScores: Record<string, number> = {};
    const subcategories = [...new Set(questions.map(q => q.subcategory))];
    
    subcategories.forEach(subcategory => {
      const subcategoryQuestions = answeredQuestions.filter(q => q.subcategory === subcategory);
      const subcategoryCorrect = subcategoryQuestions.filter(q => q.is_correct);
      
      if (subcategoryQuestions.length > 0) {
        competencyScores[subcategory] = (subcategoryCorrect.length / subcategoryQuestions.length) * 100;
      }
    });

    // Determine overall competency level
    let competencyLevel = 'novice';
    if (rawScore >= 95) competencyLevel = 'expert';
    else if (rawScore >= 80) competencyLevel = 'advanced';
    else if (rawScore >= 60) competencyLevel = 'intermediate';

    // Calculate total time spent
    const totalTimeSpent = answeredQuestions.reduce((sum, q) => sum + (q.response_time || 0), 0);

    // Update session
    const updates: any = {
      questions_answered: questionsAnswered,
      raw_score: rawScore,
      competency_scores: competencyScores,
      overall_competency_level: competencyLevel,
      time_spent: totalTimeSpent
    };

    // Check if assessment is complete
    if (questionsAnswered === totalQuestions) {
      // Get passing score from config
      const { data: config } = await this.supabase
        .from('ai_assessment_configs')
        .select('passing_score')
        .eq('id', (await this.supabase
          .from('ai_assessment_sessions')
          .select('ai_assessment_config_id')
          .eq('id', sessionId)
          .single()
        ).data?.ai_assessment_config_id)
        .single();

      const passingScore = config?.passing_score || 70;
      updates.passed = rawScore >= passingScore;
      updates.status = 'completed';
      updates.completed_at = new Date().toISOString();
    }

    await this.supabase
      .from('ai_assessment_sessions')
      .update(updates)
      .eq('id', sessionId);
  }

  /**
   * Process payment for assessment session
   */
  async processPayment(sessionId: string, paymentData: any): Promise<boolean> {
    try {
      // TODO: Integrate with payment processor (Stripe, etc.)
      // For now, simulate successful payment
      
      await this.supabase
        .from('ai_assessment_sessions')
        .update({
          payment_status: 'completed',
          status: 'in_progress'
        })
        .eq('id', sessionId);

      // Generate questions after successful payment
      const { data: session } = await this.supabase
        .from('ai_assessment_sessions')
        .select('ai_assessment_config_id')
        .eq('id', sessionId)
        .single();

      if (session) {
        const config = await this.getAssessmentConfig(session.ai_assessment_config_id);
        if (config) {
          await this.generateQuestionsForSession(sessionId, config);
        }
      }

      return true;
    } catch (error) {
      console.error('Payment processing error:', error);
      return false;
    }
  }
}

// Export singleton instance
export const assessmentSessionService = new AssessmentSessionService();
