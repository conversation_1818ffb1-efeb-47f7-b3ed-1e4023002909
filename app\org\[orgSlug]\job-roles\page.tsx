'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { createBrowserClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Building2, Users, Plus, Search, MoreVertical, Edit, Trash2, Eye, Target, Brain, TrendingUp } from 'lucide-react';

interface JobRole {
  id: string;
  name: string;
  job_description: string;
  duties: string[];
  seniority_level: string;
  department_id: string | null;
  department_name: string | null;
  is_active: boolean;
  created_at: string;
  skill_count: number;
  staff_count: number;
}

interface JobRoleStats {
  totalRoles: number;
  activeRoles: number;
  departmentRoles: number;
  organizationRoles: number;
}

export default function OrganizationJobRolesPage() {
  const params = useParams();
  const router = useRouter();
  const orgSlug = params.orgSlug as string;

  const supabase = createBrowserClient();

  const [loading, setLoading] = useState(true);
  const [jobRoles, setJobRoles] = useState<JobRole[]>([]);
  const [stats, setStats] = useState<JobRoleStats>({
    totalRoles: 0,
    activeRoles: 0,
    departmentRoles: 0,
    organizationRoles: 0
  });

  // Filters and search
  const [searchQuery, setSearchQuery] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [seniorityFilter, setSeniorityFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    fetchJobRoles();
  }, [orgSlug]);

  const fetchJobRoles = async () => {
    try {
      setLoading(true);
      console.log('🚀 Fetching job roles for organization:', orgSlug);

      // Step 1: Get organization
      const { data: organization, error: orgError } = await supabase
        .from('organizations')
        .select('id, name')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('❌ Organization query failed:', orgError);
        return;
      }
      console.log('✅ Organization found:', organization);

      // Step 2: Get job roles with department info and counts
      const { data: rolesData, error: rolesError } = await supabase
        .from('job_roles')
        .select(`
          *,
          departments(name),
          job_role_skill_requirements(count),
          employment_relationships(count)
        `)
        .eq('organization_id', organization.id)
        .order('created_at', { ascending: false });

      if (rolesError) {
        console.error('❌ Job roles query failed:', rolesError);
        return;
      }
      console.log('✅ Job roles found:', rolesData?.length || 0);

      // Transform the data
      const transformedRoles: JobRole[] = (rolesData || []).map(role => ({
        id: role.id,
        name: role.name,
        job_description: role.job_description,
        duties: role.duties || [],
        seniority_level: role.seniority_level,
        department_id: role.department_id,
        department_name: role.departments?.name || null,
        is_active: role.is_active,
        created_at: role.created_at,
        skill_count: Array.isArray(role.job_role_skill_requirements) ? role.job_role_skill_requirements.length : 0,
        staff_count: Array.isArray(role.employment_relationships) ? role.employment_relationships.length : 0
      }));

      setJobRoles(transformedRoles);

      // Calculate stats
      const activeRoles = transformedRoles.filter(r => r.is_active).length;
      const departmentRoles = transformedRoles.filter(r => r.department_id !== null).length;
      const organizationRoles = transformedRoles.filter(r => r.department_id === null).length;

      setStats({
        totalRoles: transformedRoles.length,
        activeRoles,
        departmentRoles,
        organizationRoles
      });

    } catch (error) {
      console.error('💥 Unexpected error in fetchJobRoles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRole = () => {
    router.push(`/org/${orgSlug}/job-roles/create`);
  };

  const handleViewRole = (roleId: string) => {
    router.push(`/org/${orgSlug}/job-roles/${roleId}`);
  };

  const handleEditRole = (roleId: string) => {
    router.push(`/org/${orgSlug}/job-roles/${roleId}/edit`);
  };

  const getSeniorityBadgeColor = (level: string) => {
    switch (level) {
      case 'executive': return 'bg-purple-100 text-purple-800';
      case 'lead': return 'bg-blue-100 text-blue-800';
      case 'senior': return 'bg-green-100 text-green-800';
      case 'mid': return 'bg-yellow-100 text-yellow-800';
      case 'junior': return 'bg-orange-100 text-orange-800';
      case 'entry': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Job Roles</h1>
          <p className="text-muted-foreground">
            Define and manage job roles with required skills and competencies
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Target className="mr-2 h-4 w-4" />
            Import Templates
          </Button>
          <Button onClick={handleCreateRole}>
            <Plus className="mr-2 h-4 w-4" />
            Create Job Role
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
              <Target className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Roles</div>
              <div className="text-3xl font-bold mt-1">{stats.totalRoles}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeRoles} active
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Department Roles</div>
              <div className="text-3xl font-bold mt-1">{stats.departmentRoles}</div>
              <p className="text-xs text-muted-foreground">
                Department-specific
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/20">
              <Users className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Organization Roles</div>
              <div className="text-3xl font-bold mt-1">{stats.organizationRoles}</div>
              <p className="text-xs text-muted-foreground">
                Cross-department
              </p>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg shadow-orange-500/20">
              <Brain className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Avg Skills</div>
              <div className="text-3xl font-bold mt-1">
                {jobRoles.length > 0 ? Math.round(jobRoles.reduce((sum, r) => sum + r.skill_count, 0) / jobRoles.length) : 0}
              </div>
              <p className="text-xs text-muted-foreground">
                Per role
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search job roles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Job Roles Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[300px]">Role</TableHead>
                <TableHead>Department</TableHead>
                <TableHead>Seniority</TableHead>
                <TableHead>Skills</TableHead>
                <TableHead>Staff</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center h-24 text-muted-foreground">
                    Loading job roles...
                  </TableCell>
                </TableRow>
              ) : jobRoles.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center h-24">
                    <div className="flex flex-col items-center justify-center py-8">
                      <Target className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Job Roles Found</h3>
                      <p className="text-muted-foreground text-center mb-4">
                        Create your first job role to define required skills and competencies
                      </p>
                      <Button onClick={handleCreateRole}>
                        <Plus className="mr-2 h-4 w-4" />
                        Create Job Role
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                jobRoles.map((role) => (
                  <TableRow
                    key={role.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleViewRole(role.id)}
                  >
                    <TableCell>
                      <div>
                        <div className="font-medium">{role.name}</div>
                        <div className="text-sm text-muted-foreground line-clamp-1">
                          {role.job_description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {role.department_name ? (
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4 text-muted-foreground" />
                          <span>{role.department_name}</span>
                        </div>
                      ) : (
                        <Badge variant="outline">Organization-wide</Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={getSeniorityBadgeColor(role.seniority_level)}>
                        {role.seniority_level}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Brain className="h-4 w-4 text-muted-foreground" />
                        <span>{role.skill_count}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>{role.staff_count}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={role.is_active ? 'default' : 'secondary'}>
                        {role.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewRole(role.id);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            <span>View Details</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditRole(role.id);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-2" />
                            <span>Edit Role</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}