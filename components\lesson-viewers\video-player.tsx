"use client"

import { useState, useRef } from 'react'
import ReactPlayer from 'react-player'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Play, Pause, Volume2, VolumeX, Maximize, RotateCcw, RotateCw } from 'lucide-react'

interface VideoPlayerProps {
  url: string
  title?: string
  transcript?: string
  onProgress?: (progress: { played: number; playedSeconds: number; loaded: number; loadedSeconds: number }) => void
  onEnded?: () => void
  autoPlay?: boolean
  controls?: boolean
}

export function VideoPlayer({ 
  url, 
  title, 
  transcript, 
  onProgress, 
  onEnded, 
  autoPlay = false,
  controls = true 
}: VideoPlayerProps) {
  const [playing, setPlaying] = useState(autoPlay)
  const [volume, setVolume] = useState(0.8)
  const [muted, setMuted] = useState(false)
  const [played, setPlayed] = useState(0)
  const [loaded, setLoaded] = useState(0)
  const [duration, setDuration] = useState(0)
  const [seeking, setSeeking] = useState(false)
  const [showTranscript, setShowTranscript] = useState(false)
  const [playbackRate, setPlaybackRate] = useState(1)
  
  const playerRef = useRef<ReactPlayer>(null)

  const handlePlayPause = () => {
    setPlaying(!playing)
  }

  const handleProgress = (state: any) => {
    if (!seeking) {
      setPlayed(state.played)
    }
    onProgress?.(state)
  }

  const handleSeekMouseDown = () => {
    setSeeking(true)
  }

  const handleSeekChange = (value: number[]) => {
    setPlayed(value[0] / 100)
  }

  const handleSeekMouseUp = (value: number[]) => {
    setSeeking(false)
    playerRef.current?.seekTo(value[0] / 100)
  }

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0] / 100)
    setMuted(value[0] === 0)
  }

  const handleMute = () => {
    setMuted(!muted)
  }

  const handleFullscreen = () => {
    const playerElement = playerRef.current?.getInternalPlayer()
    if (playerElement && playerElement.requestFullscreen) {
      playerElement.requestFullscreen()
    }
  }

  const handleRewind = () => {
    const currentTime = playerRef.current?.getCurrentTime() || 0
    playerRef.current?.seekTo(Math.max(0, currentTime - 10))
  }

  const handleFastForward = () => {
    const currentTime = playerRef.current?.getCurrentTime() || 0
    playerRef.current?.seekTo(Math.min(duration, currentTime + 10))
  }

  const formatTime = (seconds: number) => {
    const date = new Date(seconds * 1000)
    const hh = date.getUTCHours()
    const mm = date.getUTCMinutes()
    const ss = date.getUTCSeconds().toString().padStart(2, '0')
    if (hh) {
      return `${hh}:${mm.toString().padStart(2, '0')}:${ss}`
    }
    return `${mm}:${ss}`
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardContent className="p-0">
          <div className="relative bg-black rounded-lg overflow-hidden">
            <ReactPlayer
              ref={playerRef}
              url={url}
              width="100%"
              height="400px"
              playing={playing}
              volume={muted ? 0 : volume}
              playbackRate={playbackRate}
              onProgress={handleProgress}
              onDuration={setDuration}
              onEnded={onEnded}
              controls={false}
              config={{
                youtube: {
                  playerVars: { showinfo: 1 }
                },
                vimeo: {
                  playerOptions: { byline: false }
                }
              }}
            />
            
            {controls && (
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                {/* Progress Bar */}
                <div className="mb-3">
                  <Slider
                    value={[played * 100]}
                    max={100}
                    step={0.1}
                    onValueChange={handleSeekChange}
                    onValueCommit={handleSeekMouseUp}
                    onPointerDown={handleSeekMouseDown}
                    className="w-full"
                  />
                </div>
                
                {/* Controls */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handlePlayPause}
                      className="text-white hover:bg-white/20"
                    >
                      {playing ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRewind}
                      className="text-white hover:bg-white/20"
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleFastForward}
                      className="text-white hover:bg-white/20"
                    >
                      <RotateCw className="h-4 w-4" />
                    </Button>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleMute}
                        className="text-white hover:bg-white/20"
                      >
                        {muted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                      </Button>
                      <Slider
                        value={[muted ? 0 : volume * 100]}
                        max={100}
                        step={1}
                        onValueChange={handleVolumeChange}
                        className="w-20"
                      />
                    </div>
                    
                    <span className="text-white text-sm">
                      {formatTime(played * duration)} / {formatTime(duration)}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <select
                      value={playbackRate}
                      onChange={(e) => setPlaybackRate(parseFloat(e.target.value))}
                      className="bg-black/50 text-white text-sm rounded px-2 py-1 border border-white/20"
                    >
                      <option value={0.5}>0.5x</option>
                      <option value={0.75}>0.75x</option>
                      <option value={1}>1x</option>
                      <option value={1.25}>1.25x</option>
                      <option value={1.5}>1.5x</option>
                      <option value={2}>2x</option>
                    </select>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleFullscreen}
                      className="text-white hover:bg-white/20"
                    >
                      <Maximize className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      {transcript && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold">Transcript</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowTranscript(!showTranscript)}
              >
                {showTranscript ? 'Hide' : 'Show'} Transcript
              </Button>
            </div>
            {showTranscript && (
              <div className="prose prose-sm max-w-none">
                <p className="whitespace-pre-wrap text-muted-foreground">
                  {transcript}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
