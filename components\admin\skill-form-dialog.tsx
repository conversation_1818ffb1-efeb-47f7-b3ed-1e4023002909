'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

const skillSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  category_id: z.string().min(1, 'Category is required'),
  subcategory: z.string().max(100, 'Subcategory must be less than 100 characters').optional(),
  skill_type: z.enum(['hard', 'soft', 'hybrid'], {
    required_error: 'Skill type is required',
  }),
  description: z.string().max(1000, 'Description must be less than 1000 characters').optional(),
  certification_available: z.boolean().default(false),
  market_demand: z.enum(['low', 'medium', 'high', 'critical'], {
    required_error: 'Market demand is required',
  }),
  is_active: z.boolean().default(true),
});

type SkillFormData = z.infer<typeof skillSchema>;

interface Skill {
  id: string;
  name: string;
  category_id: string;
  subcategory: string;
  skill_type: 'hard' | 'soft' | 'hybrid';
  description: string;
  certification_available: boolean;
  market_demand: 'low' | 'medium' | 'high' | 'critical';
  is_active: boolean;
}

interface SkillCategory {
  id: string;
  name: string;
  color_code: string;
}

interface SkillFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  skill?: Skill | null;
  categories: SkillCategory[];
  onSuccess: () => void;
}

export function SkillFormDialog({
  open,
  onOpenChange,
  skill,
  categories,
  onSuccess,
}: SkillFormDialogProps) {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const isEditing = Boolean(skill);

  const form = useForm<SkillFormData>({
    resolver: zodResolver(skillSchema),
    defaultValues: {
      name: '',
      category_id: '',
      subcategory: '',
      skill_type: 'hard',
      description: '',
      certification_available: false,
      market_demand: 'medium',
      is_active: true,
    },
  });

  useEffect(() => {
    if (skill) {
      form.reset({
        name: skill.name,
        category_id: skill.category_id,
        subcategory: skill.subcategory || '',
        skill_type: skill.skill_type,
        description: skill.description || '',
        certification_available: skill.certification_available,
        market_demand: skill.market_demand,
        is_active: skill.is_active,
      });
    } else {
      form.reset({
        name: '',
        category_id: '',
        subcategory: '',
        skill_type: 'hard',
        description: '',
        certification_available: false,
        market_demand: 'medium',
        is_active: true,
      });
    }
  }, [skill, form]);

  const onSubmit = async (data: SkillFormData) => {
    setLoading(true);
    try {
      const url = isEditing 
        ? `/api/admin/skills/${skill.id}`
        : '/api/admin/skills';
      
      const method = isEditing ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save skill');
      }

      toast({
        title: "Success",
        description: isEditing 
          ? 'Skill updated successfully' 
          : 'Skill created successfully',
      });

      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      console.error('Error saving skill:', error);
      toast({
        title: "Error",
        description: error.message || 'Failed to save skill',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Skill' : 'Create Skill'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the skill information below.'
              : 'Create a new skill in the registry.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Skill Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., JavaScript, Public Speaking" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            <div className="flex items-center gap-2">
                              <div 
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: category.color_code }}
                              />
                              {category.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="subcategory"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subcategory</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Programming Languages" {...field} />
                    </FormControl>
                    <FormDescription>
                      Optional subcategory
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="skill_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Skill Type</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="hard">Hard Skill</SelectItem>
                        <SelectItem value="soft">Soft Skill</SelectItem>
                        <SelectItem value="hybrid">Hybrid Skill</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="market_demand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Market Demand</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Detailed description of the skill..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="certification_available"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Certification Available</FormLabel>
                      <FormDescription>
                        Can be certified
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="is_active"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                    <div className="space-y-0.5">
                      <FormLabel>Active</FormLabel>
                      <FormDescription>
                        Skill is available for use
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? 'Update Skill' : 'Create Skill'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
