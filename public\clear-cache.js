/**
 * Cache Clearing Script
 * Run this in browser console to clear all caches
 */

async function clearAllCaches() {
  console.log('🧹 Starting cache cleanup...');
  
  try {
    // 1. Clear all Cache API caches
    const cacheNames = await caches.keys();
    console.log('📦 Found caches:', cacheNames);
    
    await Promise.all(
      cacheNames.map(cacheName => {
        console.log(`🗑️ Deleting cache: ${cacheName}`);
        return caches.delete(cacheName);
      })
    );
    
    // 2. Unregister service workers
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      console.log('🔧 Found service workers:', registrations.length);
      
      await Promise.all(
        registrations.map(registration => {
          console.log('🗑️ Unregistering service worker');
          return registration.unregister();
        })
      );
    }
    
    // 3. Clear localStorage and sessionStorage
    localStorage.clear();
    sessionStorage.clear();
    console.log('🗑️ Cleared local and session storage');
    
    // 4. Clear IndexedDB (if any)
    if ('indexedDB' in window) {
      // This is a simplified approach - in practice you'd need to know DB names
      console.log('🗑️ IndexedDB cleanup (manual check recommended)');
    }
    
    console.log('✅ Cache cleanup complete! Please refresh the page.');
    
    // Auto-refresh after cleanup
    setTimeout(() => {
      window.location.reload(true);
    }, 1000);
    
  } catch (error) {
    console.error('❌ Error during cache cleanup:', error);
  }
}

// Auto-run if this script is loaded directly
if (typeof window !== 'undefined') {
  console.log('🚀 Cache clearing script loaded. Run clearAllCaches() to clean up.');
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { clearAllCaches };
}
