# 🌙 Luna Learning Platform

**Luna** is a comprehensive, enterprise-grade learning management platform that transforms how organizations deliver training and individuals advance their careers. Built with Next.js 15, Supabase, and modern web technologies, <PERSON> provides a scalable three-tier course architecture designed for any industry.

## 🎯 **What is <PERSON>?**

Luna is built on a revolutionary **three-tier course architecture** that provides unmatched flexibility and scalability:

- **📚 Programs**: Industry-focused learning umbrellas (IT, Healthcare, Finance, etc.)
- **🛤️ Pathways**: Career-specific learning tracks within programs (Help Desk, Network Admin, etc.)
- **📖 Courses**: Reusable training modules that can be shared across multiple pathways
- **🏢 Organizations**: Multi-tenant support with department-based management
- **🧑‍💼 Individuals**: Personal learning journeys with progress tracking and certifications

## ✨ **Core Features**

### 🏗️ **Three-Tier Course Architecture**
- **Programs Management**: Create industry-focused learning programs with full CRUD operations
- **Pathways Management**: Design career-specific learning tracks within programs
- **Course Reusability**: Share courses across multiple pathways for maximum efficiency
- **Content Structure**: Organize courses into modules and lessons with rich media support
- **Sequencing Control**: Define course order and prerequisites within pathways

### 🎓 **Advanced Learning Management**
- **Interactive Content**: Video lessons, quizzes, assignments, and interactive modules
- **Progress Tracking**: Real-time monitoring of user progress through pathways and courses
- **Enrollment System**: Individual and organizational enrollment with context switching
- **Assessment Engine**: Comprehensive skills evaluation and testing capabilities
- **Certification Tracking**: Digital credentials and completion certificates

### 🏢 **Enterprise-Grade Organization Management**
- **Multi-Tenant Architecture**: Secure organization-specific data isolation
- **Department Hierarchies**: Custom department structures per organization
- **Role-Based Access Control**: Platform Admin, Organization Owner/Admin, Department Admin, Staff
- **Employment Relationships**: Flexible user-organization-department associations
- **Context Switching**: Seamless transition between individual and organizational modes

### 💼 **Talent & Recruitment Management**
- **Job Board System**: Post vacancies with skills requirements and manage applications
- **Interview Scheduling**: Comprehensive interview management and evaluation tools
- **Skills-Based Matching**: Match candidates based on verified competencies and pathway completion
- **Performance Analytics**: Track learning outcomes and career progression

### 🔐 **Enterprise Security**
- **Supabase Authentication**: Secure user authentication with session management
- **Row-Level Security**: Database-level access control and data isolation
- **API Security**: Comprehensive validation and error handling
- **Audit Logging**: Complete activity tracking and security monitoring

## 🌟 **Luna's Vision**

Luna represents the future of enterprise learning management - providing a scalable, flexible platform that adapts to any industry's training needs. With our innovative three-tier architecture, organizations can create comprehensive learning programs while individuals enjoy personalized career development paths. Luna bridges the gap between organizational training requirements and individual career aspirations, creating a unified learning ecosystem that grows with your business.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/JennineHamilton/luna.git
   cd luna
   ```

2. **Install dependencies**
   ```bash
   npm install --legacy-peer-deps
   ```

3. **Set up environment variables**
   ```bash
   # Create .env.local file with your Supabase credentials
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

4. **Set up the database**
   ```bash
   # Apply database migrations in Supabase SQL Editor
   # Run the migrations in supabase/migrations/ folder:
   # - 20250127_three_tier_course_architecture.sql
   # - 20250127_seed_course_data.sql
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📚 Documentation

### 📖 **Getting Started**
- [Installation Guide](docs/getting-started/installation.md) - Detailed setup instructions
- [Environment Setup](docs/getting-started/environment-setup.md) - Configuration guide
- [Quick Start Tutorial](docs/getting-started/quick-start.md) - 5-minute setup

### 🏗️ **Development**
- [Architecture Overview](docs/development/architecture.md) - System design and structure
- [Database Schema](docs/development/database-schema.md) - Complete database documentation
- [API Reference](docs/development/api-reference.md) - REST API endpoints
- [Component Library](docs/development/component-library.md) - UI components guide

### 🚀 **Deployment**
- [Production Setup](docs/deployment/production-setup.md) - Deploy to Vercel and production
- [Environment Variables](docs/deployment/environment-variables.md) - Configuration reference
- [Troubleshooting](docs/deployment/troubleshooting.md) - SSR fixes and common issues

### 📋 **Project Overview**
- [Current Architecture](docs/luna-project-overview/current-architecture.md) - Employment-based system design
- [Project Overview](docs/luna-project-overview/project-overview.md) - Complete transformation roadmap
- [Quick Reference](docs/luna-project-overview/quick-reference.md) - Key concepts and status

### 🤝 **Contributing**
- [Development Workflow](docs/contributing/development-workflow.md) - How to contribute
- [Code Style Guide](docs/contributing/code-style.md) - Coding standards
- [Pull Request Template](docs/contributing/pull-request-template.md) - PR guidelines

## 🛠️ Tech Stack

### **Frontend**
- **Next.js 15.2.4** - React framework with App Router
- **React 19** - Latest React with concurrent features
- **TypeScript** - Type-safe JavaScript development
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **Lucide Icons** - Beautiful icon library
- **TipTap** - Rich text editor for content creation
- **Recharts** - Data visualization and analytics

### **Backend & Database**
- **Supabase** - Backend-as-a-Service platform
- **PostgreSQL** - Relational database with advanced features
- **Row Level Security (RLS)** - Database-level access control
- **Supabase Auth** - Authentication and user management
- **Real-time Subscriptions** - Live data updates

### **UI & Components**
- **Radix UI Components** - Accessible primitives (Dialog, Dropdown, etc.)
- **React Hook Form** - Form handling and validation
- **Zod** - Schema validation and type safety
- **Sonner** - Toast notifications
- **React Dropzone** - File upload handling
- **Embla Carousel** - Touch-friendly carousels

### **Development Tools**
- **ESLint** - Code linting and quality
- **Rimraf** - Cross-platform file cleanup
- **Autoprefixer** - CSS vendor prefixing
- **PostCSS** - CSS processing and optimization

## 📊 Project Status

### ✅ **Phase 1: Foundation - COMPLETED**
- [x] **Employment-Based Architecture** - Dual-context system with multi-employment support
- [x] **Authentication System** - Supabase Auth with context-aware user management
- [x] **Database Schema** - Complete Luna employment-based schema with RLS policies
- [x] **Context Switching** - Seamless transition between individual and organizational modes
- [x] **Multi-Tenant Support** - Organization-specific data isolation and management
- [x] **Department Structure** - Custom department hierarchies per organization
- [x] **Role-Based Access Control** - Platform Admin, Org Owner/Admin, Department Admin, Staff
- [x] **API Infrastructure** - Comprehensive API endpoints with security and validation
- [x] **Admin Dashboard** - Platform management with organization and user oversight
- [x] **Individual Dashboard** - Personal training and career development interface
- [x] **Organization Dashboard** - Department management and team oversight
- [x] **SSR Optimization** - Resolved deployment issues and server-side rendering

### ✅ **Phase 2: Three-Tier Course Architecture - COMPLETED**
- [x] **Programs Management** - Industry-focused learning programs with full CRUD operations
- [x] **Pathways Management** - Career-specific learning tracks within programs
- [x] **Course Architecture** - Reusable courses with modules and lessons structure
- [x] **Database Schema** - Complete three-tier architecture with foreign keys and indexes
- [x] **API Endpoints** - Full REST API for programs, pathways, and course management
- [x] **Admin Interfaces** - Management dashboards for programs and pathways
- [x] **Form Validation** - Comprehensive form handling with Zod schema validation
- [x] **Enrollment System** - User enrollment tracking for courses and pathways
- [x] **Content Structure** - Course modules and lessons with rich content support
- [x] **Seed Data** - Sample programs, pathways, and courses for testing

### 🚧 **Phase 3: Enhanced Learning Features - IN PROGRESS**
- [x] **Job Board System** - Vacancy posting and application management
- [x] **Interview Management** - Scheduling and evaluation tools
- [ ] **Course Content Management** - Rich text editor and media upload for lessons
- [ ] **Progress Tracking** - Real-time user progress through pathways and courses
- [ ] **Assessment Engine** - Quizzes, assignments, and skills testing
- [ ] **Skills Gap Analysis** - AI-powered skills assessment and gap identification

### 🎯 **Phase 4: Advanced Features - PLANNED**
- [ ] **AI-Powered Learning** - Adaptive learning algorithms and personalized recommendations
- [ ] **Advanced Analytics** - Detailed reporting dashboard with learning insights
- [ ] **Real-time Collaboration** - Communication tools and mentoring features
- [ ] **Mobile Optimization** - Enhanced mobile experience and PWA capabilities
- [ ] **Third-Party Integrations** - LTI compliance and external tool connections
- [ ] **Certification Management** - Digital badges and industry-recognized certificates

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](docs/contributing/development-workflow.md) for details.

### Development Process
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](docs/) - Comprehensive project documentation
- **Issues**: [GitHub Issues](https://github.com/JennineHamilton/luna/issues) - Bug reports and feature requests
- **Test Pages**: `/test-connection` - Connection diagnostics and troubleshooting

## 🙏 Acknowledgments

- **Supabase** - For the powerful backend-as-a-service platform
- **Vercel** - For seamless deployment and hosting
- **Radix UI** - For accessible and customizable components
- **Tailwind CSS** - For the utility-first CSS framework
- **Next.js Team** - For the incredible React framework

---

**Built with ❤️ for scalable enterprise learning and career development**

## 🏗️ **Architecture Highlights**

### **Three-Tier Course System**
```
Programs (Industry Focus)
    ├── IT Program
    ├── Healthcare Program
    └── Finance Program
        └── Pathways (Career Tracks)
            ├── Help Desk Specialist
            ├── Network Administrator
            └── Software Developer
                └── Courses (Reusable Content)
                    ├── Professional Communication
                    ├── Technical Fundamentals
                    └── Industry-Specific Skills
```

### **Database Architecture**
- **Programs Table**: Industry-focused learning umbrellas
- **Learning Paths Table**: Career pathways within programs (extended existing table)
- **Courses Table**: Reusable training modules with rich metadata
- **Course Modules**: Sections within courses for organized content
- **Course Lessons**: Individual learning units (video, text, quiz, assignment)
- **Pathway Courses**: Junction table linking pathways to courses with sequencing
- **User Enrollments**: Individual and organizational enrollment tracking

### **API Structure**
- **`/api/admin/programs`** - Programs CRUD operations
- **`/api/admin/pathways`** - Pathways CRUD operations
- **`/api/admin/pathways/[id]/courses`** - Course assignment and sequencing
- **`/api/admin/courses`** - Course management (existing, updated)
- **Authentication**: Consistent auth system across all endpoints
- **Validation**: Zod schema validation for all form inputs
