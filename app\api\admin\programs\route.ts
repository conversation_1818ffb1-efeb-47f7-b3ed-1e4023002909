import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-admin';

export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const status = searchParams.get('status');
    const industry = searchParams.get('industry');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Create admin client
    const adminClient = createAdminClient();

    // Build query
    let query = adminClient
      .from('programs')
      .select(`
        *,
        created_by_user:users!programs_created_by_fkey(
          id,
          full_name,
          email
        ),
        learning_paths(
          id,
          title,
          status,
          difficulty_level,
          estimated_duration_hours,
          sort_order
        )
      `)
      .order('sort_order', { ascending: true });

    // Apply filters
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    if (industry && industry !== 'all') {
      query = query.eq('industry', industry);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,industry.ilike.%${search}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: programs, error: programsError } = await query;

    if (programsError) {
      console.error('Error fetching programs:', programsError);
      return NextResponse.json(
        { error: programsError.message || 'Failed to fetch programs' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = adminClient
      .from('programs')
      .select('*', { count: 'exact', head: true });

    if (status && status !== 'all') {
      countQuery = countQuery.eq('status', status);
    }

    if (industry && industry !== 'all') {
      countQuery = countQuery.eq('industry', industry);
    }

    if (search) {
      countQuery = countQuery.or(`name.ilike.%${search}%,description.ilike.%${search}%,industry.ilike.%${search}%`);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error('Error counting programs:', countError);
    }

    // Add pathway counts to programs
    const programsWithCounts = (programs || []).map((program: any) => ({
      ...program,
      _count: {
        pathways: program.learning_paths?.length || 0,
        active_pathways: program.learning_paths?.filter((p: any) => p.status === 'active').length || 0
      }
    }));

    return NextResponse.json({
      programs: programsWithCounts,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error: any) {
    console.error('Programs GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      industry,
      cover_image_url,
      status,
      sort_order
    } = body;

    // Validate required fields
    if (!name || !description) {
      return NextResponse.json(
        { error: 'Name and description are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Check if program name already exists
    const { data: existingProgram } = await adminClient
      .from('programs')
      .select('id')
      .eq('name', name.trim())
      .single();

    if (existingProgram) {
      return NextResponse.json(
        { error: 'A program with this name already exists' },
        { status: 400 }
      );
    }

    // Create program
    const { data: program, error: programError } = await adminClient
      .from('programs')
      .insert({
        name: name.trim(),
        description: description.trim(),
        industry: industry?.trim(),
        cover_image_url,
        status: status || 'active',
        sort_order: sort_order || 0,
        created_by: authResult.user.id
      })
      .select(`
        *,
        created_by_user:users!programs_created_by_fkey(
          id,
          full_name,
          email
        )
      `)
      .single();

    if (programError) {
      console.error('Error creating program:', programError);
      return NextResponse.json(
        { error: programError.message || 'Failed to create program' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Program created successfully',
      program
    });

  } catch (error: any) {
    console.error('Program creation API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
