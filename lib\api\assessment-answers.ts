import { createAdminClient, supabase } from '@/lib/supabase';

export interface AssessmentAnswer {
  id: string;
  assessment_id: string;
  question_id: string;
  user_id: string;
  answer_text: string;
  additional_data?: any;
  created_at: string;
}

// Save an answer for a question
export async function saveAssessmentAnswer(
  assessmentId: string,
  questionId: string,
  userId: string,
  answerText: string,
  additionalData?: any
): Promise<AssessmentAnswer> {
  // First check if the answer already exists
  const { data: existingAnswer } = await supabase
    .from('assessment_answers')
    .select('id')
    .eq('assessment_id', assessmentId)
    .eq('question_id', questionId)
    .eq('user_id', userId)
    .maybeSingle();

  if (existingAnswer) {
    // Update existing answer
    const { data, error } = await supabase
      .from('assessment_answers')
      .update({
        answer_text: answerText,
        additional_data: additionalData
      })
      .eq('id', existingAnswer.id)
      .select()
      .single();

    if (error) {
      throw new Error(`Error updating answer: ${error.message}`);
    }

    return data;
  } else {
    // Insert new answer
    const { data, error } = await supabase
      .from('assessment_answers')
      .insert({
        assessment_id: assessmentId,
        question_id: questionId,
        user_id: userId,
        answer_text: answerText,
        additional_data: additionalData
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Error saving answer: ${error.message}`);
    }

    return data;
  }
}

// Get answers for a specific assessment completion
export async function getAssessmentAnswers(
  assessmentId: string,
  userId: string
): Promise<AssessmentAnswer[]> {
  const { data, error } = await supabase
    .from('assessment_answers')
    .select('*')
    .eq('assessment_id', assessmentId)
    .eq('user_id', userId)
    .order('created_at', { ascending: true });

  if (error) {
    throw new Error(`Error retrieving answers: ${error.message}`);
  }

  return data;
}

// Get answers for a specific prospect (visible to BPOs)
export async function getProspectAssessmentAnswers(
  prospectId: string
): Promise<{
  assessmentId: string;
  assessmentTitle: string;
  answers: AssessmentAnswer[];
}[]> {
  // This requires admin privileges since BPOs need to view these
  const adminClient = createAdminClient();
  
  // First get all completions for this prospect
  const { data: completions, error: completionsError } = await adminClient
    .from('assessment_completions')
    .select('assessment_id, status, completed_at')
    .eq('user_id', prospectId)
    .eq('status', 'completed');

  if (completionsError) {
    throw new Error(`Error retrieving assessment completions: ${completionsError.message}`);
  }

  // Get assessment details
  const { data: assessments, error: assessmentsError } = await adminClient
    .from('assessments')
    .select('id, title')
    .in('id', completions.map(c => c.assessment_id));

  if (assessmentsError) {
    throw new Error(`Error retrieving assessments: ${assessmentsError.message}`);
  }

  // Get all answers for each completed assessment
  const results = await Promise.all(
    completions.map(async (completion) => {
      const { data: answers, error: answersError } = await adminClient
        .from('assessment_answers')
        .select('*')
        .eq('assessment_id', completion.assessment_id)
        .eq('user_id', prospectId);

      if (answersError) {
        throw new Error(`Error retrieving answers: ${answersError.message}`);
      }

      const assessment = assessments.find(a => a.id === completion.assessment_id);
      
      return {
        assessmentId: completion.assessment_id,
        assessmentTitle: assessment?.title || 'Unknown Assessment',
        answers
      };
    })
  );

  return results;
}

// Get typing test performance metrics for a prospect
export async function getProspectTypingStats(
  prospectId: string
): Promise<{
  avgWpm: number;
  avgAccuracy: number;
  assessmentCount: number;
  lastCompletedAt: string | null;
}> {
  const { data: answers, error } = await supabase
    .from('assessment_answers')
    .select('*')
    .eq('user_id', prospectId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Error retrieving typing stats: ${error.message}`);
  }

  // Filter for typing test answers that have additional_data
  const typingTestAnswers = answers.filter(answer => 
    answer.additional_data && 
    answer.additional_data.wpm !== undefined
  );

  if (typingTestAnswers.length === 0) {
    return {
      avgWpm: 0,
      avgAccuracy: 0,
      assessmentCount: 0,
      lastCompletedAt: null
    };
  }

  // Calculate average WPM and accuracy
  const totalWpm = typingTestAnswers.reduce((sum, answer) => 
    sum + answer.additional_data.wpm, 0);
  
  const totalAccuracy = typingTestAnswers.reduce((sum, answer) => 
    sum + answer.additional_data.accuracy, 0);

  return {
    avgWpm: Math.round(totalWpm / typingTestAnswers.length),
    avgAccuracy: Math.round(totalAccuracy / typingTestAnswers.length),
    assessmentCount: typingTestAnswers.length,
    lastCompletedAt: typingTestAnswers[0]?.created_at || null
  };
} 