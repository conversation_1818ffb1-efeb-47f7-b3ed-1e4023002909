import { NextRequest, NextResponse } from 'next/server';
import { createBrowserClient } from '@/lib/supabase';

/**
 * GET /api/organization/departments/[id]
 * Get a specific department by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = createBrowserClient();

    const { data: department, error } = await supabase
      .from('departments')
      .select(`
        id,
        name,
        description,
        department_head_id,
        organization_id,
        created_at,
        updated_at
      `)
      .eq('id', id)
      .single();

    if (error || !department) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Get employee count
    const { data: employments } = await supabase
      .from('employment_relationships')
      .select('id')
      .eq('department_id', id)
      .eq('status', 'active');

    const departmentWithCount = {
      ...department,
      employee_count: employments?.length || 0
    };

    return NextResponse.json(departmentWithCount);

  } catch (error) {
    console.error('Error fetching department:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/organization/departments/[id]
 * Update a department
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { name, description } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Department name is required' },
        { status: 400 }
      );
    }

    const supabase = createBrowserClient();

    // Check if department exists
    const { data: existingDept, error: fetchError } = await supabase
      .from('departments')
      .select('id, organization_id, name')
      .eq('id', id)
      .single();

    if (fetchError || !existingDept) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Check if new name conflicts with existing departments (excluding current one)
    if (name.trim() !== existingDept.name) {
      const { data: conflictDept } = await supabase
        .from('departments')
        .select('id')
        .eq('organization_id', existingDept.organization_id)
        .eq('name', name.trim())
        .neq('id', id)
        .single();

      if (conflictDept) {
        return NextResponse.json(
          { error: 'A department with this name already exists' },
          { status: 409 }
        );
      }
    }

    // Update department
    const { data: updatedDepartment, error: updateError } = await supabase
      .from('departments')
      .update({
        name: name.trim(),
        description: description?.trim() || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating department:', updateError);
      return NextResponse.json(
        { error: 'Failed to update department' },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedDepartment);

  } catch (error) {
    console.error('Error in update department API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/organization/departments/[id]
 * Delete a department
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const supabase = createBrowserClient();

    // Check if department exists
    const { data: department, error: fetchError } = await supabase
      .from('departments')
      .select('id, name')
      .eq('id', id)
      .single();

    if (fetchError || !department) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Check if department has active employees
    const { data: employments } = await supabase
      .from('employment_relationships')
      .select('id')
      .eq('department_id', id)
      .eq('status', 'active');

    if (employments && employments.length > 0) {
      return NextResponse.json(
        { 
          error: `Cannot delete department. It has ${employments.length} active employee(s). Please reassign them first.` 
        },
        { status: 400 }
      );
    }

    // Delete department
    const { error: deleteError } = await supabase
      .from('departments')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting department:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete department' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Department deleted successfully' });

  } catch (error) {
    console.error('Error in delete department API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
