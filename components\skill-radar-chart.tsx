"use client"

import { useTheme } from "next-themes"
import { useEffect, useState } from "react"
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, Radar, Tooltip } from "recharts"

const data = [
  {
    subject: "Communication",
    score: 85,
    fullMark: 100,
  },
  {
    subject: "Problem Solving",
    score: 78,
    fullMark: 100,
  },
  {
    subject: "Technical Knowledge",
    score: 65,
    fullMark: 100,
  },
  {
    subject: "Customer Service",
    score: 90,
    fullMark: 100,
  },
  {
    subject: "Time Management",
    score: 72,
    fullMark: 100,
  },
  {
    subject: "CRM Proficiency",
    score: 68,
    fullMark: 100,
  },
]

export function SkillRadarChart() {
  const { theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div className="h-[300px] flex items-center justify-center">Loading chart...</div>
  }

  const isDark = theme === "dark"

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
          <PolarGrid stroke={isDark ? "#374151" : "#e5e7eb"} />
          <PolarAngleAxis
            dataKey="subject"
            tick={{
              fill: isDark ? "#9ca3af" : "#6b7280",
              fontSize: 12,
              fontWeight: 500,
            }}
          />
          <Radar name="Your Skills" dataKey="score" stroke="#4f46e5" fill="#4f46e5" fillOpacity={0.3} />
          <Tooltip
            contentStyle={{
              backgroundColor: isDark ? "#1f2937" : "#ffffff",
              borderColor: isDark ? "#374151" : "#e5e7eb",
              color: isDark ? "#ffffff" : "#000000",
              borderRadius: "0.375rem",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
              fontWeight: 500,
            }}
            formatter={(value) => [`${value}%`, "Score"]}
          />
        </RadarChart>
      </ResponsiveContainer>
    </div>
  )
}
