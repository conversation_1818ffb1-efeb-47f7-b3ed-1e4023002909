/**
 * Dynamic Navigation Configuration
 * Centralized navigation structure for all dashboard contexts
 */

import {
  Home,
  User,
  BookOpen,
  ClipboardCheck,
  Brain,
  Phone,
  FileCheck,
  Award,
  Briefcase,
  FileText,
  MessageSquare,
  Settings,
  HelpCircle,
  Building2,
  Users,
  BarChart3,
  UserPlus,
  Shield,
  Database,
  GraduationCap,
  Route,
  Search,
  Inbox,
  Calendar,
  Puzzle,
  Monitor,
  type LucideIcon,
} from "lucide-react"

export interface NavItem {
  title: string
  url: string
  icon: LucideIcon
  badge?: number
  isActive?: boolean
  description?: string
}

export interface NavGroup {
  label: string
  items: NavItem[]
}

export type DashboardContext = 'individual' | 'organization' | 'admin'

/**
 * Individual Dashboard Navigation
 */
export const individualNavGroups: NavGroup[] = [
  {
    label: "Main Menu",
    items: [
      {
        title: "Dashboard",
        url: "/individual",
        icon: Home,
        description: "Overview of your learning journey"
      },
      {
        title: "Profile",
        url: "/individual/profile",
        icon: User,
        description: "Manage your personal information"
      },
    ]
  },
  {
    label: "Learning",
    items: [
      {
        title: "Learning Paths",
        url: "/individual/training",
        icon: BookOpen,
        description: "Structured learning programs"
      },
      {
        title: "Assessments",
        url: "/individual/assessments",
        icon: ClipboardCheck,
        description: "Test your knowledge and skills"
      },
      {
        title: "Role-Call Training",
        url: "/individual/role-call-training",
        icon: Phone,
        description: "Interactive training sessions"
      },
      {
        title: "Courses Marketplace",
        url: "/individual/courses-marketplace",
        icon: Award,
        description: "Discover new courses"
      },
    ]
  },
  {
    label: "Career",
    items: [
      {
        title: "Job Board",
        url: "/individual/job-board",
        icon: Briefcase,
        description: "Find career opportunities"
      },
      {
        title: "My Applications",
        url: "/individual/applications",
        icon: FileText,
        description: "Track your job applications"
      },
      {
        title: "Interviews",
        url: "/individual/interviews",
        icon: MessageSquare,
        description: "Manage interview schedules"
      },
    ]
  },
  {
    label: "Settings",
    items: [
      {
        title: "Help & Support",
        url: "/individual/help",
        icon: HelpCircle,
        description: "Get assistance and support"
      },
      {
        title: "Settings",
        url: "/individual/settings",
        icon: Settings,
        description: "Configure your preferences"
      },
    ]
  },
]

/**
 * Organization Dashboard Navigation
 */
export const organizationNavGroups: NavGroup[] = [
  {
    label: "Overview",
    items: [
      {
        title: "Dashboard",
        url: "/organization",
        icon: Home,
        description: "Organization overview and metrics"
      },
      {
        title: "Analytics",
        url: "/organization/analytics",
        icon: BarChart3,
        description: "Performance insights and reports"
      },
    ]
  },
  {
    label: "Team Management",
    items: [
      {
        title: "Staff",
        url: "/organization/staff",
        icon: Users,
        description: "Manage team members"
      },
      {
        title: "Departments",
        url: "/organization/departments",
        icon: Building2,
        description: "Organize teams by departments"
      },
      {
        title: "Job Roles",
        url: "/organization/job-roles",
        icon: UserPlus,
        description: "Define and manage job positions"
      },
    ]
  },
  {
    label: "Learning & Development",
    items: [
      {
        title: "Training Programs",
        url: "/organization/training",
        icon: GraduationCap,
        description: "Manage training initiatives"
      },
      {
        title: "Assessments",
        url: "/organization/assessments",
        icon: ClipboardCheck,
        description: "Create and manage assessments"
      },
      {
        title: "Learning Paths",
        url: "/organization/learning-paths",
        icon: Route,
        description: "Design learning journeys"
      },
    ]
  },
  {
    label: "Recruitment",
    items: [
      {
        title: "Vacancies",
        url: "/organization/vacancies",
        icon: Briefcase,
        description: "Manage job openings"
      },
      {
        title: "Candidates",
        url: "/organization/candidates",
        icon: Search,
        description: "Review job applicants"
      },
      {
        title: "Interviews",
        url: "/organization/interviews",
        icon: Calendar,
        description: "Schedule and manage interviews"
      },
    ]
  },
  {
    label: "Settings",
    items: [
      {
        title: "Business Profile",
        url: "/organization/business-profile",
        icon: Building2,
        description: "Update organization details"
      },
      {
        title: "Settings",
        url: "/organization/settings",
        icon: Settings,
        description: "Configure organization preferences"
      },
    ]
  },
]

/**
 * Admin Dashboard Navigation
 */
export const adminNavGroups: NavGroup[] = [
  {
    label: "Platform Management",
    items: [
      {
        title: "Dashboard",
        url: "/admin",
        icon: Home,
        description: "Platform overview and metrics"
      },
      {
        title: "Analytics",
        url: "/admin/analytics",
        icon: BarChart3,
        description: "Platform-wide analytics"
      },
      {
        title: "System Health",
        url: "/admin/system",
        icon: Monitor,
        description: "Monitor system performance"
      },
    ]
  },
  {
    label: "User Management",
    items: [
      {
        title: "Organizations",
        url: "/admin/organizations",
        icon: Building2,
        description: "Manage client organizations"
      },
      {
        title: "Users",
        url: "/admin/users",
        icon: Users,
        description: "Manage platform users"
      },
      {
        title: "Permissions",
        url: "/admin/permissions",
        icon: Shield,
        description: "Configure access controls"
      },
    ]
  },
  {
    label: "Content Management",
    items: [
      {
        title: "Programs",
        url: "/admin/programs",
        icon: GraduationCap,
        description: "Manage learning programs"
      },
      {
        title: "Pathways",
        url: "/admin/pathways",
        icon: Route,
        description: "Configure learning pathways"
      },
      {
        title: "Courses",
        url: "/admin/courses",
        icon: BookOpen,
        description: "Manage course content"
      },
      {
        title: "Assessments",
        url: "/admin/assessments",
        icon: FileText,
        description: "Manage assessment library"
      },
    ]
  },
  {
    label: "Platform Settings",
    items: [
      {
        title: "Configuration",
        url: "/admin/platform",
        icon: Brain,
        description: "Platform configuration"
      },
      {
        title: "Data Management",
        url: "/admin/data",
        icon: Database,
        description: "Manage platform data"
      },
      {
        title: "Settings",
        url: "/admin/settings",
        icon: Settings,
        description: "Platform settings"
      },
    ]
  },
]

/**
 * Get navigation groups based on context
 */
export function getNavigationGroups(context: DashboardContext): NavGroup[] {
  switch (context) {
    case 'individual':
      return individualNavGroups
    case 'organization':
      return organizationNavGroups
    case 'admin':
      return adminNavGroups
    default:
      return individualNavGroups
  }
}

/**
 * Determine context from pathname
 */
export function getContextFromPathname(pathname: string): DashboardContext {
  if (pathname.startsWith('/admin')) {
    return 'admin'
  }
  if (pathname.startsWith('/organization') || pathname.startsWith('/org/')) {
    return 'organization'
  }
  return 'individual'
}

/**
 * Get active navigation item based on pathname
 */
export function getActiveNavItem(navGroups: NavGroup[], pathname: string): NavItem | null {
  for (const group of navGroups) {
    for (const item of group.items) {
      if (pathname === item.url || (item.url !== '/' && pathname.startsWith(item.url))) {
        return item
      }
    }
  }
  return null
}
