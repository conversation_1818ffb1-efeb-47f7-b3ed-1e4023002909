/**
 * Navigation Hook
 * Provides dynamic navigation configuration based on user context and permissions
 */

"use client"

import { usePathname } from "next/navigation"
import { useMemo } from "react"
import { useLunaAuth, useCurrentContext } from "@/hooks/use-luna-auth"
import { 
  getNavigationGroups, 
  getContextFromPathname, 
  getActiveNavItem,
  type NavGroup,
  type NavItem,
  type DashboardContext 
} from "@/lib/navigation-config"

export interface NavigationState {
  navGroups: NavGroup[]
  activeItem: NavItem | null
  context: DashboardContext
  isLoading: boolean
}

/**
 * Hook to get dynamic navigation based on current context
 */
export function useNavigation(): NavigationState {
  const pathname = usePathname()
  const { user, loading } = useLunaAuth()
  const { context } = useCurrentContext()

  const navigationState = useMemo(() => {
    // Always return navigation immediately - don't wait for loading
    // if (loading || !user) {
    //   return {
    //     navGroups: [],
    //     activeItem: null,
    //     context: 'individual' as DashboardContext,
    //     isLoading: true
    //   }
    // }

    // Determine context from pathname - pathname takes priority over user context
    let currentContext: DashboardContext = getContextFromPathname(pathname)

    // Only override with user context if we're on a generic route (like root)
    // but NOT if we're explicitly on /individual or /org routes
    if (context?.type === 'organization' &&
        !pathname.startsWith('/admin') &&
        !pathname.startsWith('/individual') &&
        !pathname.startsWith('/org/') &&
        pathname === '/') {
      currentContext = 'organization'
    }

    // Get navigation groups for current context
    const navGroups = getNavigationGroups(currentContext)
    
    // Filter navigation based on user permissions
    const filteredNavGroups = filterNavigationByPermissions(navGroups, user, currentContext)
    
    // Get active navigation item
    const activeItem = getActiveNavItem(filteredNavGroups, pathname)

    return {
      navGroups: filteredNavGroups,
      activeItem,
      context: currentContext,
      isLoading: false
    }
  }, [pathname, user, loading, context])

  return navigationState
}

/**
 * Filter navigation items based on user permissions
 */
function filterNavigationByPermissions(
  navGroups: NavGroup[], 
  user: any, 
  context: DashboardContext
): NavGroup[] {
  // For now, return all navigation items
  // In the future, this can be enhanced to filter based on user roles and permissions
  
  if (context === 'admin') {
    // Only show admin navigation to platform admins
    if (user.role !== 'platform_admin') {
      return []
    }
  }

  if (context === 'organization') {
    // Only show organization navigation to users with organization access
    const hasOrgAccess = user.employmentRelationships?.length > 0 || user.role === 'platform_admin'
    if (!hasOrgAccess) {
      return []
    }
  }

  return navGroups
}

/**
 * Hook to get breadcrumbs based on current navigation
 */
export function useBreadcrumbs() {
  const pathname = usePathname()
  const { activeItem, context } = useNavigation()

  const breadcrumbs = useMemo(() => {
    const segments = pathname.split('/').filter(Boolean)
    const crumbs = []

    // Add context root
    if (context === 'admin') {
      crumbs.push({ label: 'Admin', href: '/admin' })
    } else if (context === 'organization') {
      crumbs.push({ label: 'Organization', href: '/organization' })
    } else {
      crumbs.push({ label: 'Dashboard', href: '/individual' })
    }

    // Add current page if it's not the root
    if (activeItem && activeItem.url !== `/${context}`) {
      crumbs.push({ label: activeItem.title, href: activeItem.url })
    }

    // Add additional segments for nested pages
    if (segments.length > 2) {
      const additionalSegments = segments.slice(2)
      additionalSegments.forEach((segment, index) => {
        const href = `/${segments.slice(0, 3 + index).join('/')}`
        const label = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ')
        
        crumbs.push({ label, href })
      })
    }

    return crumbs
  }, [pathname, activeItem, context])

  return breadcrumbs
}

/**
 * Hook to check if user has access to a specific navigation item
 */
export function useNavigationAccess() {
  const { user } = useLunaAuth()

  const hasAccess = (item: NavItem): boolean => {
    if (!user) return false

    // Admin routes require platform admin role
    if (item.url.startsWith('/admin')) {
      return user.role === 'platform_admin'
    }

    // Organization routes require organization access
    if (item.url.startsWith('/organization')) {
      return user.employmentRelationships?.length > 0 || user.role === 'platform_admin'
    }

    // Individual routes are accessible to all authenticated users
    return true
  }

  return { hasAccess }
}

/**
 * Hook to get navigation statistics
 */
export function useNavigationStats() {
  const { navGroups } = useNavigation()

  const stats = useMemo(() => {
    const totalGroups = navGroups.length
    const totalItems = navGroups.reduce((acc, group) => acc + group.items.length, 0)
    const itemsWithBadges = navGroups
      .flatMap(group => group.items)
      .filter(item => item.badge && item.badge > 0).length

    return {
      totalGroups,
      totalItems,
      itemsWithBadges
    }
  }, [navGroups])

  return stats
}
