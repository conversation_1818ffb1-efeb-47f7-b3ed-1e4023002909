import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get skill categories with skill counts
    const { data: categories, error: categoriesError } = await supabase
      .from('skill_categories')
      .select(`
        *,
        skills(count)
      `)
      .order('sort_order');

    if (categoriesError) {
      console.error('Error fetching skill categories:', categoriesError);
      return NextResponse.json(
        { error: 'Failed to fetch skill categories' },
        { status: 500 }
      );
    }

    // Get skills with category info
    const { data: skills, error: skillsError } = await supabase
      .from('skills')
      .select(`
        *,
        skill_categories(name, color_code)
      `)
      .order('name');

    if (skillsError) {
      console.error('Error fetching skills:', skillsError);
      return NextResponse.json(
        { error: 'Failed to fetch skills' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      categories: categories || [],
      skills: skills || []
    });

  } catch (error) {
    console.error('Unexpected error in skills API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.category_id || !body.skill_type || !body.market_demand) {
      return NextResponse.json(
        { error: 'Name, category, skill type, and market demand are required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('skills')
      .insert([{
        name: body.name,
        category_id: body.category_id,
        subcategory: body.subcategory || null,
        skill_type: body.skill_type,
        description: body.description || null,
        certification_available: body.certification_available || false,
        market_demand: body.market_demand,
        is_active: body.is_active !== undefined ? body.is_active : true,
      }])
      .select(`
        *,
        skill_categories(name, color_code, icon)
      `)
      .single();

    if (error) {
      console.error('Error creating skill:', error);
      return NextResponse.json(
        { error: 'Failed to create skill' },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Unexpected error in skills POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
