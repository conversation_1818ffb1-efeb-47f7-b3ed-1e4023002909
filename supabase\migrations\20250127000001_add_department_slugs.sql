-- Add slug support to departments table
-- This enables slug-based routing for departments

-- Step 1: Add slug column to departments table
ALTER TABLE departments ADD COLUMN IF NOT EXISTS slug TEXT;

-- Step 2: Create a function to generate slugs from names
CREATE OR REPLACE FUNCTION generate_slug(input_text TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN lower(
    regexp_replace(
      regexp_replace(
        regexp_replace(input_text, '[^a-zA-Z0-9\s-]', '', 'g'),
        '\s+', '-', 'g'
      ),
      '-+', '-', 'g'
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Step 3: Generate slugs for existing departments
UPDATE departments 
SET slug = generate_slug(name) 
WHERE slug IS NULL;

-- Step 4: Make slug column NOT NULL and add unique constraint
ALTER TABLE departments ALTER COLUMN slug SET NOT NULL;
ALTER TABLE departments ADD CONSTRAINT departments_org_slug_unique UNIQUE(organization_id, slug);

-- Step 5: Create index for performance
CREATE INDEX IF NOT EXISTS idx_departments_slug ON departments(slug);
CREATE INDEX IF NOT EXISTS idx_departments_org_slug ON departments(organization_id, slug);

-- Step 6: Create trigger to auto-generate slugs for new departments
CREATE OR REPLACE FUNCTION set_department_slug()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.slug IS NULL OR NEW.slug = '' THEN
    NEW.slug := generate_slug(NEW.name);
  END IF;
  
  -- Ensure slug is unique within organization
  DECLARE
    base_slug TEXT := NEW.slug;
    counter INTEGER := 1;
  BEGIN
    WHILE EXISTS (
      SELECT 1 FROM departments 
      WHERE organization_id = NEW.organization_id 
      AND slug = NEW.slug 
      AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
    ) LOOP
      NEW.slug := base_slug || '-' || counter;
      counter := counter + 1;
    END LOOP;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create trigger
DROP TRIGGER IF EXISTS trigger_set_department_slug ON departments;
CREATE TRIGGER trigger_set_department_slug
  BEFORE INSERT OR UPDATE ON departments
  FOR EACH ROW
  EXECUTE FUNCTION set_department_slug();
