import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Database } from '@/types/supabase';

// PUT - Update modules order
export async function PUT(req: Request) {
  try {
    const { modules } = await req.json();
    
    if (!modules || !Array.isArray(modules)) {
      return NextResponse.json(
        { error: 'Invalid request data - "modules" must be an array' }, 
        { status: 400 }
      );
    }
    
    // Check that modules has the right structure
    const isValidModules = modules.every(
      (m: any) => typeof m.id === 'string' && typeof m.requiredOrder === 'number'
    );
    
    if (!isValidModules) {
      return NextResponse.json(
        { error: 'Invalid modules format - each module must have an id (string) and requiredOrder (number)' }, 
        { status: 400 }
      );
    }
    
    // Create Supabase client
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Verify user is authenticated and is an admin
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get the user role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();
    
    if (userError || !userData || userData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 });
    }
    
    // Prepare an array of promises for each update
    const updatePromises = modules.map((module: { id: string, requiredOrder: number }) => {
      return supabase
        .from('training_modules')
        .update({ required_order: module.requiredOrder })
        .eq('id', module.id);
    });
    
    // Execute all updates in parallel
    const results = await Promise.all(updatePromises);
    
    // Check for errors
    const errors = results.filter(result => result.error).map(result => result.error);
    
    if (errors.length > 0) {
      console.error('Errors updating module order:', errors);
      return NextResponse.json(
        { error: 'Some modules could not be updated', details: errors }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Module order updated successfully' 
    });
  } catch (error: any) {
    console.error('Error updating module order:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update module order' }, 
      { status: 500 }
    );
  }
} 