import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerComponentClient<Database>({ cookies })
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get query parameters
    const { searchParams } = new URL(request.url)
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || 'all'
    const level = searchParams.get('level') || 'all'
    const sortBy = searchParams.get('sortBy') || 'popular'

    // Build the query
    let query = supabase
      .from('courses')
      .select(`
        id,
        name,
        description,
        slug,
        level,
        estimated_duration,
        status,
        price,
        cover_image_url,
        preview_video_url,
        meta_description,
        tags,
        learning_objectives,
        target_audience,
        enrollment_count,
        completion_rate,
        average_rating,
        course_complexity,
        certification_available,
        is_standalone,
        language,
        created_at,
        updated_at,
        instructor:users!courses_instructor_id_fkey(
          id,
          full_name,
          email
        )
      `)
      .eq('status', 'published') // Only show published courses
      .eq('is_standalone', true) // Only show standalone courses for marketplace

    // Apply search filter
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,tags.cs.{${search}}`)
    }

    // Apply level filter
    if (level !== 'all') {
      query = query.eq('level', level)
    }

    // Apply category filter (using tags for now)
    if (category !== 'all') {
      query = query.contains('tags', [category])
    }

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        query = query.order('enrollment_count', { ascending: false })
        break
      case 'rating':
        query = query.order('average_rating', { ascending: false })
        break
      case 'newest':
        query = query.order('created_at', { ascending: false })
        break
      case 'price_low':
        query = query.order('price', { ascending: true, nullsFirst: false })
        break
      case 'price_high':
        query = query.order('price', { ascending: false, nullsFirst: true })
        break
      default:
        query = query.order('enrollment_count', { ascending: false })
    }

    const { data: courses, error } = await query

    if (error) {
      console.error('Error fetching courses:', error)
      return NextResponse.json(
        { error: 'Failed to fetch courses' },
        { status: 500 }
      )
    }

    // Transform the data to match the frontend interface
    const transformedCourses = courses?.map(course => ({
      id: course.id,
      title: course.name,
      description: course.description || '',
      instructor: course.instructor?.full_name || 'Unknown Instructor',
      category: course.tags?.[0] || 'General',
      level: course.level?.charAt(0).toUpperCase() + course.level?.slice(1) || 'Beginner',
      duration: course.estimated_duration ? `${Math.ceil(course.estimated_duration / 60 / 7)} weeks` : 'Self-paced',
      price: course.price || 0,
      rating: course.average_rating || 0,
      reviewCount: Math.floor(course.enrollment_count * 0.3), // Estimate review count
      enrolledCount: course.enrollment_count || 0,
      thumbnail: course.cover_image_url || '/api/placeholder/300/200',
      tags: course.tags || [],
      isPopular: course.enrollment_count > 1000,
      isFeatured: course.course_complexity === 'advanced' || course.certification_available,
      slug: course.slug,
      originalPrice: course.price ? Math.round(course.price * 1.3) : undefined,
      certification: course.certification_available,
      language: course.language || 'en',
      learningObjectives: course.learning_objectives || [],
      targetAudience: course.target_audience
    })) || []

    // Get categories from all courses for filter options
    const allTags = courses?.flatMap(course => course.tags || []) || []
    const uniqueCategories = ['all', ...Array.from(new Set(allTags))]

    return NextResponse.json({
      courses: transformedCourses,
      categories: uniqueCategories,
      total: transformedCourses.length
    })

  } catch (error) {
    console.error('Error in courses API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
