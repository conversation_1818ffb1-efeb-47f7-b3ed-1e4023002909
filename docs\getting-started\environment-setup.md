# 🔧 Environment Setup

This guide covers setting up your development environment for the Luna Skills Platform, including environment variables, database configuration, and development tools.

## 📋 Environment Variables

### Required Variables

Create a `.env.local` file in your project root with the following variables:

```env
# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================

# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co

# Supabase anonymous key (safe to expose in browser)
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Supabase service role key (server-side only, keep secret)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application URL (used for redirects and API calls)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment (development, staging, production)
NODE_ENV=development

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Analytics and monitoring
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Email service (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# File upload configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760  # 10MB in bytes
NEXT_PUBLIC_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.jpg,.jpeg,.png

# Rate limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000  # 15 minutes in milliseconds
```

### Environment-Specific Configurations

#### Development Environment
```env
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
DEBUG=true
LOG_LEVEL=debug
```

#### Staging Environment
```env
NODE_ENV=staging
NEXT_PUBLIC_APP_URL=https://staging.bpoplatform.com
DEBUG=false
LOG_LEVEL=info
```

#### Production Environment
```env
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://bpoplatform.com
DEBUG=false
LOG_LEVEL=error
```

## 🗄️ Database Configuration

### Supabase Setup

#### 1. Create Database Tables

Run the following SQL in your Supabase SQL Editor:

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'bpo_admin', 'prospect');
CREATE TYPE user_status AS ENUM ('active', 'inactive', 'pending_activation');
CREATE TYPE training_status AS ENUM ('not_started', 'in_progress', 'completed');
```

#### 2. Set Up Row Level Security (RLS)

```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE prospects ENABLE ROW LEVEL SECURITY;
ALTER TABLE bpos ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_modules ENABLE ROW LEVEL SECURITY;
-- ... (continue for all tables)
```

#### 3. Create Security Functions

```sql
-- Function to check if user is platform admin
CREATE OR REPLACE FUNCTION is_platform_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Database Migrations

#### Using Supabase CLI
```bash
# Initialize Supabase in your project
supabase init

# Link to your remote project
supabase link --project-ref your-project-ref

# Create a new migration
supabase migration new add_new_feature

# Apply migrations
supabase db push
```

#### Manual Migration Process
1. Create SQL files in `database/migrations/`
2. Name them with timestamps: `20240101000000_migration_name.sql`
3. Run them in order in Supabase SQL Editor

## 🔐 Authentication Setup

### Supabase Auth Configuration

#### 1. Configure Auth Settings

In your Supabase dashboard:
1. Go to **Authentication** → **Settings**
2. Configure the following:

```json
{
  "site_url": "http://localhost:3000",
  "redirect_urls": [
    "http://localhost:3000/auth/callback",
    "https://yourdomain.com/auth/callback"
  ],
  "jwt_expiry": 3600,
  "refresh_token_rotation_enabled": true,
  "security_update_password_require_reauthentication": true
}
```

#### 2. Set Up Email Templates

Customize email templates in **Authentication** → **Email Templates**:

- **Confirm signup**: Welcome email with account activation
- **Magic Link**: Passwordless login email
- **Change Email Address**: Email change confirmation
- **Reset Password**: Password reset instructions

#### 3. Configure OAuth Providers (Optional)

Enable social login providers:

```json
{
  "google": {
    "enabled": true,
    "client_id": "your_google_client_id",
    "client_secret": "your_google_client_secret"
  },
  "github": {
    "enabled": true,
    "client_id": "your_github_client_id",
    "client_secret": "your_github_client_secret"
  }
}
```

## 🛠️ Development Tools

### VS Code Configuration

Create `.vscode/settings.json`:

```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "files.associations": {
    "*.css": "tailwindcss"
  }
}
```

### ESLint Configuration

The project includes a comprehensive ESLint configuration. To customize:

```json
{
  "extends": [
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "warn",
    "prefer-const": "error",
    "no-console": "warn"
  }
}
```

### Prettier Configuration

Create `.prettierrc`:

```json
{
  "semi": false,
  "trailingComma": "es5",
  "singleQuote": true,
  "tabWidth": 2,
  "useTabs": false,
  "printWidth": 100,
  "plugins": ["prettier-plugin-tailwindcss"]
}
```

## 🔄 Git Configuration

### Git Hooks with Husky

The project uses Husky for git hooks:

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "npm run type-check"
    }
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{md,json}": [
      "prettier --write"
    ]
  }
}
```

### Git Ignore Configuration

Ensure your `.gitignore` includes:

```gitignore
# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
.next/
out/
build/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Supabase
.supabase/
```

## 📊 Monitoring and Analytics

### Performance Monitoring

Configure performance monitoring:

```typescript
// lib/monitoring.ts
export const performanceConfig = {
  webVitals: {
    enabled: process.env.NODE_ENV === 'production',
    endpoint: '/api/analytics/web-vitals'
  },
  errorTracking: {
    enabled: true,
    sentry: {
      dsn: process.env.NEXT_PUBLIC_SENTRY_DSN
    }
  }
}
```

### Analytics Setup

```typescript
// lib/analytics.ts
export const analyticsConfig = {
  googleAnalytics: {
    measurementId: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID
  },
  customEvents: {
    trackPageViews: true,
    trackUserInteractions: true,
    trackPerformanceMetrics: true
  }
}
```

## 🧪 Testing Environment

### Test Database Setup

Create a separate Supabase project for testing:

```env
# .env.test.local
NEXT_PUBLIC_SUPABASE_URL=https://test-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=test_anon_key
SUPABASE_SERVICE_ROLE_KEY=test_service_role_key
```

### Test Configuration

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1'
  },
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/']
}
```

## 🚀 Deployment Configuration

### Vercel Deployment

Create `vercel.json`:

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase_url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key"
  }
}
```

### Environment Variables in Production

Set these in your deployment platform:

1. **Vercel**: Project Settings → Environment Variables
2. **Netlify**: Site Settings → Environment Variables
3. **Railway**: Project → Variables

## 🔍 Validation and Testing

### Environment Validation

Create a validation script:

```typescript
// scripts/validate-env.ts
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
]

requiredEnvVars.forEach(envVar => {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`)
  }
})

console.log('✅ All required environment variables are set')
```

### Health Check Endpoint

Create an API endpoint to verify configuration:

```typescript
// app/api/health/route.ts
export async function GET() {
  const checks = {
    database: await checkDatabaseConnection(),
    auth: await checkAuthConfiguration(),
    environment: validateEnvironmentVariables()
  }
  
  return Response.json(checks)
}
```

---

**Next Steps**: After setting up your environment, proceed to the [Quick Start Guide](quick-start.md) to begin development.
