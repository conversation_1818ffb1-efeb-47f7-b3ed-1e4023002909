import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-admin';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Get program with related data
    const { data: program, error: programError } = await adminClient
      .from('programs')
      .select(`
        *,
        created_by_user:users!programs_created_by_fkey(
          id,
          full_name,
          email
        ),
        learning_paths(
          id,
          title,
          description,
          slug,
          estimated_duration_hours,
          difficulty_level,
          status,
          sort_order,
          is_featured,
          created_at,
          pathway_courses(
            id,
            sequence_order,
            is_required,
            course:courses(
              id,
              name,
              level,
              estimated_duration,
              status
            )
          )
        )
      `)
      .eq('id', id)
      .single();

    if (programError) {
      console.error('Error fetching program:', programError);
      return NextResponse.json(
        { error: programError.message || 'Program not found' },
        { status: programError.code === 'PGRST116' ? 404 : 500 }
      );
    }

    // Add counts and organize data
    const programWithCounts = {
      ...program,
      _count: {
        pathways: program.learning_paths?.length || 0,
        active_pathways: program.learning_paths?.filter((p: any) => p.status === 'active').length || 0,
        total_courses: program.learning_paths?.reduce((acc: number, pathway: any) => 
          acc + (pathway.pathway_courses?.length || 0), 0) || 0
      }
    };

    return NextResponse.json(programWithCounts);

  } catch (error: any) {
    console.error('Program GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      industry,
      cover_image_url,
      status,
      sort_order
    } = body;

    // Validate required fields
    if (!name || !description) {
      return NextResponse.json(
        { error: 'Name and description are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Check if program name already exists (excluding current program)
    const { data: existingProgram } = await adminClient
      .from('programs')
      .select('id')
      .eq('name', name.trim())
      .neq('id', id)
      .single();

    if (existingProgram) {
      return NextResponse.json(
        { error: 'A program with this name already exists' },
        { status: 400 }
      );
    }

    // Update program
    const { data: program, error: programError } = await adminClient
      .from('programs')
      .update({
        name: name.trim(),
        description: description.trim(),
        industry: industry?.trim(),
        cover_image_url,
        status: status || 'active',
        sort_order: sort_order || 0,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        created_by_user:users!programs_created_by_fkey(
          id,
          full_name,
          email
        )
      `)
      .single();

    if (programError) {
      console.error('Error updating program:', programError);
      return NextResponse.json(
        { error: programError.message || 'Failed to update program' },
        { status: programError.code === 'PGRST116' ? 404 : 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Program updated successfully',
      program
    });

  } catch (error: any) {
    console.error('Program update API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Check if program has pathways
    const { data: pathways } = await adminClient
      .from('learning_paths')
      .select('id')
      .eq('program_id', id);

    if (pathways && pathways.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete program with existing pathways. Please remove all pathways first.' },
        { status: 400 }
      );
    }

    // Delete program
    const { error: deleteError } = await adminClient
      .from('programs')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting program:', deleteError);
      return NextResponse.json(
        { error: deleteError.message || 'Failed to delete program' },
        { status: deleteError.code === 'PGRST116' ? 404 : 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Program deleted successfully'
    });

  } catch (error: any) {
    console.error('Program delete API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
