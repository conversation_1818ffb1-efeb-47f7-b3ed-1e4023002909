import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { Database } from '@/types/supabase';

// Create admin client to bypass Row Level Security policies
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);

export async function POST(req: NextRequest) {
  try {
    console.log("🔍 Profile visibility toggle endpoint called");
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Get the session to verify user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log("❌ No session found");
      return NextResponse.json(
        { error: 'Unauthorized - You must be logged in' },
        { status: 401 }
      );
    }
    
    console.log("✅ User authenticated:", session.user.id);
    
    // Get user data to verify role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();
    
    if (userError || !userData || userData.role !== 'prospect') {
      console.log("❌ User role check failed:", userError?.message || "Not a prospect");
      return NextResponse.json(
        { error: 'Forbidden - Only prospects can update this information' },
        { status: 403 }
      );
    }
    
    console.log("✅ User role verified:", userData.role);
    
    // Get the prospect record associated with the user using admin client to bypass RLS
    const { data: prospectData, error: prospectError } = await adminClient
      .from('prospects')
      .select('id, profile_visibility')
      .eq('user_id', session.user.id)
      .single();
    
    if (prospectError) {
      console.log("❌ Failed to find prospect profile:", prospectError.message);
      return NextResponse.json(
        { error: prospectError.message || 'Failed to find prospect profile' },
        { status: 500 }
      );
    }
    
    console.log("✅ Found prospect profile:", prospectData.id);
    console.log("📊 Current visibility:", prospectData.profile_visibility);
    
    // Toggle visibility
    const newVisibility = !prospectData.profile_visibility;
    
    // Use admin client to update prospect to bypass RLS policies
    const { data: updateResult, error: updateProspectError } = await adminClient
      .from('prospects')
      .update({ profile_visibility: newVisibility })
      .eq('id', prospectData.id)
      .select();
    
    if (updateProspectError) {
      console.log("❌ Failed to update profile visibility:", updateProspectError.message);
      return NextResponse.json(
        { error: updateProspectError.message || 'Failed to update profile visibility' },
        { status: 500 }
      );
    }
    
    console.log("✅ Profile visibility updated successfully", updateResult);
    
    return NextResponse.json({
      success: true,
      visibility: newVisibility,
      message: newVisibility 
        ? 'Your profile is now visible to employers' 
        : 'Your profile is now hidden from employers'
    });
    
  } catch (error: any) {
    console.error('❌ Error updating profile visibility:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 