import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser, createAuthErrorResponse } from '@/lib/auth';
import { createApiClient } from '@/lib/supabase-server';
import { Database } from '@/types/database.types';

export async function POST(req: NextRequest) {
  try {
    // Use standardized authentication
    const authResult = await getAuthenticatedUser();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const user = authResult.user;
    const supabase = await createApiClient();

    // Parse request body
    const body = await req.json();
    const { action, skillData, skillIndex } = body;

    console.log('Skills API - Request:', { action, skillData, skillIndex });

    // Get the prospect's profile using proper RLS (no admin client needed!)
    const { data: prospectData, error: prospectError } = await supabase
      .from('prospects')
      .select('id, skills')
      .eq('user_id', user.id)
      .single();

    if (prospectError) {
      console.error('Skills API - Error fetching prospect:', prospectError);
      return NextResponse.json(
        { error: prospectError.message || 'Failed to find prospect profile' },
        { status: 500 }
      );
    }
    
    console.log('Skills API - Prospect data:', { 
      id: prospectData.id, 
      skills: prospectData.skills,
      skillsType: typeof prospectData.skills,
      isArray: Array.isArray(prospectData.skills)
    });
    
    // Ensure skills is always an array, even if null or undefined
    let skills = Array.isArray(prospectData.skills) ? JSON.parse(JSON.stringify(prospectData.skills)) : [];
    
    console.log('Skills API - Initial skills array:', skills);
    
    // Sanitize skills to ensure it's a valid JSON structure
    const sanitizeSkill = (skill: any) => {
      // Keep only the valid properties
      return {
        name: typeof skill.name === 'string' ? skill.name : '',
        level: typeof skill.level === 'string' ? skill.level : 'Beginner',
        proficiency: typeof skill.proficiency === 'number' ? skill.proficiency : 50
      };
    };
    
    // Handle the different actions
    switch (action) {
      case 'add':
        // Add a new skill (sanitized)
        skills.push(sanitizeSkill(skillData));
        break;
        
      case 'update':
        // Update an existing skill
        if (skillIndex >= 0 && skillIndex < skills.length) {
          skills[skillIndex] = {
            ...sanitizeSkill(skills[skillIndex]),
            ...sanitizeSkill(skillData)
          };
        } else {
          return NextResponse.json(
            { error: 'Invalid skill index' },
            { status: 400 }
          );
        }
        break;
        
      case 'delete':
        // Delete a skill
        if (skillIndex >= 0 && skillIndex < skills.length) {
          skills = skills.filter((_: any, index: number) => index !== skillIndex);
        } else {
          return NextResponse.json(
            { error: 'Invalid skill index' },
            { status: 400 }
          );
        }
        break;
        
      case 'update_batch':
        // Update the entire skills array
        if (Array.isArray(body.skills)) {
          skills = body.skills.map(sanitizeSkill);
        } else {
          return NextResponse.json(
            { error: 'Invalid skills array' },
            { status: 400 }
          );
        }
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
    
    // Update skills using proper RLS (much simpler now!)
    const { error: updateError } = await supabase
      .from('prospects')
      .update({ skills })
      .eq('id', prospectData.id);

    if (updateError) {
      console.error('Skills API - Update error:', updateError);
      return NextResponse.json(
        { error: updateError.message || 'Failed to update skills' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Skills ${action === 'add' ? 'added' : action === 'update' ? 'updated' : action === 'delete' ? 'deleted' : 'updated'} successfully`,
      skills
    });
  } catch (error: any) {
    console.error('Error updating skills:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 