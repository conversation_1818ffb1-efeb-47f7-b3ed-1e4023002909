import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { Database } from '@/types/supabase';

// Create admin client to bypass Row Level Security policies
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);

// Define contact info type for type safety
interface ContactInfo {
  phone?: string;
  location?: {
    city?: string;
    country?: string;
  };
  [key: string]: any; // For other potential fields
}

export async function POST(req: NextRequest) {
  try {
    console.log("🔍 Personal info update endpoint called");
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Get the session to verify user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log("❌ No session found");
      return NextResponse.json(
        { error: 'Unauthorized - You must be logged in' },
        { status: 401 }
      );
    }
    
    console.log("✅ User authenticated:", session.user.id);
    
    // Get user data to verify role
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('role')
      .eq('id', session.user.id)
      .single();
    
    if (userError || !userData || userData.role !== 'prospect') {
      console.log("❌ User role check failed:", userError?.message || "Not a prospect");
      return NextResponse.json(
        { error: 'Forbidden - Only prospects can update this information' },
        { status: 403 }
      );
    }
    
    console.log("✅ User role verified:", userData.role);
    
    // Parse request body
    const body = await req.json();
    console.log("📦 Request body:", body);
    const { firstName, lastName, phone, city, country } = body;
    
    // Update user's full name in users table
    const fullName = `${firstName} ${lastName}`.trim();
    
    if (fullName) {
      console.log("🔄 Updating full name:", fullName);
      const { error: updateUserError } = await supabase
        .from('users')
        .update({ full_name: fullName })
        .eq('id', session.user.id);
      
      if (updateUserError) {
        console.log("❌ Failed to update user name:", updateUserError.message);
        return NextResponse.json(
          { error: updateUserError.message || 'Failed to update user name' },
          { status: 500 }
        );
      }
      console.log("✅ User name updated successfully");
    }
    
    // Get the prospect record associated with the user using admin client to bypass RLS
    const { data: prospectData, error: prospectError } = await adminClient
      .from('prospects')
      .select('id, contact_info')
      .eq('user_id', session.user.id)
      .single();
    
    if (prospectError) {
      console.log("❌ Failed to find prospect profile:", prospectError.message);
      return NextResponse.json(
        { error: prospectError.message || 'Failed to find prospect profile' },
        { status: 500 }
      );
    }
    
    console.log("✅ Found prospect profile:", prospectData.id);
    console.log("📋 Current contact info:", prospectData.contact_info);
    
    // Update contact information
    const contactInfo = (prospectData.contact_info as ContactInfo) || {};
    const updatedContactInfo: ContactInfo = {
      ...contactInfo,
      phone: phone || contactInfo.phone,
      location: {
        city: city || (contactInfo.location?.city || ''),
        country: country || (contactInfo.location?.country || '')
      }
    };
    
    console.log("🔄 Updating contact info:", updatedContactInfo);
    
    // Use admin client to update prospect to bypass RLS policies
    const { data: updateResult, error: updateProspectError } = await adminClient
      .from('prospects')
      .update({ contact_info: updatedContactInfo })
      .eq('id', prospectData.id)
      .select();
    
    if (updateProspectError) {
      console.log("❌ Failed to update contact information:", updateProspectError.message);
      return NextResponse.json(
        { error: updateProspectError.message || 'Failed to update contact information' },
        { status: 500 }
      );
    }
    
    console.log("✅ Contact information updated successfully", updateResult);
    
    return NextResponse.json({
      success: true,
      message: 'Personal information updated successfully'
    });
    
  } catch (error: any) {
    console.error('❌ Error updating personal information:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 