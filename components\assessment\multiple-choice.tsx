'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { AssessmentQuestion } from '@/types/assessment';
import { cn } from '@/lib/utils';

interface MultipleChoiceProps {
  question: AssessmentQuestion;
  onComplete: (answer: string) => void;
}

export function MultipleChoice({ question, onComplete }: MultipleChoiceProps) {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const options = question.options as string[] || [];

  const handleSelect = (option: string) => {
    if (!isSubmitted) {
      setSelectedOption(option);
    }
  };

  const handleSubmit = () => {
    if (selectedOption) {
      setIsSubmitted(true);
      onComplete(selectedOption);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Multiple Choice Question</CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Question text */}
        <div className="text-lg font-medium">
          {question.question_text}
        </div>
        
        {/* Options */}
        <div className="space-y-2">
          {options.map((option, index) => (
            <Button
              key={index}
              variant={selectedOption === option ? "default" : "outline"}
              className={cn(
                "w-full justify-start text-left h-auto py-3 px-4",
                isSubmitted && selectedOption === option && "ring-2 ring-primary"
              )}
              onClick={() => handleSelect(option)}
              disabled={isSubmitted}
            >
              <div className="flex items-center gap-3">
                <div className={cn(
                  "flex-shrink-0 w-5 h-5 rounded-full border border-primary flex items-center justify-center",
                  selectedOption === option ? "bg-primary text-primary-foreground" : "bg-background"
                )}>
                  {selectedOption === option && <Check className="h-3 w-3" />}
                </div>
                <span>{option}</span>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-end">
        <Button 
          onClick={handleSubmit}
          disabled={!selectedOption || isSubmitted}
        >
          {isSubmitted ? 'Submitted' : 'Submit Answer'}
        </Button>
      </CardFooter>
    </Card>
  );
} 