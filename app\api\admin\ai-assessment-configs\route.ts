import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { createClient } from '@/lib/supabase-server';

/**
 * Get all AI assessment configurations
 * GET /api/admin/ai-assessment-configs
 */
export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const supabase = createClient();

    // Fetch AI assessment configurations with prompt templates
    const { data: configs, error } = await supabase
      .from('ai_assessment_configs')
      .select(`
        *,
        prompt_template:ai_prompt_templates(
          id,
          name,
          system_prompt,
          description
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching AI assessment configs:', error);
      return NextResponse.json(
        { error: 'Failed to fetch AI assessment configurations' },
        { status: 500 }
      );
    }

    // Get statistics
    const totalConfigs = configs?.length || 0;
    const publishedConfigs = configs?.filter(c => c.status === 'published').length || 0;
    const draftConfigs = configs?.filter(c => c.status === 'draft').length || 0;

    return NextResponse.json({
      configs: configs || [],
      statistics: {
        total: totalConfigs,
        published: publishedConfigs,
        draft: draftConfigs,
        archived: totalConfigs - publishedConfigs - draftConfigs
      }
    });

  } catch (error: any) {
    console.error('AI assessment configs fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch AI assessment configurations' },
      { status: 500 }
    );
  }
}

/**
 * Create new AI assessment configuration
 * POST /api/admin/ai-assessment-configs
 */
export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const body = await req.json();
    const {
      prompt_template_id,
      name,
      description,
      question_count = 20,
      difficulty_distribution = { easy: 30, medium: 50, hard: 20 },
      question_types = ['multiple_choice'],
      subcategories = [],
      duration_minutes = 45,
      passing_score = 70,
      category,
      scoring_rubric = {},
      competency_levels = { novice: 0, intermediate: 60, advanced: 80, expert: 95 },
      retake_limit = 3,
      retake_fee = 0,
      certification_eligible = false,
      ai_model_provider = 'together',
      ai_model_name = 'meta-llama/Llama-2-70b-chat-hf',
      temperature = 0.7,
      max_tokens = 4000
    } = body;

    // Validate required fields
    if (!name || !category || !prompt_template_id) {
      return NextResponse.json(
        { error: 'Name, category, and prompt template are required' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Verify prompt template exists
    const { data: template, error: templateError } = await supabase
      .from('ai_prompt_templates')
      .select('id, name')
      .eq('id', prompt_template_id)
      .single();

    if (templateError || !template) {
      return NextResponse.json(
        { error: 'Invalid prompt template ID' },
        { status: 400 }
      );
    }

    // Create the configuration
    const { data: config, error } = await supabase
      .from('ai_assessment_configs')
      .insert({
        prompt_template_id,
        name,
        description,
        question_count,
        difficulty_distribution,
        question_types,
        subcategories,
        duration_minutes,
        passing_score,
        category,
        scoring_rubric,
        competency_levels,
        retake_limit,
        retake_fee,
        certification_eligible,
        ai_model_provider,
        ai_model_name,
        temperature,
        max_tokens,
        status: 'draft'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating AI assessment config:', error);
      return NextResponse.json(
        { error: 'Failed to create AI assessment configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      config,
      message: 'AI assessment configuration created successfully'
    });

  } catch (error: any) {
    console.error('AI assessment config creation error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create AI assessment configuration' },
      { status: 500 }
    );
  }
}

/**
 * Update AI assessment configuration status
 * PATCH /api/admin/ai-assessment-configs
 */
export async function PATCH(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const body = await req.json();
    const { id, status, ...updates } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Configuration ID is required' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Update the configuration
    const { data: config, error } = await supabase
      .from('ai_assessment_configs')
      .update({
        ...updates,
        ...(status && { status }),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating AI assessment config:', error);
      return NextResponse.json(
        { error: 'Failed to update AI assessment configuration' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      config,
      message: 'AI assessment configuration updated successfully'
    });

  } catch (error: any) {
    console.error('AI assessment config update error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update AI assessment configuration' },
      { status: 500 }
    );
  }
}
