-- =============================================================================
-- COMPLETE DATABASE RESET SCRIPT
-- This will completely reset the Luna database and apply the new schema
-- =============================================================================

-- Drop all existing tables in reverse dependency order
DROP TABLE IF EXISTS user_training_data CASCADE;
DROP TABLE IF EXISTS user_contexts CASCADE;
DROP TABLE IF EXISTS team_memberships CASCADE;
DROP TABLE IF EXISTS teams CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;
DROP TABLE IF EXISTS individuals CASCADE;
DROP TABLE IF EXISTS learning_paths CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop any remaining old tables
DROP TABLE IF EXISTS prospects CASCADE;
DROP TABLE IF EXISTS organization_memberships CASCADE;
DROP TABLE IF EXISTS bpo_companies CASCADE;
DROP TABLE IF EXISTS training_data CASCADE;
DROP TABLE IF EXISTS modules CASCADE;
DROP TABLE IF EXISTS lessons CASCADE;
DROP TABLE IF EXISTS activities CASCADE;

-- Drop existing types
DROP TYPE IF EXISTS training_status CASCADE;
DROP TYPE IF EXISTS learning_status CASCADE;
DROP TYPE IF EXISTS profile_visibility CASCADE;
DROP TYPE IF EXISTS org_status CASCADE;
DROP TYPE IF EXISTS context_type CASCADE;
DROP TYPE IF EXISTS membership_status CASCADE;
DROP TYPE IF EXISTS team_member_role CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;

-- Drop existing functions
DROP FUNCTION IF EXISTS validate_luna_schema() CASCADE;
DROP FUNCTION IF EXISTS invite_to_team(UUID, UUID, VARCHAR, team_member_role, TEXT) CASCADE;
DROP FUNCTION IF EXISTS create_team(UUID, UUID, VARCHAR, TEXT) CASCADE;
DROP FUNCTION IF EXISTS get_user_available_contexts(UUID) CASCADE;
DROP FUNCTION IF EXISTS switch_user_context(UUID, context_type, UUID) CASCADE;
DROP FUNCTION IF EXISTS generate_unique_slug(TEXT, TEXT, TEXT) CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Drop any existing policies
DO $$ 
DECLARE 
    r RECORD;
BEGIN
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') 
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(r.policyname) || ' ON ' || quote_ident(r.schemaname) || '.' || quote_ident(r.tablename) || ' CASCADE';
    END LOOP;
END $$;

-- Drop any existing triggers
DO $$ 
DECLARE 
    r RECORD;
BEGIN
    FOR r IN (SELECT trigger_name, event_object_table FROM information_schema.triggers WHERE trigger_schema = 'public') 
    LOOP
        EXECUTE 'DROP TRIGGER IF EXISTS ' || quote_ident(r.trigger_name) || ' ON ' || quote_ident(r.event_object_table) || ' CASCADE';
    END LOOP;
END $$;

-- Reset complete - database is now clean
SELECT 'Database reset complete - ready for new schema' as status;
