import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth';

/**
 * GET /api/auth/user
 * Get current authenticated user information
 */
export async function GET(request: NextRequest) {
  try {
    console.log('Luna Auth API - Starting authentication check...');
    
    const authResult = await getAuthenticatedUser();
    
    console.log('Luna Auth API - Auth result:', {
      hasUser: !!authResult.user,
      error: authResult.error,
      status: authResult.status
    });

    if (authResult.user) {
      console.log('Luna Auth API - User details:', {
        id: authResult.user.id,
        email: authResult.user.email,
        role: authResult.user.role,
        hasEmployment: authResult.user.hasEmployment,
        isOrganizationAdmin: authResult.user.isOrganizationAdmin,
        isDepartmentAdmin: authResult.user.isDepartmentAdmin,
        employmentCount: authResult.user.employmentRelationships.length,
        currentContext: authResult.user.currentContext
      });
    }

    return NextResponse.json({
      success: !!authResult.user,
      user: authResult.user,
      error: authResult.error,
      status: authResult.status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Luna Auth API - Unexpected error during auth check:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during authentication check',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
