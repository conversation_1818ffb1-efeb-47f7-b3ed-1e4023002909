"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { ErrorPage } from "@/components/ui/error-display";
import { createError, ErrorType, logError } from "@/lib/utils";

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error }>;
  context?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Create standardized error and log it
    const appError = createError(
      ErrorType.UNKNOWN,
      error.message,
      'An unexpected error occurred while loading this content.',
      {
        originalError: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        context: this.props.context || 'ErrorBoundary'
      }
    );

    logError(appError, this.props.context);
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback or default error page
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} />;
      }

      return <DefaultErrorFallback error={this.state.error} context={this.props.context} />;
    }

    return this.props.children;
  }
}

function DefaultErrorFallback({ error, context }: { error?: Error; context?: string }) {
  const router = useRouter();

  // Convert JavaScript error to AppError for consistent display
  const appError = createError(
    ErrorType.UNKNOWN,
    error?.message || 'Unknown error occurred',
    'An unexpected error occurred while loading this content.',
    {
      originalError: error?.message,
      stack: error?.stack,
      context: context || 'ErrorBoundary'
    }
  );

  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <ErrorPage
      error={appError}
      onRetry={handleRetry}
      showDetails={process.env.NODE_ENV === 'development'}
    />
  );
}