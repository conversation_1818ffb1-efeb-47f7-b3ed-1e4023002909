'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  MoreHorizontal, 
  BookOpen, 
  Clock, 
  Edit, 
  Trash, 
  ChevronDown, 
  ChevronRight,
  Menu,
  ListOrdered,
  GraduationCap,
  LayoutList,
  Plus
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/lib/supabase';

interface Lesson {
  id: string;
  title: string;
  description: string | null;
  order_index: number;
  duration_minutes: number | null;
}

interface ModuleCardProps {
  id: string;
  title: string;
  description: string;
  coverImageUrl?: string;
  durationMinutes?: number;
  status: 'draft' | 'published' | 'archived';
  requiredOrder?: number;
  lessonCount: number;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onManageLessons: (id: string) => void;
}

export function ModuleCard({
  id,
  title,
  description,
  coverImageUrl,
  durationMinutes,
  status,
  requiredOrder,
  lessonCount,
  onEdit,
  onDelete,
  onManageLessons,
}: ModuleCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [isLoadingLessons, setIsLoadingLessons] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published':
        return 'bg-green-500';
      case 'draft':
        return 'bg-amber-500';
      case 'archived':
        return 'bg-gray-500';
      default:
        return 'bg-blue-500';
    }
  };
  
  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'Duration not set';
    
    if (minutes < 60) {
      return `${minutes} mins`;
    }
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
    }
    
    return `${hours}h ${remainingMinutes}m`;
  };
  
  const defaultImage = 'https://images.unsplash.com/photo-1600267204091-5c1ab8b10c02?w=800&auto=format&fit=crop&q=80';

  const toggleExpand = async () => {
    if (!isExpanded && lessons.length === 0) {
      await fetchLessons();
    }
    setIsExpanded(!isExpanded);
  };

  const fetchLessons = async () => {
    setIsLoadingLessons(true);
    try {
      const { data, error } = await supabase
        .from('lessons')
        .select('*')
        .eq('module_id', id)
        .order('order_index', { ascending: true });
      
      if (error) {
        console.error('Error fetching lessons:', error);
        return;
      }
      
      setLessons(data || []);
    } catch (err) {
      console.error('Error fetching lessons:', err);
    } finally {
      setIsLoadingLessons(false);
    }
  };

  const handleAddLesson = (e: React.MouseEvent) => {
    e.stopPropagation();
    onManageLessons(id);
  };
  
  return (
    <Card className="w-full transition-all duration-200">
      <div className="flex flex-col md:flex-row">
        <div className="w-full md:w-44 h-32 md:h-auto bg-gray-100 shrink-0">
          <img
            src={coverImageUrl || defaultImage}
            alt={title}
            className="w-full h-full object-cover"
          />
        </div>
        
        <div className="flex-1">
          <div className="flex items-center px-4 pt-4">
            <div className="mr-2 cursor-pointer" onClick={toggleExpand}>
              {isExpanded ? (
                <ChevronDown className="h-5 w-5 text-muted-foreground" />
              ) : (
                <ChevronRight className="h-5 w-5 text-muted-foreground" />
              )}
            </div>
            
            <div className="flex-1">
              <CardHeader className="p-0">
                <div className="flex items-center gap-2">
                  <CardTitle className="text-lg">{title}</CardTitle>
                  <Badge
                    className={`${getStatusColor(status)} border-none font-medium text-white`}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Badge>
                  {requiredOrder !== undefined && (
                    <Badge variant="outline" className="ml-1">
                      Order: {requiredOrder}
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <Clock className="mr-1 h-4 w-4" />
                    <span>{formatDuration(durationMinutes)}</span>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="mr-1 h-4 w-4" />
                    <span>{lessonCount} {lessonCount === 1 ? 'Lesson' : 'Lessons'}</span>
                  </div>
                </div>
              </CardHeader>
            </div>
            
            <div className="ml-auto pr-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => onEdit(id)}>
                    <Edit className="mr-2 h-4 w-4" />
                    <span>Edit Module</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onManageLessons(id)}>
                    <ListOrdered className="mr-2 h-4 w-4" />
                    <span>Manage Lessons</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onDelete(id)}
                    className="text-red-600"
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    <span>Delete</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          {isExpanded && (
            <>
              <div className="px-4 pb-3 pt-1">
                <div 
                  className="text-sm text-gray-600" 
                  dangerouslySetInnerHTML={{ __html: description }}
                />
              </div>
              
              <div className="border-t px-4 py-3">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium flex items-center">
                    <LayoutList className="mr-2 h-4 w-4" />
                    Lessons
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7"
                    onClick={handleAddLesson}
                  >
                    <Plus className="mr-1 h-3 w-3" />
                    Add Lesson
                  </Button>
                </div>
                
                {isLoadingLessons ? (
                  // Loading skeletons
                  Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-2 py-2">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-28" />
                        <Skeleton className="h-3 w-44" />
                      </div>
                    </div>
                  ))
                ) : lessons.length === 0 ? (
                  <div className="text-sm text-muted-foreground py-2">
                    No lessons added yet. Click "Add Lesson" to create your first lesson.
                  </div>
                ) : (
                  <div className="space-y-2">
                    {lessons.map(lesson => (
                      <div 
                        key={lesson.id} 
                        className="flex items-center py-2 px-3 rounded-md hover:bg-muted transition-colors"
                      >
                        <div className="flex items-center flex-1">
                          <div className="bg-primary/10 text-primary w-7 h-7 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                            {lesson.order_index}
                          </div>
                          <div>
                            <h4 className="text-sm font-medium">{lesson.title}</h4>
                            {lesson.description && (
                              <p className="text-xs text-muted-foreground line-clamp-1">
                                {lesson.description}
                              </p>
                            )}
                          </div>
                        </div>
                        {lesson.duration_minutes && (
                          <span className="text-xs text-muted-foreground">
                            {lesson.duration_minutes} mins
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </Card>
  );
} 