"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import {
  Plus,
  Edit,
  Save,
  X,
  Trash2,
  BookOpen,
  PlayCircle,
  FileText,
  Brain,
  GripVertical,
  Clock,
  DollarSign,
  ChevronDown,
  ChevronRight,
  Settings,
  Eye,
  EyeOff
} from 'lucide-react'
import { toast } from "sonner"
import { LessonForm } from "./lesson-form"

interface Module {
  id: string
  course_id: string
  name: string
  description: string
  sequence_order: number
  estimated_duration: number
  is_standalone: boolean
  single_price?: number
  status: string
  learning_objectives: string[]
  module_type: string
  difficulty_level: string
  required_resources: string[]
  module_prerequisites: string[]
  lessons?: Lesson[]
}

interface Lesson {
  id: string
  module_id: string
  name: string
  description: string
  lesson_type: string
  content_url?: string
  content_data: any
  sequence_order: number
  estimated_duration: number
  is_mandatory: boolean
  status: string
  has_assessment: boolean
  assessment_type?: string
  weight_in_module: number
  required_software: string[]
  downloadable_resources: any[]
  external_links: any[]
  lesson_prerequisites: string[]
  video_duration?: number
  transcript?: string
  reading_time?: number
  passing_score?: number
  attempts_allowed?: number
  time_limit?: number
  submission_format: string[]
  max_file_size?: number
}

interface CourseModulesSectionProps {
  courseId: string
  modules: Module[]
  onUpdate: () => void
}

export function CourseModulesSection({ courseId, modules, onUpdate }: CourseModulesSectionProps) {
  const [addingModule, setAddingModule] = useState(false)
  const [addingLesson, setAddingLesson] = useState<string | null>(null)
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set())
  const [expandedLessons, setExpandedLessons] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(false)

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  const [newModule, setNewModule] = useState({
    name: '',
    description: '',
    module_type: 'theory',
    difficulty_level: 'beginner',
    estimated_duration: '',
    is_standalone: false,
    single_price: '',
    status: 'draft'
  })

  // Store module edit forms by module ID
  const [moduleEditForms, setModuleEditForms] = useState<Record<string, any>>({})

  // Store lesson edit forms by lesson ID
  const [lessonEditForms, setLessonEditForms] = useState<Record<string, any>>({})



  // Helper functions
  const calculateModuleDuration = (module: Module) => {
    if (!module.lessons || module.lessons.length === 0) return 0
    return module.lessons.reduce((total, lesson) => total + (lesson.estimated_duration || 0), 0)
  }

  const toggleModuleExpansion = (moduleId: string) => {
    const newExpanded = new Set(expandedModules)
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId)
    } else {
      newExpanded.add(moduleId)
      // Initialize edit form for this module if not exists
      if (!moduleEditForms[moduleId]) {
        const module = modules.find(m => m.id === moduleId)
        if (module) {
          setModuleEditForms(prev => ({
            ...prev,
            [moduleId]: {
              name: module.name,
              description: module.description,
              module_type: module.module_type,
              difficulty_level: module.difficulty_level,
              is_standalone: module.is_standalone,
              single_price: module.single_price?.toString() || '',
              status: module.status
            }
          }))
        }
      }
    }
    setExpandedModules(newExpanded)
  }

  const toggleLessonExpansion = (lessonId: string) => {
    const newExpanded = new Set(expandedLessons)
    if (newExpanded.has(lessonId)) {
      newExpanded.delete(lessonId)
    } else {
      newExpanded.add(lessonId)
      // Initialize edit form for this lesson if not exists
      if (!lessonEditForms[lessonId]) {
        const lesson = modules.flatMap(m => m.lessons || []).find(l => l.id === lessonId)
        if (lesson) {
          setLessonEditForms(prev => ({
            ...prev,
            [lessonId]: {
              name: lesson.name,
              description: lesson.description,
              lesson_type: lesson.lesson_type,
              content_url: lesson.content_url || '',
              estimated_duration: lesson.estimated_duration?.toString() || '',
              is_mandatory: lesson.is_mandatory,
              has_assessment: lesson.has_assessment,
              assessment_type: lesson.assessment_type || '',
              weight_in_module: lesson.weight_in_module?.toString() || '0',
              status: lesson.status,
              // Content delivery options
              content_type: 'url',
              uploaded_file: null,
              // Video-specific fields
              video_duration: lesson.video_duration?.toString() || '',
              transcript: lesson.transcript || '',
              // Text-specific fields
              reading_time: lesson.reading_time?.toString() || '',
              // Presentation-specific fields
              slide_count: lesson.slide_count?.toString() || '',
              // Quiz-specific fields
              passing_score: lesson.passing_score?.toString() || '',
              attempts_allowed: lesson.attempts_allowed?.toString() || '3',
              time_limit: lesson.time_limit?.toString() || '',
              // Live lesson-specific fields
              zoom_meeting_id: lesson.zoom_meeting_id || '',
              zoom_meeting_url: lesson.zoom_meeting_url || '',
              scheduled_start: lesson.scheduled_start || '',
              scheduled_end: lesson.scheduled_end || '',
              waiting_room_enabled: lesson.waiting_room_enabled ?? true,
              max_participants: lesson.max_participants?.toString() || ''
            }
          }))
        }
      }
    }
    setExpandedLessons(newExpanded)
  }

  const updateModuleForm = (moduleId: string, field: string, value: any) => {
    setModuleEditForms(prev => ({
      ...prev,
      [moduleId]: {
        ...prev[moduleId],
        [field]: value
      }
    }))
  }

  const updateLessonForm = (lessonId: string, field: string, value: any) => {
    setLessonEditForms(prev => ({
      ...prev,
      [lessonId]: {
        ...prev[lessonId],
        [field]: value
      }
    }))
  }



  // Drag and drop handlers
  const handleModuleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    if (active.id !== over?.id) {
      const oldIndex = modules.findIndex(module => module.id === active.id)
      const newIndex = modules.findIndex(module => module.id === over?.id)

      const reorderedModules = arrayMove(modules, oldIndex, newIndex)

      // Update sequence orders
      try {
        const updatePromises = reorderedModules.map((module, index) =>
          fetch(`/api/admin/modules/${module.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              sequence_order: index + 1,
              name: module.name,
              description: module.description,
              module_type: module.module_type,
              difficulty_level: module.difficulty_level,
              is_standalone: module.is_standalone,
              single_price: module.single_price,
              status: module.status
            })
          })
        )

        await Promise.all(updatePromises)
        onUpdate()
        toast.success('Modules reordered successfully')
      } catch (error) {
        console.error('Error reordering modules:', error)
        toast.error('Failed to reorder modules')
      }
    }
  }

  const handleLessonDragEnd = async (event: DragEndEvent, moduleId: string) => {
    const { active, over } = event
    const module = modules.find(m => m.id === moduleId)

    if (active.id !== over?.id && module?.lessons) {
      const oldIndex = module.lessons.findIndex(lesson => lesson.id === active.id)
      const newIndex = module.lessons.findIndex(lesson => lesson.id === over?.id)

      const reorderedLessons = arrayMove(module.lessons, oldIndex, newIndex)

      // Update sequence orders
      try {
        const updatePromises = reorderedLessons.map((lesson, index) =>
          fetch(`/api/admin/lessons/${lesson.id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              sequence_order: index + 1,
              name: lesson.name,
              description: lesson.description,
              lesson_type: lesson.lesson_type,
              estimated_duration: lesson.estimated_duration,
              is_mandatory: lesson.is_mandatory,
              status: lesson.status
            })
          })
        )

        await Promise.all(updatePromises)
        onUpdate()
        toast.success('Lessons reordered successfully')
      } catch (error) {
        console.error('Error reordering lessons:', error)
        toast.error('Failed to reorder lessons')
      }
    }
  }

  const getLessonTypeFields = (lessonType: string) => {
    switch (lessonType) {
      case 'video':
        return ['content_url', 'video_duration', 'transcript']
      case 'text':
        return ['content_url', 'reading_time']
      case 'quiz':
        return ['passing_score', 'attempts_allowed', 'time_limit']
      case 'assignment':
        return ['submission_format', 'max_file_size', 'passing_score']
      case 'interactive':
        return ['content_url']
      default:
        return []
    }
  }

  const handleAddModule = async () => {
    try {
      setLoading(true)
      
      const payload = {
        course_id: courseId,
        name: newModule.name.trim(),
        description: newModule.description.trim(),
        module_type: newModule.module_type,
        difficulty_level: newModule.difficulty_level,
        estimated_duration: newModule.estimated_duration ? parseInt(newModule.estimated_duration) : null,
        is_standalone: newModule.is_standalone,
        single_price: newModule.single_price ? parseFloat(newModule.single_price) : null,
        status: newModule.status,
        sequence_order: modules.length + 1
      }

      const response = await fetch(`/api/admin/courses/${courseId}/modules`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create module')
      }

      toast.success('Module created successfully')
      setAddingModule(false)
      setNewModule({
        name: '',
        description: '',
        module_type: 'theory',
        difficulty_level: 'beginner',
        estimated_duration: '',
        is_standalone: false,
        single_price: '',
        status: 'draft'
      })
      onUpdate()
      
    } catch (error: any) {
      console.error('Error creating module:', error)
      toast.error(error.message || 'Failed to create module')
    } finally {
      setLoading(false)
    }
  }

  const handleEditModule = async (moduleId: string) => {
    try {
      setLoading(true)
      const moduleForm = moduleEditForms[moduleId]

      const payload = {
        name: moduleForm.name.trim(),
        description: moduleForm.description.trim(),
        module_type: moduleForm.module_type,
        difficulty_level: moduleForm.difficulty_level,
        is_standalone: moduleForm.is_standalone,
        single_price: moduleForm.single_price ? parseFloat(moduleForm.single_price) : null,
        status: moduleForm.status
      }

      const response = await fetch(`/api/admin/modules/${moduleId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update module')
      }

      toast.success('Module updated successfully')
      onUpdate()

    } catch (error: any) {
      console.error('Error updating module:', error)
      toast.error(error.message || 'Failed to update module')
    } finally {
      setLoading(false)
    }
  }

  const handleEditLesson = async (lessonId: string) => {
    try {
      setLoading(true)
      const lessonForm = lessonEditForms[lessonId]

      const payload = {
        name: lessonForm.name.trim(),
        description: lessonForm.description.trim(),
        lesson_type: lessonForm.lesson_type,
        content_url: lessonForm.content_url?.trim() || null,
        estimated_duration: lessonForm.estimated_duration ? parseInt(lessonForm.estimated_duration) : null,
        is_mandatory: lessonForm.is_mandatory,
        has_assessment: lessonForm.has_assessment,
        assessment_type: lessonForm.has_assessment ? lessonForm.assessment_type : null,
        weight_in_module: lessonForm.weight_in_module ? parseFloat(lessonForm.weight_in_module) : 0,
        status: lessonForm.status,
        // Conditional fields based on lesson type
        video_duration: lessonForm.lesson_type === 'video' ? (lessonForm.video_duration ? parseInt(lessonForm.video_duration) : null) : null,
        transcript: lessonForm.lesson_type === 'video' ? lessonForm.transcript?.trim() || null : null,
        reading_time: lessonForm.lesson_type === 'text' ? (lessonForm.reading_time ? parseInt(lessonForm.reading_time) : null) : null,
        passing_score: ['quiz', 'assignment'].includes(lessonForm.lesson_type) ? (lessonForm.passing_score ? parseInt(lessonForm.passing_score) : null) : null,
        attempts_allowed: lessonForm.lesson_type === 'quiz' ? (lessonForm.attempts_allowed ? parseInt(lessonForm.attempts_allowed) : 3) : null,
        time_limit: lessonForm.lesson_type === 'quiz' ? (lessonForm.time_limit ? parseInt(lessonForm.time_limit) : null) : null,
        submission_format: lessonForm.lesson_type === 'assignment' ? lessonForm.submission_format : [],
        max_file_size: lessonForm.lesson_type === 'assignment' ? (lessonForm.max_file_size ? parseInt(lessonForm.max_file_size) : null) : null
      }

      const response = await fetch(`/api/admin/lessons/${lessonId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update lesson')
      }

      toast.success('Lesson updated successfully')
      onUpdate()

    } catch (error: any) {
      console.error('Error updating lesson:', error)
      toast.error(error.message || 'Failed to update lesson')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteModule = async (moduleId: string) => {
    if (!confirm('Are you sure you want to delete this module? This action cannot be undone.')) {
      return
    }

    try {
      setLoading(true)

      const response = await fetch(`/api/admin/modules/${moduleId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete module')
      }

      toast.success('Module deleted successfully')
      onUpdate()

    } catch (error: any) {
      console.error('Error deleting module:', error)
      toast.error(error.message || 'Failed to delete module')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteLesson = async (lessonId: string) => {
    if (!confirm('Are you sure you want to delete this lesson? This action cannot be undone.')) {
      return
    }

    try {
      setLoading(true)

      const response = await fetch(`/api/admin/lessons/${lessonId}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete lesson')
      }

      toast.success('Lesson deleted successfully')
      onUpdate()

    } catch (error: any) {
      console.error('Error deleting lesson:', error)
      toast.error(error.message || 'Failed to delete lesson')
    } finally {
      setLoading(false)
    }
  }



  const formatDuration = (minutes: number | null) => {
    if (!minutes) return 'Not set'
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  const formatPrice = (price: number | null) => {
    if (price === null) return 'Free'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  // Sortable Module Component
  const SortableModule = ({ module }: { module: Module }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
    } = useSortable({ id: module.id })

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    }

    const moduleForm = moduleEditForms[module.id] || {
      name: module.name || '',
      description: module.description || '',
      module_type: module.module_type || 'theory',
      difficulty_level: module.difficulty_level || 'beginner',
      is_standalone: module.is_standalone || false,
      single_price: module.single_price?.toString() || '',
      status: module.status || 'draft'
    }
    const isExpanded = expandedModules.has(module.id)

    return (
      <div ref={setNodeRef} style={style} className="border rounded-lg bg-white">
        {/* Module Header */}
        <div
          className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50"
          onClick={() => toggleModuleExpansion(module.id)}
        >
          <div className="flex items-center space-x-3">
            <div {...attributes} {...listeners} className="cursor-grab active:cursor-grabbing">
              <GripVertical className="h-4 w-4 text-gray-400" />
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
            <BookOpen className="h-5 w-5" />
            <div>
              <h4 className="font-medium">{module.name}</h4>
              <p className="text-sm text-gray-500">{module.description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{module.module_type}</Badge>
            <Badge variant={module.status === 'published' ? 'default' : 'secondary'}>
              {module.status}
            </Badge>
            <span className="text-sm text-gray-500">
              {module.lessons?.length || 0} lessons
            </span>
            <span className="text-sm text-gray-500">
              {formatDuration(calculateModuleDuration(module))}
            </span>
          </div>
        </div>

        {/* Module Edit Form - Expanded */}
        {isExpanded && moduleForm && (
          <div className="px-6 pb-4 border-t bg-gray-50">
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Module Name *</Label>
                  <Input
                    value={moduleForm.name}
                    onChange={(e) => updateModuleForm(module.id, 'name', e.target.value)}
                    placeholder="Enter module name"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Module Type</Label>
                  <Select
                    value={moduleForm.module_type}
                    onValueChange={(value) => updateModuleForm(module.id, 'module_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="theory">Theory</SelectItem>
                      <SelectItem value="practical">Practical</SelectItem>
                      <SelectItem value="assessment">Assessment</SelectItem>
                      <SelectItem value="project">Project</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Description</Label>
                <Textarea
                  value={moduleForm.description}
                  onChange={(e) => updateModuleForm(module.id, 'description', e.target.value)}
                  placeholder="Describe what this module covers"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Difficulty Level</Label>
                  <Select
                    value={moduleForm.difficulty_level}
                    onValueChange={(value) => updateModuleForm(module.id, 'difficulty_level', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select
                    value={moduleForm.status}
                    onValueChange={(value) => updateModuleForm(module.id, 'status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Price (if standalone)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={moduleForm.single_price}
                    onChange={(e) => updateModuleForm(module.id, 'single_price', e.target.value)}
                    placeholder="29.99"
                    disabled={!moduleForm.is_standalone}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={moduleForm.is_standalone}
                    onCheckedChange={(checked) => updateModuleForm(module.id, 'is_standalone', checked)}
                  />
                  <Label>Can be purchased separately</Label>
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleEditModule(module.id)}
                    disabled={loading || !moduleForm.name.trim()}
                    size="sm"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {loading ? 'Saving...' : 'Save Changes'}
                  </Button>
                  <Button
                    onClick={() => handleDeleteModule(module.id)}
                    disabled={loading}
                    size="sm"
                    variant="destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </div>
              </div>

              {/* Lessons Section */}
              <div className="mt-6">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-medium">Lessons</h5>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setAddingLesson(module.id)}
                    disabled={addingLesson === module.id}
                  >
                    <Plus className="h-3 w-3 mr-2" />
                    Add Lesson
                  </Button>
                </div>

                {/* Add New Lesson Form */}
                {addingLesson === module.id && (
                  <LessonForm
                    moduleId={module.id}
                    onCancel={() => setAddingLesson(null)}
                    onSuccess={() => {
                      setAddingLesson(null)
                      onUpdate()
                    }}
                  />
                )}

                {/* Lessons List */}
                {module.lessons && module.lessons.length > 0 && (
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={(event) => handleLessonDragEnd(event, module.id)}
                  >
                    <SortableContext
                      items={module.lessons.map(lesson => lesson.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className="space-y-2">
                        {module.lessons
                          .sort((a, b) => a.sequence_order - b.sequence_order)
                          .map((lesson) => (
                            <SortableLesson key={lesson.id} lesson={lesson} />
                          ))}
                      </div>
                    </SortableContext>
                  </DndContext>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  // Sortable Lesson Component
  const SortableLesson = ({ lesson }: { lesson: Lesson }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
    } = useSortable({ id: lesson.id })

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
    }

    const isExpanded = expandedLessons.has(lesson.id)
    const lessonForm = lessonEditForms[lesson.id] || {
      name: lesson.name || '',
      description: lesson.description || '',
      lesson_type: lesson.lesson_type || 'video',
      content_url: lesson.content_url || '',
      estimated_duration: lesson.estimated_duration?.toString() || '',
      is_mandatory: lesson.is_mandatory || true,
      has_assessment: lesson.has_assessment || false,
      assessment_type: lesson.assessment_type || '',
      weight_in_module: lesson.weight_in_module?.toString() || '0',
      status: lesson.status || 'draft',
      content_type: 'url',
      uploaded_file: null,
      video_duration: lesson.video_duration?.toString() || '',
      transcript: lesson.transcript || '',
      reading_time: lesson.reading_time?.toString() || '',
      slide_count: lesson.slide_count?.toString() || '',
      passing_score: lesson.passing_score?.toString() || '',
      attempts_allowed: lesson.attempts_allowed?.toString() || '3',
      time_limit: lesson.time_limit?.toString() || '',
      zoom_meeting_id: lesson.zoom_meeting_id || '',
      zoom_meeting_url: lesson.zoom_meeting_url || '',
      scheduled_start: lesson.scheduled_start || '',
      scheduled_end: lesson.scheduled_end || '',
      waiting_room_enabled: lesson.waiting_room_enabled ?? true,
      max_participants: lesson.max_participants?.toString() || '',
      submission_format: lesson.submission_format || [],
      max_file_size: lesson.max_file_size?.toString() || ''
    }

    return (
      <div ref={setNodeRef} style={style} className="border rounded-lg bg-white">
        {/* Lesson Header */}
        <div
          className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50"
          onClick={() => toggleLessonExpansion(lesson.id)}
        >
          <div className="flex items-center space-x-3">
            <div {...attributes} {...listeners} className="cursor-grab active:cursor-grabbing">
              <GripVertical className="h-3 w-3 text-gray-400" />
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
            {getLessonTypeIcon(lesson.lesson_type)}
            <div>
              <p className="font-medium text-sm">{lesson.name}</p>
              <p className="text-xs text-gray-500">{lesson.description}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {lesson.lesson_type}
            </Badge>
            {lesson.estimated_duration && (
              <span className="text-xs text-gray-500">
                {formatDuration(lesson.estimated_duration)}
              </span>
            )}
            {lesson.is_mandatory && (
              <Badge variant="secondary" className="text-xs">
                Required
              </Badge>
            )}
            {lesson.has_assessment && (
              <Badge variant="default" className="text-xs">
                Assessment
              </Badge>
            )}
          </div>
        </div>

        {/* Lesson Edit Form - Expanded */}
        {isExpanded && lessonForm && (
          <div className="px-6 pb-4 border-t bg-gray-50">
            <div className="py-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Lesson Name *</Label>
                  <Input
                    value={lessonForm.name}
                    onChange={(e) => updateLessonForm(lesson.id, 'name', e.target.value)}
                    placeholder="Enter lesson name"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Lesson Type</Label>
                  <Select
                    value={lessonForm.lesson_type}
                    onValueChange={(value) => updateLessonForm(lesson.id, 'lesson_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="video">Video</SelectItem>
                      <SelectItem value="text">Text/Reading</SelectItem>
                      <SelectItem value="presentation">Presentation</SelectItem>
                      <SelectItem value="quiz">Quiz</SelectItem>
                      <SelectItem value="live">Live Lesson</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Description</Label>
                <Textarea
                  value={lessonForm.description}
                  onChange={(e) => updateLessonForm(lesson.id, 'description', e.target.value)}
                  placeholder="Describe what this lesson covers"
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Duration (minutes)</Label>
                  <Input
                    type="number"
                    value={lessonForm.estimated_duration}
                    onChange={(e) => updateLessonForm(lesson.id, 'estimated_duration', e.target.value)}
                    placeholder="15"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Status</Label>
                  <Select
                    value={lessonForm.status}
                    onValueChange={(value) => updateLessonForm(lesson.id, 'status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Weight (%)</Label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={lessonForm.weight_in_module}
                    onChange={(e) => updateLessonForm(lesson.id, 'weight_in_module', e.target.value)}
                    placeholder="0"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={lessonForm.is_mandatory}
                    onCheckedChange={(checked) => updateLessonForm(lesson.id, 'is_mandatory', checked)}
                  />
                  <Label>Mandatory</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={lessonForm.has_assessment}
                    onCheckedChange={(checked) => updateLessonForm(lesson.id, 'has_assessment', checked)}
                  />
                  <Label>Has Assessment</Label>
                </div>
              </div>

              {/* Conditional Fields Based on Lesson Type */}
              {lessonForm.lesson_type === 'video' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-blue-50 rounded-lg">
                  <div className="space-y-2">
                    <Label>Video URL</Label>
                    <Input
                      value={lessonForm.content_url}
                      onChange={(e) => updateLessonForm(lesson.id, 'content_url', e.target.value)}
                      placeholder="https://youtube.com/watch?v=..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Video Duration (minutes)</Label>
                    <Input
                      type="number"
                      value={lessonForm.video_duration}
                      onChange={(e) => updateLessonForm(lesson.id, 'video_duration', e.target.value)}
                      placeholder="10"
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label>Transcript (optional)</Label>
                    <Textarea
                      value={lessonForm.transcript}
                      onChange={(e) => updateLessonForm(lesson.id, 'transcript', e.target.value)}
                      placeholder="Video transcript for accessibility..."
                      rows={3}
                    />
                  </div>
                </div>
              )}

              {lessonForm.lesson_type === 'text' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-green-50 rounded-lg">
                  <div className="space-y-2">
                    <Label>Content URL</Label>
                    <Input
                      value={lessonForm.content_url}
                      onChange={(e) => updateLessonForm(lesson.id, 'content_url', e.target.value)}
                      placeholder="Link to reading material"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Reading Time (minutes)</Label>
                    <Input
                      type="number"
                      value={lessonForm.reading_time}
                      onChange={(e) => updateLessonForm(lesson.id, 'reading_time', e.target.value)}
                      placeholder="5"
                    />
                  </div>
                </div>
              )}

              {lessonForm.lesson_type === 'quiz' && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-purple-50 rounded-lg">
                  <div className="space-y-2">
                    <Label>Passing Score (%)</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={lessonForm.passing_score}
                      onChange={(e) => updateLessonForm(lesson.id, 'passing_score', e.target.value)}
                      placeholder="80"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Attempts Allowed</Label>
                    <Input
                      type="number"
                      min="1"
                      value={lessonForm.attempts_allowed}
                      onChange={(e) => updateLessonForm(lesson.id, 'attempts_allowed', e.target.value)}
                      placeholder="3"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Time Limit (minutes)</Label>
                    <Input
                      type="number"
                      value={lessonForm.time_limit}
                      onChange={(e) => updateLessonForm(lesson.id, 'time_limit', e.target.value)}
                      placeholder="30"
                    />
                  </div>
                </div>
              )}

              {lessonForm.lesson_type === 'assignment' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-orange-50 rounded-lg">
                  <div className="space-y-2">
                    <Label>Passing Score (%)</Label>
                    <Input
                      type="number"
                      min="0"
                      max="100"
                      value={lessonForm.passing_score}
                      onChange={(e) => updateLessonForm(lesson.id, 'passing_score', e.target.value)}
                      placeholder="70"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Max File Size (MB)</Label>
                    <Input
                      type="number"
                      value={lessonForm.max_file_size}
                      onChange={(e) => updateLessonForm(lesson.id, 'max_file_size', e.target.value)}
                      placeholder="10"
                    />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label>Submission Formats</Label>
                    <Input
                      value={Array.isArray(lessonForm.submission_format) ? lessonForm.submission_format.join(', ') : ''}
                      onChange={(e) => updateLessonForm(lesson.id, 'submission_format', e.target.value.split(',').map(f => f.trim()).filter(f => f))}
                      placeholder="pdf, docx, txt"
                    />
                  </div>
                </div>
              )}

              {lessonForm.lesson_type === 'interactive' && (
                <div className="p-4 bg-indigo-50 rounded-lg">
                  <div className="space-y-2">
                    <Label>Interactive Content URL</Label>
                    <Input
                      value={lessonForm.content_url}
                      onChange={(e) => updateLessonForm(lesson.id, 'content_url', e.target.value)}
                      placeholder="Link to interactive content or simulation"
                    />
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button
                  size="sm"
                  onClick={() => handleEditLesson(lesson.id)}
                  disabled={loading || !lessonForm.name.trim()}
                >
                  <Save className="h-3 w-3 mr-2" />
                  {loading ? 'Saving...' : 'Save Changes'}
                </Button>
                <Button
                  onClick={() => handleDeleteLesson(lesson.id)}
                  disabled={loading}
                  size="sm"
                  variant="destructive"
                >
                  <Trash2 className="h-3 w-3 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  const getLessonTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <PlayCircle className="h-4 w-4" />
      case 'text': return <FileText className="h-4 w-4" />
      case 'quiz': return <Brain className="h-4 w-4" />
      case 'assignment': return <Edit className="h-4 w-4" />
      case 'interactive': return <BookOpen className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Course Modules</h3>
          <p className="text-sm text-muted-foreground">
            Organize your course content into modules and lessons
          </p>
        </div>
        <Button onClick={() => setAddingModule(true)} disabled={addingModule}>
          <Plus className="h-4 w-4 mr-2" />
          Add Module
        </Button>
      </div>

      {/* Add New Module Form */}
      {addingModule && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Module</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="module-name">Module Name *</Label>
                <Input
                  id="module-name"
                  value={newModule.name}
                  onChange={(e) => setNewModule({ ...newModule, name: e.target.value })}
                  placeholder="Enter module name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="module-type">Module Type</Label>
                <Select value={newModule.module_type} onValueChange={(value) => setNewModule({ ...newModule, module_type: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="theory">Theory</SelectItem>
                    <SelectItem value="practical">Practical</SelectItem>
                    <SelectItem value="assessment">Assessment</SelectItem>
                    <SelectItem value="project">Project</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="module-description">Description</Label>
              <Textarea
                id="module-description"
                value={newModule.description}
                onChange={(e) => setNewModule({ ...newModule, description: e.target.value })}
                placeholder="Describe what this module covers"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="module-difficulty">Difficulty Level</Label>
                <Select value={newModule.difficulty_level} onValueChange={(value) => setNewModule({ ...newModule, difficulty_level: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="module-duration">Duration (minutes)</Label>
                <Input
                  id="module-duration"
                  type="number"
                  value={newModule.estimated_duration}
                  onChange={(e) => setNewModule({ ...newModule, estimated_duration: e.target.value })}
                  placeholder="60"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="module-price">Price (if standalone)</Label>
                <Input
                  id="module-price"
                  type="number"
                  step="0.01"
                  value={newModule.single_price}
                  onChange={(e) => setNewModule({ ...newModule, single_price: e.target.value })}
                  placeholder="29.99"
                  disabled={!newModule.is_standalone}
                />
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="module-standalone"
                  checked={newModule.is_standalone}
                  onCheckedChange={(checked) => setNewModule({ ...newModule, is_standalone: checked })}
                />
                <Label htmlFor="module-standalone">Can be purchased separately</Label>
              </div>
            </div>

            <div className="flex items-center space-x-2 pt-4">
              <Button onClick={handleAddModule} disabled={loading || !newModule.name.trim()}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Creating...' : 'Create Module'}
              </Button>
              <Button variant="outline" onClick={() => setAddingModule(false)} disabled={loading}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Modules List */}
      {modules.length === 0 && !addingModule ? (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No modules yet</h3>
            <p className="text-muted-foreground mb-4">
              Start building your course by adding modules and lessons.
            </p>
            <Button onClick={() => setAddingModule(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Module
            </Button>
          </CardContent>
        </Card>
      ) : (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleModuleDragEnd}
        >
          <SortableContext
            items={modules.map(module => module.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-4">
              {modules
                .sort((a, b) => a.sequence_order - b.sequence_order)
                .map((module) => (
                  <SortableModule key={module.id} module={module} />
                ))}
            </div>
          </SortableContext>
        </DndContext>
      )}
    </div>
  )
}
