'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { PathwayFormDialog } from '@/components/admin/pathway-form-dialog';
import {
  Route,
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  BookOpen,
  Clock,
  Star,
  RefreshCw
} from 'lucide-react';

type Program = {
  id: string;
  name: string;
  industry: string;
};

type Pathway = {
  id: string;
  title: string;
  description: string;
  slug: string;
  estimated_duration_hours: number;
  difficulty_level: number;
  sort_order: number;
  is_featured: boolean;
  created_at: string;
  program?: Program;
  pathway_courses?: Array<{
    id: string;
    sequence_order: number;
    is_required: boolean;
    course: {
      id: string;
      name: string;
      level: string;
      estimated_duration: number;
      status: string;
    };
  }>;
  _count?: {
    courses: number;
    required_courses: number;
    optional_courses: number;
  };
};

export default function PathwaysPage() {
  const [pathways, setPathways] = useState<Pathway[]>([]);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [programFilter, setProgramFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [selectedPathway, setSelectedPathway] = useState<Pathway | null>(null);

  // Fetch programs for filter
  const fetchPrograms = async () => {
    try {
      const response = await fetch('/api/admin/programs');
      const data = await response.json();
      if (data.programs) {
        setPrograms(data.programs);
      }
    } catch (error) {
      console.error('Error fetching programs:', error);
    }
  };

  // Fetch pathways
  const fetchPathways = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (programFilter !== 'all') params.append('program_id', programFilter);
      if (difficultyFilter !== 'all') params.append('difficulty', difficultyFilter);
      if (searchTerm) params.append('search', searchTerm);
      
      const response = await fetch(`/api/admin/pathways?${params}`);
      const data = await response.json();
      
      if (data.pathways) {
        setPathways(data.pathways);
      }
    } catch (error) {
      console.error('Error fetching pathways:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPrograms();
  }, []);

  useEffect(() => {
    fetchPathways();
  }, [programFilter, difficultyFilter, searchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPathways();
  };

  const getDifficultyBadge = (level: number) => {
    switch (level) {
      case 1:
        return { variant: 'secondary' as const, text: 'Beginner' };
      case 2:
        return { variant: 'default' as const, text: 'Intermediate' };
      case 3:
        return { variant: 'default' as const, text: 'Advanced' };
      case 4:
        return { variant: 'destructive' as const, text: 'Expert' };
      case 5:
        return { variant: 'destructive' as const, text: 'Master' };
      default:
        return { variant: 'secondary' as const, text: 'Unknown' };
    }
  };

  const handleViewPathway = (pathway: Pathway) => {
    window.location.href = `/admin/pathways/${pathway.id}`;
  };

  const handleEditPathway = (pathway: Pathway) => {
    setSelectedPathway(pathway);
    setFormDialogOpen(true);
  };

  const handleDeletePathway = async (pathway: Pathway) => {
    if (!confirm(`Are you sure you want to delete "${pathway.title}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/pathways/${pathway.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchPathways();
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to delete pathway');
      }
    } catch (error) {
      console.error('Error deleting pathway:', error);
      alert('Failed to delete pathway');
    }
  };

  const handleCreatePathway = () => {
    setSelectedPathway(null);
    setFormDialogOpen(true);
  };

  const handleFormSuccess = () => {
    setFormDialogOpen(false);
    setSelectedPathway(null);
    fetchPathways();
  };

  return (
    <div className="px-[50px] pt-8 pb-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pathways</h1>
          <p className="text-muted-foreground">
            Manage career learning pathways within programs
          </p>
        </div>
        <Button onClick={handleCreatePathway}>
          <Plus className="h-4 w-4 mr-2" />
          Create Pathway
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pathways</CardTitle>
            <Route className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pathways.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Featured Pathways</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pathways.filter(p => p.is_featured).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Courses</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pathways.reduce((acc, p) => acc + (p._count?.courses || 0), 0)}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {pathways.length > 0 
                ? Math.round(pathways.reduce((acc, p) => acc + (p.estimated_duration_hours || 0), 0) / pathways.length)
                : 0}h
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search pathways..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
            <select
              value={programFilter}
              onChange={(e) => setProgramFilter(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Programs</option>
              {programs.map((program) => (
                <option key={program.id} value={program.id}>
                  {program.name}
                </option>
              ))}
            </select>
            <select
              value={difficultyFilter}
              onChange={(e) => setDifficultyFilter(e.target.value)}
              className="px-3 py-2 border rounded-md"
            >
              <option value="all">All Difficulties</option>
              <option value="1">Beginner</option>
              <option value="2">Intermediate</option>
              <option value="3">Advanced</option>
              <option value="4">Expert</option>
              <option value="5">Master</option>
            </select>
            <Button type="submit" variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            <Button type="button" variant="outline" onClick={fetchPathways}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Pathways Table */}
      <Card>
        <CardHeader>
          <CardTitle>Pathways</CardTitle>
          <CardDescription>
            {pathways.length} pathway{pathways.length !== 1 ? 's' : ''} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading pathways...</span>
            </div>
          ) : pathways.length === 0 ? (
            <div className="text-center py-8">
              <Route className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No pathways found</h3>
              <p className="text-muted-foreground mb-4">
                Get started by creating your first pathway.
              </p>
              <Button onClick={handleCreatePathway}>
                <Plus className="h-4 w-4 mr-2" />
                Create Pathway
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Pathway</TableHead>
                  <TableHead>Program</TableHead>
                  <TableHead>Difficulty</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Courses</TableHead>
                  <TableHead>Featured</TableHead>
                  <TableHead className="w-[50px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pathways.map((pathway) => {
                  const difficultyBadge = getDifficultyBadge(pathway.difficulty_level);
                  return (
                    <TableRow key={pathway.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{pathway.title}</div>
                          <div className="text-sm text-muted-foreground line-clamp-2">
                            {pathway.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{pathway.program?.name}</div>
                          <Badge variant="outline" className="text-xs">
                            {pathway.program?.industry}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={difficultyBadge.variant}>
                          {difficultyBadge.text}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {pathway.estimated_duration_hours}h
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{pathway._count?.courses || 0} total</div>
                          <div className="text-muted-foreground">
                            {pathway._count?.required_courses || 0} required
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {pathway.is_featured && (
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        )}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewPathway(pathway)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditPathway(pathway)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDeletePathway(pathway)}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Pathway Form Dialog */}
      <PathwayFormDialog
        open={formDialogOpen}
        onOpenChange={setFormDialogOpen}
        pathway={selectedPathway}
        onSuccess={handleFormSuccess}
      />
    </div>
  );
}
