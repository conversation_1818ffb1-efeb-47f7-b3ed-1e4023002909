import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    const { data: skill, error } = await supabase
      .from('skills')
      .select(`
        *,
        skill_categories(name, color_code, icon)
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Error fetching skill:', error);
      return NextResponse.json(
        { error: 'Failed to fetch skill' },
        { status: 500 }
      );
    }

    if (!skill) {
      return NextResponse.json(
        { error: 'Skill not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(skill);

  } catch (error) {
    console.error('Unexpected error in skill GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.category_id || !body.skill_type || !body.market_demand) {
      return NextResponse.json(
        { error: 'Name, category, skill type, and market demand are required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .from('skills')
      .update({
        name: body.name,
        category_id: body.category_id,
        subcategory: body.subcategory || null,
        skill_type: body.skill_type,
        description: body.description || null,
        certification_available: body.certification_available || false,
        market_demand: body.market_demand,
        is_active: body.is_active !== undefined ? body.is_active : true,
      })
      .eq('id', params.id)
      .select(`
        *,
        skill_categories(name, color_code, icon)
      `)
      .single();

    if (error) {
      console.error('Error updating skill:', error);
      return NextResponse.json(
        { error: 'Failed to update skill' },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { error: 'Skill not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Unexpected error in skill PUT:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // TODO: Check if skill is being used in job roles, assessments, etc.
    // For now, we'll allow deletion

    const { error } = await supabase
      .from('skills')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting skill:', error);
      return NextResponse.json(
        { error: 'Failed to delete skill' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Unexpected error in skill DELETE:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
