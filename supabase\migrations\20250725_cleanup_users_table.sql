-- Clean up users table - Remove duplicates and rename team columns to employment
-- Migration: 20250725_cleanup_users_table.sql

-- Step 1: Migrate any existing data from users to individuals before dropping columns
-- First, ensure all users who have learning data get an individuals record

INSERT INTO individuals (
  user_id,
  skills,
  career_interests,
  learning_style,
  created_at,
  updated_at
)
SELECT 
  u.id,
  CASE 
    WHEN u.personal_skills IS NOT NULL AND u.personal_skills != '[]'::jsonb 
    THEN ARRAY[u.personal_skills]
    ELSE '{}'::jsonb[]
  END,
  CASE 
    WHEN u.industry_interests IS NOT NULL AND array_length(u.industry_interests, 1) > 0
    THEN jsonb_build_object('industries', to_jsonb(u.industry_interests), 'goals', COALESCE(u.career_goals, '{}'::jsonb))
    ELSE COALESCE(u.career_goals, '{}'::jsonb)
  END,
  COALESCE(u.learning_preferences, '{}'::jsonb),
  u.created_at,
  u.updated_at
FROM users u
WHERE u.role = 'individual'
AND NOT EXISTS (SELECT 1 FROM individuals i WHERE i.user_id = u.id)
AND (
  u.personal_skills IS NOT NULL AND u.personal_skills != '[]'::jsonb
  OR u.learning_preferences IS NOT NULL AND u.learning_preferences != '{}'::jsonb
  OR u.industry_interests IS NOT NULL AND array_length(u.industry_interests, 1) > 0
  OR u.career_goals IS NOT NULL AND u.career_goals != '{}'::jsonb
);

-- Step 2: Rename team-related columns to employment-related columns
ALTER TABLE users RENAME COLUMN searchable_by_teams TO searchable_by_organizations;
ALTER TABLE users RENAME COLUMN allow_team_invitations TO allow_employment_invitations;

-- Step 3: Drop duplicate columns that now exist in individuals table
ALTER TABLE users DROP COLUMN IF EXISTS personal_skills;
ALTER TABLE users DROP COLUMN IF EXISTS learning_preferences;
ALTER TABLE users DROP COLUMN IF EXISTS industry_interests;
ALTER TABLE users DROP COLUMN IF EXISTS career_goals;

-- Step 4: Update any existing indexes
DROP INDEX IF EXISTS idx_users_searchable;
CREATE INDEX idx_users_searchable_orgs ON users(searchable_by_organizations) WHERE searchable_by_organizations = true;

-- Step 5: Add comments to clarify table purposes
COMMENT ON TABLE users IS 'Core user table for all platform users (platform admins, organization users, individuals). Contains authentication, basic profile, and employment-related settings.';
COMMENT ON TABLE individuals IS 'Extended profile table for users who engage in training/learning activities. Contains career development, learning progress, and training-specific data.';

-- Step 6: Add helpful comments to key columns
COMMENT ON COLUMN users.role IS 'Platform-level role: platform_admin (routes to /admin) or individual (routes to /user)';
COMMENT ON COLUMN users.searchable_by_organizations IS 'Whether this user can be found by organizations for employment invitations';
COMMENT ON COLUMN users.allow_employment_invitations IS 'Whether this user accepts employment invitations from organizations';

COMMENT ON COLUMN individuals.user_id IS 'Reference to users table - only users who engage in training should have records here';
COMMENT ON COLUMN individuals.learning_status IS 'Current learning/training status for this individual';
COMMENT ON COLUMN individuals.skills IS 'Array of skill objects with proficiency levels and certifications';
COMMENT ON COLUMN individuals.career_interests IS 'Career goals, industry interests, and professional aspirations';

-- Step 7: Verify the cleanup
SELECT 
  'users table columns' as table_info,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'users' 
AND table_schema = 'public'
ORDER BY ordinal_position;
