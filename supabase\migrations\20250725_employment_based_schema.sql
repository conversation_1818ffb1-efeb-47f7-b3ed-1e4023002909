-- Luna Employment-Based Schema Migration
-- Transform from team collaboration to employment relationship model

-- Step 1: Create new enum types
CREATE TYPE employment_role AS ENUM (
  'staff_member',
  'department_admin',
  'organization_admin'
);

CREATE TYPE employment_status AS ENUM (
  'invited',
  'active',
  'on_leave',
  'terminated',
  'resigned'
);

-- Step 2: Create departments table (replaces teams concept)
CREATE TABLE departments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  department_head_id UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT departments_org_name_unique UNIQUE(organization_id, name)
);

-- Step 3: Create employment relationships table (replaces team_memberships)
CREATE TABLE employment_relationships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  department_id UUID NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
  role employment_role NOT NULL DEFAULT 'staff_member',
  status employment_status NOT NULL DEFAULT 'invited',
  job_title TEXT,
  hire_date DATE,
  termination_date DATE,
  invited_by UUID REFERENCES users(id),
  invited_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT employment_user_org_unique UNIQUE(user_id, organization_id),
  CONSTRAINT employment_termination_check CHECK (termination_date IS NULL OR termination_date >= hire_date)
);

-- Step 4: Create employment invitations table
CREATE TABLE employment_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  department_id UUID NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
  role employment_role NOT NULL DEFAULT 'staff_member',
  job_title TEXT,
  invited_by UUID NOT NULL REFERENCES users(id),
  invitation_token TEXT UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
  accepted_at TIMESTAMP WITH TIME ZONE,
  declined_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Constraints
  CONSTRAINT invitation_email_org_unique UNIQUE(email, organization_id),
  CONSTRAINT invitation_expires_check CHECK (expires_at > created_at)
);

-- Step 5: Migrate existing teams data to departments
INSERT INTO departments (id, organization_id, name, description, created_at, updated_at)
SELECT 
  id,
  organization_id,
  name,
  description,
  created_at,
  updated_at
FROM teams;

-- Step 6: Migrate existing team_memberships to employment_relationships
INSERT INTO employment_relationships (
  user_id, 
  organization_id, 
  department_id, 
  role, 
  status, 
  joined_at, 
  created_at, 
  updated_at
)
SELECT 
  tm.user_id,
  t.organization_id,
  tm.team_id, -- This becomes department_id
  CASE
    WHEN tm.role = 'owner' THEN 'department_admin'::employment_role
    WHEN tm.role = 'admin' THEN 'department_admin'::employment_role
    ELSE 'staff_member'::employment_role
  END,
  CASE 
    WHEN tm.status = 'active' THEN 'active'::employment_status
    ELSE 'invited'::employment_status
  END,
  tm.joined_at,
  tm.created_at,
  tm.updated_at
FROM team_memberships tm
JOIN teams t ON tm.team_id = t.id;

-- Step 7: Update user_contexts table
ALTER TABLE user_contexts DROP COLUMN IF EXISTS active_team_id;
ALTER TABLE user_contexts ADD COLUMN active_department_id UUID REFERENCES departments(id);
ALTER TABLE user_contexts ADD COLUMN active_employment_id UUID REFERENCES employment_relationships(id);

-- Step 8: Update user_training_data table
ALTER TABLE user_training_data DROP COLUMN IF EXISTS team_id;
ALTER TABLE user_training_data ADD COLUMN department_id UUID REFERENCES departments(id);
ALTER TABLE user_training_data ADD COLUMN employment_id UUID REFERENCES employment_relationships(id);

-- Step 9: Create indexes for performance
CREATE INDEX idx_departments_organization_id ON departments(organization_id);
CREATE INDEX idx_departments_head ON departments(department_head_id);
CREATE INDEX idx_employment_user_id ON employment_relationships(user_id);
CREATE INDEX idx_employment_organization_id ON employment_relationships(organization_id);
CREATE INDEX idx_employment_department_id ON employment_relationships(department_id);
CREATE INDEX idx_employment_status ON employment_relationships(status);
CREATE INDEX idx_invitations_email ON employment_invitations(email);
CREATE INDEX idx_invitations_token ON employment_invitations(invitation_token);
CREATE INDEX idx_invitations_expires ON employment_invitations(expires_at);

-- Step 10: Create triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON departments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employment_relationships_updated_at BEFORE UPDATE ON employment_relationships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 11: Create employment management functions

-- Function to invite user to employment
CREATE OR REPLACE FUNCTION invite_to_employment(
  p_email TEXT,
  p_organization_id UUID,
  p_department_id UUID,
  p_role employment_role DEFAULT 'staff_member',
  p_job_title TEXT DEFAULT NULL,
  p_invited_by UUID DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invitation_id UUID;
BEGIN
  -- Check if user is already employed by this organization
  IF EXISTS (
    SELECT 1 FROM employment_relationships er
    JOIN users u ON er.user_id = u.id
    WHERE u.email = p_email AND er.organization_id = p_organization_id
    AND er.status IN ('active', 'invited')
  ) THEN
    RAISE EXCEPTION 'User is already employed or invited by this organization';
  END IF;

  -- Create invitation
  INSERT INTO employment_invitations (
    email, organization_id, department_id, role, job_title, invited_by
  ) VALUES (
    p_email, p_organization_id, p_department_id, p_role, p_job_title, p_invited_by
  ) RETURNING id INTO v_invitation_id;

  RETURN v_invitation_id;
END;
$$;

-- Function to accept employment invitation
CREATE OR REPLACE FUNCTION accept_employment_invitation(
  p_invitation_token TEXT,
  p_user_id UUID
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invitation employment_invitations%ROWTYPE;
  v_employment_id UUID;
BEGIN
  -- Get invitation details
  SELECT * INTO v_invitation
  FROM employment_invitations
  WHERE invitation_token = p_invitation_token
  AND expires_at > NOW()
  AND accepted_at IS NULL
  AND declined_at IS NULL;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation token';
  END IF;

  -- Verify email matches user
  IF NOT EXISTS (SELECT 1 FROM users WHERE id = p_user_id AND email = v_invitation.email) THEN
    RAISE EXCEPTION 'User email does not match invitation';
  END IF;

  -- Create employment relationship
  INSERT INTO employment_relationships (
    user_id, organization_id, department_id, role, status,
    job_title, invited_by, invited_at, joined_at, hire_date
  ) VALUES (
    p_user_id, v_invitation.organization_id, v_invitation.department_id,
    v_invitation.role, 'active', v_invitation.job_title,
    v_invitation.invited_by, v_invitation.created_at, NOW(), CURRENT_DATE
  ) RETURNING id INTO v_employment_id;

  -- Mark invitation as accepted
  UPDATE employment_invitations
  SET accepted_at = NOW()
  WHERE id = v_invitation.id;

  -- Create employment context for user
  INSERT INTO user_contexts (
    user_id, context_type, active_organization_id,
    active_department_id, active_employment_id
  ) VALUES (
    p_user_id, 'organization', v_invitation.organization_id,
    v_invitation.department_id, v_employment_id
  ) ON CONFLICT (user_id) DO UPDATE SET
    context_type = 'organization',
    active_organization_id = v_invitation.organization_id,
    active_department_id = v_invitation.department_id,
    active_employment_id = v_employment_id;

  RETURN v_employment_id;
END;
$$;

-- Function to switch to employment context
CREATE OR REPLACE FUNCTION switch_to_employment_context(
  p_user_id UUID,
  p_employment_id UUID
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_employment employment_relationships%ROWTYPE;
BEGIN
  -- Verify employment belongs to user and is active
  SELECT * INTO v_employment
  FROM employment_relationships
  WHERE id = p_employment_id
  AND user_id = p_user_id
  AND status = 'active';

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or inactive employment relationship';
  END IF;

  -- Update user context
  UPDATE user_contexts SET
    context_type = 'organization',
    active_organization_id = v_employment.organization_id,
    active_department_id = v_employment.department_id,
    active_employment_id = v_employment.id
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$;

-- Function to switch to individual context
CREATE OR REPLACE FUNCTION switch_to_individual_context(
  p_user_id UUID
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update user context to individual
  UPDATE user_contexts SET
    context_type = 'individual',
    active_organization_id = NULL,
    active_department_id = NULL,
    active_employment_id = NULL
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$;

-- Step 12: Drop old tables (commented out for safety - uncomment when ready)
-- DROP TABLE IF EXISTS team_memberships;
-- DROP TABLE IF EXISTS teams;
-- DROP TYPE IF EXISTS team_member_role;
