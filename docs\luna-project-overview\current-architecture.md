# 🏗️ Luna Platform: Current Architecture

## 📊 Database Schema Overview

### **Core Philosophy: Employment-Based Dual-Context**

Luna operates on a unique **employment-based architecture** where:
- **Individual users** maintain personal accounts for training and career development
- **Organizations** can employ individuals through **department-based structures**
- **Users can work for multiple organizations** simultaneously
- **Context switching** allows seamless transition between individual and organizational modes
- **Data continuity** ensures training data remains with both user and organization

---

## 🗄️ Database Tables

### **👤 User Management**

#### **`users` (Core User Table)**
**Purpose**: Universal table for ALL platform users
```sql
- id (UUID, Primary Key)
- email (VARCHAR, Unique, NOT NULL)
- full_name (VARCHAR, NOT NULL)
- role (user_role: 'platform_admin' | 'individual')
- avatar_url, bio, timezone
- profile_visibility (profile_visibility enum)
- searchable_by_organizations (BOOLEAN)
- allow_employment_invitations (BOOLEAN)
- status, last_active_at, email_verified
- created_at, updated_at
```

#### **`individuals` (Training Profiles)**
**Purpose**: Extended profiles for users who engage in training
```sql
- id (UUID, Primary Key)
- user_id (UUID, References users.id)
- contact_info, education, experience, skills, certifications (JSONB)
- profile_image_url, intro_video_url, resume_url, portfolio_url
- learning_status, current_learning_path_id
- total_learning_hours, completed_courses
- skill_assessments, career_interests, salary_expectations
- learning_style, availability, notification_preferences
- created_at, updated_at
```

### **🏢 Organization Structure**

#### **`organizations` (Multi-Tenant Organizations)**
```sql
- id (UUID, Primary Key)
- name, slug (unique), description
- industry, size_range, subscription_tier
- subdomain, website_url, logo_url
- settings (JSONB), billing_info (JSONB)
- status, created_by, created_at, updated_at
```

#### **`departments` (Custom Department Structure)**
```sql
- id (UUID, Primary Key)
- organization_id (UUID, References organizations.id)
- name, description
- department_head_id (UUID, References users.id)
- settings (JSONB)
- created_at, updated_at
```

### **💼 Employment System**

#### **`employment_relationships` (Multi-Employment Support)**
```sql
- id (UUID, Primary Key)
- user_id (UUID, References users.id)
- organization_id (UUID, References organizations.id)
- department_id (UUID, References departments.id)
- role (employment_role: 'organization_admin' | 'department_admin' | 'staff_member')
- status ('active' | 'inactive' | 'terminated')
- job_title, hire_date, termination_date
- invited_by, invited_at, joined_at
- created_at, updated_at
```

#### **`employment_invitations` (Invitation System)**
```sql
- id (UUID, Primary Key)
- email, organization_id, department_id
- role (employment_role), job_title
- invitation_token, expires_at
- invited_by, accepted_at, declined_at
- created_at, updated_at
```

### **🔄 Context Management**

#### **`user_contexts` (Dual-Context Switching)**
```sql
- id (UUID, Primary Key)
- user_id (UUID, References users.id, Unique)
- active_context (context_type: 'individual' | 'organization')
- active_organization_id, active_department_id, active_employment_id
- last_org_context (UUID) -- For quick switching
- recent_contexts (JSONB), session_data (JSONB)
- created_at, updated_at
```

#### **`user_training_data` (Context-Aware Training)**
```sql
- id (UUID, Primary Key)
- user_id (UUID, References users.id)
- training_context ('individual' | 'organization')
- employment_id, department_id (for organizational context)
- skills_data, progress_data, performance_metrics (JSONB)
- created_at, updated_at
```

---

## 🔐 Security & Permissions

### **Row Level Security (RLS)**
- **Enabled on all tables** with context-aware policies
- **Multi-tenant isolation** prevents cross-organization data access
- **Individual data protection** ensures personal training data privacy

### **Role-Based Access Control**

#### **Platform Level**
- **Platform Admin**: Full system access (`/admin/*`)
- **Individual Users**: Personal account access (`/user/*`)

#### **Organization Level**
- **Organization Admin**: Full organization management
- **Department Admin**: Specific department management only
- **Staff Member**: Personal tasks + assigned department work

---

## 🚀 Key Features Implemented

### **✅ Multi-Employment Support**
- Users can work for multiple organizations simultaneously
- Each employment relationship has its own role and department
- Context switching between different employments

### **✅ Dual-Context System**
- **Individual Context**: Personal training, career development
- **Organizational Context**: Company training, department goals
- **Seamless switching** with preserved state

### **✅ Department-Based Structure**
- Organizations create custom department names
- Department admins manage specific departments only
- Flexible organizational hierarchy

### **✅ Data Continuity**
- Training data remains with user after employment ends
- Organizations retain performance data for their context
- No data loss during employment transitions

### **✅ Invitation System**
- Organizations invite users to specific departments
- Role-based invitations (org admin, dept admin, staff)
- Token-based secure invitation acceptance

---

## 🔧 Database Functions

### **Employment Management**
- `invite_to_employment()` - Send employment invitations
- `accept_employment_invitation()` - Accept and create employment
- `switch_to_organization_context()` - Switch to org mode
- `switch_to_individual_context()` - Switch to individual mode
- `get_user_employment_summary()` - Dashboard statistics

### **Triggers & Automation**
- Automatic timestamp updates on all tables
- Context switching automation
- Employment status management

---

## 📈 Current Status

### **✅ Completed (Phase 1)**
- Database schema fully implemented
- 12 test users with employment relationships
- 3 organizations with 6 departments
- Authentication system working
- Context switching infrastructure ready

### **🔄 In Progress (Phase 2)**
- API endpoints migration from team-based to employment-based
- Dashboard implementations
- Skills framework integration
- Employment management UI

### **📋 Next Steps**
- Update all API endpoints for employment model
- Implement context switching UI
- Build employment invitation system
- Create skills gap analysis features
