"use client"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Trophy, 
  Star, 
  Award, 
  Target, 
  Zap, 
  Crown, 
  Medal, 
  Gem,
  CheckCircle,
  BookOpen,
  Phone,
  Users,
  TrendingUp,
  Lightbulb
} from "lucide-react"

interface BadgeDisplayProps {
  badge: {
    id: string
    title: string
    description: string
    badge_type: string
    earned_at?: string
    metadata?: any
  }
  size?: 'sm' | 'md' | 'lg'
  showDescription?: boolean
}

// Badge type configurations with colors and icons
const BADGE_CONFIGS = {
  // Assessment badges
  'assessment_complete': {
    icon: CheckCircle,
    gradient: 'from-green-400 to-emerald-600',
    bgColor: 'bg-green-50 dark:bg-green-950/20',
    textColor: 'text-green-700 dark:text-green-300',
    borderColor: 'border-green-200 dark:border-green-800'
  },
  'assessment_perfect': {
    icon: Trophy,
    gradient: 'from-yellow-400 to-orange-500',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
    textColor: 'text-yellow-700 dark:text-yellow-300',
    borderColor: 'border-yellow-200 dark:border-yellow-800'
  },
  'assessment_streak': {
    icon: Zap,
    gradient: 'from-purple-400 to-pink-600',
    bgColor: 'bg-purple-50 dark:bg-purple-950/20',
    textColor: 'text-purple-700 dark:text-purple-300',
    borderColor: 'border-purple-200 dark:border-purple-800'
  },
  
  // Module completion badges
  'module_complete': {
    icon: BookOpen,
    gradient: 'from-blue-400 to-indigo-600',
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    textColor: 'text-blue-700 dark:text-blue-300',
    borderColor: 'border-blue-200 dark:border-blue-800'
  },
  'fast_learner': {
    icon: TrendingUp,
    gradient: 'from-cyan-400 to-teal-600',
    bgColor: 'bg-cyan-50 dark:bg-cyan-950/20',
    textColor: 'text-cyan-700 dark:text-cyan-300',
    borderColor: 'border-cyan-200 dark:border-cyan-800'
  },
  
  // Special achievement badges
  'first_assessment': {
    icon: Star,
    gradient: 'from-amber-400 to-yellow-600',
    bgColor: 'bg-amber-50 dark:bg-amber-950/20',
    textColor: 'text-amber-700 dark:text-amber-300',
    borderColor: 'border-amber-200 dark:border-amber-800'
  },
  'training_champion': {
    icon: Crown,
    gradient: 'from-violet-400 to-purple-600',
    bgColor: 'bg-violet-50 dark:bg-violet-950/20',
    textColor: 'text-violet-700 dark:text-violet-300',
    borderColor: 'border-violet-200 dark:border-violet-800'
  },
  'call_practice_master': {
    icon: Phone,
    gradient: 'from-rose-400 to-pink-600',
    bgColor: 'bg-rose-50 dark:bg-rose-950/20',
    textColor: 'text-rose-700 dark:text-rose-300',
    borderColor: 'border-rose-200 dark:border-rose-800'
  },
  'team_player': {
    icon: Users,
    gradient: 'from-emerald-400 to-green-600',
    bgColor: 'bg-emerald-50 dark:bg-emerald-950/20',
    textColor: 'text-emerald-700 dark:text-emerald-300',
    borderColor: 'border-emerald-200 dark:border-emerald-800'
  },
  'innovator': {
    icon: Lightbulb,
    gradient: 'from-orange-400 to-red-500',
    bgColor: 'bg-orange-50 dark:bg-orange-950/20',
    textColor: 'text-orange-700 dark:text-orange-300',
    borderColor: 'border-orange-200 dark:border-orange-800'
  },
  
  // Default badge
  'default': {
    icon: Award,
    gradient: 'from-gray-400 to-gray-600',
    bgColor: 'bg-gray-50 dark:bg-gray-950/20',
    textColor: 'text-gray-700 dark:text-gray-300',
    borderColor: 'border-gray-200 dark:border-gray-800'
  }
}

export function BadgeDisplay({ badge, size = 'md', showDescription = true }: BadgeDisplayProps) {
  const config = BADGE_CONFIGS[badge.badge_type as keyof typeof BADGE_CONFIGS] || BADGE_CONFIGS.default
  const IconComponent = config.icon

  const sizeClasses = {
    sm: {
      card: 'p-3',
      icon: 'h-8 w-8',
      iconContainer: 'h-12 w-12',
      title: 'text-sm font-medium',
      description: 'text-xs',
      badge: 'text-xs px-2 py-1'
    },
    md: {
      card: 'p-4',
      icon: 'h-10 w-10',
      iconContainer: 'h-16 w-16',
      title: 'text-base font-semibold',
      description: 'text-sm',
      badge: 'text-xs px-2 py-1'
    },
    lg: {
      card: 'p-6',
      icon: 'h-12 w-12',
      iconContainer: 'h-20 w-20',
      title: 'text-lg font-bold',
      description: 'text-base',
      badge: 'text-sm px-3 py-1'
    }
  }

  const classes = sizeClasses[size]

  return (
    <Card className={`${config.bgColor} ${config.borderColor} border-2 transition-all hover:shadow-lg hover:scale-105 cursor-pointer`}>
      <CardContent className={classes.card}>
        <div className="flex flex-col items-center text-center space-y-3">
          {/* Badge Icon with Gradient Background */}
          <div className={`${classes.iconContainer} rounded-full bg-gradient-to-br ${config.gradient} flex items-center justify-center shadow-lg`}>
            <IconComponent className={`${classes.icon} text-white`} />
          </div>
          
          {/* Badge Title */}
          <h3 className={`${classes.title} ${config.textColor}`}>
            {badge.title}
          </h3>
          
          {/* Badge Description */}
          {showDescription && badge.description && (
            <p className={`${classes.description} text-muted-foreground text-center`}>
              {badge.description}
            </p>
          )}
          
          {/* Earned Date */}
          {badge.earned_at && (
            <Badge variant="secondary" className={classes.badge}>
              Earned {new Date(badge.earned_at).toLocaleDateString()}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Badge grid component for displaying multiple badges
interface BadgeGridProps {
  badges: Array<{
    id: string
    title: string
    description: string
    badge_type: string
    earned_at?: string
    metadata?: any
  }>
  size?: 'sm' | 'md' | 'lg'
  showDescription?: boolean
  emptyMessage?: string
}

export function BadgeGrid({ 
  badges, 
  size = 'md', 
  showDescription = true, 
  emptyMessage = "No badges earned yet. Complete assessments and training modules to earn your first badge!" 
}: BadgeGridProps) {
  if (!badges || badges.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="mx-auto w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
          <Award className="h-8 w-8 text-gray-400" />
        </div>
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    )
  }

  const gridCols = size === 'sm' ? 'grid-cols-2 md:grid-cols-4' : 
                   size === 'md' ? 'grid-cols-1 md:grid-cols-3' : 
                   'grid-cols-1 md:grid-cols-2'

  return (
    <div className={`grid ${gridCols} gap-4`}>
      {badges.map((badge) => (
        <BadgeDisplay
          key={badge.id}
          badge={badge}
          size={size}
          showDescription={showDescription}
        />
      ))}
    </div>
  )
}

// Helper function to generate badge data based on achievements
export function generateBadgeForAssessment(assessmentTitle: string, score: number, isFirstAssessment: boolean = false) {
  const badges = []
  
  // First assessment badge
  if (isFirstAssessment) {
    badges.push({
      id: `first-assessment-${Date.now()}`,
      title: "First Steps",
      description: "Completed your first assessment",
      badge_type: "first_assessment",
      earned_at: new Date().toISOString()
    })
  }
  
  // Assessment completion badge
  badges.push({
    id: `assessment-complete-${Date.now()}`,
    title: "Assessment Complete",
    description: `Completed ${assessmentTitle}`,
    badge_type: "assessment_complete",
    earned_at: new Date().toISOString()
  })
  
  // Perfect score badge
  if (score >= 100) {
    badges.push({
      id: `perfect-score-${Date.now()}`,
      title: "Perfect Score",
      description: `Achieved 100% on ${assessmentTitle}`,
      badge_type: "assessment_perfect",
      earned_at: new Date().toISOString()
    })
  }
  
  return badges
}
