-- =============================================================================
-- SEED DATA FOR THREE-TIER COURSE ARCHITECTURE
-- Creates sample Programs → Pathways → Courses with realistic content
-- =============================================================================

-- =============================================================================
-- STEP 1: INSERT PROGRAMS (Industry-focused umbrellas)
-- =============================================================================

INSERT INTO programs (id, name, description, industry, status, sort_order, created_by) VALUES
-- IT Program
('550e8400-e29b-41d4-a716-************', 
 'Information Technology Program', 
 'Comprehensive IT training covering software development, system administration, cybersecurity, and emerging technologies.',
 'Technology', 'active', 1,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

-- BPO Program  
('550e8400-e29b-41d4-a716-446655440002',
 'Business Process Outsourcing Program',
 'Professional training for BPO operations including customer service, data processing, technical support, and quality management.',
 'Business Services', 'active', 2,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

-- Healthcare Program
('550e8400-e29b-41d4-a716-************',
 'Healthcare & Medical Program',
 'Healthcare training covering medical administration, patient care, healthcare technology, and compliance.',
 'Healthcare', 'active', 3,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

-- Finance Program
('550e8400-e29b-41d4-a716-************',
 'Finance & Accounting Program',
 'Financial services training including accounting, financial analysis, compliance, and fintech solutions.',
 'Finance', 'active', 4,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1));

-- =============================================================================
-- STEP 2: UPDATE LEARNING_PATHS TO BE PATHWAYS WITHIN PROGRAMS
-- =============================================================================

-- First, let's insert some new pathways or update existing ones
INSERT INTO learning_paths (id, title, description, slug, program_id, estimated_duration_hours, difficulty_level, sort_order, is_featured, created_by) VALUES
-- IT Program Pathways
('660e8400-e29b-41d4-a716-************',
 'Help Desk Specialist',
 'Entry-level IT support pathway covering troubleshooting, customer service, and basic system administration.',
 'help-desk-specialist',
 '550e8400-e29b-41d4-a716-************', -- IT Program
 120, 1, 1, true,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

('660e8400-e29b-41d4-a716-446655440002',
 'Network Administrator',
 'Advanced networking pathway covering network design, security, and infrastructure management.',
 'network-administrator', 
 '550e8400-e29b-41d4-a716-************', -- IT Program
 200, 3, 2, true,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

('660e8400-e29b-41d4-a716-************',
 'Software Developer',
 'Full-stack development pathway covering programming languages, frameworks, and software engineering practices.',
 'software-developer',
 '550e8400-e29b-41d4-a716-************', -- IT Program
 300, 4, 3, true,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

-- BPO Program Pathways
('660e8400-e29b-41d4-a716-************',
 'Customer Service Representative',
 'Customer service excellence pathway covering communication, problem-solving, and CRM systems.',
 'customer-service-rep',
 '550e8400-e29b-41d4-a716-446655440002', -- BPO Program
 80, 1, 1, true,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

('660e8400-e29b-41d4-a716-446655440005',
 'Technical Support Specialist',
 'Technical support pathway covering product knowledge, troubleshooting, and escalation procedures.',
 'technical-support-specialist',
 '550e8400-e29b-41d4-a716-446655440002', -- BPO Program
 150, 2, 2, true,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

('660e8400-e29b-41d4-a716-446655440006',
 'Quality Assurance Analyst',
 'QA pathway covering quality standards, process improvement, and performance metrics.',
 'qa-analyst',
 '550e8400-e29b-41d4-a716-446655440002', -- BPO Program
 100, 2, 3, false,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

-- Healthcare Program Pathways
('660e8400-e29b-41d4-a716-446655440007',
 'Medical Administrative Assistant',
 'Healthcare administration pathway covering medical terminology, patient records, and healthcare systems.',
 'medical-admin-assistant',
 '550e8400-e29b-41d4-a716-************', -- Healthcare Program
 90, 1, 1, true,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1)),

('660e8400-e29b-41d4-a716-446655440008',
 'Healthcare Data Analyst',
 'Healthcare analytics pathway covering medical data analysis, compliance, and reporting.',
 'healthcare-data-analyst',
 '550e8400-e29b-41d4-a716-************', -- Healthcare Program
 180, 3, 2, true,
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1));

-- =============================================================================
-- STEP 3: INSERT COURSES (Reusable across pathways)
-- =============================================================================

INSERT INTO courses (id, name, description, slug, level, estimated_duration, status, instructor_id, learning_objectives, tags) VALUES
-- Foundation Courses (shared across multiple pathways)
('770e8400-e29b-41d4-a716-************',
 'Professional Communication Skills',
 'Essential communication skills for workplace success including written, verbal, and digital communication.',
 'professional-communication-skills',
 'beginner', 480, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Master professional email writing', 'Develop presentation skills', 'Practice active listening', 'Handle difficult conversations'],
 ARRAY['communication', 'soft-skills', 'professional-development']),

('770e8400-e29b-41d4-a716-446655440002',
 'Customer Service Excellence',
 'Comprehensive customer service training covering service standards, conflict resolution, and customer retention.',
 'customer-service-excellence',
 'beginner', 360, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Understand customer service principles', 'Handle customer complaints effectively', 'Use CRM systems', 'Measure service quality'],
 ARRAY['customer-service', 'soft-skills', 'crm']),

('770e8400-e29b-41d4-a716-************',
 'Time Management & Productivity',
 'Productivity techniques and time management strategies for professional environments.',
 'time-management-productivity',
 'beginner', 240, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Master time management techniques', 'Use productivity tools', 'Set and achieve goals', 'Manage workplace stress'],
 ARRAY['productivity', 'time-management', 'soft-skills']),

-- IT-Specific Courses
('770e8400-e29b-41d4-a716-************',
 'Computer Hardware Fundamentals',
 'Introduction to computer hardware components, assembly, and troubleshooting.',
 'computer-hardware-fundamentals',
 'beginner', 600, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Identify computer components', 'Assemble a computer', 'Troubleshoot hardware issues', 'Understand system specifications'],
 ARRAY['hardware', 'troubleshooting', 'it-fundamentals']),

('770e8400-e29b-41d4-a716-446655440005',
 'Network Security Basics',
 'Introduction to network security concepts, threats, and protection strategies.',
 'network-security-basics',
 'intermediate', 720, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Understand security threats', 'Implement basic security measures', 'Monitor network traffic', 'Respond to security incidents'],
 ARRAY['security', 'networking', 'cybersecurity']),

('770e8400-e29b-41d4-a716-446655440006',
 'JavaScript Programming Fundamentals',
 'Complete introduction to JavaScript programming for web development.',
 'javascript-programming-fundamentals',
 'intermediate', 900, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Write JavaScript code', 'Understand DOM manipulation', 'Handle events and user interactions', 'Debug JavaScript applications'],
 ARRAY['programming', 'javascript', 'web-development']),

-- BPO-Specific Courses
('770e8400-e29b-41d4-a716-446655440007',
 'Call Center Operations',
 'Comprehensive training for call center operations including phone etiquette and call handling.',
 'call-center-operations',
 'beginner', 480, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Master phone etiquette', 'Handle different call types', 'Use call center software', 'Meet performance metrics'],
 ARRAY['call-center', 'phone-skills', 'customer-service']),

('770e8400-e29b-41d4-a716-446655440008',
 'Data Entry & Processing',
 'Accurate and efficient data entry techniques with quality control measures.',
 'data-entry-processing',
 'beginner', 300, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Achieve typing accuracy standards', 'Use data entry software', 'Implement quality control', 'Handle confidential information'],
 ARRAY['data-entry', 'accuracy', 'data-processing']),

-- Healthcare-Specific Courses
('770e8400-e29b-41d4-a716-446655440009',
 'Medical Terminology',
 'Essential medical terminology for healthcare professionals and administrators.',
 'medical-terminology',
 'beginner', 540, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Understand medical prefixes and suffixes', 'Learn body system terminology', 'Use medical abbreviations correctly', 'Communicate with healthcare professionals'],
 ARRAY['medical-terminology', 'healthcare', 'medical-knowledge']),

('770e8400-e29b-41d4-a716-446655440010',
 'HIPAA Compliance Training',
 'Comprehensive HIPAA compliance training for healthcare workers.',
 'hipaa-compliance-training',
 'intermediate', 360, 'published',
 (SELECT id FROM users WHERE role = 'platform_admin' LIMIT 1),
 ARRAY['Understand HIPAA regulations', 'Protect patient information', 'Handle data breaches', 'Maintain compliance documentation'],
 ARRAY['hipaa', 'compliance', 'healthcare', 'privacy']);

-- =============================================================================
-- STEP 4: CREATE COURSE MODULES FOR SAMPLE COURSES
-- =============================================================================

-- Modules for Professional Communication Skills
INSERT INTO course_modules (id, course_id, name, description, sequence_order, estimated_duration) VALUES
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Written Communication', 'Master professional writing skills including emails, reports, and documentation.', 1, 120),
('880e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-************', 'Verbal Communication', 'Develop speaking and presentation skills for professional settings.', 2, 120),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Digital Communication', 'Navigate modern digital communication tools and platforms.', 3, 120),
('880e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 'Interpersonal Skills', 'Build strong working relationships and handle difficult conversations.', 4, 120);

-- Modules for Customer Service Excellence
INSERT INTO course_modules (id, course_id, name, description, sequence_order, estimated_duration) VALUES
('880e8400-e29b-41d4-a716-446655440005', '770e8400-e29b-41d4-a716-446655440002', 'Service Fundamentals', 'Core principles of excellent customer service.', 1, 90),
('880e8400-e29b-41d4-a716-446655440006', '770e8400-e29b-41d4-a716-446655440002', 'Conflict Resolution', 'Handle customer complaints and difficult situations.', 2, 90),
('880e8400-e29b-41d4-a716-446655440007', '770e8400-e29b-41d4-a716-446655440002', 'CRM Systems', 'Use customer relationship management tools effectively.', 3, 90),
('880e8400-e29b-41d4-a716-446655440008', '770e8400-e29b-41d4-a716-446655440002', 'Service Metrics', 'Understand and improve service quality measurements.', 4, 90);

-- =============================================================================
-- STEP 5: CREATE SAMPLE LESSONS
-- =============================================================================

-- Lessons for Written Communication Module
INSERT INTO course_lessons (id, module_id, name, description, lesson_type, sequence_order, estimated_duration) VALUES
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'Professional Email Writing', 'Learn to write clear, concise, and professional emails.', 'video', 1, 30),
('990e8400-e29b-41d4-a716-446655440002', '880e8400-e29b-41d4-a716-************', 'Business Report Writing', 'Structure and write effective business reports.', 'video', 2, 45),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'Documentation Best Practices', 'Create clear and useful documentation.', 'text', 3, 30),
('990e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'Writing Assessment', 'Test your writing skills with practical exercises.', 'quiz', 4, 15);

-- =============================================================================
-- STEP 6: LINK PATHWAYS TO COURSES (PATHWAY_COURSES)
-- =============================================================================

-- Help Desk Specialist Pathway Courses
INSERT INTO pathway_courses (pathway_id, course_id, sequence_order, is_required) VALUES
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 1, true),  -- Professional Communication
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440002', 2, true),  -- Customer Service Excellence
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 3, true),  -- Computer Hardware Fundamentals
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 4, false); -- Time Management (optional)

-- Customer Service Representative Pathway Courses
INSERT INTO pathway_courses (pathway_id, course_id, sequence_order, is_required) VALUES
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 1, true),  -- Professional Communication
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440002', 2, true),  -- Customer Service Excellence
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-446655440007', 3, true),  -- Call Center Operations
('660e8400-e29b-41d4-a716-************', '770e8400-e29b-41d4-a716-************', 4, false); -- Time Management (optional)

-- Network Administrator Pathway Courses
INSERT INTO pathway_courses (pathway_id, course_id, sequence_order, is_required) VALUES
('660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-************', 1, true),  -- Computer Hardware Fundamentals
('660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-446655440005', 2, true),  -- Network Security Basics
('660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-************', 3, true),  -- Professional Communication
('660e8400-e29b-41d4-a716-446655440002', '770e8400-e29b-41d4-a716-************', 4, false); -- Time Management (optional)

-- Medical Administrative Assistant Pathway Courses
INSERT INTO pathway_courses (pathway_id, course_id, sequence_order, is_required) VALUES
('660e8400-e29b-41d4-a716-446655440007', '770e8400-e29b-41d4-a716-446655440009', 1, true),  -- Medical Terminology
('660e8400-e29b-41d4-a716-446655440007', '770e8400-e29b-41d4-a716-446655440010', 2, true),  -- HIPAA Compliance
('660e8400-e29b-41d4-a716-446655440007', '770e8400-e29b-41d4-a716-************', 3, true),  -- Professional Communication
('660e8400-e29b-41d4-a716-446655440007', '770e8400-e29b-41d4-a716-446655440008', 4, false); -- Data Entry (optional)

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Show the three-tier structure
SELECT 'SEED DATA SUMMARY' as info;

SELECT 'Programs created:' as info, COUNT(*) as count FROM programs;
SELECT 'Pathways created:' as info, COUNT(*) as count FROM learning_paths WHERE program_id IS NOT NULL;
SELECT 'Courses created:' as info, COUNT(*) as count FROM courses;
SELECT 'Course modules created:' as info, COUNT(*) as count FROM course_modules;
SELECT 'Course lessons created:' as info, COUNT(*) as count FROM course_lessons;
SELECT 'Pathway-course links created:' as info, COUNT(*) as count FROM pathway_courses;

-- Show sample structure
SELECT 'SAMPLE THREE-TIER STRUCTURE:' as info;
SELECT 
  p.name as program,
  lp.title as pathway,
  c.name as course,
  pc.sequence_order,
  pc.is_required
FROM programs p
JOIN learning_paths lp ON p.id = lp.program_id
JOIN pathway_courses pc ON lp.id = pc.pathway_id
JOIN courses c ON pc.course_id = c.id
ORDER BY p.name, lp.title, pc.sequence_order
LIMIT 10;
