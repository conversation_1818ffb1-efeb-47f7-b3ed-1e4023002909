'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { 
  Bar<PERSON>hart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { 
  ChartBar, 
  TrendingUp, 
  Users, 
  Calendar, 
  Clock, 
  CheckCircle2, 
  XCircle, 
  HelpCircle, 
  Filter, 
  Download
} from 'lucide-react';

// Sample colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

export default function BPOAnalyticsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [bpoData, setBpoData] = useState<any>(null);
  const [timeRange, setTimeRange] = useState('month');
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      total_applications: 0,
      active_vacancies: 0,
      scheduled_interviews: 0,
      hired_candidates: 0
    },
    application_sources: [
      { name: 'Direct', value: 0 },
      { name: 'Job Board', value: 0 },
      { name: 'Referral', value: 0 },
      { name: 'Social Media', value: 0 },
      { name: 'Other', value: 0 }
    ],
    application_trends: [] as any[],
    vacancy_performance: [] as any[],
    status_distribution: [
      { name: 'Reviewing', value: 0 },
      { name: 'Interview', value: 0 },
      { name: 'Accepted', value: 0 },
      { name: 'Rejected', value: 0 }
    ],
    time_to_fill: { avg_days: 0, data: [] as any[] }
  });

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Get the current user
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;
        
        setUser(session.user);
        
        // Get the BPO company this user belongs to
        const { data: teamMember, error: teamMemberError } = await supabase
          .from('bpo_teams')
          .select('bpo_id')
          .eq('user_id', session.user.id)
          .single();
          
        if (teamMemberError || !teamMember) {
          console.error('Error fetching team member:', teamMemberError);
          return;
        }
        
        // Get BPO data
        const { data: bpo, error: bpoError } = await supabase
          .from('bpos')
          .select('*')
          .eq('id', teamMember.bpo_id)
          .single();
          
        if (bpoError || !bpo) {
          console.error('Error fetching BPO data:', bpoError);
          return;
        }
        
        setBpoData(bpo);
        
        // Fetch analytics data based on the time range
        await fetchAnalyticsData(teamMember.bpo_id, timeRange);
        
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const fetchAnalyticsData = async (bpoId: string, range: string) => {
    try {
      setLoading(true);
      
      // In a real implementation, we would fetch actual data from the database
      // For now, we'll use mock data that looks realistic
      
      // Calculate date range for filtering
      const now = new Date();
      let startDate = new Date();
      
      switch (range) {
        case 'week':
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(now.getMonth() - 1); // Default to last month
      }
      
      // Get overview metrics
      // In a real app, these would be actual aggregated queries
      
      // Total active vacancies
      const { count: activeVacancies } = await supabase
        .from('job_postings')
        .select('*', { count: 'exact', head: true })
        .eq('bpo_id', bpoId)
        .eq('status', 'open');
      
      // Total applications in the period
      const { count: totalApplications } = await supabase
        .from('applications')
        .select('applications.id', { count: 'exact', head: true })
        .eq('job_postings.bpo_id', bpoId)
        .gte('submitted_at', startDate.toISOString());
      
      // Total scheduled interviews
      const { count: scheduledInterviews } = await supabase
        .from('interviews')
        .select('interviews.id', { count: 'exact', head: true })
        .eq('applications.job_postings.bpo_id', bpoId)
        .gte('scheduled_at', startDate.toISOString());
      
      // Total hired candidates
      const { count: hiredCandidates } = await supabase
        .from('applications')
        .select('applications.id', { count: 'exact', head: true })
        .eq('job_postings.bpo_id', bpoId)
        .eq('status', 'accepted')
        .gte('updated_at', startDate.toISOString());
      
      // Generate application trends (daily/weekly/monthly data points)
      let trendInterval = 'day';
      let dataPoints = 30;
      
      if (range === 'week') {
        trendInterval = 'day';
        dataPoints = 7;
      } else if (range === 'month') {
        trendInterval = 'day';
        dataPoints = 30;
      } else if (range === 'quarter') {
        trendInterval = 'week';
        dataPoints = 12;
      } else if (range === 'year') {
        trendInterval = 'month';
        dataPoints = 12;
      }
      
      // In a real app, you would aggregate data by the interval
      // For now, we'll generate realistic mock data
      const applicationTrends = generateMockTrends(dataPoints, trendInterval);
      
      // Generate vacancy performance data
      // In a real app, this would come from actual data
      const { data: vacancies } = await supabase
        .from('job_postings')
        .select('id, title')
        .eq('bpo_id', bpoId)
        .limit(5);
        
      const vacancyPerformance = (vacancies || []).map((vacancy: any, index: number) => ({
        name: vacancy.title,
        applications: Math.floor(Math.random() * 50) + 5,
        interviews: Math.floor(Math.random() * 20) + 2,
        hires: Math.floor(Math.random() * 5)
      }));
      
      // Status distribution
      const statusDistribution = [
        { name: 'Reviewing', value: Math.floor(Math.random() * 40) + 20 },
        { name: 'Interview', value: Math.floor(Math.random() * 30) + 10 },
        { name: 'Accepted', value: Math.floor(Math.random() * 15) + 5 },
        { name: 'Rejected', value: Math.floor(Math.random() * 25) + 15 }
      ];
      
      // Application sources
      const applicationSources = [
        { name: 'Direct', value: Math.floor(Math.random() * 30) + 20 },
        { name: 'Job Board', value: Math.floor(Math.random() * 40) + 30 },
        { name: 'Referral', value: Math.floor(Math.random() * 20) + 10 },
        { name: 'Social Media', value: Math.floor(Math.random() * 25) + 15 },
        { name: 'Other', value: Math.floor(Math.random() * 15) + 5 }
      ];
      
      // Time to fill data (average days from posting to hire)
      const timeToFillData = [
        { name: 'Entry Level', days: Math.floor(Math.random() * 15) + 10 },
        { name: 'Mid Level', days: Math.floor(Math.random() * 20) + 15 },
        { name: 'Senior Level', days: Math.floor(Math.random() * 30) + 20 }
      ];
      
      const avgTimeToFill = timeToFillData.reduce((sum, item) => sum + item.days, 0) / timeToFillData.length;
      
      // Update analytics data state
      setAnalyticsData({
        overview: {
          total_applications: totalApplications || 0,
          active_vacancies: activeVacancies || 0,
          scheduled_interviews: scheduledInterviews || 0,
          hired_candidates: hiredCandidates || 0
        },
        application_sources: applicationSources,
        application_trends: applicationTrends,
        vacancy_performance: vacancyPerformance,
        status_distribution: statusDistribution,
        time_to_fill: { 
          avg_days: avgTimeToFill, 
          data: timeToFillData 
        }
      });
      
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      toast({
        title: "Data error",
        description: "Failed to fetch analytics data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value);
    if (bpoData) {
      fetchAnalyticsData(bpoData.id, value);
    }
  };

  const generateMockTrends = (dataPoints: number, interval: string) => {
    const result = [];
    const now = new Date();
    
    for (let i = dataPoints - 1; i >= 0; i--) {
      const date = new Date();
      
      if (interval === 'day') {
        date.setDate(now.getDate() - i);
      } else if (interval === 'week') {
        date.setDate(now.getDate() - (i * 7));
      } else if (interval === 'month') {
        date.setMonth(now.getMonth() - i);
      }
      
      // Generate realistic looking data with some randomness but following a trend
      const applications = Math.floor(Math.random() * 15) + 5 + (dataPoints - i);
      const interviews = Math.floor(applications * 0.6);
      const hires = Math.floor(interviews * 0.3);
      
      let name = '';
      if (interval === 'day') {
        name = date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
      } else if (interval === 'week') {
        name = `Week ${dataPoints - i}`;
      } else if (interval === 'month') {
        name = date.toLocaleDateString(undefined, { month: 'short' });
      }
      
      result.push({
        name,
        applications,
        interviews,
        hires
      });
    }
    
    return result;
  };

  const exportData = () => {
    toast({
      title: "Export started",
      description: "Analytics data is being prepared for download",
    });
    
    // In a real app, this would generate a CSV or Excel file with the data
    setTimeout(() => {
      toast({
        title: "Export completed",
        description: "Analytics data has been exported successfully",
      });
    }, 1500);
  };

  if (loading && !analyticsData.overview.total_applications) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Recruitment Analytics</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Track and analyze your recruitment performance
          </p>
        </div>
        
        <div className="flex gap-4">
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">Last 7 days</SelectItem>
              <SelectItem value="month">Last 30 days</SelectItem>
              <SelectItem value="quarter">Last 3 months</SelectItem>
              <SelectItem value="year">Last 12 months</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            <span>Export</span>
          </Button>
        </div>
      </div>
      
      {/* Overview Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Applications
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {analyticsData.overview.total_applications}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <ChartBar className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Active Vacancies
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {analyticsData.overview.active_vacancies}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-purple-500 rounded-md p-3">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Scheduled Interviews
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {analyticsData.overview.scheduled_interviews}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-amber-500 rounded-md p-3">
                <CheckCircle2 className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Hired Candidates
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {analyticsData.overview.hired_candidates}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Charts */}
      <Tabs defaultValue="trends" className="w-full">
        <TabsList className="grid w-full md:w-[600px] grid-cols-4">
          <TabsTrigger value="trends">Application Trends</TabsTrigger>
          <TabsTrigger value="vacancies">Vacancy Performance</TabsTrigger>
          <TabsTrigger value="sources">Application Sources</TabsTrigger>
          <TabsTrigger value="metrics">Key Metrics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="trends" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Application Trends</CardTitle>
              <CardDescription>
                Applications, interviews and hires over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={analyticsData.application_trends}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="applications" stroke="#8884d8" activeDot={{ r: 8 }} />
                    <Line type="monotone" dataKey="interviews" stroke="#82ca9d" />
                    <Line type="monotone" dataKey="hires" stroke="#ffc658" />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="vacancies" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Vacancy Performance</CardTitle>
              <CardDescription>
                Applications, interviews, and hires by job vacancy
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={analyticsData.vacancy_performance}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="applications" fill="#8884d8" />
                    <Bar dataKey="interviews" fill="#82ca9d" />
                    <Bar dataKey="hires" fill="#ffc658" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="sources" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Application Sources</CardTitle>
              <CardDescription>
                Where candidates are coming from
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <div className="h-80 w-full max-w-xl">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={analyticsData.application_sources}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {analyticsData.application_sources.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} applications`, 'Count']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="metrics" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Candidate Status Distribution</CardTitle>
              <CardDescription>
                Current status of all candidates in the pipeline
              </CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <div className="h-80 w-full max-w-xl">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={analyticsData.status_distribution}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {analyticsData.status_distribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} candidates`, 'Count']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Time to Fill (Days)</CardTitle>
              <CardDescription>
                Average days from job posting to hire by job level
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={analyticsData.time_to_fill.data}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="days" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              
              <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-2" />
                  <p className="text-sm font-medium">
                    Average time to fill: <span className="font-bold">{analyticsData.time_to_fill.avg_days.toFixed(1)} days</span>
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 