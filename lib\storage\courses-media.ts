import { createClient } from '@supabase/supabase-js';

// Create a Supabase client with service role for storage operations
function getSupabaseAdmin() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export const COURSES_MEDIA_BUCKET = 'courses-media';

// Storage folder structure
export const STORAGE_FOLDERS = {
  COURSES: 'courses',
  MODULES: 'modules', 
  LESSONS: 'lessons',
  AI_GENERATED: 'ai-generated',
  TEMPLATES: 'templates'
} as const;

// File type configurations
export const ALLOWED_FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  VIDEOS: ['video/mp4', 'video/webm', 'video/mov', 'video/avi'],
  DOCUMENTS: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  PRESENTATIONS: ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation']
} as const;

export const MAX_FILE_SIZES = {
  IMAGE: 10 * 1024 * 1024, // 10MB
  VIDEO: 500 * 1024 * 1024, // 500MB
  DOCUMENT: 50 * 1024 * 1024, // 50MB
  PRESENTATION: 100 * 1024 * 1024 // 100MB
} as const;

// Helper function to get file path
export function getCoursesMediaPath(
  contentType: keyof typeof STORAGE_FOLDERS,
  contentId: string,
  fileName: string
): string {
  return `${STORAGE_FOLDERS[contentType]}/${contentId}/${fileName}`;
}

// Helper function to get public URL
export function getCoursesMediaUrl(filePath: string): string {
  const supabaseAdmin = getSupabaseAdmin();
  const { data } = supabaseAdmin.storage
    .from(COURSES_MEDIA_BUCKET)
    .getPublicUrl(filePath);

  return data.publicUrl;
}

// Upload file to courses media bucket
export async function uploadCoursesMedia(
  file: File,
  contentType: keyof typeof STORAGE_FOLDERS,
  contentId: string,
  fileName?: string
) {
  try {
    const supabaseAdmin = getSupabaseAdmin();
    const finalFileName = fileName || `${Date.now()}-${file.name}`;
    const filePath = getCoursesMediaPath(contentType, contentId, finalFileName);

    const { data, error } = await supabaseAdmin.storage
      .from(COURSES_MEDIA_BUCKET)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      throw error;
    }

    return {
      path: data.path,
      publicUrl: getCoursesMediaUrl(data.path),
      fileName: finalFileName,
      fileSize: file.size,
      mimeType: file.type
    };
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
}

// Delete file from courses media bucket
export async function deleteCoursesMedia(filePath: string) {
  try {
    const supabaseAdmin = getSupabaseAdmin();
    const { error } = await supabaseAdmin.storage
      .from(COURSES_MEDIA_BUCKET)
      .remove([filePath]);

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
}

// Validate file type and size
export function validateFile(file: File, allowedTypes: string[], maxSize: number) {
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  if (file.size > maxSize) {
    throw new Error(`File size ${file.size} exceeds maximum allowed size of ${maxSize} bytes`);
  }

  return true;
}

// Create storage bucket if it doesn't exist
export async function ensureCoursesMediaBucket() {
  try {
    const supabaseAdmin = getSupabaseAdmin();
    // Check if bucket exists
    const { data: buckets, error: listError } = await supabaseAdmin.storage.listBuckets();
    
    if (listError) {
      throw listError;
    }

    const bucketExists = buckets?.some(bucket => bucket.name === COURSES_MEDIA_BUCKET);

    if (!bucketExists) {
      // Create the bucket
      const { error: createError } = await supabaseAdmin.storage.createBucket(COURSES_MEDIA_BUCKET, {
        public: true,
        allowedMimeTypes: [
          ...ALLOWED_FILE_TYPES.IMAGES,
          ...ALLOWED_FILE_TYPES.VIDEOS,
          ...ALLOWED_FILE_TYPES.DOCUMENTS,
          ...ALLOWED_FILE_TYPES.PRESENTATIONS
        ],
        fileSizeLimit: MAX_FILE_SIZES.VIDEO // Use largest size as bucket limit
      });

      if (createError) {
        throw createError;
      }

      console.log(`Created storage bucket: ${COURSES_MEDIA_BUCKET}`);
    }

    return { success: true };
  } catch (error) {
    console.error('Error ensuring courses media bucket:', error);
    throw error;
  }
}
