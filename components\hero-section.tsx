"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, Star } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface HeroSectionProps {
  className?: string
}

export function HeroSection({ className = "" }: HeroSectionProps) {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className={`relative min-h-[600px] overflow-hidden ${className}`}>
      {/* Vibrant Blue, Yellow, Orange, Purple Glossy Gradient */}
      <div className="absolute inset-0">
        {/* Base vibrant gradient - Blue to Yellow to Orange to Purple */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-yellow-300 via-orange-400 to-purple-500"></div>
        <div className="absolute inset-0 bg-gradient-to-tr from-blue-300 via-orange-300 to-purple-400 opacity-80"></div>
        <div className="absolute inset-0 bg-gradient-to-bl from-yellow-400 via-orange-500 to-blue-600 opacity-60"></div>

        {/* Vibrant radial gradients for depth */}
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: `
            radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 80% 30%, rgba(168, 85, 247, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 60% 80%, rgba(251, 146, 60, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 30% 70%, rgba(250, 204, 21, 0.4) 0%, transparent 50%),
            radial-gradient(circle at 90% 10%, rgba(147, 51, 234, 0.3) 0%, transparent 50%)
          `,
          backgroundSize: '800px 800px, 600px 600px, 700px 700px, 500px 500px, 650px 650px',
          backgroundPosition: '0 0, 300px 200px, 500px 400px, 200px 500px, 600px 100px'
        }}></div>

        {/* Strong glossy shine effects */}
        <div className="absolute inset-0 bg-gradient-to-t from-white/10 via-white/30 to-white/50"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-transparent to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-tl from-transparent via-white/20 to-white/30"></div>

        {/* Light overlay to make text readable */}
        <div className="absolute inset-0 bg-white/60"></div>

        {/* Seamless gradient transition to next section - Extended and smoother */}
        <div className="absolute bottom-0 left-0 right-0 h-48 bg-gradient-to-b from-transparent via-gray-50/30 to-gray-50"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 py-16 px-6">
        <div className="max-w-6xl mx-auto text-center">
          {/* Topline Badge */}
          <div className="flex items-center justify-center gap-2 text-sm text-blue-600 mb-8">
            <Star className="h-4 w-4 text-blue-600 fill-blue-600" />
            <span className="font-medium">The World's Most Powerful AI-Driven Professional Search</span>
          </div>

          {/* Main Heading - Professional & Sophisticated */}
          <div className="mb-8">
            <h1 className="mb-6 max-w-5xl mx-auto text-center" style={{
              fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif',
              fontSize: '3rem',
              fontWeight: 700,
              color: '#111827',
              lineHeight: '1',
              letterSpacing: '-0.025em'
            }}>
              Learn new skills, Upskill existing ones,
              <br />
              Nurture your development, and Achieve your goals.
            </h1>

            <p className="text-base md:text-lg text-gray-600 max-w-3xl mx-auto font-normal leading-relaxed mb-8" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
              Learn new skills, Upskill existing ones, Nurture your development, and Achieve your goals.
            </p>

          </div>

          {/* Unified AI Chat Interface with KPIs */}
          <div className="bg-white rounded-2xl shadow-xl p-8 mb-8 max-w-6xl mx-auto border border-gray-100" style={{
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.05)'
          }}>
            {/* KPI Cards - Integrated into AI Chat */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="text-center">
                <div className="flex items-center justify-center mb-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-500 mb-1" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>Training Progress</div>
                <div className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>0%</div>
                <p className="text-xs text-gray-400 mt-1" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                  0 of 0 modules completed
                </p>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-3">
                  <div className="bg-blue-600 h-1.5 rounded-full" style={{ width: '0%' }}></div>
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-500 mb-1" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>Active Modules</div>
                <div className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>0</div>
                <div className="flex items-center justify-center gap-2 mt-1">
                  <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                  <p className="text-xs text-gray-400" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                    0 of 0 in progress
                  </p>
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-amber-500 to-orange-600 text-white shadow-lg shadow-amber-500/20">
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                    </svg>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-500 mb-1" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>Achievements</div>
                <div className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>0</div>
                <div className="flex items-center justify-center gap-2 mt-1">
                  <div className="h-2 w-2 rounded-full bg-amber-500"></div>
                  <p className="text-xs text-gray-400" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                    0 of 0 assessments
                  </p>
                </div>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center mb-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/20">
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                </div>
                <div className="text-sm font-medium text-gray-500 mb-1" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>AI Call Practice</div>
                <div className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>0.0</div>
                <div className="flex items-center justify-center gap-2 mt-1">
                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                  <p className="text-xs text-gray-400" style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                    0/100 Avg. Score
                  </p>
                </div>
              </div>
            </div>

            {/* Separator */}
            <div className="border-t border-gray-100 mb-6"></div>
            <div className="relative mb-6">
              <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="I am looking for a PR expert to help me get publicity for my..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-14 pr-40 py-5 text-base border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 rounded-xl font-normal placeholder:text-gray-400 bg-gray-50/50 hover:bg-white transition-all duration-200"
                style={{
                  fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif',
                  fontSize: '16px'
                }}
              />
              <Button
                className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                size="sm"
                style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}
              >
                <Star className="h-4 w-4 mr-2 fill-current" />
                Search with AI
              </Button>
            </div>

            {/* Enhanced Quick Search Pills */}
            <div className="flex flex-wrap gap-3 justify-center mb-8">
              <button className="bg-gradient-to-r from-gray-50 to-gray-100/80 hover:from-blue-50 hover:to-blue-100/80 text-gray-700 hover:text-blue-700 px-5 py-3 rounded-full text-sm font-medium transition-all duration-200 border border-gray-200/60 hover:border-blue-200 shadow-sm hover:shadow-md"
                style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                Graphic Designer in Colombia
              </button>
              <button className="bg-gradient-to-r from-gray-50 to-gray-100/80 hover:from-blue-50 hover:to-blue-100/80 text-gray-700 hover:text-blue-700 px-5 py-3 rounded-full text-sm font-medium transition-all duration-200 border border-gray-200/60 hover:border-blue-200 shadow-sm hover:shadow-md"
                style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                AI Sales Strategy Expert in the US
              </button>
              <button className="bg-gradient-to-r from-gray-50 to-gray-100/80 hover:from-blue-50 hover:to-blue-100/80 text-gray-700 hover:text-blue-700 px-5 py-3 rounded-full text-sm font-medium transition-all duration-200 border border-gray-200/60 hover:border-blue-200 shadow-sm hover:shadow-md"
                style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                Fractional CFO in Texas
              </button>
            </div>

            {/* Enhanced Bottom Actions */}
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5"
                style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                <Star className="h-4 w-4 mr-2 fill-current" />
                Get Your Luna Rate (Beta)
              </Button>
              <Button variant="link" className="text-blue-600 hover:text-blue-700 font-medium text-sm underline-offset-4 hover:underline transition-all duration-200"
                style={{ fontFamily: 'SF Pro Rounded, Inter, -apple-system, BlinkMacSystemFont, system-ui, sans-serif' }}>
                Click Here to Learn More About the Luna Rate
              </Button>
            </div>
          </div>

          {/* Trusted By Section */}
          <div className="text-center">
            <p className="text-sm text-gray-500 mb-6 font-medium">Trusted by Professionals at</p>
            <div className="flex items-center justify-center gap-8 opacity-70">
              <span className="text-gray-600 font-semibold">Google</span>
              <span className="text-gray-600 font-semibold">Meta</span>
              <span className="text-gray-600 font-semibold">Stanford</span>
              <span className="text-gray-600 font-semibold">OpenAI</span>
              <span className="text-gray-600 font-semibold">BCG</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
