import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';

// Create admin client for operations that need to bypass RLS
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "";
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);

// POST - Mark a lesson as complete
export async function POST(req: Request) {
  try {
    const { lessonId } = await req.json();
    
    if (!lessonId) {
      return NextResponse.json(
        { error: 'Lesson ID is required' }, 
        { status: 400 }
      );
    }
    
    // Create Supabase client with user's session
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Verify user is authenticated
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Get user data
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("id, role")
      .eq("id", session.user.id)
      .single();
    
    if (userError || !userData || userData.role !== "prospect") {
      return NextResponse.json({ error: 'Only prospects can complete lessons' }, { status: 403 });
    }
    
    // Get the prospect ID
    const { data: prospectData, error: prospectError } = await adminClient
      .from("prospects")
      .select("id")
      .eq("user_id", userData.id)
      .single();
    
    if (prospectError || !prospectData) {
      return NextResponse.json({ 
        error: 'Prospect not found', 
        details: prospectError?.message || "No prospect profile found"
      }, { status: 404 });
    }
    
    const prospectId = prospectData.id;
    
    // Verify the lesson exists
    const { data: lesson, error: lessonError } = await adminClient
      .from("lessons")
      .select("id, module_id")
      .eq("id", lessonId)
      .single();
    
    if (lessonError || !lesson) {
      return NextResponse.json({ 
        error: 'Lesson not found', 
        details: lessonError?.message 
      }, { status: 404 });
    }
    
    // Get all activities for this lesson
    const { data: activities, error: activitiesError } = await adminClient
      .from("activities")
      .select("id")
      .eq("lesson_id", lessonId);
    
    if (activitiesError) {
      return NextResponse.json({ 
        error: 'Failed to fetch activities', 
        details: activitiesError.message 
      }, { status: 500 });
    }
    
    // If there are no activities for this lesson, create a dummy one to track completion
    if (!activities || activities.length === 0) {
      // Create a dummy activity for this lesson to ensure we can track completion
      const { data: dummyActivity, error: createError } = await adminClient
        .from("activities")
        .insert({
          lesson_id: lessonId,
          title: `Auto-generated completion activity for lesson ${lessonId}`,
          type: 'reading',
          content: {},
          order_index: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();
        
      if (createError) {
        return NextResponse.json({ 
          error: 'Failed to create tracking activity', 
          details: createError.message 
        }, { status: 500 });
      }
      
      // Add the dummy activity to the activities array
      activities?.push(dummyActivity);
    }
    
    const now = new Date().toISOString();
    const progressUpdates = [];
    const errors = [];
    
    // Mark all activities as completed
    if (activities && activities.length > 0) {
      // Check existing progress records for these activities
      const activityIds = activities.map(activity => activity.id);
      const { data: existingRecords, error: recordsError } = await adminClient
        .from("progress_records")
        .select("id, activity_id, status")
        .eq("prospect_id", prospectId)
        .in("activity_id", activityIds);
      
      if (recordsError) {
        return NextResponse.json({ 
          error: 'Failed to fetch existing progress records', 
          details: recordsError.message 
        }, { status: 500 });
      }
      
      // Create a map of activity_id to progress status
      const progressMap = new Map();
      existingRecords?.forEach(record => {
        progressMap.set(record.activity_id, record.status);
      });
      
      // Create or update progress records for each activity
      for (const activity of activities) {
        // Skip if already completed
        if (progressMap.get(activity.id) === 'completed') {
          continue;
        }
        
        // If record exists but not completed, update it
        if (progressMap.has(activity.id)) {
          const { error } = await adminClient
            .from("progress_records")
            .update({
              status: 'completed',
              completed_at: now,
              updated_at: now
            })
            .eq("prospect_id", prospectId)
            .eq("activity_id", activity.id);
            
          if (error) {
            errors.push({
              operation: 'update',
              entity: 'progress_record',
              message: error.message,
              code: error.code,
              details: error.details
            });
          } else {
            progressUpdates.push(activity.id);
          }
        } else {
          // Create new record
          const { error } = await adminClient
            .from("progress_records")
            .insert({
              prospect_id: prospectId,
              activity_id: activity.id,
              status: 'completed',
              score: 100, // Default score
              completed_at: now,
              created_at: now,
              updated_at: now
            });
            
          if (error) {
            errors.push({
              operation: 'insert',
              entity: 'progress_record',
              message: error.message,
              code: error.code,
              details: error.details
            });
          } else {
            progressUpdates.push(activity.id);
          }
        }
      }
    }
    
    // Check if there were any errors
    if (errors.length > 0) {
      console.error('Errors updating progress records:', JSON.stringify(errors));
      return NextResponse.json({ 
        error: 'Some records could not be marked as completed', 
        details: errors 
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Lesson marked as completed successfully',
      completed: progressUpdates.length,
      lesson_completed: progressUpdates.length > 0
    });
    
  } catch (error: any) {
    console.error('Error marking lesson as complete:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to complete lesson' }, 
      { status: 500 }
    );
  }
} 