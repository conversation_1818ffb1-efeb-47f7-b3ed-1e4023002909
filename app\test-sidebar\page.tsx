"use client"

import { LunaLayout } from "@/components/luna-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useLunaAuth, useCurrentContext } from "@/hooks/use-luna-auth"
import {
  CheckCircle2,
  AlertCircle,
  Info,
  Zap,
  Users,
  Settings,
  BarChart3,
  Bug
} from "lucide-react"

export default function TestSidebarPage() {
  const { user, switchContext } = useLunaAuth()
  const { context, availableEmployments } = useCurrentContext()

  return (
    <LunaLayout>
      <div className="p-6 space-y-6">
        {/* Page Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">🎉 New Luna Sidebar - Live!</h1>
          <p className="text-muted-foreground">
            The new unified sidebar has been successfully implemented across the Luna platform.
          </p>
        </div>

        {/* Implementation Status */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Individual Pages</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Complete</div>
              <p className="text-xs text-muted-foreground">
                All individual pages now use LunaLayout
              </p>
              <div className="mt-4">
                <Badge variant="secondary">app/individual/layout.tsx</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Organization Pages</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Complete</div>
              <p className="text-xs text-muted-foreground">
                Organization and org/[slug] layouts updated
              </p>
              <div className="mt-4 space-x-1">
                <Badge variant="secondary">app/organization/</Badge>
                <Badge variant="secondary">app/org/[orgSlug]/</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Admin Pages</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">Complete</div>
              <p className="text-xs text-muted-foreground">
                Admin layout updated with proper navigation
              </p>
              <div className="mt-4">
                <Badge variant="secondary">app/admin/layout.tsx</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Features Implemented */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Key Features Implemented
            </CardTitle>
            <CardDescription>
              What's working in the new Luna sidebar
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Context-Aware Navigation</div>
                    <div className="text-sm text-muted-foreground">
                      Different nav items for Individual/Organization/Admin
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Integrated Context Switcher</div>
                    <div className="text-sm text-muted-foreground">
                      Switch between contexts directly in sidebar
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Theme Support</div>
                    <div className="text-sm text-muted-foreground">
                      Built-in light/dark/system theme switcher
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Responsive Design</div>
                    <div className="text-sm text-muted-foreground">
                      Collapsible sidebar with mobile optimization
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Breadcrumb Navigation</div>
                    <div className="text-sm text-muted-foreground">
                      Automatic breadcrumbs based on current route
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Legacy Cleanup</div>
                    <div className="text-sm text-muted-foreground">
                      Removed old sidebar implementations
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Navigation Test
            </CardTitle>
            <CardDescription>
              Test the context-aware navigation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <h4 className="font-medium">Individual Context</h4>
                <p className="text-sm text-muted-foreground">
                  Learning, Career, Support sections
                </p>
                <Button variant="outline" size="sm" asChild>
                  <a href="/individual">Test Individual</a>
                </Button>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Organization Context</h4>
                <p className="text-sm text-muted-foreground">
                  Team Management, L&D, Analytics
                </p>
                <Button variant="outline" size="sm" asChild>
                  <a href="/organization">Test Organization</a>
                </Button>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Admin Context</h4>
                <p className="text-sm text-muted-foreground">
                  Platform Management, Content, Settings
                </p>
                <Button variant="outline" size="sm" asChild>
                  <a href="/admin">Test Admin</a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Next Steps
            </CardTitle>
            <CardDescription>
              Recommended actions for continued improvement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <div className="font-medium">Test All User Flows</div>
                  <div className="text-sm text-muted-foreground">
                    Verify navigation works correctly for all user types and contexts
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <div className="font-medium">Update Badge Counts</div>
                  <div className="text-sm text-muted-foreground">
                    Connect real data to navigation badge counts for notifications
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <div className="font-medium">Performance Testing</div>
                  <div className="text-sm text-muted-foreground">
                    Monitor bundle size reduction and rendering performance
                  </div>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <div className="font-medium">Remove Legacy Components</div>
                  <div className="text-sm text-muted-foreground">
                    Clean up remaining old sidebar components when confident in new implementation
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Debug Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bug className="h-5 w-5" />
              Debug Information
            </CardTitle>
            <CardDescription>
              Current authentication and context state
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium">User Information</h4>
                <div className="text-sm space-y-1">
                  <div>ID: {user?.id || 'Not available'}</div>
                  <div>Name: {user?.full_name || 'Not available'}</div>
                  <div>Email: {user?.email || 'Not available'}</div>
                  <div>Role: {user?.role || 'Not available'}</div>
                  <div>Is Platform Admin: {user?.isPlatformAdmin ? 'Yes' : 'No'}</div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Context Information</h4>
                <div className="text-sm space-y-1">
                  <div>Current Context: {context?.type || 'Not available'}</div>
                  <div>Organization ID: {context?.organization_id || 'N/A'}</div>
                  <div>Available Employments: {availableEmployments?.length || 0}</div>
                  <div>Switch Function: {switchContext ? 'Available' : 'Not available'}</div>
                </div>
              </div>
            </div>

            {availableEmployments && availableEmployments.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Available Organizations</h4>
                <div className="space-y-1">
                  {availableEmployments.map((emp, index) => (
                    <div key={emp.organization_id} className="text-sm p-2 bg-muted rounded">
                      <div>#{index + 1}: {emp.organization_name}</div>
                      <div className="text-xs text-muted-foreground">
                        Role: {emp.role} • Department: {emp.department_name}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Success Message */}
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
              <div>
                <div className="font-semibold text-green-800 dark:text-green-200">
                  Implementation Complete!
                </div>
                <div className="text-sm text-green-700 dark:text-green-300">
                  The new Luna sidebar has been successfully implemented across all major page layouts.
                  The platform now has a unified, context-aware navigation system.
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </LunaLayout>
  )
}
