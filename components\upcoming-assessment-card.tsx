import { Clock } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

type UpcomingAssessmentCardProps = {
  title: string
  dueDate: string
  daysRemaining: number
  questions: number
  duration: number
  status: "due-soon" | "upcoming"
  description?: string
}

export function UpcomingAssessmentCard({
  title,
  dueDate,
  daysRemaining,
  questions,
  duration,
  status,
  description,
}: UpcomingAssessmentCardProps) {
  return (
    <div className="rounded-lg border bg-card dark:bg-gray-800/50 p-3 transition-all duration-200 hover:shadow-sm">
      <div className="flex items-center justify-between">
        <h3 className="font-medium">{title}</h3>
        <Badge
          className={cn(
            status === "due-soon"
              ? "bg-amber-100 text-amber-600 hover:bg-amber-100 dark:bg-amber-900/30 dark:text-amber-400"
              : "bg-gray-100 text-gray-600 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-400",
          )}
        >
          {status === "due-soon" ? "Due Soon" : "Upcoming"}
        </Badge>
      </div>
      <p className="mt-1 text-sm text-muted-foreground">
        {questions} questions • {duration} minutes
      </p>
      {description && <p className="mt-1 text-xs text-muted-foreground">{description}</p>}
      <div className="mt-2 flex items-center text-sm">
        <Clock className="mr-1 h-3 w-3 text-muted-foreground" />
        <span className="text-muted-foreground">
          Due {dueDate} • {daysRemaining} days remaining
        </span>
      </div>
    </div>
  )
}
