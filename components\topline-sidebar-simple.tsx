"use client"

import React from 'react'

interface ToplineSidebarProps {
  children?: React.ReactNode
}

export function ToplineSidebarSimple({ children }: ToplineSidebarProps) {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="fixed left-0 top-0 h-full w-16 bg-white border-r border-gray-200 shadow-sm">
        <div className="p-4">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">L</span>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 ml-16">
        <div className="p-8">
          {children}
        </div>
      </div>
    </div>
  )
}
