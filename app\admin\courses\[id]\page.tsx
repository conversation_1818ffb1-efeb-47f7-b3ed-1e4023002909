"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import {
  ArrowLeft,
  BookOpen,
  Clock,
  Users,
  FileText
} from 'lucide-react'
import { toast } from "sonner"
import { CourseBasicInfoSection } from '@/components/admin/course-details/course-basic-info-section'
import { CourseMediaSection } from '@/components/admin/course-details/course-media-section'
import { CourseLearningSection } from '@/components/admin/course-details/course-learning-section'
import { CourseTechnicalSection } from '@/components/admin/course-details/course-technical-section'
import { CourseTagsSection } from '@/components/admin/course-details/course-tags-section'
import { CourseModulesSection } from '@/components/admin/course-details/course-modules-section'
import { CourseSettingsSection } from '@/components/admin/course-details/course-settings-section'

interface Course {
  id: string
  name: string
  description: string
  slug: string
  level: string
  estimated_duration: number
  status: string
  price: number
  cover_image_url?: string
  preview_video_url?: string
  meta_description?: string
  tags: string[]
  learning_objectives: string[]
  target_audience?: string
  instructor_id?: string
  instructor_bio?: string
  enrollment_count: number
  completion_rate: number
  average_rating: number
  course_complexity: string
  certification_available: boolean
  is_standalone: boolean
  required_software: string[]
  hardware_requirements?: string
  language: string
  accessibility_features: string[]
  prerequisite_courses: string[]
  created_at: string
  updated_at: string
}

interface Module {
  id: string
  course_id: string
  name: string
  description: string
  sequence_order: number
  estimated_duration: number
  is_standalone: boolean
  single_price?: number
  status: string
  learning_objectives: string[]
  module_type: string
  difficulty_level: string
  required_resources: string[]
  module_prerequisites: string[]
  lessons?: Lesson[]
}

interface Lesson {
  id: string
  module_id: string
  name: string
  description: string
  lesson_type: string
  content_url?: string
  content_data: any
  sequence_order: number
  estimated_duration: number
  is_mandatory: boolean
  status: string
  has_assessment: boolean
  assessment_type?: string
  weight_in_module: number
  required_software: string[]
  downloadable_resources: any[]
  external_links: any[]
  lesson_prerequisites: string[]
  video_duration?: number
  transcript?: string
  reading_time?: number
  passing_score?: number
  attempts_allowed?: number
  time_limit?: number
  submission_format: string[]
  max_file_size?: number
}

export default function CourseDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const courseId = params.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [modules, setModules] = useState<Module[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (courseId) {
      fetchCourseDetails()
    }
  }, [courseId])

  const fetchCourseDetails = async () => {
    try {
      setLoading(true)

      // Fetch course details
      const courseResponse = await fetch(`/api/admin/courses/${courseId}`)
      if (!courseResponse.ok) {
        throw new Error('Failed to fetch course')
      }
      const courseData = await courseResponse.json()
      setCourse(courseData)

      // Fetch course modules with lessons
      const modulesResponse = await fetch(`/api/admin/courses/${courseId}/modules`)
      if (modulesResponse.ok) {
        const modulesData = await modulesResponse.json()
        setModules(modulesData.modules || [])
      }

    } catch (error) {
      console.error('Error fetching course details:', error)
      toast.error('Failed to load course details')
    } finally {
      setLoading(false)
    }
  }

  const handleCourseUpdate = () => {
    fetchCourseDetails()
    toast.success('Course updated successfully')
  }



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading course details...</p>
        </div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Course not found</h2>
          <p className="mt-2 text-gray-600">The course you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/admin/courses')} className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Courses
          </Button>
        </div>
      </div>
    )
  }

  const totalLessons = modules.reduce((total, module) => total + (module.lessons?.length || 0), 0)
  const totalDuration = course.estimated_duration || 0

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/admin/courses')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Courses
              </Button>
              <div className="h-6 w-px bg-gray-300" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {course.name || 'Course Details'}
                </h1>
                <p className="text-sm text-gray-500 mt-1">
                  Manage course information, modules, and settings
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant={course.status === 'published' ? 'default' : 'secondary'}>
                {course.status}
              </Badge>
              <Badge variant="outline">
                {course.course_complexity}
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - 30/70 Layout */}
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-full mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-10 gap-8">

            {/* Left Column - Course Information (30%) */}
            <div className="lg:col-span-3 space-y-6">

              {/* Course Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Course Overview</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <BookOpen className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                      <div className="text-xl font-bold text-blue-600">{modules.length}</div>
                      <div className="text-xs text-blue-600">Modules</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <FileText className="h-5 w-5 text-green-600 mx-auto mb-1" />
                      <div className="text-xl font-bold text-green-600">{totalLessons}</div>
                      <div className="text-xs text-green-600">Lessons</div>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <Clock className="h-5 w-5 text-purple-600 mx-auto mb-1" />
                      <div className="text-xl font-bold text-purple-600">{Math.round(totalDuration / 60)}h</div>
                      <div className="text-xs text-purple-600">Duration</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <Users className="h-5 w-5 text-orange-600 mx-auto mb-1" />
                      <div className="text-xl font-bold text-orange-600">{course.enrollment_count}</div>
                      <div className="text-xs text-orange-600">Enrolled</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Accordion type="multiple" defaultValue={["basic-info"]} className="space-y-4">
                <AccordionItem value="basic-info">
                  <AccordionTrigger className="text-lg font-semibold">Course Information</AccordionTrigger>
                  <AccordionContent>
                    <CourseBasicInfoSection
                      course={course}
                      onUpdate={handleCourseUpdate}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="media">
                  <AccordionTrigger className="text-lg font-semibold">Media & URLs</AccordionTrigger>
                  <AccordionContent>
                    <CourseMediaSection
                      course={course}
                      onUpdate={handleCourseUpdate}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="learning">
                  <AccordionTrigger className="text-lg font-semibold">Learning Details</AccordionTrigger>
                  <AccordionContent>
                    <CourseLearningSection
                      course={course}
                      onUpdate={handleCourseUpdate}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="technical">
                  <AccordionTrigger className="text-lg font-semibold">Technical Requirements</AccordionTrigger>
                  <AccordionContent>
                    <CourseTechnicalSection
                      course={course}
                      onUpdate={handleCourseUpdate}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="tags">
                  <AccordionTrigger className="text-lg font-semibold">Tags & Categories</AccordionTrigger>
                  <AccordionContent>
                    <CourseTagsSection
                      course={course}
                      onUpdate={handleCourseUpdate}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="settings">
                  <AccordionTrigger className="text-lg font-semibold">Course Settings</AccordionTrigger>
                  <AccordionContent>
                    <CourseSettingsSection
                      course={course}
                      onUpdate={handleCourseUpdate}
                    />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            {/* Right Column - Modules & Lessons (70%) */}
            <div className="lg:col-span-7 space-y-6">
              <CourseModulesSection
                courseId={courseId}
                modules={modules}
                onUpdate={fetchCourseDetails}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
