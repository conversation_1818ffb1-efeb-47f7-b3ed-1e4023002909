'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog'
import { useToast } from '@/hooks/use-toast'
import { 
  LogOut, 
  Users, 
  AlertTriangle, 
  RefreshCw, 
  Shield,
  Clock,
  User
} from 'lucide-react'

interface SessionData {
  totalUsers: number
  recentlyActiveUsers: number
  users: Array<{
    id: string
    email: string
    full_name: string
    last_sign_in_at: string
  }>
}

export function HardLogoutControl() {
  const [loading, setLoading] = useState(false)
  const [sessionData, setSessionData] = useState<SessionData | null>(null)
  const [loadingSessionData, setLoadingSessionData] = useState(false)
  const { toast } = useToast()

  const fetchSessionData = async () => {
    setLoadingSessionData(true)
    try {
      const response = await fetch('/api/admin/hard-logout', {
        method: 'GET',
      })

      if (!response.ok) {
        throw new Error('Failed to fetch session data')
      }

      const data = await response.json()
      setSessionData(data)
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to fetch session data",
        variant: "destructive"
      })
    } finally {
      setLoadingSessionData(false)
    }
  }

  const handleHardLogout = async (action: 'logout_all_users' | 'logout_single_user', targetUserId?: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/hard-logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action,
          targetUserId 
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Hard logout failed')
      }

      toast({
        title: "Success",
        description: data.message,
        variant: "default"
      })

      // Show additional info if there were errors
      if (data.errors && data.errors.length > 0) {
        toast({
          title: "Partial Success",
          description: `${data.loggedOutUsers} users logged out, but ${data.errors.length} errors occurred.`,
          variant: "destructive"
        })
      }

      // Refresh session data
      await fetchSessionData()

    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Hard logout failed",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const formatLastSignIn = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMins / 60)

    if (diffMins < 60) {
      return `${diffMins} minutes ago`
    } else if (diffHours < 24) {
      return `${diffHours} hours ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  return (
    <Card className="border-red-200 dark:border-red-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
          <Shield className="h-5 w-5" />
          Session Management
        </CardTitle>
        <CardDescription>
          Monitor active user sessions and perform emergency logout operations. Use with extreme caution.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Session Overview */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchSessionData}
              disabled={loadingSessionData}
            >
              {loadingSessionData ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </Button>
            
            {sessionData && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  {sessionData.totalUsers} Total Users
                </Badge>
                <Badge variant="default" className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {sessionData.recentlyActiveUsers} Active (24h)
                </Badge>
              </div>
            )}
          </div>
        </div>

        {/* Recently Active Users */}
        {sessionData && sessionData.users.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Recently Active Users</h4>
            <div className="max-h-40 overflow-y-auto space-y-1">
              {sessionData.users.slice(0, 10).map((user) => (
                <div key={user.id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded text-sm">
                  <div className="flex items-center gap-2">
                    <User className="h-3 w-3" />
                    <span className="font-medium">{user.full_name}</span>
                    <span className="text-gray-500">({user.email})</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">
                      {formatLastSignIn(user.last_sign_in_at)}
                    </span>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="outline" size="sm" className="h-6 px-2">
                          <LogOut className="h-3 w-3" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-red-500" />
                            Logout User
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to force logout <strong>{user.full_name}</strong> ({user.email})?
                            This will immediately invalidate their session and they will need to log in again.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleHardLogout('logout_single_user', user.id)}
                            className="bg-red-600 hover:bg-red-700"
                            disabled={loading}
                          >
                            {loading ? 'Logging out...' : 'Logout User'}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Hard Logout All Button */}
        <div className="pt-4 border-t border-red-200 dark:border-red-800">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button 
                variant="destructive" 
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <LogOut className="mr-2 h-4 w-4" />
                    Emergency Logout All Users
                  </>
                )}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                  Emergency Hard Logout
                </AlertDialogTitle>
                <AlertDialogDescription className="space-y-2">
                  <p>
                    <strong>⚠️ CRITICAL ACTION:</strong> This will immediately log out ALL users from the platform except yourself.
                  </p>
                  <p>
                    All active user sessions will be invalidated and users will need to log in again.
                    This action should only be used in emergency situations such as:
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>Security breach or suspected unauthorized access</li>
                    <li>System maintenance requiring all users to be offline</li>
                    <li>Critical platform updates</li>
                  </ul>
                  <p className="text-red-600 font-medium">
                    This action cannot be undone. Are you absolutely sure?
                  </p>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={() => handleHardLogout('logout_all_users')}
                  className="bg-red-600 hover:bg-red-700"
                  disabled={loading}
                >
                  {loading ? 'Logging out all users...' : 'Yes, Logout All Users'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        <div className="text-xs text-gray-500 bg-yellow-50 dark:bg-yellow-900/20 p-2 rounded">
          <strong>Note:</strong> This feature uses Supabase Admin API to invalidate user sessions. 
          Logged out users will receive an authentication error on their next request and will be redirected to login.
        </div>
      </CardContent>
    </Card>
  )
}
