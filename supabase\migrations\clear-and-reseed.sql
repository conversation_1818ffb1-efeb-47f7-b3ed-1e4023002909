-- =============================================================================
-- LUNA DATABASE: COMPREHENSIVE CLEAR AND RESEED SCRIPT
-- Employment-Based Architecture with Complete Data Population
-- =============================================================================

-- Clear existing data in proper dependency order
DELETE FROM user_training_data;
DELETE FROM user_contexts;
DELETE FROM employment_invitations;
DELETE FROM employment_relationships;
DELETE FROM departments;
DELETE FROM organizations;
DELETE FROM individuals;
DELETE FROM users;

-- =============================================================================
-- STEP 1: INSERT USERS (Core User Table)
-- =============================================================================

-- Platform Admin
INSERT INTO users (
  id, email, full_name, role,
  avatar_url, bio, timezone,
  profile_visibility, searchable_by_organizations, allow_employment_invitations,
  status, email_verified, last_active_at, created_at, updated_at
) VALUES (
  'b2b059b1-4e51-4749-903e-a16fa23630e7',
  '<EMAIL>',
  'Platform Administrator',
  'platform_admin',
  'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
  'Platform administrator with full system access and management capabilities.',
  'UTC',
  'public',
  true,
  true,
  'active',
  true,
  NOW(),
  NOW(),
  NOW()
);

-- Individual Users with comprehensive profiles
INSERT INTO users (
  id, email, full_name, role,
  avatar_url, bio, timezone,
  profile_visibility, searchable_by_organizations, allow_employment_invitations,
  status, email_verified, last_active_at, created_at, updated_at
) VALUES
  (
    'f220b2b0-05bd-4986-b883-9ab51123e520',
    '<EMAIL>',
    'Mike Chen',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=mike',
    'Senior UX/UI Designer with 8+ years experience in digital product design.',
    'America/Los_Angeles',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '2 hours',
    NOW(),
    NOW()
  ),
  (
    '19028181-f4d1-4872-9235-2d5f771a1bae',
    '<EMAIL>',
    'Alex Rodriguez',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=alex',
    'Data Analyst specializing in business intelligence and predictive analytics.',
    'America/New_York',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '1 day',
    NOW(),
    NOW()
  ),
  (
    '47028720-f0d6-4e65-a7fe-e5cb09a4d0b9',
    '<EMAIL>',
    'Carlos Gonzalez',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=carlos',
    'Technical Support Specialist with expertise in customer service and troubleshooting.',
    'America/Chicago',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '3 days',
    NOW(),
    NOW()
  ),
  (
    '8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e',
    '<EMAIL>',
    'Emma Davis',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=emma',
    'Registered Nurse with 8+ years experience in patient care and medical procedures.',
    'America/Denver',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '5 hours',
    NOW(),
    NOW()
  ),
  (
    'a9387548-0f8e-4992-8a7e-8a7e8a7e8a7e',
    '<EMAIL>',
    'David Franco',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=david',
    'Healthcare Training Instructor specializing in medical education and simulation training.',
    'America/Phoenix',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '1 week',
    NOW(),
    NOW()
  ),
  (
    'afd8cd82-0291-46a1-b205-f800b30baefb',
    '<EMAIL>',
    'Jennifer Martinez',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=jennifer',
    'HR Manager with expertise in talent acquisition and employee development.',
    'America/Los_Angeles',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '2 days',
    NOW(),
    NOW()
  ),
  (
    'd3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f',
    '<EMAIL>',
    'John Smith',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
    'Full-Stack Developer with expertise in modern web technologies and cloud architecture.',
    'America/New_York',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '6 hours',
    NOW(),
    NOW()
  ),
  (
    'e4a901a6-e9f1-4fbf-a392-e4ae7cd6fd73',
    '<EMAIL>',
    'Maria Garcia',
    'individual',
    'https://api.dicebear.com/7.x/avataaars/svg?seed=maria',
    'Digital Marketing Specialist with expertise in content strategy and social media marketing.',
    'America/Miami',
    'public',
    true,
    true,
    'active',
    true,
    NOW() - INTERVAL '4 hours',
    NOW(),
    NOW()
  );




-- =============================================================================
-- STEP 2: INSERT INDIVIDUALS (Training Profiles)
-- =============================================================================

-- Create individual profiles for all non-admin users
INSERT INTO individuals (
  id, user_id,
  contact_info, education, experience, skills, certifications,
  profile_image_url, intro_video_url, resume_url, portfolio_url,
  learning_status, current_learning_path_id, total_learning_hours, completed_courses,
  skill_assessments, career_interests, salary_expectations,
  learning_style, availability, notification_preferences,
  created_at, updated_at
) VALUES
  (
    gen_random_uuid(),
    'f220b2b0-05bd-4986-b883-9ab51123e520', -- Mike Chen
    '{"phone": "******-0101", "linkedin": "linkedin.com/in/mikechen", "location": "San Francisco, CA"}'::jsonb,
    ARRAY['{"degree": "Bachelor of Fine Arts", "institution": "Art Institute", "year": 2015, "field": "Graphic Design"}'::jsonb],
    ARRAY['{"title": "Senior UX Designer", "company": "TechCorp", "duration": "2020-Present", "description": "Lead design for enterprise applications"}'::jsonb],
    ARRAY['{"name": "UI/UX Design", "level": "Expert", "years": 8}'::jsonb, '{"name": "Figma", "level": "Expert", "years": 6}'::jsonb],
    ARRAY['{"name": "Google UX Design Certificate", "issuer": "Google", "year": 2020}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=mike',
    'https://example.com/videos/mike-intro.mp4',
    'https://example.com/resumes/mike-chen.pdf',
    'https://mikechen.design',
    'in_progress',
    NULL,
    156.5,
    12,
    '{"design_thinking": 95, "prototyping": 88, "user_research": 82}'::jsonb,
    '{"primary": "Design Leadership", "secondary": "Design Systems"}'::jsonb,
    '{"min": 85000, "max": 120000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Visual", "pace": "Fast", "environment": "Collaborative"}'::jsonb,
    '{"hours_per_week": 40, "flexible_schedule": true, "remote_work": true}'::jsonb,
    '{"email": true, "push": true, "sms": false, "frequency": "daily"}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    '19028181-f4d1-4872-9235-2d5f771a1bae', -- Alex Rodriguez
    '{"phone": "******-0102", "linkedin": "linkedin.com/in/alexrodriguez", "location": "New York, NY"}'::jsonb,
    ARRAY['{"degree": "Master of Science", "institution": "NYU", "year": 2018, "field": "Data Science"}'::jsonb],
    ARRAY['{"title": "Senior Data Analyst", "company": "FinanceCorpNY", "duration": "2019-Present", "description": "Lead analytics for investment strategies"}'::jsonb],
    ARRAY['{"name": "Python", "level": "Expert", "years": 6}'::jsonb, '{"name": "SQL", "level": "Expert", "years": 7}'::jsonb],
    ARRAY['{"name": "AWS Certified Data Analytics", "issuer": "Amazon", "year": 2021}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=alex',
    'https://example.com/videos/alex-intro.mp4',
    'https://example.com/resumes/alex-rodriguez.pdf',
    'https://alexrodriguez.data',
    'in_progress',
    NULL,
    203.0,
    18,
    '{"statistical_analysis": 92, "machine_learning": 85, "data_visualization": 88}'::jsonb,
    '{"primary": "Data Science Leadership", "secondary": "AI/ML Engineering"}'::jsonb,
    '{"min": 95000, "max": 130000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Analytical", "pace": "Moderate", "environment": "Independent"}'::jsonb,
    '{"hours_per_week": 45, "flexible_schedule": true, "remote_work": true}'::jsonb,
    '{"email": true, "push": true, "sms": false, "frequency": "weekly"}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    '47028720-f0d6-4e65-a7fe-e5cb09a4d0b9', -- Carlos Gonzalez
    '{"phone": "******-0103", "linkedin": "linkedin.com/in/carlosgonzalez", "location": "Chicago, IL"}'::jsonb,
    ARRAY['{"degree": "Associate Degree", "institution": "Chicago Community College", "year": 2017, "field": "Information Technology"}'::jsonb],
    ARRAY['{"title": "Technical Support Specialist", "company": "TechCorp", "duration": "2018-Present", "description": "Customer support and technical troubleshooting"}'::jsonb],
    ARRAY['{"name": "Technical Support", "level": "Expert", "years": 6}'::jsonb, '{"name": "Customer Service", "level": "Expert", "years": 8}'::jsonb],
    ARRAY['{"name": "CompTIA A+", "issuer": "CompTIA", "year": 2018}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=carlos',
    NULL,
    'https://example.com/resumes/carlos-gonzalez.pdf',
    NULL,
    'in_progress',
    NULL,
    89.5,
    8,
    '{"technical_support": 90, "customer_service": 95, "problem_solving": 87}'::jsonb,
    '{"primary": "Support Team Leadership", "secondary": "Technical Training"}'::jsonb,
    '{"min": 55000, "max": 75000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Hands-on", "pace": "Moderate", "environment": "Team-based"}'::jsonb,
    '{"hours_per_week": 40, "flexible_schedule": false, "remote_work": false}'::jsonb,
    '{"email": true, "push": true, "sms": true, "frequency": "daily"}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    '8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e', -- Emma Davis
    '{"phone": "******-0104", "linkedin": "linkedin.com/in/emmadavis", "location": "Denver, CO"}'::jsonb,
    ARRAY['{"degree": "Bachelor of Science in Nursing", "institution": "University of Colorado", "year": 2016, "field": "Nursing"}'::jsonb],
    ARRAY['{"title": "Registered Nurse", "company": "HealthPlus Medical", "duration": "2016-Present", "description": "Patient care in medical-surgical unit"}'::jsonb],
    ARRAY['{"name": "Patient Care", "level": "Expert", "years": 8}'::jsonb, '{"name": "Medical Documentation", "level": "Advanced", "years": 8}'::jsonb],
    ARRAY['{"name": "Registered Nurse License", "issuer": "Colorado Board of Nursing", "year": 2016}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=emma',
    NULL,
    'https://example.com/resumes/emma-davis.pdf',
    NULL,
    'in_progress',
    NULL,
    134.0,
    15,
    '{"patient_care": 95, "medical_documentation": 88, "healthcare_tech": 72}'::jsonb,
    '{"primary": "Nurse Management", "secondary": "Healthcare Technology"}'::jsonb,
    '{"min": 70000, "max": 90000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Practical", "pace": "Intensive", "environment": "Collaborative"}'::jsonb,
    '{"hours_per_week": 36, "flexible_schedule": true, "remote_work": false}'::jsonb,
    '{"email": true, "push": true, "sms": true, "frequency": "daily"}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    'a9387548-0f8e-4992-8a7e-8a7e8a7e8a7e', -- David Franco
    '{"phone": "******-0105", "linkedin": "linkedin.com/in/davidfranco", "location": "Phoenix, AZ"}'::jsonb,
    ARRAY['{"degree": "Master of Education", "institution": "Arizona State University", "year": 2014, "field": "Adult Learning"}'::jsonb],
    ARRAY['{"title": "Healthcare Training Instructor", "company": "HealthPlus Medical", "duration": "2015-Present", "description": "Design and deliver medical training programs"}'::jsonb],
    ARRAY['{"name": "Instructional Design", "level": "Expert", "years": 9}'::jsonb, '{"name": "Medical Training", "level": "Expert", "years": 9}'::jsonb],
    ARRAY['{"name": "Certified Professional in Learning and Performance", "issuer": "ATD", "year": 2018}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=david',
    'https://example.com/videos/david-intro.mp4',
    'https://example.com/resumes/david-franco.pdf',
    'https://davidfranco.training',
    'completed',
    NULL,
    278.5,
    24,
    '{"instructional_design": 95, "medical_training": 92, "curriculum_development": 88}'::jsonb,
    '{"primary": "Training Director", "secondary": "Learning Technology Specialist"}'::jsonb,
    '{"min": 75000, "max": 95000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Blended", "pace": "Moderate", "environment": "Collaborative"}'::jsonb,
    '{"hours_per_week": 40, "flexible_schedule": true, "remote_work": true}'::jsonb,
    '{"email": true, "push": false, "sms": false, "frequency": "weekly"}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    'afd8cd82-0291-46a1-b205-f800b30baefb', -- Jennifer Martinez
    '{"phone": "******-0106", "linkedin": "linkedin.com/in/jennifermartinez", "location": "Los Angeles, CA"}'::jsonb,
    ARRAY['{"degree": "Master of Business Administration", "institution": "UCLA", "year": 2015, "field": "Human Resources"}'::jsonb],
    ARRAY['{"title": "HR Manager", "company": "TechCorp", "duration": "2017-Present", "description": "Lead talent acquisition and employee development"}'::jsonb],
    ARRAY['{"name": "HR Management", "level": "Expert", "years": 7}'::jsonb, '{"name": "Talent Acquisition", "level": "Expert", "years": 8}'::jsonb],
    ARRAY['{"name": "SHRM-CP", "issuer": "SHRM", "year": 2018}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=jennifer',
    NULL,
    'https://example.com/resumes/jennifer-martinez.pdf',
    NULL,
    'in_progress',
    NULL,
    167.0,
    14,
    '{"hr_management": 92, "talent_acquisition": 95, "employee_development": 88}'::jsonb,
    '{"primary": "HR Director", "secondary": "Organizational Development"}'::jsonb,
    '{"min": 85000, "max": 110000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Interactive", "pace": "Moderate", "environment": "Collaborative"}'::jsonb,
    '{"hours_per_week": 45, "flexible_schedule": true, "remote_work": true}'::jsonb,
    '{"email": true, "push": true, "sms": false, "frequency": "daily"}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    'd3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f', -- John Smith
    '{"phone": "******-0107", "linkedin": "linkedin.com/in/johnsmith", "location": "New York, NY"}'::jsonb,
    ARRAY['{"degree": "Bachelor of Science", "institution": "MIT", "year": 2016, "field": "Computer Science"}'::jsonb],
    ARRAY['{"title": "Full-Stack Developer", "company": "TechCorp", "duration": "2018-Present", "description": "Lead development of enterprise web applications"}'::jsonb],
    ARRAY['{"name": "JavaScript", "level": "Expert", "years": 8}'::jsonb, '{"name": "React", "level": "Expert", "years": 6}'::jsonb],
    ARRAY['{"name": "AWS Solutions Architect", "issuer": "Amazon", "year": 2022}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
    'https://example.com/videos/john-intro.mp4',
    'https://example.com/resumes/john-smith.pdf',
    'https://johnsmith.dev',
    'in_progress',
    NULL,
    245.5,
    22,
    '{"javascript": 95, "react": 92, "nodejs": 88, "aws": 85}'::jsonb,
    '{"primary": "Senior Software Engineer", "secondary": "Technical Lead"}'::jsonb,
    '{"min": 110000, "max": 140000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Project-based", "pace": "Fast", "environment": "Independent"}'::jsonb,
    '{"hours_per_week": 50, "flexible_schedule": true, "remote_work": true}'::jsonb,
    '{"email": true, "push": true, "sms": false, "frequency": "daily"}'::jsonb,
    NOW(),
    NOW()
  ),
  (
    gen_random_uuid(),
    'e4a901a6-e9f1-4fbf-a392-e4ae7cd6fd73', -- Maria Garcia
    '{"phone": "******-0108", "linkedin": "linkedin.com/in/mariagarcia", "location": "Miami, FL"}'::jsonb,
    ARRAY['{"degree": "Bachelor of Arts", "institution": "University of Miami", "year": 2017, "field": "Marketing"}'::jsonb],
    ARRAY['{"title": "Digital Marketing Specialist", "company": "Freelance", "duration": "2018-Present", "description": "Digital marketing consultant for various clients"}'::jsonb],
    ARRAY['{"name": "Digital Marketing", "level": "Expert", "years": 6}'::jsonb, '{"name": "Content Strategy", "level": "Advanced", "years": 5}'::jsonb],
    ARRAY['{"name": "Google Ads Certified", "issuer": "Google", "year": 2021}'::jsonb],
    'https://api.dicebear.com/7.x/avataaars/svg?seed=maria',
    'https://example.com/videos/maria-intro.mp4',
    'https://example.com/resumes/maria-garcia.pdf',
    'https://mariagarcia.marketing',
    'in_progress',
    NULL,
    198.0,
    16,
    '{"digital_marketing": 92, "content_strategy": 88, "seo": 85, "social_media": 90}'::jsonb,
    '{"primary": "Marketing Consultant", "secondary": "Digital Strategy Expert"}'::jsonb,
    '{"min": 65000, "max": 85000, "currency": "USD", "type": "annual"}'::jsonb,
    '{"type": "Case-study", "pace": "Self-paced", "environment": "Independent"}'::jsonb,
    '{"hours_per_week": 35, "flexible_schedule": true, "remote_work": true}'::jsonb,
    '{"email": true, "push": false, "sms": false, "frequency": "weekly"}'::jsonb,
    NOW(),
    NOW()
  );

-- =============================================================================
-- STEP 3: INSERT ORGANIZATIONS
-- =============================================================================

INSERT INTO organizations (
  id, name, slug, description, industry, size_range,
  logo_url, website_url, branding_config,
  subdomain, custom_domain,
  subscription_tier, max_teams, max_members, features_enabled,
  created_by, status, settings,
  created_at, updated_at
) VALUES
  (
    '11111111-1111-1111-1111-111111111111',
    'TechCorp Solutions',
    'techcorp-solutions',
    'Leading technology solutions provider specializing in enterprise software development and digital transformation services.',
    'Technology',
    'large',
    'https://api.dicebear.com/7.x/initials/svg?seed=TechCorp',
    'https://techcorp.com',
    '{"primary_color": "#2563eb", "secondary_color": "#1e40af", "logo_style": "modern"}'::jsonb,
    'techcorp',
    'techcorp.luna.platform',
    'enterprise',
    50,
    500,
    '{"advanced_analytics": true, "custom_branding": true, "api_access": true}'::jsonb,
    'b2b059b1-4e51-4749-903e-a16fa23630e7',
    'active',
    '{"timezone": "America/Los_Angeles", "working_hours": "9-17", "training_budget": 50000}'::jsonb,
    NOW() - INTERVAL '6 months',
    NOW()
  ),
  (
    '2222**************-2222-************',
    'Creative Agency Inc',
    'creative-agency',
    'Full-service creative and marketing agency focused on brand development, digital marketing, and creative content production.',
    'Marketing',
    'medium',
    'https://api.dicebear.com/7.x/initials/svg?seed=Creative',
    'https://creativeagency.com',
    '{"primary_color": "#7c3aed", "secondary_color": "#6d28d9", "logo_style": "creative"}'::jsonb,
    'creative',
    'creative.luna.platform',
    'professional',
    20,
    100,
    '{"advanced_analytics": true, "custom_branding": true, "api_access": false}'::jsonb,
    'b2b059b1-4e51-4749-903e-a16fa23630e7',
    'active',
    '{"timezone": "America/New_York", "working_hours": "10-18", "training_budget": 25000}'::jsonb,
    NOW() - INTERVAL '4 months',
    NOW()
  ),
  (
    '*************-3333-3333-************',
    'HealthPlus Medical Center',
    'healthplus-medical',
    'Comprehensive healthcare facility providing medical services, patient care, and healthcare professional training programs.',
    'Healthcare',
    'large',
    'https://api.dicebear.com/7.x/initials/svg?seed=HealthPlus',
    'https://healthplus.com',
    '{"primary_color": "#059669", "secondary_color": "#047857", "logo_style": "medical"}'::jsonb,
    'healthplus',
    'healthplus.luna.platform',
    'enterprise',
    30,
    300,
    '{"advanced_analytics": true, "custom_branding": true, "api_access": true}'::jsonb,
    'b2b059b1-4e51-4749-903e-a16fa23630e7',
    'active',
    '{"timezone": "America/Denver", "working_hours": "24/7", "training_budget": 75000}'::jsonb,
    NOW() - INTERVAL '8 months',
    NOW()
  );

-- =============================================================================
-- STEP 4: INSERT DEPARTMENTS
-- =============================================================================

INSERT INTO departments (
  id, organization_id, name, description, department_head_id,
  created_at, updated_at
) VALUES
  -- TechCorp Solutions departments
  (
    'aaaaaaaa-1111-1111-1111-111111111111',
    '11111111-1111-1111-1111-111111111111',
    'Engineering',
    'Software development and technical innovation team responsible for building enterprise applications and maintaining technical infrastructure.',
    'd3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f', -- John Smith as department head
    NOW() - INTERVAL '6 months',
    NOW()
  ),
  (
    'aaaaaaaa-1111-1111-1111-111111111112',
    '11111111-1111-1111-1111-111111111111',
    'Product Design',
    'User experience and product design team focused on creating intuitive and engaging digital experiences.',
    'f220b2b0-05bd-4986-b883-9ab51123e520', -- Mike Chen as department head
    NOW() - INTERVAL '6 months',
    NOW()
  ),
  (
    'aaaaaaaa-1111-1111-1111-111111111113',
    '11111111-1111-1111-1111-111111111111',
    'Human Resources',
    'Talent acquisition, employee development, and organizational culture management.',
    'afd8cd82-0291-46a1-b205-f800b30baefb', -- Jennifer Martinez as department head
    NOW() - INTERVAL '6 months',
    NOW()
  ),
  (
    'aaaaaaaa-1111-1111-1111-111111111114',
    '11111111-1111-1111-1111-111111111111',
    'Customer Support',
    'Technical support and customer service operations ensuring client satisfaction and issue resolution.',
    '47028720-f0d6-4e65-a7fe-e5cb09a4d0b9', -- Carlos Gonzalez as department head
    NOW() - INTERVAL '6 months',
    NOW()
  ),

  -- Creative Agency Inc departments
  (
    'bbbbbbbb-**************-************',
    '2222**************-2222-************',
    'Creative Design',
    'Brand development, visual design, and creative content production for client campaigns.',
    NULL, -- No department head assigned yet
    NOW() - INTERVAL '4 months',
    NOW()
  ),
  (
    'bbbbbbbb-**************-************',
    '2222**************-2222-************',
    'Digital Marketing',
    'Digital marketing strategy, social media management, and online campaign execution.',
    'e4a901a6-e9f1-4fbf-a392-e4ae7cd6fd73', -- Maria Garcia as department head
    NOW() - INTERVAL '4 months',
    NOW()
  ),
  (
    'bbbbbbbb-**************-222222222223',
    '2222**************-2222-************',
    'Data Analytics',
    'Business intelligence, data analysis, and performance metrics for marketing campaigns.',
    '19028181-f4d1-4872-9235-2d5f771a1bae', -- Alex Rodriguez as department head
    NOW() - INTERVAL '4 months',
    NOW()
  ),

  -- HealthPlus Medical Center departments
  (
    'cccccccc-**************-************',
    '*************-3333-3333-************',
    'Patient Care',
    'Direct patient care services, nursing staff, and medical support operations.',
    '8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e', -- Emma Davis as department head
    NOW() - INTERVAL '8 months',
    NOW()
  ),
  (
    'cccccccc-**************-************',
    '*************-3333-3333-************',
    'Medical Training',
    'Healthcare professional training, continuing education, and skills development programs.',
    'a9387548-0f8e-4992-8a7e-8a7e8a7e8a7e', -- David Franco as department head
    NOW() - INTERVAL '8 months',
    NOW()
  ),
  (
    'cccccccc-**************-************',
    '*************-3333-3333-************',
    'Administration',
    'Hospital administration, operations management, and administrative support services.',
    NULL, -- No department head assigned yet
    NOW() - INTERVAL '8 months',
    NOW()
  );

-- =============================================================================
-- STEP 5: INSERT EMPLOYMENT RELATIONSHIPS
-- =============================================================================

INSERT INTO employment_relationships (
  id, user_id, organization_id, department_id, role, job_title,
  start_date, salary_range, employment_type, work_location,
  status, created_at, updated_at
) VALUES
  -- TechCorp Solutions employees
  (
    'emp-11111111-1111-1111-1111-111111111111',
    'f220b2b0-05bd-4986-b883-9ab51123e520', -- Mike Chen
    '11111111-1111-1111-1111-111111111111', -- TechCorp
    'aaaaaaaa-1111-1111-1111-111111111112', -- Product Design
    'department_admin',
    'Senior UX Designer',
    '2020-03-15',
    '{"min": 95000, "max": 110000, "currency": "USD"}'::jsonb,
    'full_time',
    'hybrid',
    'active',
    NOW() - INTERVAL '4 years 4 months',
    NOW()
  ),
  (
    'emp-11111111-1111-1111-1111-111111111112',
    'd3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f', -- John Smith
    '11111111-1111-1111-1111-111111111111', -- TechCorp
    'aaaaaaaa-1111-1111-1111-111111111111', -- Engineering
    'department_admin',
    'Senior Full-Stack Developer',
    '2018-06-01',
    '{"min": 120000, "max": 135000, "currency": "USD"}'::jsonb,
    'full_time',
    'remote',
    'active',
    NOW() - INTERVAL '6 years 1 month',
    NOW()
  ),
  (
    'emp-11111111-1111-1111-1111-111111111113',
    '47028720-f0d6-4e65-a7fe-e5cb09a4d0b9', -- Carlos Gonzalez
    '11111111-1111-1111-1111-111111111111', -- TechCorp
    'aaaaaaaa-1111-1111-1111-111111111114', -- Customer Support
    'department_admin',
    'Technical Support Manager',
    '2018-09-10',
    '{"min": 65000, "max": 75000, "currency": "USD"}'::jsonb,
    'full_time',
    'on_site',
    'active',
    NOW() - INTERVAL '5 years 10 months',
    NOW()
  ),
  (
    'emp-11111111-1111-1111-1111-111111111114',
    'afd8cd82-0291-46a1-b205-f800b30baefb', -- Jennifer Martinez
    '11111111-1111-1111-1111-111111111111', -- TechCorp
    'aaaaaaaa-1111-1111-1111-111111111113', -- Human Resources
    'organization_admin',
    'HR Director',
    '2017-01-15',
    '{"min": 95000, "max": 115000, "currency": "USD"}'::jsonb,
    'full_time',
    'hybrid',
    'active',
    NOW() - INTERVAL '7 years 6 months',
    NOW()
  ),

  -- Creative Agency Inc employees
  (
    'emp-2222**************-2222-************',
    '19028181-f4d1-4872-9235-2d5f771a1bae', -- Alex Rodriguez
    '2222**************-2222-************', -- Creative Agency
    'bbbbbbbb-**************-222222222223', -- Data Analytics
    'department_admin',
    'Senior Data Analyst',
    '2021-02-01',
    '{"min": 85000, "max": 100000, "currency": "USD"}'::jsonb,
    'full_time',
    'remote',
    'active',
    NOW() - INTERVAL '3 years 5 months',
    NOW()
  ),
  (
    'emp-2222**************-2222-************',
    'e4a901a6-e9f1-4fbf-a392-e4ae7cd6fd73', -- Maria Garcia
    '2222**************-2222-************', -- Creative Agency
    'bbbbbbbb-**************-************', -- Digital Marketing
    'department_admin',
    'Digital Marketing Manager',
    '2019-08-15',
    '{"min": 70000, "max": 85000, "currency": "USD"}'::jsonb,
    'contract',
    'remote',
    'active',
    NOW() - INTERVAL '4 years 11 months',
    NOW()
  ),

  -- HealthPlus Medical Center employees
  (
    'emp-*************-**************33333331',
    '8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e', -- Emma Davis
    '*************-3333-3333-************', -- HealthPlus
    'cccccccc-**************-************', -- Patient Care
    'department_admin',
    'Nurse Manager',
    '2016-05-01',
    '{"min": 75000, "max": 90000, "currency": "USD"}'::jsonb,
    'full_time',
    'on_site',
    'active',
    NOW() - INTERVAL '8 years 2 months',
    NOW()
  ),
  (
    'emp-*************-3333-3333-************',
    'a9387548-0f8e-4992-8a7e-8a7e8a7e8a7e', -- David Franco
    '*************-3333-3333-************', -- HealthPlus
    'cccccccc-**************-************', -- Medical Training
    'department_admin',
    'Training Director',
    '2015-03-01',
    '{"min": 80000, "max": 95000, "currency": "USD"}'::jsonb,
    'full_time',
    'hybrid',
    'active',
    NOW() - INTERVAL '9 years 4 months',
    NOW()
  );

-- =============================================================================
-- STEP 6: INSERT USER CONTEXTS (Dual-Context System)
-- =============================================================================

INSERT INTO user_contexts (
  user_id, context_type, active_organization_id, active_department_id, active_employment_id,
  created_at, updated_at
) VALUES
  -- Individual contexts (default for all users)
  ('f220b2b0-05bd-4986-b883-9ab51123e520', 'individual', NULL, NULL, NULL, NOW(), NOW()),
  ('19028181-f4d1-4872-9235-2d5f771a1bae', 'individual', NULL, NULL, NULL, NOW(), NOW()),
  ('47028720-f0d6-4e65-a7fe-e5cb09a4d0b9', 'individual', NULL, NULL, NULL, NOW(), NOW()),
  ('8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e', 'individual', NULL, NULL, NULL, NOW(), NOW()),
  ('a9387548-0f8e-4992-8a7e-8a7e8a7e8a7e', 'individual', NULL, NULL, NULL, NOW(), NOW()),
  ('afd8cd82-0291-46a1-b205-f800b30baefb', 'individual', NULL, NULL, NULL, NOW(), NOW()),
  ('d3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f', 'individual', NULL, NULL, NULL, NOW(), NOW()),
  ('e4a901a6-e9f1-4fbf-a392-e4ae7cd6fd73', 'individual', NULL, NULL, NULL, NOW(), NOW()),

  -- Employment contexts (for users with employment relationships)
  ('f220b2b0-05bd-4986-b883-9ab51123e520', 'employment', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-1111-1111-1111-111111111112', 'emp-11111111-1111-1111-1111-111111111111', NOW(), NOW()),
  ('19028181-f4d1-4872-9235-2d5f771a1bae', 'employment', '2222**************-2222-************', 'bbbbbbbb-**************-222222222223', 'emp-2222**************-2222-************', NOW(), NOW()),
  ('47028720-f0d6-4e65-a7fe-e5cb09a4d0b9', 'employment', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-1111-1111-1111-111111111114', 'emp-11111111-1111-1111-1111-111111111113', NOW(), NOW()),
  ('8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e', 'employment', '*************-3333-3333-************', 'cccccccc-**************-************', 'emp-*************-**************33333331', NOW(), NOW()),
  ('a9387548-0f8e-4992-8a7e-8a7e8a7e8a7e', 'employment', '*************-3333-3333-************', 'cccccccc-**************-************', 'emp-*************-3333-3333-************', NOW(), NOW()),
  ('afd8cd82-0291-46a1-b205-f800b30baefb', 'employment', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-1111-1111-1111-111111111113', 'emp-11111111-1111-1111-1111-111111111114', NOW(), NOW()),
  ('d3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f', 'employment', '11111111-1111-1111-1111-111111111111', 'aaaaaaaa-1111-1111-1111-111111111111', 'emp-11111111-1111-1111-1111-111111111112', NOW(), NOW()),
  ('e4a901a6-e9f1-4fbf-a392-e4ae7cd6fd73', 'employment', '2222**************-2222-************', 'bbbbbbbb-**************-************', 'emp-2222**************-2222-************', NOW(), NOW());

-- =============================================================================
-- STEP 7: INSERT EMPLOYMENT INVITATIONS (Sample Pending Invitations)
-- =============================================================================

INSERT INTO employment_invitations (
  id, email, organization_id, department_id, role, job_title,
  invitation_token, invited_by, invitation_message,
  expires_at, status, created_at, updated_at
) VALUES
  (
    gen_random_uuid(),
    '<EMAIL>',
    '11111111-1111-1111-1111-111111111111', -- TechCorp
    'aaaaaaaa-1111-1111-1111-111111111111', -- Engineering
    'staff_member',
    'Junior Software Developer',
    encode(gen_random_bytes(32), 'hex'),
    'd3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f', -- Invited by John Smith
    'We would like to invite you to join our Engineering team at TechCorp Solutions. This is an exciting opportunity to work on cutting-edge enterprise applications.',
    NOW() + INTERVAL '7 days',
    'pending',
    NOW() - INTERVAL '2 days',
    NOW()
  ),
  (
    gen_random_uuid(),
    '<EMAIL>',
    '2222**************-2222-************', -- Creative Agency
    'bbbbbbbb-**************-************', -- Creative Design
    'staff_member',
    'UI/UX Designer',
    encode(gen_random_bytes(32), 'hex'),
    'e4a901a6-e9f1-4fbf-a392-e4ae7cd6fd73', -- Invited by Maria Garcia
    'Join our creative team and help us design amazing user experiences for our clients.',
    NOW() + INTERVAL '5 days',
    'pending',
    NOW() - INTERVAL '1 day',
    NOW()
  ),
  (
    gen_random_uuid(),
    '<EMAIL>',
    '*************-3333-3333-************', -- HealthPlus
    'cccccccc-**************-************', -- Patient Care
    'staff_member',
    'Registered Nurse',
    encode(gen_random_bytes(32), 'hex'),
    '8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e', -- Invited by Emma Davis
    'We are looking for a dedicated nurse to join our patient care team. Excellent benefits and professional development opportunities.',
    NOW() + INTERVAL '10 days',
    'pending',
    NOW() - INTERVAL '3 hours',
    NOW()
  );

-- =============================================================================
-- STEP 8: INSERT USER TRAINING DATA (Context-Aware Training Records)
-- =============================================================================

INSERT INTO user_training_data (
  id, user_id, context_type, organization_id, department_id, employment_id,
  training_context, training_status, completion_percentage, total_time_spent,
  modules_completed, lessons_completed, skills_before, skills_after,
  created_at, updated_at
) VALUES
  -- Individual context training data
  (
    gen_random_uuid(),
    'f220b2b0-05bd-4986-b883-9ab51123e520', -- Mike Chen
    'individual',
    NULL, NULL, NULL,
    'individual',
    'in_progress',
    75.0,
    1080, -- 18 hours in minutes
    '["Design Systems Fundamentals", "Component Architecture"]'::jsonb,
    '["Introduction to Design Systems", "Component Libraries", "Design Tokens"]'::jsonb,
    '{"UI/UX Design": 70, "Figma": 80, "Prototyping": 65}'::jsonb,
    '{"UI/UX Design": 85, "Figma": 90, "Prototyping": 80}'::jsonb,
    NOW() - INTERVAL '2 weeks',
    NOW()
  ),
  (
    gen_random_uuid(),
    '19028181-f4d1-4872-9235-2d5f771a1bae', -- Alex Rodriguez
    'individual',
    NULL, NULL, NULL,
    'individual',
    'in_progress',
    60.0,
    1440, -- 24 hours in minutes
    '["Machine Learning Fundamentals", "Deep Learning Basics"]'::jsonb,
    '["Introduction to ML", "Neural Networks", "Python for Data Science"]'::jsonb,
    '{"Python": 85, "Statistics": 80, "Machine Learning": 60}'::jsonb,
    '{"Python": 95, "Statistics": 92, "Machine Learning": 78}'::jsonb,
    NOW() - INTERVAL '1 month',
    NOW()
  ),

  -- Employment context training data
  (
    gen_random_uuid(),
    'f220b2b0-05bd-4986-b883-9ab51123e520', -- Mike Chen at TechCorp
    'team',
    '11111111-1111-1111-1111-111111111111', -- TechCorp
    'aaaaaaaa-1111-1111-1111-111111111112', -- Product Design
    'emp-11111111-1111-1111-1111-111111111111',
    'organization',
    'completed',
    100.0,
    480, -- 8 hours in minutes
    '["Company Standards", "Brand Guidelines", "Design Process"]'::jsonb,
    '["TechCorp Design Standards", "Brand Compliance", "Team Collaboration"]'::jsonb,
    '{"Brand Guidelines": 80, "Design Process": 75, "Team Collaboration": 70}'::jsonb,
    '{"Brand Guidelines": 95, "Design Process": 90, "Team Collaboration": 88}'::jsonb,
    NOW() - INTERVAL '3 months',
    NOW()
  ),
  (
    gen_random_uuid(),
    '8599e4cd-f4ff-4f70-b2d4-af3fe6d9fd9e', -- Emma Davis at HealthPlus
    'team',
    '*************-3333-3333-************', -- HealthPlus
    'cccccccc-**************-************', -- Patient Care
    'emp-*************-**************33333331',
    'organization',
    'completed',
    100.0,
    240, -- 4 hours in minutes
    '["HIPAA Compliance", "Patient Privacy", "Data Security"]'::jsonb,
    '["HIPAA Update", "Privacy Protocols", "Security Training"]'::jsonb,
    '{"HIPAA Knowledge": 85, "Privacy Protocols": 80, "Data Security": 75}'::jsonb,
    '{"HIPAA Knowledge": 98, "Privacy Protocols": 95, "Data Security": 90}'::jsonb,
    NOW() - INTERVAL '2 months',
    NOW()
  ),
  (
    gen_random_uuid(),
    'a9387548-0f8e-4992-8a7e-8a7e8a7e8a7e', -- David Franco at HealthPlus
    'team',
    '*************-3333-3333-************', -- HealthPlus
    'cccccccc-**************-************', -- Medical Training
    'emp-*************-3333-3333-************',
    'organization',
    'in_progress',
    85.0,
    840, -- 14 hours in minutes
    '["Simulation Training", "Scenario Design", "Assessment Methods"]'::jsonb,
    '["Advanced Simulation", "High-Fidelity Scenarios", "Assessment Creation"]'::jsonb,
    '{"Simulation Design": 80, "Assessment Creation": 75, "Facilitation": 78}'::jsonb,
    '{"Simulation Design": 92, "Assessment Creation": 88, "Facilitation": 90}'::jsonb,
    NOW() - INTERVAL '3 weeks',
    NOW()
  ),
  (
    gen_random_uuid(),
    'd3d4fd7f-38cb-4da6-bf5e-ba5f5d9a9f7f', -- John Smith at TechCorp
    'team',
    '11111111-1111-1111-1111-111111111111', -- TechCorp
    'aaaaaaaa-1111-1111-1111-111111111111', -- Engineering
    'emp-11111111-1111-1111-1111-111111111112',
    'organization',
    'in_progress',
    90.0,
    1740, -- 29 hours in minutes
    '["Cloud Architecture", "Microservices", "DevOps", "AWS"]'::jsonb,
    '["Cloud Patterns", "Microservices Architecture", "DevOps Practices"]'::jsonb,
    '{"Cloud Architecture": 85, "Microservices": 80, "DevOps": 75, "AWS": 82}'::jsonb,
    '{"Cloud Architecture": 95, "Microservices": 88, "DevOps": 85, "AWS": 92}'::jsonb,
    NOW() - INTERVAL '1 month',
    NOW()
  );

-- =============================================================================
-- DATA VERIFICATION QUERIES
-- =============================================================================

-- Verify the data has been inserted correctly
SELECT 'Users created:' as info, COUNT(*) as count FROM users;
SELECT 'Individuals created:' as info, COUNT(*) as count FROM individuals;
SELECT 'Organizations created:' as info, COUNT(*) as count FROM organizations;
SELECT 'Departments created:' as info, COUNT(*) as count FROM departments;
SELECT 'Employment relationships created:' as info, COUNT(*) as count FROM employment_relationships;
SELECT 'Employment invitations created:' as info, COUNT(*) as count FROM employment_invitations;
SELECT 'User contexts created:' as info, COUNT(*) as count FROM user_contexts;
SELECT 'User training data records created:' as info, COUNT(*) as count FROM user_training_data;

-- Show sample data from each table
SELECT 'Sample Users:' as info;
SELECT email, full_name, role, status FROM users LIMIT 5;

SELECT 'Sample Organizations:' as info;
SELECT name, industry, size_range, subscription_tier FROM organizations;

SELECT 'Sample Departments:' as info;
SELECT d.name as department_name, o.name as organization_name, u.full_name as department_head
FROM departments d
JOIN organizations o ON d.organization_id = o.id
LEFT JOIN users u ON d.department_head_id = u.id
LIMIT 5;

SELECT 'Sample Employment Relationships:' as info;
SELECT u.full_name, o.name as organization, d.name as department, er.role, er.job_title
FROM employment_relationships er
JOIN users u ON er.user_id = u.id
JOIN organizations o ON er.organization_id = o.id
JOIN departments d ON er.department_id = d.id
LIMIT 5;

-- =============================================================================
-- SEEDING COMPLETE
-- =============================================================================

-- Summary message
SELECT '🎉 Luna Database Seeding Complete! 🎉' as message,
       'All tables have been populated with comprehensive sample data.' as details,
       'The employment-based dual-context architecture is now ready for testing.' as status;

-- Show organization admins
SELECT 
  u.email,
  u.full_name,
  o.name as organization,
  d.name as department,
  er.role,
  er.job_title
FROM users u
JOIN employment_relationships er ON u.id = er.user_id
JOIN organizations o ON er.organization_id = o.id
JOIN departments d ON er.department_id = d.id
WHERE er.role = 'organization_admin'
ORDER BY o.name;
