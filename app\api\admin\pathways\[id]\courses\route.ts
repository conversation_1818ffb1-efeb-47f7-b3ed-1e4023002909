import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
import { createAdminClient } from '@/lib/supabase-admin';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id: pathwayId } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Get pathway courses
    const { data: pathwayCourses, error: coursesError } = await adminClient
      .from('pathway_courses')
      .select(`
        *,
        course:courses(
          id,
          name,
          description,
          slug,
          level,
          estimated_duration,
          status,
          cover_image_url,
          learning_objectives,
          tags,
          instructor:users!courses_instructor_id_fkey(
            id,
            full_name,
            email
          )
        )
      `)
      .eq('pathway_id', pathwayId)
      .order('sequence_order', { ascending: true });

    if (coursesError) {
      console.error('Error fetching pathway courses:', coursesError);
      return NextResponse.json(
        { error: coursesError.message || 'Failed to fetch pathway courses' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      pathway_courses: pathwayCourses || []
    });

  } catch (error: any) {
    console.error('Pathway courses GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id: pathwayId } = await params;

    // Parse request body
    const body = await req.json();
    const {
      course_id,
      sequence_order,
      is_required,
      unlock_conditions
    } = body;

    // Validate required fields
    if (!course_id || sequence_order === undefined) {
      return NextResponse.json(
        { error: 'Course ID and sequence order are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Check if course is already in this pathway
    const { data: existingLink } = await adminClient
      .from('pathway_courses')
      .select('id')
      .eq('pathway_id', pathwayId)
      .eq('course_id', course_id)
      .single();

    if (existingLink) {
      return NextResponse.json(
        { error: 'Course is already assigned to this pathway' },
        { status: 400 }
      );
    }

    // Check if sequence order is already taken
    const { data: existingOrder } = await adminClient
      .from('pathway_courses')
      .select('id')
      .eq('pathway_id', pathwayId)
      .eq('sequence_order', sequence_order)
      .single();

    if (existingOrder) {
      return NextResponse.json(
        { error: 'Sequence order is already taken. Please choose a different order.' },
        { status: 400 }
      );
    }

    // Verify course exists
    const { data: course, error: courseError } = await adminClient
      .from('courses')
      .select('id, name')
      .eq('id', course_id)
      .single();

    if (courseError || !course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    // Add course to pathway
    const { data: pathwayCourse, error: linkError } = await adminClient
      .from('pathway_courses')
      .insert({
        pathway_id: pathwayId,
        course_id,
        sequence_order,
        is_required: is_required !== false, // Default to true
        unlock_conditions: unlock_conditions || {}
      })
      .select(`
        *,
        course:courses(
          id,
          name,
          description,
          level,
          estimated_duration,
          status
        )
      `)
      .single();

    if (linkError) {
      console.error('Error linking course to pathway:', linkError);
      return NextResponse.json(
        { error: linkError.message || 'Failed to add course to pathway' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Course added to pathway successfully',
      pathway_course: pathwayCourse
    });

  } catch (error: any) {
    console.error('Add course to pathway API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id: pathwayId } = await params;

    // Parse request body - expecting array of course updates
    const body = await req.json();
    const { courses } = body;

    if (!Array.isArray(courses)) {
      return NextResponse.json(
        { error: 'Courses array is required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Update courses in batch
    const updates = [];
    for (const courseUpdate of courses) {
      const { id, sequence_order, is_required, unlock_conditions } = courseUpdate;
      
      if (!id || sequence_order === undefined) {
        continue;
      }

      const { data, error } = await adminClient
        .from('pathway_courses')
        .update({
          sequence_order,
          is_required: is_required !== false,
          unlock_conditions: unlock_conditions || {}
        })
        .eq('id', id)
        .eq('pathway_id', pathwayId)
        .select();

      if (error) {
        console.error('Error updating pathway course:', error);
        return NextResponse.json(
          { error: `Failed to update course: ${error.message}` },
          { status: 500 }
        );
      }

      updates.push(data);
    }

    return NextResponse.json({
      success: true,
      message: 'Pathway courses updated successfully',
      updated_courses: updates.flat()
    });

  } catch (error: any) {
    console.error('Update pathway courses API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id: pathwayId } = await params;

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const courseId = searchParams.get('course_id');

    if (!courseId) {
      return NextResponse.json(
        { error: 'Course ID is required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Remove course from pathway
    const { error: deleteError } = await adminClient
      .from('pathway_courses')
      .delete()
      .eq('pathway_id', pathwayId)
      .eq('course_id', courseId);

    if (deleteError) {
      console.error('Error removing course from pathway:', deleteError);
      return NextResponse.json(
        { error: deleteError.message || 'Failed to remove course from pathway' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Course removed from pathway successfully'
    });

  } catch (error: any) {
    console.error('Remove course from pathway API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
