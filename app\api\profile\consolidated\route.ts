/**
 * Consolidated Profile Management API
 * Handles all profile updates: personal info, skills, experience, education
 * Replaces multiple scattered profile endpoints
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser, createAuthErrorResponse } from '@/lib/auth';
import { createApiClient, createAdminClient } from '@/lib/supabase-server';
import { withApiErrorHandler, createApiSuccessResponse } from '@/lib/api-error-handler';
import { withProfileSecurity } from '@/lib/security/enhanced-middleware';
import { ValidationSchemas } from '@/lib/security/validation-schemas';

/**
 * GET /api/profile/consolidated
 * Get complete user profile
 */
export const GET = withProfileSecurity(
  async (req: NextRequest, user: any) => {

    const supabase = await createApiClient();

    // Get prospect profile
    const { data: prospectData, error: prospectError } = await supabase
      .from('prospects')
      .select(`
        id,
        full_name,
        email,
        phone,
        location,
        bio,
        skills,
        experience,
        education,
        certifications,
        portfolio_url,
        linkedin_url,
        github_url,
        website_url,
        resume_url,
        profile_image,
        intro_video_url,
        availability_status,
        hourly_rate,
        preferred_work_type,
        years_of_experience,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .single();

    if (prospectError && prospectError.code !== 'PGRST116') {
      throw new Error(`Failed to fetch profile: ${prospectError.message}`);
    }

    return createApiSuccessResponse({
      profile: prospectData || null,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    });
  }
);

/**
 * PUT /api/profile/consolidated
 * Update user profile (consolidated endpoint)
 */
export const PUT = withProfileSecurity(
  async (req: NextRequest, user: any) => {
    const body = await req.json();
    const { section, data } = body;

    if (!section || !data) {
      throw new Error('Section and data are required');
    }

    // Validate input based on section
    let validatedData;
    switch (section) {
      case 'personal_info':
        validatedData = ValidationSchemas.Profile.personalInfo.parse(data);
        break;
      case 'skills':
        validatedData = ValidationSchemas.Profile.skills.parse(data);
        break;
      case 'experience':
        validatedData = ValidationSchemas.Profile.experience.parse(data);
        break;
      case 'education':
        validatedData = ValidationSchemas.Profile.education.parse(data);
        break;
      case 'preferences':
        validatedData = ValidationSchemas.Profile.preferences.parse(data);
        break;
      default:
        throw new Error(`Invalid section: ${section}`);
    }

    console.log('Profile Update API - Request:', { section, userId: user.id });

    const supabase = await createApiClient();

    // Get existing prospect data
    const { data: prospectData, error: prospectError } = await supabase
      .from('prospects')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (prospectError && prospectError.code !== 'PGRST116') {
      throw new Error(`Failed to fetch prospect: ${prospectError.message}`);
    }

    let updateData: any = {};
    let result: any;

    // Handle different profile sections
    switch (section) {
      case 'personal_info':
        updateData = {
          full_name: data.full_name,
          phone: data.phone,
          location: data.location,
          bio: data.bio,
          portfolio_url: data.portfolio_url,
          linkedin_url: data.linkedin_url,
          github_url: data.github_url,
          website_url: data.website_url
        };
        break;

      case 'skills':
        if (data.action === 'add') {
          const currentSkills = prospectData?.skills || [];
          updateData.skills = [...currentSkills, data.skill];
        } else if (data.action === 'remove') {
          const currentSkills = prospectData?.skills || [];
          updateData.skills = currentSkills.filter((_: any, index: number) => index !== data.skillIndex);
        } else if (data.action === 'update') {
          const currentSkills = prospectData?.skills || [];
          currentSkills[data.skillIndex] = data.skill;
          updateData.skills = currentSkills;
        } else {
          updateData.skills = data.skills;
        }
        break;

      case 'experience':
        if (data.action === 'add') {
          const currentExperience = prospectData?.experience || [];
          updateData.experience = [...currentExperience, data.experience];
        } else if (data.action === 'remove') {
          const currentExperience = prospectData?.experience || [];
          updateData.experience = currentExperience.filter((_: any, index: number) => index !== data.experienceIndex);
        } else if (data.action === 'update') {
          const currentExperience = prospectData?.experience || [];
          currentExperience[data.experienceIndex] = data.experience;
          updateData.experience = currentExperience;
        } else {
          updateData.experience = data.experience;
        }
        break;

      case 'education':
        if (data.action === 'add') {
          const currentEducation = prospectData?.education || [];
          updateData.education = [...currentEducation, data.education];
        } else if (data.action === 'remove') {
          const currentEducation = prospectData?.education || [];
          updateData.education = currentEducation.filter((_: any, index: number) => index !== data.educationIndex);
        } else if (data.action === 'update') {
          const currentEducation = prospectData?.education || [];
          currentEducation[data.educationIndex] = data.education;
          updateData.education = currentEducation;
        } else {
          updateData.education = data.education;
        }
        break;

      case 'preferences':
        updateData = {
          availability_status: data.availability_status,
          hourly_rate: data.hourly_rate,
          preferred_work_type: data.preferred_work_type,
          years_of_experience: data.years_of_experience
        };
        break;

      case 'visibility':
        updateData = {
          profile_visibility: data.profile_visibility || 'public'
        };
        break;

      default:
        throw new Error(`Invalid section: ${section}`);
    }

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    if (Object.keys(updateData).length === 0) {
      throw new Error('No valid data to update');
    }

    // Update or create prospect record
    if (prospectData) {
      // Update existing prospect
      const { data: updatedData, error: updateError } = await supabase
        .from('prospects')
        .update(updateData)
        .eq('id', prospectData.id)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Update failed: ${updateError.message}`);
      }

      result = updatedData;
    } else {
      // Create new prospect record
      const newProspectData = {
        user_id: user.id,
        email: user.email,
        full_name: user.full_name || data.full_name,
        ...updateData
      };

      const { data: createdData, error: createError } = await supabase
        .from('prospects')
        .insert(newProspectData)
        .select()
        .single();

      if (createError) {
        throw new Error(`Create failed: ${createError.message}`);
      }

      result = createdData;
    }

    console.log('✅ Profile updated successfully:', { section, prospectId: result.id });

    return createApiSuccessResponse({
      message: `${section.replace('_', ' ')} updated successfully`,
      profile: result,
      section,
      updatedFields: Object.keys(updateData)
    });
  }
);

/**
 * POST /api/profile/consolidated/upload
 * Handle profile file uploads (avatar, resume, etc.)
 */
export async function POST(req: NextRequest) {
  return withApiErrorHandler(async () => {
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    // Redirect to consolidated upload endpoint
    const url = new URL(req.url);
    const uploadUrl = `${url.origin}/api/upload/consolidated`;
    
    return NextResponse.redirect(uploadUrl, 307); // Temporary redirect preserving method
  }, 'POST /api/profile/consolidated/upload');
}
