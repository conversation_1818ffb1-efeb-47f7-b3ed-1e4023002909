'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Phone, 
  Star, 
  TrendingUp, 
  Clock, 
  Target,
  Award,
  CheckCircle,
  AlertTriangle,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Volume2,
  Users,
  Lightbulb
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface DetailedAssessment {
  category: string
  score: number
  maxScore: number
  status: 'excellent' | 'good' | 'needs-work' | 'poor'
  description: string
  strengths: string[]
  improvements: string[]
}

interface DetailedPerformanceAssessmentProps {
  variant?: 'full' | 'summary'
  className?: string
}

export function DetailedPerformanceAssessment({ 
  variant = 'full',
  className 
}: DetailedPerformanceAssessmentProps) {
  
  // Comprehensive assessment data synced with dashboard KPIs
  // IMPORTANT: These values must match dashboard trainingStats:
  // - Dashboard callPracticeScore = 78 (averageScore)
  // - Dashboard callPracticeHours = 4.2 (totalHours)
  const overallMetrics = {
    totalCalls: 24,
    averageScore: 78, // Dashboard shows 78 (out of 100)
    totalHours: 4.2, // Dashboard shows 4.2 hours
    strongestSkill: 'Script Adherence',
    improvementArea: 'Active Listening',
    readinessLevel: 'Intermediate'
  }

  const detailedAssessments: DetailedAssessment[] = [
    {
      category: 'Call Opening & Introduction',
      score: 85,
      maxScore: 100,
      status: 'excellent',
      description: 'Strong professional opening with good rapport building.',
      strengths: [
        'Clear voice projection and professional greeting',
        'Effective name usage and tone setting'
      ],
      improvements: [
        'Add more personalization',
        'Avoid rushing company introduction'
      ]
    },
    {
      category: 'Product Knowledge & Presentation',
      score: 88,
      maxScore: 100,
      status: 'excellent',
      description: 'Excellent product understanding with benefit-focused explanations.',
      strengths: [
        'Comprehensive knowledge and relevant examples',
        'Adapts presentation to prospect needs'
      ],
      improvements: [
        'Improve technical explanations',
        'Avoid over-explaining simple concepts'
      ]
    },
    {
      category: 'Active Listening & Engagement',
      score: 65,
      maxScore: 100,
      status: 'needs-work',
      description: 'Basic listening skills need improvement for better prospect engagement.',
      strengths: [
        'Acknowledges responses and asks follow-ups'
      ],
      improvements: [
        'Pause longer for responses',
        'Ask more probing questions',
        'Improve reading verbal cues'
      ]
    },
    {
      category: 'Objection Handling',
      score: 75,
      maxScore: 100,
      status: 'good',
      description: 'Handles common objections well with room for improvement on challenging prospects.',
      strengths: [
        'Calm under pressure with empathetic responses'
      ],
      improvements: [
        'Practice price objections',
        'Be more assertive with difficult prospects'
      ]
    },
    {
      category: 'Communication Clarity',
      score: 72,
      maxScore: 100,
      status: 'good',
      description: 'Clear communication with occasional jargon usage.',
      strengths: [
        'Good vocabulary and business language'
      ],
      improvements: [
        'Reduce jargon and slow down pace'
      ]
    },
    {
      category: 'Call Closing & Next Steps',
      score: 68,
      maxScore: 100,
      status: 'needs-work',
      description: 'Needs improvement in confident closing and securing commitments.',
      strengths: [
        'Good summaries and follow-up attempts'
      ],
      improvements: [
        'Be more assertive with commitments',
        'Create urgency and handle closing objections'
      ]
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
      case 'good':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
      case 'needs-work':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
      case 'poor':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'good':
        return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'needs-work':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'poor':
        return <AlertTriangle className="h-4 w-4 text-red-600" />
      default:
        return null
    }
  }

  if (variant === 'summary') {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Call Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">{overallMetrics.averageScore}/100</p>
            <p className="text-xs text-muted-foreground">Average Score</p>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Readiness Level</span>
              <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
                {overallMetrics.readinessLevel}
              </Badge>
            </div>
            <div className="text-xs text-muted-foreground">
              <p><strong>Strongest:</strong> {overallMetrics.strongestSkill}</p>
              <p><strong>Focus Area:</strong> {overallMetrics.improvementArea}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Overall Assessment Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Call Performance Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <Star className="h-6 w-6 mx-auto mb-2 text-yellow-600" />
              <p className="text-2xl font-bold">{overallMetrics.averageScore}/100</p>
              <p className="text-xs text-muted-foreground">Average Score</p>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <Phone className="h-6 w-6 mx-auto mb-2 text-green-600" />
              <p className="text-2xl font-bold">{overallMetrics.totalCalls}</p>
              <p className="text-xs text-muted-foreground">Total Calls</p>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <Clock className="h-6 w-6 mx-auto mb-2 text-purple-600" />
              <p className="text-2xl font-bold">{overallMetrics.totalHours}h</p>
              <p className="text-xs text-muted-foreground">Practice Time</p>
            </div>
          </div>
          
          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Lightbulb className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900 dark:text-blue-100">Performance Summary</span>
            </div>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>{overallMetrics.readinessLevel}</strong> level call handling skills.
              Strongest: <strong>{overallMetrics.strongestSkill}</strong> • Focus area: <strong>{overallMetrics.improvementArea}</strong>
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Skill Assessments */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Detailed Skill Assessment</h3>
        {detailedAssessments.map((assessment, index) => (
          <Card key={index}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base flex items-center gap-2">
                  {getStatusIcon(assessment.status)}
                  {assessment.category}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{assessment.score}/{assessment.maxScore}</span>
                  <Badge className={getStatusColor(assessment.status)}>
                    {assessment.status === 'excellent' ? 'Excellent' :
                     assessment.status === 'good' ? 'Good' :
                     assessment.status === 'needs-work' ? 'Needs Work' : 'Poor'}
                  </Badge>
                </div>
              </div>
              <Progress value={(assessment.score / assessment.maxScore) * 100} className="h-2" />
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">{assessment.description}</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <ThumbsUp className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-sm">Strengths</span>
                  </div>
                  <ul className="text-sm space-y-1">
                    {assessment.strengths.map((strength, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <div className="w-1 h-1 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                        {strength}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-orange-600" />
                    <span className="font-medium text-sm">Areas for Improvement</span>
                  </div>
                  <ul className="text-sm space-y-1">
                    {assessment.improvements.map((improvement, idx) => (
                      <li key={idx} className="flex items-start gap-2">
                        <div className="w-1 h-1 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                        {improvement}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
