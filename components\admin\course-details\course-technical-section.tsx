"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Save, Monitor, HardDrive, Globe, Accessibility } from 'lucide-react'
import { toast } from "sonner"

interface Course {
  id: string
  name: string
  description: string
  slug: string
  level: string
  estimated_duration: number
  status: string
  price: number
  cover_image_url?: string
  preview_video_url?: string
  meta_description?: string
  tags: string[]
  learning_objectives: string[]
  target_audience?: string
  instructor_id?: string
  instructor_bio?: string
  enrollment_count: number
  completion_rate: number
  average_rating: number
  course_complexity: string
  certification_available: boolean
  is_standalone: boolean
  required_software: string[]
  hardware_requirements?: string
  language: string
  accessibility_features: string[]
  prerequisite_courses: string[]
  created_at: string
  updated_at: string
}

interface CourseTechnicalSectionProps {
  course: Course
  onUpdate: () => void
}

export function CourseTechnicalSection({ course, onUpdate }: CourseTechnicalSectionProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    required_software: course.required_software.join(', '),
    hardware_requirements: course.hardware_requirements || '',
    language: course.language,
    accessibility_features: course.accessibility_features.join(', ')
  })

  const handleSave = async () => {
    try {
      setLoading(true)

      const payload = {
        required_software: formData.required_software 
          ? formData.required_software.split(',').map(sw => sw.trim()).filter(sw => sw.length > 0)
          : [],
        hardware_requirements: formData.hardware_requirements?.trim() || null,
        language: formData.language,
        accessibility_features: formData.accessibility_features 
          ? formData.accessibility_features.split(',').map(feat => feat.trim()).filter(feat => feat.length > 0)
          : []
      }

      const response = await fetch(`/api/admin/courses/${course.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update technical requirements')
      }

      toast.success('Technical requirements updated successfully')
      onUpdate()
      
    } catch (error: any) {
      console.error('Error updating technical requirements:', error)
      toast.error(error.message || 'Failed to update technical requirements')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <Button
          size="sm"
          onClick={handleSave}
          disabled={loading}
        >
          <Save className="h-4 w-4 mr-2" />
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
        <div className="space-y-4">
          {/* Required Software */}
          <div className="space-y-2">
            <Label htmlFor="required_software" className="flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              Required Software
            </Label>
            <Textarea
              id="required_software"
              value={formData.required_software}
              onChange={(e) => setFormData({ ...formData, required_software: e.target.value })}
              placeholder="Enter software requirements separated by commas:&#10;Visual Studio Code, Node.js 18+, Git, Chrome Browser"
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              List software, tools, or applications students need. Separate multiple items with commas.
            </p>
          </div>

          {/* Hardware Requirements */}
          <div className="space-y-2">
            <Label htmlFor="hardware_requirements" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              Hardware Requirements
            </Label>
            <Textarea
              id="hardware_requirements"
              value={formData.hardware_requirements}
              onChange={(e) => setFormData({ ...formData, hardware_requirements: e.target.value })}
              placeholder="Describe minimum hardware specifications:&#10;• 8GB RAM minimum, 16GB recommended&#10;• 10GB free disk space&#10;• Webcam and microphone for interactive sessions"
              rows={4}
            />
            <p className="text-sm text-muted-foreground">
              Specify minimum system requirements and any special hardware needs.
            </p>
          </div>

          {/* Language */}
          <div className="space-y-2">
            <Label htmlFor="language" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Course Language
            </Label>
            <Select value={formData.language} onValueChange={(value) => setFormData({ ...formData, language: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="it">Italian</SelectItem>
                <SelectItem value="pt">Portuguese</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
                <SelectItem value="ja">Japanese</SelectItem>
                <SelectItem value="ko">Korean</SelectItem>
                <SelectItem value="ar">Arabic</SelectItem>
                <SelectItem value="hi">Hindi</SelectItem>
                <SelectItem value="ru">Russian</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              Primary language for course content, videos, and materials.
            </p>
          </div>

          {/* Accessibility Features */}
          <div className="space-y-2">
            <Label htmlFor="accessibility_features" className="flex items-center gap-2">
              <Accessibility className="h-4 w-4" />
              Accessibility Features
            </Label>
            <Textarea
              id="accessibility_features"
              value={formData.accessibility_features}
              onChange={(e) => setFormData({ ...formData, accessibility_features: e.target.value })}
              placeholder="List accessibility features separated by commas:&#10;Closed captions, Screen reader compatible, High contrast mode, Keyboard navigation"
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              Describe features that make the course accessible to students with disabilities.
            </p>
          </div>
        </div>
    </div>
  )
}
