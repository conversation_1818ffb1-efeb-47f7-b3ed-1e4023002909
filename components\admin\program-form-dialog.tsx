"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { toast } from "sonner"
import { Loader2 } from "lucide-react"

const programSchema = z.object({
  name: z.string().min(1, "Program name is required"),
  description: z.string().min(1, "Description is required"),
  industry: z.string().min(1, "Industry is required"),
  cover_image_url: z.string().optional(),
  status: z.enum(['active', 'inactive', 'archived']).default('active'),
  sort_order: z.string().optional(),
})

type ProgramFormData = z.infer<typeof programSchema>

interface Program {
  id: string
  name: string
  description: string
  industry: string
  cover_image_url?: string
  status: 'active' | 'inactive' | 'archived'
  sort_order: number
}

interface ProgramFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  program?: Program | null
  onSuccess: () => void
}

export function ProgramFormDialog({
  open,
  onOpenChange,
  program,
  onSuccess
}: ProgramFormDialogProps) {
  const [loading, setLoading] = useState(false)
  const isEditing = !!program

  const form = useForm<ProgramFormData>({
    resolver: zodResolver(programSchema),
    defaultValues: {
      name: '',
      description: '',
      industry: '',
      cover_image_url: '',
      status: 'active',
      sort_order: '0',
    }
  })

  // Reset form when program changes
  useEffect(() => {
    if (program) {
      form.reset({
        name: program.name,
        description: program.description,
        industry: program.industry,
        cover_image_url: program.cover_image_url || '',
        status: program.status,
        sort_order: program.sort_order.toString(),
      })
    } else {
      form.reset({
        name: '',
        description: '',
        industry: '',
        cover_image_url: '',
        status: 'active',
        sort_order: '0',
      })
    }
  }, [program, form])

  // Cleanup when dialog closes
  useEffect(() => {
    if (!open) {
      setLoading(false)
    }
  }, [open])

  const onSubmit = async (data: ProgramFormData) => {
    setLoading(true)
    try {
      const url = isEditing 
        ? `/api/admin/programs/${program.id}`
        : '/api/admin/programs'
      
      const method = isEditing ? 'PUT' : 'POST'
      
      const payload = {
        name: data.name.trim(),
        description: data.description.trim(),
        industry: data.industry.trim(),
        cover_image_url: data.cover_image_url?.trim() || null,
        status: data.status,
        sort_order: data.sort_order ? parseInt(data.sort_order) : 0,
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save program')
      }

      toast.success(
        isEditing 
          ? 'Program updated successfully' 
          : 'Program created successfully'
      )
      
      onOpenChange(false)
      form.reset()
      
      setTimeout(() => {
        onSuccess()
      }, 100)
      
    } catch (error: any) {
      console.error('Error saving program:', error)
      toast.error(error.message || 'Failed to save program')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Program' : 'Create New Program'}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? 'Update the program information below.'
              : 'Create a new learning program. Programs contain multiple pathways.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Program Name *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="e.g., Information Technology Program" 
                        {...field} 
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select industry" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Technology">Technology</SelectItem>
                        <SelectItem value="Healthcare">Healthcare</SelectItem>
                        <SelectItem value="Finance">Finance</SelectItem>
                        <SelectItem value="Business Services">Business Services</SelectItem>
                        <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                        <SelectItem value="Education">Education</SelectItem>
                        <SelectItem value="Retail">Retail</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description *</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe what this program covers and its objectives..."
                      className="min-h-[100px]"
                      {...field} 
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={loading}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sort_order"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sort Order</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="0"
                        {...field} 
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="cover_image_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Cover Image URL</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="https://example.com/image.jpg"
                      {...field} 
                      disabled={loading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? 'Update Program' : 'Create Program'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
