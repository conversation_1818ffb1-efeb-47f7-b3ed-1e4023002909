import { NextRequest, NextResponse } from 'next/server';
import { createApiClient } from '@/lib/supabase-server';

/**
 * GET /api/org/[orgSlug]/departments/[departmentSlug]
 * Get department by slug
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orgSlug: string; departmentSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug, departmentSlug } = params;

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Get department by slug and organization
    const { data: department, error: deptError } = await supabase
      .from('departments')
      .select('id, name, description, slug, department_head_id, created_at, updated_at')
      .eq('organization_id', organization.id)
      .eq('slug', departmentSlug)
      .single();

    if (deptError || !department) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(department);

  } catch (error) {
    console.error('Error in department API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/org/[orgSlug]/departments/[departmentSlug]
 * Update a department
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { orgSlug: string; departmentSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug, departmentSlug } = params;
    const body = await request.json();
    const { name, description, department_head_id, staff_members } = body;

    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'Department name is required' },
        { status: 400 }
      );
    }

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Check if department exists and belongs to this organization
    const { data: existingDept, error: checkError } = await supabase
      .from('departments')
      .select('id, organization_id, slug')
      .eq('slug', departmentSlug)
      .eq('organization_id', organization.id)
      .single();

    if (checkError || !existingDept) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Check for duplicate department name within the organization
    const { data: duplicateDept, error: duplicateError } = await supabase
      .from('departments')
      .select('id')
      .eq('organization_id', organization.id)
      .eq('name', name.trim())
      .neq('id', existingDept.id)
      .single();

    if (duplicateError && duplicateError.code !== 'PGRST116') {
      console.error('Error checking for duplicate department:', duplicateError);
      return NextResponse.json(
        { error: 'Failed to validate department name' },
        { status: 500 }
      );
    }

    if (duplicateDept) {
      return NextResponse.json(
        { error: 'A department with this name already exists in your organization' },
        { status: 409 }
      );
    }

    // Update the department
    const { data: updatedDept, error: updateError } = await supabase
      .from('departments')
      .update({
        name: name.trim(),
        description: description?.trim() || null,
        department_head_id: department_head_id || null,
        updated_at: new Date().toISOString(),
      })
      .eq('id', existingDept.id)
      .eq('organization_id', organization.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating department:', updateError);
      return NextResponse.json(
        { error: 'Failed to update department' },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedDept);
  } catch (error) {
    console.error('Error in PUT department API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/org/[orgSlug]/departments/[departmentSlug]
 * Delete a department
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { orgSlug: string; departmentSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug, departmentSlug } = params;

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Check if department exists and belongs to this organization
    const { data: existingDept, error: checkError } = await supabase
      .from('departments')
      .select('id, name, organization_id, slug')
      .eq('slug', departmentSlug)
      .eq('organization_id', organization.id)
      .single();

    if (checkError || !existingDept) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Check if department has employees
    const { data: employees, error: empError } = await supabase
      .from('employment_relationships')
      .select('id')
      .eq('department_id', existingDept.id)
      .limit(1);

    if (empError) {
      console.error('Error checking department employees:', empError);
      return NextResponse.json(
        { error: 'Failed to validate department deletion' },
        { status: 500 }
      );
    }

    if (employees && employees.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete department with active employees. Please reassign employees first.' },
        { status: 409 }
      );
    }

    // Delete the department
    const { error: deleteError } = await supabase
      .from('departments')
      .delete()
      .eq('id', existingDept.id)
      .eq('organization_id', organization.id);

    if (deleteError) {
      console.error('Error deleting department:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete department' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Department deleted successfully' });
  } catch (error) {
    console.error('Error in DELETE department API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
