-- Luna Dual-Context Architecture Migration
-- Implement individual + employment relationship system

-- Step 1: Update user_role enum for routing
-- First, check if platform_admin already exists
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'platform_admin' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')) THEN
    ALTER TYPE user_role ADD VALUE 'platform_admin';
  END IF;
END $$;

-- Step 2: Ensure employment_role enum exists with correct values
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'employment_role') THEN
    CREATE TYPE employment_role AS ENUM (
      'organization_admin',
      'department_admin', 
      'staff_member'
    );
  END IF;
END $$;

-- Step 3: Ensure employment_status enum exists
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'employment_status') THEN
    CREATE TYPE employment_status AS ENUM (
      'invited',
      'active',
      'on_leave',
      'terminated',
      'resigned'
    );
  END IF;
END $$;

-- Step 4: Clean up old tables completely
DROP TABLE IF EXISTS team_memberships CASCADE;
DROP TABLE IF EXISTS teams CASCADE;
DROP TYPE IF EXISTS team_member_role CASCADE;

-- Step 5: Make organizations.created_by nullable for seeding
ALTER TABLE organizations ALTER COLUMN created_by DROP NOT NULL;

-- Step 6: Create departments table
CREATE TABLE IF NOT EXISTS departments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  department_head_id UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT departments_org_name_unique UNIQUE(organization_id, name)
);

-- Step 7: Create employment_relationships table (supports multi-employment)
CREATE TABLE IF NOT EXISTS employment_relationships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  department_id UUID NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
  role employment_role NOT NULL DEFAULT 'staff_member',
  status employment_status NOT NULL DEFAULT 'invited',
  job_title TEXT,
  hire_date DATE,
  termination_date DATE,
  invited_by UUID REFERENCES users(id),
  invited_at TIMESTAMP WITH TIME ZONE,
  joined_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Allow multiple employments per user (different organizations)
  CONSTRAINT employment_user_org_unique UNIQUE(user_id, organization_id),
  CONSTRAINT employment_termination_check CHECK (termination_date IS NULL OR termination_date >= hire_date)
);

-- Step 8: Create employment_invitations table
CREATE TABLE IF NOT EXISTS employment_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  department_id UUID NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
  role employment_role NOT NULL DEFAULT 'staff_member',
  job_title TEXT,
  invited_by UUID NOT NULL REFERENCES users(id),
  invitation_token TEXT UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
  accepted_at TIMESTAMP WITH TIME ZONE,
  declined_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT invitation_email_org_unique UNIQUE(email, organization_id),
  CONSTRAINT invitation_expires_check CHECK (expires_at > created_at)
);

-- Step 9: Update user_contexts for dual-context system
-- Remove old team columns and add employment context support
DO $$
BEGIN
  -- Remove old team-related columns
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'active_team_id') THEN
    ALTER TABLE user_contexts DROP COLUMN active_team_id;
  END IF;
  
  -- Add employment context columns
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'active_employment_id') THEN
    ALTER TABLE user_contexts ADD COLUMN active_employment_id UUID REFERENCES employment_relationships(id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'active_department_id') THEN
    ALTER TABLE user_contexts ADD COLUMN active_department_id UUID REFERENCES departments(id);
  END IF;
  
  -- Add context switching metadata
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'last_org_context') THEN
    ALTER TABLE user_contexts ADD COLUMN last_org_context UUID REFERENCES organizations(id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_contexts' AND column_name = 'context_preferences') THEN
    ALTER TABLE user_contexts ADD COLUMN context_preferences JSONB DEFAULT '{}';
  END IF;
END $$;

-- Step 10: Update user_training_data for employment context
DO $$
BEGIN
  -- Remove old team columns
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_training_data' AND column_name = 'team_id') THEN
    ALTER TABLE user_training_data DROP COLUMN team_id;
  END IF;
  
  -- Add employment context columns
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_training_data' AND column_name = 'employment_id') THEN
    ALTER TABLE user_training_data ADD COLUMN employment_id UUID REFERENCES employment_relationships(id);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_training_data' AND column_name = 'department_id') THEN
    ALTER TABLE user_training_data ADD COLUMN department_id UUID REFERENCES departments(id);
  END IF;
  
  -- Add context tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'user_training_data' AND column_name = 'training_context') THEN
    ALTER TABLE user_training_data ADD COLUMN training_context TEXT DEFAULT 'individual' CHECK (training_context IN ('individual', 'organization'));
  END IF;
END $$;

-- Step 11: Set all existing users to 'individual' role (except platform admin)
UPDATE users SET role = 'individual' WHERE role != 'platform_admin';

-- Step 12: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_departments_organization_id ON departments(organization_id);
CREATE INDEX IF NOT EXISTS idx_departments_head ON departments(department_head_id);
CREATE INDEX IF NOT EXISTS idx_employment_user_id ON employment_relationships(user_id);
CREATE INDEX IF NOT EXISTS idx_employment_organization_id ON employment_relationships(organization_id);
CREATE INDEX IF NOT EXISTS idx_employment_department_id ON employment_relationships(department_id);
CREATE INDEX IF NOT EXISTS idx_employment_status ON employment_relationships(status);
CREATE INDEX IF NOT EXISTS idx_employment_user_status ON employment_relationships(user_id, status);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON employment_invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON employment_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_invitations_expires ON employment_invitations(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_contexts_employment ON user_contexts(active_employment_id);
CREATE INDEX IF NOT EXISTS idx_training_data_employment ON user_training_data(employment_id);
CREATE INDEX IF NOT EXISTS idx_training_data_context ON user_training_data(training_context);

-- Step 13: Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_departments_updated_at ON departments;
CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON departments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employment_relationships_updated_at ON employment_relationships;
CREATE TRIGGER update_employment_relationships_updated_at BEFORE UPDATE ON employment_relationships
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 14: Create dual-context management functions

-- Function to invite user to employment (supports multi-employment)
CREATE OR REPLACE FUNCTION invite_to_employment(
  p_email TEXT,
  p_organization_id UUID,
  p_department_id UUID,
  p_role employment_role DEFAULT 'staff_member',
  p_job_title TEXT DEFAULT NULL,
  p_invited_by UUID DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invitation_id UUID;
BEGIN
  -- Check if user is already employed by this organization
  IF EXISTS (
    SELECT 1 FROM employment_relationships er
    JOIN users u ON er.user_id = u.id
    WHERE u.email = p_email AND er.organization_id = p_organization_id
    AND er.status IN ('active', 'invited')
  ) THEN
    RAISE EXCEPTION 'User is already employed or invited by this organization';
  END IF;

  -- Create invitation
  INSERT INTO employment_invitations (
    email, organization_id, department_id, role, job_title, invited_by
  ) VALUES (
    p_email, p_organization_id, p_department_id, p_role, p_job_title, p_invited_by
  ) RETURNING id INTO v_invitation_id;

  RETURN v_invitation_id;
END;
$$;

-- Function to accept employment invitation (preserves individual identity)
CREATE OR REPLACE FUNCTION accept_employment_invitation(
  p_invitation_token TEXT,
  p_user_id UUID
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_invitation employment_invitations%ROWTYPE;
  v_employment_id UUID;
BEGIN
  -- Get invitation details
  SELECT * INTO v_invitation
  FROM employment_invitations
  WHERE invitation_token = p_invitation_token
  AND expires_at > NOW()
  AND accepted_at IS NULL
  AND declined_at IS NULL;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation token';
  END IF;

  -- Verify email matches user
  IF NOT EXISTS (SELECT 1 FROM users WHERE id = p_user_id AND email = v_invitation.email) THEN
    RAISE EXCEPTION 'User email does not match invitation';
  END IF;

  -- Create employment relationship
  INSERT INTO employment_relationships (
    user_id, organization_id, department_id, role, status,
    job_title, invited_by, invited_at, joined_at, hire_date
  ) VALUES (
    p_user_id, v_invitation.organization_id, v_invitation.department_id,
    v_invitation.role, 'active', v_invitation.job_title,
    v_invitation.invited_by, v_invitation.created_at, NOW(), CURRENT_DATE
  ) RETURNING id INTO v_employment_id;

  -- Mark invitation as accepted
  UPDATE employment_invitations
  SET accepted_at = NOW()
  WHERE id = v_invitation.id;

  -- Update user context to remember this employment (but keep individual as primary)
  INSERT INTO user_contexts (
    user_id, context_type, active_organization_id,
    active_department_id, active_employment_id, last_org_context
  ) VALUES (
    p_user_id, 'individual', NULL, NULL, NULL, v_invitation.organization_id
  ) ON CONFLICT (user_id) DO UPDATE SET
    last_org_context = v_invitation.organization_id;

  RETURN v_employment_id;
END;
$$;

-- Function to switch to organization context
CREATE OR REPLACE FUNCTION switch_to_organization_context(
  p_user_id UUID,
  p_employment_id UUID
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_employment employment_relationships%ROWTYPE;
BEGIN
  -- Verify employment belongs to user and is active
  SELECT * INTO v_employment
  FROM employment_relationships
  WHERE id = p_employment_id
  AND user_id = p_user_id
  AND status = 'active';

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or inactive employment relationship';
  END IF;

  -- Update user context to organization mode
  UPDATE user_contexts SET
    context_type = 'organization',
    active_organization_id = v_employment.organization_id,
    active_department_id = v_employment.department_id,
    active_employment_id = v_employment.id,
    last_org_context = v_employment.organization_id
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$;

-- Function to switch back to individual context
CREATE OR REPLACE FUNCTION switch_to_individual_context(
  p_user_id UUID
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update user context to individual mode (preserve last org for quick switching)
  UPDATE user_contexts SET
    context_type = 'individual',
    active_organization_id = NULL,
    active_department_id = NULL,
    active_employment_id = NULL
    -- Keep last_org_context for quick switching
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$;

-- Function to get user's employment summary (for dashboard stats)
CREATE OR REPLACE FUNCTION get_user_employment_summary(
  p_user_id UUID
) RETURNS TABLE (
  total_employments INTEGER,
  active_employments INTEGER,
  organizations JSONB,
  current_context TEXT,
  last_org_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*)::INTEGER as total_employments,
    COUNT(CASE WHEN er.status = 'active' THEN 1 END)::INTEGER as active_employments,
    COALESCE(
      jsonb_agg(
        jsonb_build_object(
          'org_id', o.id,
          'org_name', o.name,
          'department', d.name,
          'role', er.role,
          'status', er.status
        )
      ) FILTER (WHERE er.status = 'active'),
      '[]'::jsonb
    ) as organizations,
    uc.context_type as current_context,
    uc.last_org_context as last_org_id
  FROM employment_relationships er
  JOIN organizations o ON er.organization_id = o.id
  JOIN departments d ON er.department_id = d.id
  LEFT JOIN user_contexts uc ON uc.user_id = er.user_id
  WHERE er.user_id = p_user_id
  GROUP BY uc.context_type, uc.last_org_context;
END;
$$;
