'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function DashboardPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      try {
        const response = await fetch('/api/auth/user');
        const data = await response.json();

        if (!data.success || !data.user) {
          router.push('/login');
          return;
        }

        const userData = data.user;

        // Route to appropriate dashboard based on user role
        if (userData.isPlatformAdmin) {
          router.push('/admin');
        } else if (userData.isOrgOwner || userData.isOrgAdmin || userData.role === 'org_member') {
          router.push('/organization');
        } else if (userData.role === 'individual') {
          router.push('/individual');
        }
        // If no specific role match, stay on main dashboard

      } catch (error) {
        console.error('Error checking auth:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    checkAuthAndRedirect();
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Luna Platform Dashboard
          </h1>

          <div className="bg-blue-50 p-4 rounded-md">
            <p className="text-sm text-blue-700">
              Welcome to Luna Platform! Authentication system will be implemented here.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
