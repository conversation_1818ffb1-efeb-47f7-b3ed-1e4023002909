'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { 
  Calendar, 
  Clock, 
  Search, 
  MoreVertical, 
  Video, 
  User, 
  Edit, 
  CheckCircle2, 
  XCircle,
  FileText,
  MessageSquare,
  Trash2
} from 'lucide-react';

export default function OrganizationInterviewsPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [interviews, setInterviews] = useState<any[]>([]);
  const [user, setUser] = useState<any>(null);
  const [organizationData, setOrganizationData] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [interviewStats, setInterviewStats] = useState({
    total: 0,
    upcoming: 0,
    completed: 0,
    cancelled: 0,
    noShow: 0
  });

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        
        // Get the current user
        const { data: { session } } = await supabase.auth.getSession();
        if (!session) return;
        
        setUser(session.user);
        
        // Get the Organization this user belongs to
        const { data: teamMember, error: teamMemberError } = await supabase
          .from('organization_memberships')
          .select('organization_id, role')
          .eq('user_id', session.user.id)
          .single();

        if (teamMemberError || !teamMember) {
          console.error('Error fetching team member:', teamMemberError);
          return;
        }

        // Get Organization data
        const { data: organization, error: organizationError } = await supabase
          .from('organizations')
          .select('*')
          .eq('id', teamMember.organization_id)
          .single();

        if (organizationError || !organization) {
          console.error('Error fetching Organization data:', organizationError);
          return;
        }

        setOrganizationData(organization);
        
        try {
          // First, let's try a simple query to see what interviews exist
          console.log('Fetching interviews for Organization:', organization.id);

          const { data: interviewData, error: interviewError } = await supabase
            .from('interviews')
            .select('*');

          if (interviewError) {
            console.error('Error fetching interviews:', interviewError);
            throw interviewError;
          }

          console.log('Raw interview data:', interviewData);

          // If no interviews, return empty array
          if (!interviewData || interviewData.length === 0) {
            console.log('No interviews found');
            setInterviews([]);
            setInterviewStats({
              total: 0,
              upcoming: 0,
              completed: 0,
              cancelled: 0,
              noShow: 0
            });
            return;
          }

          // Now let's try to get additional data for each interview
          const enrichedInterviews = [];

          for (const interview of interviewData) {
            try {
              let candidateName = 'Unknown Candidate';
              let candidateEmail = 'No email';
              let position = 'Unknown Position';
              let shouldInclude = true;

              console.log('Processing interview:', interview.id, 'with application_id:', interview.application_id);

              // Check if this interview belongs to the current Organization
              if (interview.organization_user_id) {
                const { data: interviewerTeam, error: teamError } = await supabase
                  .from('organization_memberships')
                  .select('organization_id')
                  .eq('user_id', interview.organization_user_id)
                  .single();

                if (!teamError && interviewerTeam && interviewerTeam.organization_id !== organization.id) {
                  console.log('Interview does not belong to current Organization, skipping');
                  shouldInclude = false;
                }
              }

              // Get application data to fetch candidate and position info
              if (shouldInclude && interview.application_id) {
                console.log('Fetching application data for:', interview.application_id);

                const { data: applicationData, error: appError } = await supabase
                  .from('applications')
                  .select(`
                    id,
                    prospect_id,
                    job_id,
                    prospects!inner(
                      id,
                      user_id,
                      contact_info
                    ),
                    job_postings!inner(
                      id,
                      title,
                      organization_id
                    )
                  `)
                  .eq('id', interview.application_id)
                  .single();

                // Also try to get the user's full name
                let userFullName = null;
                if (!appError && applicationData?.prospects?.user_id) {
                  const { data: userData, error: userError } = await supabase
                    .from('users')
                    .select('full_name, email')
                    .eq('id', applicationData.prospects.user_id)
                    .single();

                  if (!userError && userData) {
                    userFullName = userData.full_name;
                    if (!candidateEmail || candidateEmail === 'No email') {
                      candidateEmail = userData.email || 'No email';
                    }
                  }
                }

                if (!appError && applicationData) {
                  console.log('Application data found:', applicationData);

                  // Extract candidate name - prefer full name, fallback to email
                  const contactInfo = applicationData.prospects?.contact_info;
                  candidateEmail = contactInfo?.email || candidateEmail;
                  candidateName = userFullName || contactInfo?.email || candidateEmail || 'Unknown Candidate';

                  // Get position from job posting
                  position = applicationData.job_postings?.title || 'Unknown Position';

                  // Double-check Organization ownership
                  if (applicationData.job_postings?.organization_id !== organization.id) {
                    console.log('Application does not belong to current Organization, skipping');
                    shouldInclude = false;
                  }

                  console.log('Final candidate info:', { candidateName, candidateEmail, position });
                } else {
                  console.log('Error fetching application data:', appError);
                }
              }

              if (shouldInclude) {
                console.log('Adding interview with candidate:', candidateName);
                enrichedInterviews.push({
                  ...interview,
                  candidateName,
                  candidateEmail,
                  position
                });
              }

            } catch (enrichError) {
              console.error('Error enriching interview:', interview.id, enrichError);
              // Include the interview anyway with default values
              enrichedInterviews.push({
                ...interview,
                candidateName: 'Unknown Candidate',
                candidateEmail: 'No email',
                position: 'Unknown Position'
              });
            }
          }

          console.log('Enriched interviews:', enrichedInterviews);

          // Process interview data with actual candidate information
          const processedInterviews = enrichedInterviews.map(interview => {
            const now = new Date();
            const interviewDate = new Date(interview.scheduled_at);

            // Determine if the interview is upcoming (in the future)
            const isUpcoming = interviewDate > now;

            // Determine if this interview is conducted by the current user
            const isInterviewer = interview.organization_user_id === session.user.id;

            return {
              ...interview,
              isUpcoming,
              isInterviewer,
              formattedDate: interviewDate.toLocaleDateString(),
              formattedTime: interviewDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            };
          });
          
          setInterviews(processedInterviews);
          
          // Calculate statistics
          const stats = {
            total: processedInterviews.length,
            upcoming: processedInterviews.filter(i => i.isUpcoming && i.status === 'scheduled').length,
            completed: processedInterviews.filter(i => i.status === 'completed').length,
            cancelled: processedInterviews.filter(i => i.status === 'cancelled').length,
            noShow: processedInterviews.filter(i => i.status === 'no_show').length
          };
          
          setInterviewStats(stats);
        } catch (interviewError) {
          console.error('Error processing interviews:', interviewError);
          console.error('Interview error details:', {
            message: interviewError?.message,
            code: interviewError?.code,
            details: interviewError?.details,
            hint: interviewError?.hint
          });
          setInterviews([]);
          setInterviewStats({
            total: 0,
            upcoming: 0,
            completed: 0,
            cancelled: 0,
            noShow: 0
          });
        }
        
      } catch (error) {
        console.error('Error fetching data:', error);
        
        // Try to extract and log more detailed error information
        if (error instanceof Error) {
          console.error('Error message:', error.message);
          console.error('Error stack:', error.stack);
        }
        
        // Set empty state for graceful degradation
        setInterviews([]);
        setInterviewStats({
          total: 0,
          upcoming: 0,
          completed: 0,
          cancelled: 0,
          noShow: 0
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const handleScheduleNew = () => {
    router.push('/organization/interviews/schedule');
  };

  const handleJoinMeeting = (meetingLink: string) => {
    window.open(meetingLink, '_blank');
  };

  const handleEditInterview = (id: string) => {
    router.push(`/organization/interviews/edit/${id}`);
  };

  const handleUpdateStatus = async (id: string, newStatus: string) => {
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from('interviews')
        .update({ status: newStatus })
        .eq('id', id);
        
      if (error) {
        throw error;
      }
      
      // Update local state
      setInterviews(interviews.map(interview => {
        if (interview.id === id) {
          return { ...interview, status: newStatus };
        }
        return interview;
      }));
      
      // Update stats based on status changes
      const oldStats = { ...interviewStats };
      let newStats = { ...oldStats };
      
      const interview = interviews.find(i => i.id === id);
      if (interview) {
        const oldStatus = interview.status;
        
        // Decrement old status count
        if (oldStatus === 'scheduled' && interview.isUpcoming) {
          newStats.upcoming--;
        } else if (oldStatus === 'completed') {
          newStats.completed--;
        } else if (oldStatus === 'cancelled') {
          newStats.cancelled--;
        } else if (oldStatus === 'no_show') {
          newStats.noShow--;
        }
        
        // Increment new status count
        if (newStatus === 'scheduled' && interview.isUpcoming) {
          newStats.upcoming++;
        } else if (newStatus === 'completed') {
          newStats.completed++;
        } else if (newStatus === 'cancelled') {
          newStats.cancelled++;
        } else if (newStatus === 'no_show') {
          newStats.noShow++;
        }
      }
      
      setInterviewStats(newStats);
      
      toast({
        title: "Status updated",
        description: `Interview status has been updated to ${newStatus.replace('_', ' ')}`,
      });
      
    } catch (error) {
      console.error('Error updating status:', error);
      toast({
        title: "Update failed",
        description: "There was an error updating the interview status",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancelInterview = async (id: string) => {
    if (!window.confirm('Are you sure you want to cancel this interview?')) {
      return;
    }
    
    await handleUpdateStatus(id, 'cancelled');
  };

  const handleDeleteInterview = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this interview? This action cannot be undone.')) {
      return;
    }
    
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from('interviews')
        .delete()
        .eq('id', id);
        
      if (error) {
        throw error;
      }
      
      // Update local state
      const deletedInterview = interviews.find(i => i.id === id);
      setInterviews(interviews.filter(interview => interview.id !== id));
      
      // Update stats
      if (deletedInterview) {
        const newStats = { ...interviewStats };
        newStats.total--;
        
        if (deletedInterview.status === 'scheduled' && deletedInterview.isUpcoming) {
          newStats.upcoming--;
        } else if (deletedInterview.status === 'completed') {
          newStats.completed--;
        } else if (deletedInterview.status === 'cancelled') {
          newStats.cancelled--;
        } else if (deletedInterview.status === 'no_show') {
          newStats.noShow--;
        }
        
        setInterviewStats(newStats);
      }
      
      toast({
        title: "Interview deleted",
        description: "The interview has been deleted successfully",
      });
      
    } catch (error) {
      console.error('Error deleting interview:', error);
      toast({
        title: "Deletion failed",
        description: "There was an error deleting the interview",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getFilteredInterviews = () => {
    return interviews.filter(interview => {
      // Apply status filter
      if (statusFilter !== 'all') {
        if (statusFilter === 'upcoming' && (!interview.isUpcoming || interview.status !== 'scheduled')) {
          return false;
        } else if (statusFilter !== 'upcoming' && interview.status !== statusFilter) {
          return false;
        }
      }
      
      // Apply search query
      if (searchQuery) {
        const searchLower = searchQuery.toLowerCase();
        return (
          (interview.candidateName && interview.candidateName.toLowerCase().includes(searchLower)) ||
          (interview.candidateEmail && interview.candidateEmail.toLowerCase().includes(searchLower)) ||
          (interview.position && interview.position.toLowerCase().includes(searchLower))
        );
      }
      
      return true;
    });
  };

  const getStatusBadge = (status: string, isUpcoming: boolean) => {
    switch (status) {
      case 'scheduled':
        return isUpcoming ? (
          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
            Upcoming
          </Badge>
        ) : (
          <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300">
            Scheduled (Past)
          </Badge>
        );
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
            <XCircle className="h-3 w-3 mr-1" />
            Cancelled
          </Badge>
        );
      case 'no_show':
        return (
          <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
            No Show
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getInitials = (name: string) => {
    if (!name || name === 'Unknown Candidate') return 'UC';
    
    return name.split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  if (loading && interviews.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading interviews...</p>
        </div>
      </div>
    );
  }

  const filteredInterviews = getFilteredInterviews();

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Interviews</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Schedule and manage candidate interviews
          </p>
        </div>
        
        <Button onClick={handleScheduleNew}>
          <Calendar className="h-4 w-4 mr-2" />
          <span>Schedule Interview</span>
        </Button>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Interviews
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.total}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                <Clock className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Upcoming
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.upcoming}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <CheckCircle2 className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Completed
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.completed}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-red-500 rounded-md p-3">
                <XCircle className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Cancelled
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.cancelled}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-gray-500 rounded-md p-3">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    No Shows
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {interviewStats.noShow}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500 dark:text-gray-400" />
          <Input
            placeholder="Search interviews..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Button
            variant={statusFilter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('all')}
          >
            All
          </Button>
          <Button
            variant={statusFilter === 'upcoming' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('upcoming')}
          >
            Upcoming
          </Button>
          <Button
            variant={statusFilter === 'completed' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('completed')}
          >
            Completed
          </Button>
          <Button
            variant={statusFilter === 'cancelled' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('cancelled')}
          >
            Cancelled
          </Button>
          <Button
            variant={statusFilter === 'no_show' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setStatusFilter('no_show')}
          >
            No Show
          </Button>
        </div>
      </div>
      
      {/* Interviews Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Candidate</TableHead>
                <TableHead>Position</TableHead>
                <TableHead>Date & Time</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Location</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredInterviews.map((interview) => (
                <TableRow key={interview.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={`/avatars/${interview.applications?.prospects?.id}.png`} alt={interview.candidateName} />
                        <AvatarFallback>{getInitials(interview.candidateName)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{interview.candidateName}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{interview.candidateEmail}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{interview.position}</TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span className="font-medium">{interview.formattedDate}</span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">{interview.formattedTime}</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">{interview.duration_minutes} mins</span>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(interview.status, interview.isUpcoming)}</TableCell>
                  <TableCell>
                    {interview.location === 'virtual' ? (
                      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/30 dark:text-purple-400 dark:border-purple-800">
                        <Video className="h-3 w-3 mr-1" />
                        Virtual
                      </Badge>
                    ) : (
                      interview.location
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Manage Interview</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        
                        {interview.meeting_link && interview.status === 'scheduled' && interview.isUpcoming && (
                          <DropdownMenuItem onClick={() => handleJoinMeeting(interview.meeting_link)}>
                            <Video className="h-4 w-4 mr-2" />
                            <span>Join Meeting</span>
                          </DropdownMenuItem>
                        )}
                        
                        <DropdownMenuItem onClick={() => handleEditInterview(interview.id)}>
                          <Edit className="h-4 w-4 mr-2" />
                          <span>Edit Interview</span>
                        </DropdownMenuItem>
                        
                        {interview.status === 'scheduled' && interview.isUpcoming && (
                          <DropdownMenuItem 
                            className="text-red-600"
                            onClick={() => handleCancelInterview(interview.id)}
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            <span>Cancel Interview</span>
                          </DropdownMenuItem>
                        )}
                        
                        {interview.status === 'scheduled' && !interview.isUpcoming && (
                          <>
                            <DropdownMenuItem onClick={() => handleUpdateStatus(interview.id, 'completed')}>
                              <CheckCircle2 className="h-4 w-4 mr-2" />
                              <span>Mark as Completed</span>
                            </DropdownMenuItem>
                            
                            <DropdownMenuItem onClick={() => handleUpdateStatus(interview.id, 'no_show')}>
                              <User className="h-4 w-4 mr-2" />
                              <span>Mark as No Show</span>
                            </DropdownMenuItem>
                          </>
                        )}
                        
                        <DropdownMenuSeparator />
                        
                        <DropdownMenuItem 
                          className="text-red-600"
                          onClick={() => handleDeleteInterview(interview.id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          <span>Delete Interview</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
              
              {filteredInterviews.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center">
                      <Calendar className="h-8 w-8 mb-2 text-gray-400" />
                      <p>No interviews found</p>
                      <p className="text-sm mt-1">
                        {interviews.length > 0 
                          ? 'Try changing your filters or search query' 
                          : 'Schedule your first interview with a candidate'}
                      </p>
                      
                      {interviews.length === 0 && (
                        <Button 
                          variant="outline" 
                          className="mt-4"
                          onClick={handleScheduleNew}
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>Schedule Interview</span>
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
} 