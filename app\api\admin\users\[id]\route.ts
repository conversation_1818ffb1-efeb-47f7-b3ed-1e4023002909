import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Get user details
    const { data: user, error: userError } = await adminClient
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (userError) {
      console.error('Error fetching user:', userError);
      return NextResponse.json(
        { error: userError.message || 'Failed to fetch user' },
        { status: 500 }
      );
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error: any) {
    console.error('User GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Parse request body
    const body = await req.json();
    const { email, full_name, role, timezone, password } = body;

    // Validate required fields
    if (!email || !full_name || !role) {
      return NextResponse.json(
        { error: 'Email, full name, and role are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Update user in database
    const updateData: any = {
      email: email.trim(),
      full_name: full_name.trim(),
      role,
      timezone: timezone || 'UTC',
      updated_at: new Date().toISOString()
    };

    const { data: user, error: userError } = await adminClient
      .from('users')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (userError) {
      console.error('Error updating user:', userError);
      return NextResponse.json(
        { error: userError.message || 'Failed to update user' },
        { status: 500 }
      );
    }

    // Update password if provided
    if (password && password.length >= 8) {
      const { error: passwordError } = await adminClient.auth.admin.updateUserById(
        id,
        { password }
      );

      if (passwordError) {
        console.error('Error updating password:', passwordError);
        // Don't fail the request for password update errors, just log them
      }
    }

    // Update email in auth if it changed
    const { error: emailError } = await adminClient.auth.admin.updateUserById(
      id,
      {
        email: email.trim(),
        user_metadata: { full_name: full_name.trim() }
      }
    );

    if (emailError) {
      console.error('Error updating auth email:', emailError);
      // Don't fail the request for auth update errors, just log them
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error: any) {
    console.error('User PUT API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Parse request body
    const body = await req.json();
    const { status } = body;

    // Validate required fields
    if (!status || !['active', 'inactive', 'pending'].includes(status)) {
      return NextResponse.json(
        { error: 'Status is required and must be "active", "inactive", or "pending"' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // Update user status
    const { data: user, error: updateError } = await adminClient
      .from('users')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating user status:', updateError);
      return NextResponse.json(
        { error: updateError.message || 'Failed to update user status' },
        { status: 500 }
      );
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error: any) {
    console.error('User PATCH API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Delete user from auth (this will cascade to the users table via RLS)
    const { error: authError } = await adminClient.auth.admin.deleteUser(id);

    if (authError) {
      console.error('Error deleting user from auth:', authError);
      return NextResponse.json(
        { error: authError.message || 'Failed to delete user' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('User DELETE API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}