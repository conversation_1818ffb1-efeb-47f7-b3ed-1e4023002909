import { NextRequest, NextResponse } from 'next/server';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';
// import {
//   uploadCoursesMedia,
//   validateFile,
//   ALLOWED_FILE_TYPES,
//   MAX_FILE_SIZES,
//   STORAGE_FOLDERS
// } from '@/lib/storage/courses-media';

export async function POST(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return NextResponse.json({
      error: 'Media upload temporarily disabled during setup'
    }, { status: 503 });

    // Parse form data
    // const formData = await req.formData();
    // const file = formData.get('file') as File;
    // const contentType = formData.get('contentType') as keyof typeof STORAGE_FOLDERS;
    // const contentId = formData.get('contentId') as string;
    // const mediaType = formData.get('mediaType') as string;

    // Temporarily disabled
    /*
    // Validate required fields
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!contentType || !Object.keys(STORAGE_FOLDERS).includes(contentType)) {
      return NextResponse.json(
        { error: 'Invalid content type. Must be one of: ' + Object.keys(STORAGE_FOLDERS).join(', ') },
        { status: 400 }
      );
    }

    if (!contentId) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      );
    }

    if (!mediaType) {
      return NextResponse.json(
        { error: 'Media type is required' },
        { status: 400 }
      );
    }

    // Determine allowed file types and max size based on media type
    let allowedTypes: string[];
    let maxSize: number;

    switch (mediaType) {
      case 'cover-image':
      case 'image':
        allowedTypes = ALLOWED_FILE_TYPES.IMAGES;
        maxSize = MAX_FILE_SIZES.IMAGE;
        break;
      case 'preview-video':
      case 'lesson-video':
      case 'video':
        allowedTypes = ALLOWED_FILE_TYPES.VIDEOS;
        maxSize = MAX_FILE_SIZES.VIDEO;
        break;
      case 'slides':
      case 'presentation':
        allowedTypes = ALLOWED_FILE_TYPES.PRESENTATIONS;
        maxSize = MAX_FILE_SIZES.PRESENTATION;
        break;
      case 'resource':
      case 'document':
        allowedTypes = [...ALLOWED_FILE_TYPES.DOCUMENTS, ...ALLOWED_FILE_TYPES.PRESENTATIONS];
        maxSize = MAX_FILE_SIZES.DOCUMENT;
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid media type' },
          { status: 400 }
        );
    }

    // Validate file
    try {
      validateFile(file, allowedTypes, maxSize);
    } catch (validationError: any) {
      return NextResponse.json(
        { error: validationError.message },
        { status: 400 }
      );
    }

    // Generate filename with media type prefix
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    const fileName = `${mediaType}-${timestamp}.${fileExtension}`;

    // Upload file
    const uploadResult = await uploadCoursesMedia(
      file,
      contentType,
      contentId,
      fileName
    );

    return NextResponse.json({
      success: true,
      data: {
        url: uploadResult.path,
        publicUrl: uploadResult.publicUrl,
        fileName: uploadResult.fileName,
        fileSize: uploadResult.fileSize,
        mimeType: uploadResult.mimeType,
        contentType,
        contentId,
        mediaType,
        uploadedAt: new Date().toISOString()
      }
    });
  */

  } catch (error: any) {
    console.error('Media upload API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to upload file' },
      { status: 500 }
    );
  }
}

// Get upload configuration
export async function GET(req: NextRequest) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    return NextResponse.json({
      message: 'Media upload configuration temporarily disabled during setup'
    });

  } catch (error: any) {
    console.error('Media config API error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to get upload configuration' },
      { status: 500 }
    );
  }
}
