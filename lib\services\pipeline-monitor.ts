/**
 * Pipeline Monitoring Service
 * Tracks and monitors question generation pipeline performance
 */

import { createClient } from '@/lib/supabase-server';

export interface PipelineMetrics {
  totalPipelines: number;
  successfulPipelines: number;
  failedPipelines: number;
  averageGenerationTime: number;
  averageQualityScore: number;
  totalTokensUsed: number;
  averageQuestionsGenerated: number;
  successRate: number;
  commonFailureReasons: Array<{ reason: string; count: number }>;
  performanceByCategory: Record<string, {
    count: number;
    averageTime: number;
    averageQuality: number;
    successRate: number;
  }>;
}

export interface PipelineJob {
  id: string;
  userId: string;
  assessmentConfigId: string;
  sessionId?: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  duration?: number;
  questionsGenerated: number;
  qualityScore: number;
  tokensUsed: number;
  error?: string;
  metadata: Record<string, any>;
}

export class PipelineMonitor {
  private supabase = createClient();
  private static instance: PipelineMonitor;

  static getInstance(): PipelineMonitor {
    if (!PipelineMonitor.instance) {
      PipelineMonitor.instance = new PipelineMonitor();
    }
    return PipelineMonitor.instance;
  }

  /**
   * Log pipeline execution start
   */
  async logPipelineStart(
    pipelineId: string,
    userId: string,
    assessmentConfigId: string,
    sessionId?: string
  ): Promise<void> {
    try {
      await this.supabase
        .from('pipeline_jobs')
        .insert({
          id: pipelineId,
          user_id: userId,
          assessment_config_id: assessmentConfigId,
          session_id: sessionId,
          status: 'running',
          start_time: new Date().toISOString(),
          questions_generated: 0,
          quality_score: 0,
          tokens_used: 0,
          metadata: {}
        });
    } catch (error) {
      console.error('Failed to log pipeline start:', error);
    }
  }

  /**
   * Log pipeline execution completion
   */
  async logPipelineCompletion(
    pipelineId: string,
    result: {
      success: boolean;
      questionsGenerated: number;
      qualityScore: number;
      tokensUsed: number;
      generationTime: number;
      error?: string;
      metadata: Record<string, any>;
    }
  ): Promise<void> {
    try {
      await this.supabase
        .from('pipeline_jobs')
        .update({
          status: result.success ? 'completed' : 'failed',
          end_time: new Date().toISOString(),
          duration: result.generationTime,
          questions_generated: result.questionsGenerated,
          quality_score: result.qualityScore,
          tokens_used: result.tokensUsed,
          error: result.error,
          metadata: result.metadata
        })
        .eq('id', pipelineId);
    } catch (error) {
      console.error('Failed to log pipeline completion:', error);
    }
  }

  /**
   * Get pipeline metrics for dashboard
   */
  async getPipelineMetrics(timeRange: '1h' | '24h' | '7d' | '30d' = '24h'): Promise<PipelineMetrics> {
    try {
      const timeRangeHours = {
        '1h': 1,
        '24h': 24,
        '7d': 168,
        '30d': 720
      };

      const hoursAgo = timeRangeHours[timeRange];
      const startTime = new Date(Date.now() - hoursAgo * 60 * 60 * 1000).toISOString();

      // Get pipeline jobs within time range
      const { data: jobs, error } = await this.supabase
        .from('pipeline_jobs')
        .select(`
          *,
          config:ai_assessment_configs(category)
        `)
        .gte('start_time', startTime)
        .order('start_time', { ascending: false });

      if (error) {
        throw error;
      }

      const totalPipelines = jobs?.length || 0;
      const successfulPipelines = jobs?.filter(j => j.status === 'completed').length || 0;
      const failedPipelines = jobs?.filter(j => j.status === 'failed').length || 0;

      // Calculate averages
      const completedJobs = jobs?.filter(j => j.status === 'completed') || [];
      const averageGenerationTime = completedJobs.length > 0
        ? completedJobs.reduce((sum, job) => sum + (job.duration || 0), 0) / completedJobs.length
        : 0;

      const averageQualityScore = completedJobs.length > 0
        ? completedJobs.reduce((sum, job) => sum + (job.quality_score || 0), 0) / completedJobs.length
        : 0;

      const totalTokensUsed = jobs?.reduce((sum, job) => sum + (job.tokens_used || 0), 0) || 0;

      const averageQuestionsGenerated = completedJobs.length > 0
        ? completedJobs.reduce((sum, job) => sum + (job.questions_generated || 0), 0) / completedJobs.length
        : 0;

      const successRate = totalPipelines > 0 ? (successfulPipelines / totalPipelines) * 100 : 0;

      // Common failure reasons
      const failedJobs = jobs?.filter(j => j.status === 'failed') || [];
      const failureReasons = failedJobs.reduce((acc, job) => {
        const reason = job.error || 'Unknown error';
        acc[reason] = (acc[reason] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const commonFailureReasons = Object.entries(failureReasons)
        .map(([reason, count]) => ({ reason, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Performance by category
      const performanceByCategory: Record<string, any> = {};
      const jobsByCategory = jobs?.reduce((acc, job) => {
        const category = job.config?.category || 'unknown';
        if (!acc[category]) acc[category] = [];
        acc[category].push(job);
        return acc;
      }, {} as Record<string, any[]>) || {};

      Object.entries(jobsByCategory).forEach(([category, categoryJobs]) => {
        const completedCategoryJobs = categoryJobs.filter(j => j.status === 'completed');
        const successfulCategoryJobs = categoryJobs.filter(j => j.status === 'completed').length;
        
        performanceByCategory[category] = {
          count: categoryJobs.length,
          averageTime: completedCategoryJobs.length > 0
            ? completedCategoryJobs.reduce((sum, job) => sum + (job.duration || 0), 0) / completedCategoryJobs.length
            : 0,
          averageQuality: completedCategoryJobs.length > 0
            ? completedCategoryJobs.reduce((sum, job) => sum + (job.quality_score || 0), 0) / completedCategoryJobs.length
            : 0,
          successRate: categoryJobs.length > 0 ? (successfulCategoryJobs / categoryJobs.length) * 100 : 0
        };
      });

      return {
        totalPipelines,
        successfulPipelines,
        failedPipelines,
        averageGenerationTime,
        averageQualityScore,
        totalTokensUsed,
        averageQuestionsGenerated,
        successRate,
        commonFailureReasons,
        performanceByCategory
      };

    } catch (error) {
      console.error('Failed to get pipeline metrics:', error);
      throw new Error('Failed to retrieve pipeline metrics');
    }
  }

  /**
   * Get recent pipeline jobs
   */
  async getRecentJobs(limit: number = 50): Promise<PipelineJob[]> {
    try {
      const { data: jobs, error } = await this.supabase
        .from('pipeline_jobs')
        .select(`
          *,
          user:users(email),
          config:ai_assessment_configs(name, category)
        `)
        .order('start_time', { ascending: false })
        .limit(limit);

      if (error) {
        throw error;
      }

      return jobs?.map(job => ({
        id: job.id,
        userId: job.user_id,
        assessmentConfigId: job.assessment_config_id,
        sessionId: job.session_id,
        status: job.status,
        startTime: job.start_time,
        endTime: job.end_time,
        duration: job.duration,
        questionsGenerated: job.questions_generated,
        qualityScore: job.quality_score,
        tokensUsed: job.tokens_used,
        error: job.error,
        metadata: {
          ...job.metadata,
          userEmail: job.user?.email,
          configName: job.config?.name,
          configCategory: job.config?.category
        }
      })) || [];

    } catch (error) {
      console.error('Failed to get recent jobs:', error);
      throw new Error('Failed to retrieve recent pipeline jobs');
    }
  }

  /**
   * Get pipeline performance trends
   */
  async getPerformanceTrends(days: number = 7): Promise<Array<{
    date: string;
    totalJobs: number;
    successfulJobs: number;
    averageTime: number;
    averageQuality: number;
  }>> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      
      const { data: jobs, error } = await this.supabase
        .from('pipeline_jobs')
        .select('*')
        .gte('start_time', startDate.toISOString())
        .order('start_time', { ascending: true });

      if (error) {
        throw error;
      }

      // Group jobs by date
      const jobsByDate = (jobs || []).reduce((acc, job) => {
        const date = job.start_time.split('T')[0];
        if (!acc[date]) acc[date] = [];
        acc[date].push(job);
        return acc;
      }, {} as Record<string, any[]>);

      // Calculate trends
      const trends = Object.entries(jobsByDate).map(([date, dayJobs]) => {
        const successfulJobs = dayJobs.filter(j => j.status === 'completed');
        
        return {
          date,
          totalJobs: dayJobs.length,
          successfulJobs: successfulJobs.length,
          averageTime: successfulJobs.length > 0
            ? successfulJobs.reduce((sum, job) => sum + (job.duration || 0), 0) / successfulJobs.length
            : 0,
          averageQuality: successfulJobs.length > 0
            ? successfulJobs.reduce((sum, job) => sum + (job.quality_score || 0), 0) / successfulJobs.length
            : 0
        };
      });

      return trends;

    } catch (error) {
      console.error('Failed to get performance trends:', error);
      throw new Error('Failed to retrieve performance trends');
    }
  }

  /**
   * Clean up old pipeline jobs
   */
  async cleanupOldJobs(daysToKeep: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();
      
      const { count, error } = await this.supabase
        .from('pipeline_jobs')
        .delete()
        .lt('start_time', cutoffDate);

      if (error) {
        throw error;
      }

      console.log(`Cleaned up ${count || 0} old pipeline jobs`);
      return count || 0;

    } catch (error) {
      console.error('Failed to cleanup old jobs:', error);
      throw new Error('Failed to cleanup old pipeline jobs');
    }
  }
}

// Export singleton instance
export const pipelineMonitor = PipelineMonitor.getInstance();
