"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, Pin, PinOff, ChevronDown, Home, User, Settings } from 'lucide-react'

interface TestSidebarProps {
  children?: React.ReactNode
}

export function TestSidebar({ children }: TestSidebarProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isPinned, setIsPinned] = useState(false)
  const [showDropdown, setShowDropdown] = useState(false)

  const handleToggle = () => {
    setIsExpanded(!isExpanded)
    setIsPinned(!isPinned)
  }

  const handleMouseEnter = () => {
    if (!isPinned) {
      setIsExpanded(true)
    }
  }

  const handleMouseLeave = () => {
    if (!isPinned) {
      setIsExpanded(false)
    }
  }

  return (
    <div className="flex h-screen bg-gray-50" style={{ fontFamily: 'Inter, sans-serif' }}>
      {/* Sidebar */}
      <div
        className={`fixed left-0 top-0 h-full bg-white border-r border-gray-200 transition-all duration-300 ease-in-out z-50 ${
          isExpanded ? 'w-64 shadow-xl' : 'w-16 shadow-sm'
        }`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-4 py-4 border-b border-gray-100">
          {isExpanded ? (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">T</span>
              </div>
              <span className="font-semibold text-gray-900">Topline</span>
            </div>
          ) : (
            <div className="mx-auto">
              <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">T</span>
              </div>
            </div>
          )}
          
          {isExpanded && (
            <button
              onClick={() => setIsPinned(!isPinned)}
              className="p-1.5 hover:bg-gray-100 rounded-md transition-colors text-gray-500"
            >
              {isPinned ? <PinOff className="w-4 h-4" /> : <Pin className="w-4 h-4" />}
            </button>
          )}
        </div>

        {/* Context Switcher */}
        {isExpanded && (
          <div className="px-4 py-3 border-b border-gray-100">
            <button
              onClick={() => setShowDropdown(!showDropdown)}
              className="w-full flex items-center justify-between px-3 py-2 text-sm rounded-md hover:bg-gray-50 transition-colors text-gray-700"
            >
              <span className="font-medium">Individual Context</span>
              <ChevronDown className={`w-4 h-4 transition-transform ${showDropdown ? 'rotate-180' : ''}`} />
            </button>
            
            {showDropdown && (
              <div className="absolute left-4 right-4 mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 text-gray-700">
                    Individual Context
                  </button>
                  <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-50 text-gray-500">
                    Organization Context
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Menu Items */}
        <nav className="flex-1 overflow-y-auto py-4">
          <div className="px-2 space-y-1">
            <div className="relative group">
              <a
                href="#"
                className="flex items-center px-3 py-2 rounded-md transition-all duration-200 bg-indigo-600 text-white"
              >
                <Home className="w-4 h-4 flex-shrink-0" />
                {isExpanded && <span className="ml-3 text-sm font-medium">Home</span>}
              </a>
              {!isExpanded && (
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  Home
                </div>
              )}
            </div>
            
            <div className="relative group">
              <a
                href="#"
                className="flex items-center px-3 py-2 rounded-md transition-all duration-200 text-gray-700 hover:bg-gray-100"
              >
                <User className="w-4 h-4 flex-shrink-0 text-gray-500" />
                {isExpanded && <span className="ml-3 text-sm font-medium">Profile</span>}
              </a>
              {!isExpanded && (
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  Profile
                </div>
              )}
            </div>
            
            <div className="relative group">
              <a
                href="#"
                className="flex items-center px-3 py-2 rounded-md transition-all duration-200 text-gray-700 hover:bg-gray-100"
              >
                <Settings className="w-4 h-4 flex-shrink-0 text-gray-500" />
                {isExpanded && <span className="ml-3 text-sm font-medium">Settings</span>}
              </a>
              {!isExpanded && (
                <div className="absolute left-full top-1/2 transform -translate-y-1/2 ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                  Settings
                </div>
              )}
            </div>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ease-in-out ${isExpanded ? 'ml-64' : 'ml-16'}`}>
        {/* Header with Trigger */}
        <div className="flex items-center p-4 bg-white border-b border-gray-200">
          <button
            onClick={handleToggle}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors mr-4 text-gray-600"
          >
            <Menu className="w-5 h-5" />
          </button>
          <h1 className="text-lg font-semibold text-gray-700">Test Dashboard</h1>
        </div>
        
        <div className="p-8">
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
            <strong>✅ Test Sidebar Working!</strong> If you can see this green box and the sidebar features, then the styling is working correctly.
          </div>
          {children}
        </div>
      </div>
    </div>
  )
}
