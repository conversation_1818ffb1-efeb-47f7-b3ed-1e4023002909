/**
 * Rate Limiting Module
 * Handles API rate limiting and request throttling
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-error-handler';

// Rate limiting configuration
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// Rate limiting store (in-memory for development, use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Production-ready rate limit configurations
export const RATE_LIMITS = {
  // General API endpoints - 100 requests per 15 minutes
  DEFAULT: { windowMs: 15 * 60 * 1000, maxRequests: 100 },

  // Authentication endpoints - 10 attempts per 15 minutes (prevent brute force)
  AUTH: { windowMs: 15 * 60 * 1000, maxRequests: 10 },

  // File upload endpoints - 20 uploads per hour (prevent abuse)
  UPLOAD: { windowMs: 60 * 60 * 1000, maxRequests: 20 },

  // Admin endpoints - 200 requests per 15 minutes (higher limit for admin operations)
  ADMIN: { windowMs: 15 * 60 * 1000, maxRequests: 200 },

  // Public endpoints - 50 requests per minute (health checks, status)
  PUBLIC: { windowMs: 60 * 1000, maxRequests: 50 },

  // Profile updates - 30 requests per 15 minutes (prevent spam)
  PROFILE: { windowMs: 15 * 60 * 1000, maxRequests: 30 },

  // Database queries - 150 requests per 15 minutes
  DATABASE: { windowMs: 15 * 60 * 1000, maxRequests: 150 },

  // Development mode - higher limits for testing
  DEVELOPMENT: { windowMs: 15 * 60 * 1000, maxRequests: 1000 }
} as const;

/**
 * Get client identifier for rate limiting
 */
function getClientIdentifier(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ip = forwarded?.split(',')[0] || realIp || 'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';
  
  // Create a more unique identifier
  return `${ip}:${userAgent.substring(0, 50)}`;
}

/**
 * Clean up expired rate limit entries
 */
function cleanupExpiredEntries(now: number): void {
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

/**
 * Check if request should be rate limited
 */
function shouldRateLimit(
  clientId: string, 
  config: RateLimitConfig, 
  now: number
): { limited: boolean; resetTime: number; remaining: number } {
  const entry = rateLimitStore.get(clientId);
  
  if (!entry || now > entry.resetTime) {
    // First request or window expired
    const resetTime = now + config.windowMs;
    rateLimitStore.set(clientId, { count: 1, resetTime });
    return { limited: false, resetTime, remaining: config.maxRequests - 1 };
  }
  
  if (entry.count >= config.maxRequests) {
    // Rate limit exceeded
    return { limited: true, resetTime: entry.resetTime, remaining: 0 };
  }
  
  // Increment count
  entry.count++;
  rateLimitStore.set(clientId, entry);
  
  return { 
    limited: false, 
    resetTime: entry.resetTime, 
    remaining: config.maxRequests - entry.count 
  };
}

/**
 * Rate limiting middleware
 */
export function withRateLimit(config: RateLimitConfig = RATE_LIMITS.DEFAULT) {
  return function rateLimitMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      // Check if rate limiting is disabled
      if (process.env.DISABLE_RATE_LIMITING === 'true') {
        return handler(request);
      }

      try {
        // Get client identifier (IP + User-Agent for better uniqueness)
        const clientId = getClientIdentifier(request);
        const now = Date.now();
        
        // Clean up expired entries
        cleanupExpiredEntries(now);
        
        // Check rate limit
        const { limited, resetTime, remaining } = shouldRateLimit(clientId, config, now);
        
        if (limited) {
          const retryAfter = Math.ceil((resetTime - now) / 1000);
          
          return NextResponse.json(
            {
              error: 'Too Many Requests',
              message: 'Rate limit exceeded. Please try again later.',
              retryAfter
            },
            {
              status: 429,
              headers: {
                'Retry-After': retryAfter.toString(),
                'X-RateLimit-Limit': config.maxRequests.toString(),
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString()
              }
            }
          );
        }
        
        // Execute handler
        const response = await handler(request);
        
        // Add rate limit headers to successful responses
        response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
        response.headers.set('X-RateLimit-Remaining', remaining.toString());
        response.headers.set('X-RateLimit-Reset', Math.ceil(resetTime / 1000).toString());
        
        return response;
        
      } catch (error) {
        return handleApiError(error, 'Rate Limiting Error');
      }
    };
  };
}

/**
 * Get current rate limit status for a client
 */
export function getRateLimitStatus(
  request: NextRequest, 
  config: RateLimitConfig = RATE_LIMITS.DEFAULT
): { remaining: number; resetTime: number; limited: boolean } {
  const clientId = getClientIdentifier(request);
  const now = Date.now();
  const entry = rateLimitStore.get(clientId);
  
  if (!entry || now > entry.resetTime) {
    return {
      remaining: config.maxRequests,
      resetTime: now + config.windowMs,
      limited: false
    };
  }
  
  return {
    remaining: Math.max(0, config.maxRequests - entry.count),
    resetTime: entry.resetTime,
    limited: entry.count >= config.maxRequests
  };
}

/**
 * Clear rate limit for a specific client (admin function)
 */
export function clearRateLimit(clientId: string): boolean {
  return rateLimitStore.delete(clientId);
}

/**
 * Get rate limit statistics (admin function)
 */
export function getRateLimitStats(): {
  totalClients: number;
  activeClients: number;
  entries: Array<{ clientId: string; count: number; resetTime: number }>;
} {
  const now = Date.now();
  const entries: Array<{ clientId: string; count: number; resetTime: number }> = [];
  let activeClients = 0;
  
  for (const [clientId, entry] of rateLimitStore.entries()) {
    entries.push({ clientId, count: entry.count, resetTime: entry.resetTime });
    if (now <= entry.resetTime) {
      activeClients++;
    }
  }
  
  return {
    totalClients: rateLimitStore.size,
    activeClients,
    entries
  };
}
