/**
 * Optimized Image Component
 * Advanced image optimization with lazy loading, WebP support, and responsive sizing
 */

"use client"

import * as React from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { LazyComponent } from "@/lib/performance-utils"

// =============================================================================
// TYPES
// =============================================================================

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  containerClassName?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  fill?: boolean
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  objectPosition?: string
  loading?: 'lazy' | 'eager'
  fallback?: React.ReactNode
  onLoad?: () => void
  onError?: () => void
  responsive?: boolean
  aspectRatio?: number
  maxWidth?: number
  maxHeight?: number
}

interface ResponsiveImageProps extends Omit<OptimizedImageProps, 'width' | 'height'> {
  breakpoints?: {
    mobile?: number
    tablet?: number
    desktop?: number
  }
  aspectRatio: number
}

interface AvatarImageProps extends Omit<OptimizedImageProps, 'aspectRatio'> {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  fallbackText?: string
  shape?: 'circle' | 'square' | 'rounded'
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Generate optimized image URLs for different formats and sizes
 */
function getOptimizedImageUrl(src: string, width?: number, height?: number, quality = 75): string {
  // If it's already a Next.js optimized URL or external URL, return as-is
  if (src.startsWith('/_next/') || src.startsWith('http')) {
    return src
  }

  // For local images, let Next.js handle optimization
  return src
}

/**
 * Generate responsive image sizes string
 */
function generateSizes(breakpoints?: { mobile?: number; tablet?: number; desktop?: number }): string {
  if (!breakpoints) {
    return '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
  }

  const { mobile = 100, tablet = 50, desktop = 33 } = breakpoints
  return `(max-width: 768px) ${mobile}vw, (max-width: 1200px) ${tablet}vw, ${desktop}vw`
}

/**
 * Create blur data URL for placeholder
 */
function createBlurDataURL(width = 10, height = 10): string {
  const canvas = document.createElement('canvas')
  canvas.width = width
  canvas.height = height
  const ctx = canvas.getContext('2d')
  
  if (ctx) {
    ctx.fillStyle = '#f3f4f6'
    ctx.fillRect(0, 0, width, height)
  }
  
  return canvas.toDataURL()
}

// =============================================================================
// OPTIMIZED IMAGE COMPONENT
// =============================================================================

export const OptimizedImage = React.forwardRef<HTMLDivElement, OptimizedImageProps>(({
  src,
  alt,
  width,
  height,
  className,
  containerClassName,
  priority = false,
  quality = 75,
  placeholder = 'blur',
  blurDataURL,
  sizes,
  fill = false,
  objectFit = 'cover',
  objectPosition = 'center',
  loading = 'lazy',
  fallback,
  onLoad,
  onError,
  responsive = false,
  aspectRatio,
  maxWidth,
  maxHeight,
  ...props
}, ref) => {
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [hasError, setHasError] = React.useState(false)
  const [optimizedSrc, setOptimizedSrc] = React.useState(src)

  React.useEffect(() => {
    setOptimizedSrc(getOptimizedImageUrl(src, width, height, quality))
  }, [src, width, height, quality])

  const handleLoad = React.useCallback(() => {
    setIsLoaded(true)
    onLoad?.()
  }, [onLoad])

  const handleError = React.useCallback(() => {
    setHasError(true)
    onError?.()
  }, [onError])

  // Calculate responsive dimensions
  const responsiveDimensions = React.useMemo(() => {
    if (!responsive || !aspectRatio) return { width, height }

    const containerWidth = maxWidth || 1200
    const calculatedHeight = containerWidth / aspectRatio

    return {
      width: Math.min(containerWidth, width || containerWidth),
      height: Math.min(calculatedHeight, height || calculatedHeight, maxHeight || calculatedHeight)
    }
  }, [responsive, aspectRatio, width, height, maxWidth, maxHeight])

  // Generate blur placeholder if not provided
  const placeholderDataURL = React.useMemo(() => {
    if (blurDataURL) return blurDataURL
    if (placeholder === 'blur' && typeof window !== 'undefined') {
      return createBlurDataURL(responsiveDimensions.width || 10, responsiveDimensions.height || 10)
    }
    return undefined
  }, [blurDataURL, placeholder, responsiveDimensions])

  if (hasError && fallback) {
    return <div ref={ref} className={containerClassName}>{fallback}</div>
  }

  const imageElement = (
    <Image
      src={optimizedSrc}
      alt={alt}
      width={fill ? undefined : responsiveDimensions.width}
      height={fill ? undefined : responsiveDimensions.height}
      fill={fill}
      priority={priority}
      quality={quality}
      placeholder={placeholder}
      blurDataURL={placeholderDataURL}
      sizes={sizes || (responsive ? generateSizes() : undefined)}
      loading={priority ? 'eager' : loading}
      onLoad={handleLoad}
      onError={handleError}
      className={cn(
        'transition-opacity duration-300',
        isLoaded ? 'opacity-100' : 'opacity-0',
        fill && 'object-cover',
        className
      )}
      style={{
        objectFit: fill ? objectFit : undefined,
        objectPosition: fill ? objectPosition : undefined,
      }}
      {...props}
    />
  )

  const containerStyle: React.CSSProperties = {}
  if (aspectRatio && !fill) {
    containerStyle.aspectRatio = aspectRatio.toString()
  }

  return (
    <div
      ref={ref}
      className={cn(
        'relative overflow-hidden',
        fill && 'w-full h-full',
        containerClassName
      )}
      style={containerStyle}
    >
      {priority ? imageElement : (
        <LazyComponent
          threshold={0.1}
          rootMargin="50px"
          fallback={
            <div className={cn(
              'w-full h-full bg-muted animate-pulse',
              aspectRatio && 'aspect-ratio'
            )} />
          }
        >
          {imageElement}
        </LazyComponent>
      )}
    </div>
  )
})

OptimizedImage.displayName = "OptimizedImage"

// =============================================================================
// RESPONSIVE IMAGE COMPONENT
// =============================================================================

export const ResponsiveImage = React.forwardRef<HTMLDivElement, ResponsiveImageProps>(({
  breakpoints,
  aspectRatio,
  sizes,
  ...props
}, ref) => {
  const responsiveSizes = sizes || generateSizes(breakpoints)

  return (
    <OptimizedImage
      ref={ref}
      responsive={true}
      aspectRatio={aspectRatio}
      sizes={responsiveSizes}
      {...props}
    />
  )
})

ResponsiveImage.displayName = "ResponsiveImage"

// =============================================================================
// AVATAR IMAGE COMPONENT
// =============================================================================

export const AvatarImage = React.forwardRef<HTMLDivElement, AvatarImageProps>(({
  size = 'md',
  fallbackText,
  shape = 'circle',
  className,
  containerClassName,
  ...props
}, ref) => {
  const sizeMap = {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 64,
    '2xl': 80
  }

  const dimensions = sizeMap[size]
  
  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-none',
    rounded: 'rounded-lg'
  }

  const fallbackElement = fallbackText ? (
    <div className={cn(
      'flex items-center justify-center bg-muted text-muted-foreground font-medium',
      shapeClasses[shape],
      className
    )}>
      {fallbackText.slice(0, 2).toUpperCase()}
    </div>
  ) : undefined

  return (
    <OptimizedImage
      ref={ref}
      width={dimensions}
      height={dimensions}
      className={cn(shapeClasses[shape], className)}
      containerClassName={cn(
        'flex-shrink-0',
        shapeClasses[shape],
        containerClassName
      )}
      fallback={fallbackElement}
      priority={size === 'xs' || size === 'sm'} // Prioritize small avatars
      {...props}
    />
  )
})

AvatarImage.displayName = "AvatarImage"

// =============================================================================
// IMAGE GALLERY COMPONENT
// =============================================================================

interface ImageGalleryProps {
  images: Array<{
    src: string
    alt: string
    caption?: string
  }>
  columns?: number
  gap?: number
  aspectRatio?: number
  className?: string
}

export const ImageGallery = React.forwardRef<HTMLDivElement, ImageGalleryProps>(({
  images,
  columns = 3,
  gap = 4,
  aspectRatio = 1,
  className,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={cn(
        'grid',
        `grid-cols-1 md:grid-cols-${Math.min(columns, 3)} lg:grid-cols-${columns}`,
        `gap-${gap}`,
        className
      )}
      {...props}
    >
      {images.map((image, index) => (
        <div key={index} className="space-y-2">
          <ResponsiveImage
            src={image.src}
            alt={image.alt}
            aspectRatio={aspectRatio}
            priority={index < 3} // Prioritize first 3 images
          />
          {image.caption && (
            <p className="text-sm text-muted-foreground text-center">
              {image.caption}
            </p>
          )}
        </div>
      ))}
    </div>
  )
})

ImageGallery.displayName = "ImageGallery"

// =============================================================================
// HERO IMAGE COMPONENT
// =============================================================================

interface HeroImageProps extends Omit<OptimizedImageProps, 'width' | 'height'> {
  height?: string | number
  overlay?: boolean
  overlayOpacity?: number
  children?: React.ReactNode
}

export const HeroImage = React.forwardRef<HTMLDivElement, HeroImageProps>(({
  height = '60vh',
  overlay = false,
  overlayOpacity = 0.4,
  children,
  className,
  containerClassName,
  ...props
}, ref) => {
  return (
    <div
      ref={ref}
      className={cn('relative w-full', containerClassName)}
      style={{ height: typeof height === 'number' ? `${height}px` : height }}
    >
      <OptimizedImage
        fill={true}
        priority={true}
        objectFit="cover"
        className={className}
        {...props}
      />
      
      {overlay && (
        <div
          className="absolute inset-0 bg-black"
          style={{ opacity: overlayOpacity }}
        />
      )}
      
      {children && (
        <div className="absolute inset-0 flex items-center justify-center">
          {children}
        </div>
      )}
    </div>
  )
})

HeroImage.displayName = "HeroImage"
