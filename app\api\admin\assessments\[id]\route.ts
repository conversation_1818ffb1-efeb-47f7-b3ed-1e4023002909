import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { createClient } from '@/lib/supabase-server';

/**
 * Get specific assessment
 * GET /api/admin/assessments/[id]
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = params;
    const supabase = createClient();

    const { data: assessment, error } = await supabase
      .from('assessments')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !assessment) {
      return NextResponse.json(
        { error: 'Assessment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      assessment
    });

  } catch (error: any) {
    console.error('Assessment fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch assessment' },
      { status: 500 }
    );
  }
}

/**
 * Update assessment
 * PUT /api/admin/assessments/[id]
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = params;
    const body = await req.json();
    const {
      name,
      instructions,
      passing_score,
      duration_minutes,
      question_count,
      price,
      question_types,
      system_prompt,
      status
    } = body;

    // Validate required fields
    if (!name || !system_prompt) {
      return NextResponse.json(
        { error: 'Name and system prompt are required' },
        { status: 400 }
      );
    }

    const supabase = createClient();

    const { data: assessment, error } = await supabase
      .from('assessments')
      .update({
        name,
        instructions,
        passing_score,
        duration_minutes,
        question_count,
        price,
        question_types,
        system_prompt,
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating assessment:', error);
      return NextResponse.json(
        { error: 'Failed to update assessment', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      assessment,
      message: 'Assessment updated successfully'
    });

  } catch (error: any) {
    console.error('Assessment update error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update assessment' },
      { status: 500 }
    );
  }
}

/**
 * Delete assessment
 * DELETE /api/admin/assessments/[id]
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = params;
    const supabase = createClient();

    const { error } = await supabase
      .from('assessments')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting assessment:', error);
      return NextResponse.json(
        { error: 'Failed to delete assessment', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Assessment deleted successfully'
    });

  } catch (error: any) {
    console.error('Assessment deletion error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete assessment' },
      { status: 500 }
    );
  }
}
