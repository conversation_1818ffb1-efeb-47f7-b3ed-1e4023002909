-- Insert clean data with correct Supabase Auth IDs
-- Run this after clearing the data

-- Step 1: Create organizations
INSERT INTO organizations (id, name, slug, description, website_url, industry, size_range, max_teams, max_members, status, created_at, updated_at)
VALUES 
  ('11111111-1111-1111-1111-111111111111', 'TechCorp Solutions', 'techcorp-solutions', 'Leading technology solutions provider', 'https://techcorp.com', 'Technology', '51-200', 10, 100, 'active', NOW(), NOW()),
  ('22222222-2222-2222-2222-222222222222', 'Creative Agency Inc', 'creative-agency-inc', 'Full-service creative and marketing agency', 'https://creativeagency.com', 'Marketing', '11-50', 5, 50, 'active', NOW(), NOW()),
  ('33333333-3333-3333-3333-333333333333', 'StartupHub', 'startuphub', 'Innovation and startup incubator', 'https://startuphub.com', 'Consulting', '2-10', 3, 25, 'active', NOW(), NOW());

-- Step 2: Create departments
INSERT INTO departments (id, organization_id, name, description, created_at, updated_at)
VALUES 
  -- TechCorp departments
  ('11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', 'Engineering', 'Software development team', NOW(), NOW()),
  ('11111111-1111-1111-1111-111111111112', '11111111-1111-1111-1111-111111111111', 'Product', 'Product management and design', NOW(), NOW()),
  
  -- Creative Agency departments  
  ('22222222-2222-2222-2222-222222222221', '22222222-2222-2222-2222-222222222222', 'Design', 'Creative design team', NOW(), NOW()),
  ('22222222-2222-2222-2222-222222222222', '22222222-2222-2222-2222-222222222222', 'Marketing', 'Digital marketing specialists', NOW(), NOW()),
  
  -- StartupHub departments
  ('33333333-3333-3333-3333-333333333331', '33333333-3333-3333-3333-333333333333', 'Operations', 'Business operations', NOW(), NOW()),
  ('33333333-3333-3333-3333-333333333332', '33333333-3333-3333-3333-333333333333', 'Consulting', 'Startup consulting services', NOW(), NOW());

-- Step 3: Create employment relationships with REAL auth user IDs
-- NOTE: Only create employment for users who should have organization access
-- Leave individual-only users without employment relationships

INSERT INTO employment_relationships (id, user_id, organization_id, department_id, role, job_title, status, created_at, updated_at)
VALUES 
  -- Organization admins (using real auth IDs)
  ('44444444-4444-4444-4444-444444444444', 'f220b2b0-05bd-4986-b883-9ab51123e520', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', 'organization_admin', 'CTO', 'active', NOW(), NOW()),
  ('55555555-5555-5555-5555-555555555555', '19028181-f4d1-4872-9235-2d5f771a1bae', '22222222-2222-2222-2222-222222222222', '22222222-2222-2222-2222-222222222221', 'organization_admin', 'Creative Director', 'active', NOW(), NOW());

-- Step 4: Create user contexts
INSERT INTO user_contexts (user_id, active_context, active_employment_id, active_department_id, last_org_context, created_at, updated_at)
VALUES
  -- Team contexts for employed users (using 'team' instead of 'organization')
  ('f220b2b0-05bd-4986-b883-9ab51123e520', 'team', '44444444-4444-4444-4444-444444444444', '11111111-1111-1111-1111-111111111111', '11111111-1111-1111-1111-111111111111', NOW(), NOW()),
  ('19028181-f4d1-4872-9235-2d5f771a1bae', 'team', '55555555-5555-5555-5555-555555555555', '22222222-2222-2222-2222-222222222221', '22222222-2222-2222-2222-222222222222', NOW(), NOW());

-- Step 5: Create individual contexts for users without employment
-- Get all users who don't have employment relationships and give them individual contexts
INSERT INTO user_contexts (user_id, active_context, created_at, updated_at)
SELECT
  u.id,
  'individual',
  NOW(),
  NOW()
FROM users u
LEFT JOIN employment_relationships er ON u.id = er.user_id
WHERE er.user_id IS NULL
  AND u.role = 'individual'
  AND NOT EXISTS (SELECT 1 FROM user_contexts uc WHERE uc.user_id = u.id);

-- Verify the data
SELECT 'Organizations created:' as info, COUNT(*) as count FROM organizations;
SELECT 'Departments created:' as info, COUNT(*) as count FROM departments;
SELECT 'Employment relationships created:' as info, COUNT(*) as count FROM employment_relationships;
SELECT 'User contexts created:' as info, COUNT(*) as count FROM user_contexts;

-- Show organization admins
SELECT 
  u.email,
  u.full_name,
  o.name as organization,
  d.name as department,
  er.role,
  er.job_title
FROM users u
JOIN employment_relationships er ON u.id = er.user_id
JOIN organizations o ON er.organization_id = o.id
JOIN departments d ON er.department_id = d.id
WHERE er.role = 'organization_admin'
ORDER BY o.name;

-- Show individual users (no employment)
SELECT 
  u.email,
  u.full_name,
  uc.active_context
FROM users u
JOIN user_contexts uc ON u.id = uc.user_id
WHERE uc.active_context = 'individual'
ORDER BY u.email;
