/**
 * Progress Indicator Components
 * Standardized progress display components with consistent styling and accessibility
 */

"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, Target, TrendingUp } from "lucide-react"

// TypeScript interfaces
export interface ProgressData {
  current: number
  total: number
  percentage: number
  label?: string
  description?: string
}

export interface ProgressStats {
  completed: number
  inProgress: number
  notStarted: number
  total: number
}

export interface ProgressIndicatorProps {
  progress: ProgressData
  variant?: 'default' | 'compact' | 'detailed' | 'circular'
  size?: 'sm' | 'md' | 'lg'
  showLabel?: boolean
  showPercentage?: boolean
  showFraction?: boolean
  className?: string
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red'
}

export interface ProgressOverviewProps {
  stats: ProgressStats
  title?: string
  description?: string
  timeSpent?: string
  className?: string
  variant?: 'card' | 'inline'
}

export interface ProgressStatsProps {
  stats: ProgressStats
  labels?: {
    completed?: string
    inProgress?: string
    notStarted?: string
    total?: string
  }
  showIcons?: boolean
  layout?: 'horizontal' | 'vertical' | 'grid'
  className?: string
}

// Color schemes for progress indicators
const progressColors = {
  blue: {
    bg: "bg-blue-500",
    gradient: "from-blue-500 to-indigo-500",
    light: "bg-blue-100 text-blue-700",
    ring: "ring-blue-500/20"
  },
  green: {
    bg: "bg-green-500",
    gradient: "from-green-500 to-emerald-500",
    light: "bg-green-100 text-green-700",
    ring: "ring-green-500/20"
  },
  purple: {
    bg: "bg-purple-500",
    gradient: "from-purple-500 to-pink-500",
    light: "bg-purple-100 text-purple-700",
    ring: "ring-purple-500/20"
  },
  orange: {
    bg: "bg-orange-500",
    gradient: "from-orange-500 to-amber-500",
    light: "bg-orange-100 text-orange-700",
    ring: "ring-orange-500/20"
  },
  red: {
    bg: "bg-red-500",
    gradient: "from-red-500 to-rose-500",
    light: "bg-red-100 text-red-700",
    ring: "ring-red-500/20"
  }
}

// Get progress color based on percentage
const getProgressColor = (percentage: number): keyof typeof progressColors => {
  if (percentage >= 100) return 'green'
  if (percentage >= 75) return 'blue'
  if (percentage >= 50) return 'purple'
  if (percentage >= 25) return 'orange'
  return 'red'
}

/**
 * Basic Progress Indicator Component
 */
export const ProgressIndicator = React.forwardRef<
  HTMLDivElement,
  ProgressIndicatorProps
>(({
  progress,
  variant = 'default',
  size = 'md',
  showLabel = true,
  showPercentage = true,
  showFraction = false,
  className,
  color,
  ...props
}, ref) => {
  const autoColor = color || getProgressColor(progress.percentage)
  const colorScheme = progressColors[autoColor]
  
  const sizeClasses = {
    sm: "h-1.5",
    md: "h-2.5",
    lg: "h-3"
  }
  
  if (variant === 'circular') {
    const radius = size === 'sm' ? 16 : size === 'lg' ? 24 : 20
    const circumference = 2 * Math.PI * radius
    const strokeDashoffset = circumference - (progress.percentage / 100) * circumference
    
    return (
      <div ref={ref} className={cn("flex items-center gap-3", className)} {...props}>
        <div className="relative">
          <svg
            className={cn(
              "transform -rotate-90",
              size === 'sm' && "w-10 h-10",
              size === 'md' && "w-12 h-12",
              size === 'lg' && "w-16 h-16"
            )}
            viewBox="0 0 50 50"
          >
            <circle
              cx="25"
              cy="25"
              r={radius}
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              className="text-gray-200 dark:text-gray-700"
            />
            <circle
              cx="25"
              cy="25"
              r={radius}
              stroke="currentColor"
              strokeWidth="2"
              fill="none"
              strokeDasharray={circumference}
              strokeDashoffset={strokeDashoffset}
              className={cn("transition-all duration-500 ease-in-out", colorScheme.bg)}
              strokeLinecap="round"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className={cn(
              "font-semibold",
              size === 'sm' && "text-xs",
              size === 'md' && "text-sm",
              size === 'lg' && "text-base"
            )}>
              {Math.round(progress.percentage)}%
            </span>
          </div>
        </div>
        {showLabel && progress.label && (
          <div className="flex-1 min-w-0">
            <p className="font-medium truncate">{progress.label}</p>
            {progress.description && (
              <p className="text-sm text-muted-foreground truncate">{progress.description}</p>
            )}
          </div>
        )}
      </div>
    )
  }
  
  if (variant === 'compact') {
    return (
      <div ref={ref} className={cn("space-y-1", className)} {...props}>
        {showLabel && progress.label && (
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium truncate">{progress.label}</span>
            {showPercentage && (
              <span className="text-muted-foreground">{Math.round(progress.percentage)}%</span>
            )}
          </div>
        )}
        <Progress 
          value={progress.percentage} 
          className={cn("w-full", sizeClasses[size])}
          aria-label={progress.label ? `${progress.label}: ${progress.percentage}%` : `Progress: ${progress.percentage}%`}
        />
        {showFraction && (
          <div className="text-xs text-muted-foreground">
            {progress.current} of {progress.total}
          </div>
        )}
      </div>
    )
  }
  
  return (
    <div ref={ref} className={cn("space-y-2", className)} {...props}>
      {(showLabel || showPercentage || showFraction) && (
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            {showLabel && progress.label && (
              <p className="font-medium truncate">{progress.label}</p>
            )}
            {progress.description && (
              <p className="text-sm text-muted-foreground truncate">{progress.description}</p>
            )}
          </div>
          <div className="flex items-center gap-2 text-sm">
            {showFraction && (
              <span className="text-muted-foreground">
                {progress.current}/{progress.total}
              </span>
            )}
            {showPercentage && (
              <Badge variant="outline" className={colorScheme.light}>
                {Math.round(progress.percentage)}%
              </Badge>
            )}
          </div>
        </div>
      )}
      <Progress 
        value={progress.percentage} 
        className={cn("w-full", sizeClasses[size])}
        aria-label={progress.label ? `${progress.label}: ${progress.percentage}%` : `Progress: ${progress.percentage}%`}
      />
    </div>
  )
})

ProgressIndicator.displayName = "ProgressIndicator"

/**
 * Progress Overview Card Component
 */
export const ProgressOverview = React.forwardRef<
  HTMLDivElement,
  ProgressOverviewProps
>(({
  stats,
  title = "Learning Progress",
  description,
  timeSpent,
  className,
  variant = 'card',
  ...props
}, ref) => {
  const overallPercentage = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0
  const progressData: ProgressData = {
    current: stats.completed,
    total: stats.total,
    percentage: overallPercentage,
    label: title,
    description
  }
  
  if (variant === 'inline') {
    return (
      <div ref={ref} className={cn("space-y-4", className)} {...props}>
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">{title}</h3>
            {description && (
              <p className="text-sm text-muted-foreground">{description}</p>
            )}
          </div>
          {timeSpent && (
            <Badge variant="outline" className="bg-blue-50 text-blue-700">
              <Clock className="mr-1 h-3 w-3" />
              {timeSpent}
            </Badge>
          )}
        </div>
        <ProgressIndicator 
          progress={progressData}
          variant="default"
          showLabel={false}
          showPercentage={true}
          showFraction={true}
        />
      </div>
    )
  }
  
  return (
    <Card ref={ref} className={cn("border-none shadow-sm overflow-hidden", className)} {...props}>
      <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6 text-white">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h2 className="text-xl font-semibold">{title}</h2>
            {description && (
              <p className="text-blue-100 mt-1">{description}</p>
            )}
          </div>
          {timeSpent && (
            <Badge className="bg-white/20 text-white border-white/30 hover:bg-white/30">
              <Clock className="mr-1 h-3 w-3" />
              {timeSpent} spent
            </Badge>
          )}
        </div>
        <div className="mt-6">
          <div className="flex items-center justify-between text-sm mb-2">
            <span>Overall Progress</span>
            <span>{overallPercentage}%</span>
          </div>
          <Progress 
            value={overallPercentage} 
            className="h-2.5 bg-blue-400/30 [&>div]:bg-gradient-to-r [&>div]:from-emerald-400 [&>div]:to-cyan-400"
            aria-label={`Overall progress: ${overallPercentage}%`}
          />
        </div>
      </div>
    </Card>
  )
})

ProgressOverview.displayName = "ProgressOverview"

/**
 * Progress Statistics Component
 */
export const ProgressStats = React.forwardRef<
  HTMLDivElement,
  ProgressStatsProps
>(({
  stats,
  labels = {},
  showIcons = true,
  layout = 'grid',
  className,
  ...props
}, ref) => {
  const defaultLabels = {
    completed: 'Completed',
    inProgress: 'In Progress',
    notStarted: 'Not Started',
    total: 'Total',
    ...labels
  }
  
  const statItems = [
    {
      key: 'completed',
      value: stats.completed,
      label: defaultLabels.completed,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      key: 'inProgress',
      value: stats.inProgress,
      label: defaultLabels.inProgress,
      icon: Clock,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      key: 'notStarted',
      value: stats.notStarted,
      label: defaultLabels.notStarted,
      icon: Target,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50'
    },
    {
      key: 'total',
      value: stats.total,
      label: defaultLabels.total,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50'
    }
  ]
  
  const layoutClasses = {
    horizontal: "flex flex-wrap gap-4",
    vertical: "space-y-4",
    grid: "grid grid-cols-2 md:grid-cols-4 gap-4"
  }
  
  return (
    <div ref={ref} className={cn(layoutClasses[layout], className)} {...props}>
      {statItems.map((item) => {
        const Icon = item.icon
        return (
          <div key={item.key} className="flex flex-col space-y-1">
            <div className="flex items-center gap-2">
              {showIcons && (
                <div className={cn("p-1 rounded", item.bgColor)}>
                  <Icon className={cn("h-4 w-4", item.color)} />
                </div>
              )}
              <span className="text-sm text-muted-foreground">{item.label}</span>
            </div>
            <div className="flex items-baseline gap-1">
              <span className="text-2xl font-bold">{item.value}</span>
              <span className="text-xs text-muted-foreground">
                {item.key === 'total' ? 'items' : 'modules'}
              </span>
            </div>
          </div>
        )
      })}
    </div>
  )
})

ProgressStats.displayName = "ProgressStats"

// Export utility functions
export { getProgressColor, progressColors }
