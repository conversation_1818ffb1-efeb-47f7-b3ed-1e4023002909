# 🚀 Production Deployment Guide

This guide covers deploying the Luna Skills Platform to production, including Vercel deployment, environment configuration, and troubleshooting common issues.

## 🌐 Deployment Platforms

### Vercel (Recommended)

Luna is optimized for deployment on Vercel with Next.js 15 and Supabase integration.

#### Prerequisites
- GitHub repository with your Luna code
- Vercel account connected to GitHub
- Supabase project set up

#### Deployment Steps

1. **Connect Repository to Vercel**
   ```bash
   # Push your code to GitHub
   git add .
   git commit -m "Deploy to production"
   git push origin master
   ```

2. **Import Project in Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Select "Next.js" framework preset

3. **Configure Environment Variables**
   Add these environment variables in Vercel dashboard:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
   NODE_ENV=production
   ```

4. **Deploy**
   - Click "Deploy"
   - Wait for build to complete
   - Visit your deployed application

## 🔧 Build Configuration

### Next.js Configuration

The `next.config.mjs` is optimized for production deployment:

```javascript
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  serverExternalPackages: [],
  
  // Transpile packages for compatibility
  transpilePackages: [
    'recharts',
    '@radix-ui/react-accordion',
    '@radix-ui/react-dialog',
    // ... other packages
  ],

  // Bundle optimization
  webpack: (config, { isServer }) => {
    // Optimize imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(process.cwd()),
    }

    // Basic fallbacks for server-side rendering
    if (isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }

    return config
  },
}
```

### Package.json Scripts

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "clean": "rimraf .next && npm cache clean --force"
  }
}
```

## 🐛 Troubleshooting Common Issues

### SSR (Server-Side Rendering) Issues

#### "self is not defined" Error

**Problem**: Browser-specific libraries causing SSR errors during build.

**Solution**: Use dynamic imports for client-side only libraries:

```typescript
// ❌ Wrong - causes SSR errors
import confetti from 'canvas-confetti'

// ✅ Correct - dynamic import
const triggerConfetti = async () => {
  if (typeof window !== "undefined") {
    try {
      const confetti = (await import('canvas-confetti')).default
      confetti({ /* options */ })
    } catch (error) {
      console.warn('Failed to load confetti:', error)
    }
  }
}
```

#### Vendor Chunk Errors

**Problem**: Missing vendor chunks during build.

**Solution**: Simplified webpack configuration without complex chunk splitting:

```javascript
// Remove complex splitChunks configuration
// Keep webpack config minimal for stability
```

### Build Failures

#### Dependency Conflicts

**Problem**: Peer dependency warnings or conflicts.

**Solution**: Use legacy peer deps flag:

```bash
npm install --legacy-peer-deps
```

#### Memory Issues

**Problem**: Build runs out of memory.

**Solution**: Increase Node.js memory limit:

```bash
# In package.json
"build": "NODE_OPTIONS='--max-old-space-size=4096' next build"
```

### Runtime Issues

#### Authentication Problems

**Problem**: Users can't log in or sessions expire.

**Solution**: Check Supabase configuration:

1. Verify environment variables are set correctly
2. Check Supabase project is active
3. Verify RLS policies are properly configured
4. Test with `/api/test-connection` endpoint

#### Database Connection Issues

**Problem**: Database queries failing.

**Solution**: 
1. Check Supabase service role key
2. Verify database is accessible
3. Test connection with diagnostic endpoints
4. Check RLS policies for proper access

## 📊 Performance Optimization

### Build Optimization

1. **Clean builds**: Always clean before production builds
   ```bash
   npm run clean
   npm run build
   ```

2. **Bundle analysis**: Analyze bundle size
   ```bash
   npm install -g @next/bundle-analyzer
   ANALYZE=true npm run build
   ```

### Runtime Performance

1. **Image optimization**: Use Next.js Image component
2. **Code splitting**: Leverage dynamic imports
3. **Caching**: Implement proper caching strategies
4. **Database optimization**: Use proper indexes and queries

## 🔒 Security Considerations

### Environment Variables

- Never commit `.env.local` to version control
- Use different keys for development and production
- Rotate keys regularly
- Use Vercel's environment variable encryption

### Database Security

- Enable Row Level Security (RLS) on all tables
- Use service role key only on server-side
- Implement proper authentication checks
- Regular security audits

## 📈 Monitoring & Analytics

### Error Tracking

Consider integrating error tracking:

```bash
npm install @sentry/nextjs
```

### Performance Monitoring

Monitor Core Web Vitals and application performance:

```javascript
// In _app.tsx or layout.tsx
export function reportWebVitals(metric) {
  console.log(metric)
  // Send to analytics service
}
```

## 🔄 Continuous Deployment

### GitHub Actions (Optional)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Vercel
on:
  push:
    branches: [master]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📞 Support

If you encounter deployment issues:

1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Test locally with production build: `npm run build && npm start`
3. Use diagnostic endpoints: `/test-connection`
4. Check Vercel deployment logs
5. Verify all environment variables are set correctly

---

**Next Steps**: After successful deployment, configure your custom domain and set up monitoring.
