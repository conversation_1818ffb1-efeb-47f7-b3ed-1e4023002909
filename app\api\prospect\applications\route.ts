import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Create admin client to bypass RLS
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    )

    // Get prospect ID for current user
    const { data: prospect, error: prospectError } = await adminClient
      .from('prospects')
      .select('id')
      .eq('user_id', session.user.id)
      .single()

    if (prospectError || !prospect) {
      return NextResponse.json(
        { error: 'Prospect profile not found' },
        { status: 404 }
      )
    }

    // Get applications for this prospect
    const { data: applications, error: applicationsError } = await adminClient
      .from('applications')
      .select(`
        id,
        status,
        submitted_at,
        reviewed_at,
        job_id,
        job_postings!inner(
          id,
          title,
          job_type,
          location,
          bpos!inner(
            id,
            name,
            logo_url
          )
        )
      `)
      .eq('prospect_id', prospect.id)
      .order('submitted_at', { ascending: false })

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError)
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      )
    }

    // Calculate stats
    const stats = {
      total: applications?.length || 0,
      submitted: applications?.filter(app => app.status === 'submitted').length || 0,
      reviewing: applications?.filter(app => app.status === 'reviewing').length || 0,
      accepted: applications?.filter(app => app.status === 'accepted').length || 0,
      rejected: applications?.filter(app => app.status === 'rejected').length || 0,
      interview_scheduled: applications?.filter(app => app.status === 'interview_scheduled').length || 0
    }

    return NextResponse.json({
      success: true,
      applications: applications || [],
      stats
    })

  } catch (error: any) {
    console.error('Prospect applications API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
