'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, AlertCircle, Info } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AssessmentQuestion } from '@/types/assessment';

interface LongAnswerProps {
  question: AssessmentQuestion;
  onComplete: (answer: string) => void;
  timeLimit?: number;
}

export function LongAnswer({ question, onComplete, timeLimit = 600 }: LongAnswerProps) {
  const [answer, setAnswer] = useState('');
  const [timeRemaining, setTimeRemaining] = useState(timeLimit);
  const [started, setStarted] = useState(false);
  const [showGuidelines, setShowGuidelines] = useState(true);
  const [wordCount, setWordCount] = useState(0);
  const [charCount, setCharCount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Start timer when user begins answering
  useEffect(() => {
    if (started) {
      const timer = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            handleSubmit();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      return () => clearInterval(timer);
    }
  }, [started]);

  // Update word and character count
  useEffect(() => {
    const words = answer.trim() ? answer.trim().split(/\s+/).length : 0;
    setWordCount(words);
    setCharCount(answer.length);
  }, [answer]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setAnswer(e.target.value);
    
    // Start timer when the user starts typing
    if (!started && e.target.value.length > 0) {
      setStarted(true);
    }
  };

  const handleSubmit = () => {
    // Validate minimum length (50 words)
    if (wordCount < 50) {
      setError('Your answer must be at least 50 words long.');
      return;
    }

    setError(null);
    onComplete(answer);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const guidelines = [
    "Provide a thorough and detailed response",
    "Use specific examples where appropriate",
    "Consider different perspectives or approaches",
    "Minimum 50 words, recommended 100-300 words",
    "Responses are evaluated on clarity, completeness, and relevance"
  ];

  return (
    <Card className="w-full">
      <CardHeader className="relative">
        <CardTitle>Scenario Question</CardTitle>
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Provide a detailed response to the scenario
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="font-mono">{formatTime(timeRemaining)}</span>
          </div>
        </div>
        {started && (
          <Progress 
            value={(1 - timeRemaining / timeLimit) * 100} 
            className="h-2 mt-2" 
          />
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Question display */}
        <div className="bg-muted p-4 rounded-md text-sm font-medium border">
          {question.question_text}
        </div>
        
        {showGuidelines && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Response Guidelines</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 mt-2 space-y-1 text-sm">
                {guidelines.map((guideline, i) => (
                  <li key={i}>{guideline}</li>
                ))}
              </ul>
              <Button 
                variant="link" 
                className="p-0 h-auto mt-2 text-xs"
                onClick={() => setShowGuidelines(false)}
              >
                Hide Guidelines
              </Button>
            </AlertDescription>
          </Alert>
        )}
        
        <div className="space-y-2">
          <textarea
            className="w-full min-h-[200px] p-3 border rounded-md focus:ring-2 focus:ring-primary"
            placeholder="Type your response here..."
            value={answer}
            onChange={handleChange}
          />
          
          <div className="flex justify-between text-sm">
            <div className={`${wordCount < 50 ? 'text-red-500' : 'text-green-600'}`}>
              Words: {wordCount} {wordCount < 50 ? `(minimum 50)` : ''}
            </div>
            <div>
              Characters: {charCount}
            </div>
          </div>
        </div>
        
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-end">
        <Button onClick={handleSubmit}>
          Submit Response
        </Button>
      </CardFooter>
    </Card>
  );
} 