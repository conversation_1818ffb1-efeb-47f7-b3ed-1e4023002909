/**
 * Security Configuration
 * Environment-specific security settings and configurations
 */

export interface SecurityConfig {
  rateLimiting: {
    enabled: boolean;
    strictMode: boolean;
    customLimits?: Record<string, { windowMs: number; maxRequests: number }>;
  };
  validation: {
    strictValidation: boolean;
    sanitizeInput: boolean;
    validateFileTypes: boolean;
    maxFileSize: number;
  };
  headers: {
    enableSecurityHeaders: boolean;
    enableCors: boolean;
    corsOrigins: string[];
  };
  logging: {
    enableAuditLogging: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    logSensitiveData: boolean;
  };
  authentication: {
    requireAuth: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
  };
}

/**
 * Development environment configuration
 */
const developmentConfig: SecurityConfig = {
  rateLimiting: {
    enabled: process.env.DISABLE_RATE_LIMITING !== 'true',
    strictMode: false,
    customLimits: {
      // Higher limits for development
      DEFAULT: { windowMs: 15 * 60 * 1000, maxRequests: 1000 },
      AUTH: { windowMs: 15 * 60 * 1000, maxRequests: 100 },
      UPLOAD: { windowMs: 60 * 60 * 1000, maxRequests: 100 }
    }
  },
  validation: {
    strictValidation: false,
    sanitizeInput: true,
    validateFileTypes: true,
    maxFileSize: 100 * 1024 * 1024 // 100MB
  },
  headers: {
    enableSecurityHeaders: true,
    enableCors: true,
    corsOrigins: ['http://localhost:3000', 'http://localhost:3001']
  },
  logging: {
    enableAuditLogging: true,
    logLevel: 'debug',
    logSensitiveData: true // OK in development
  },
  authentication: {
    requireAuth: true,
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
    maxLoginAttempts: 10
  }
};

/**
 * Production environment configuration
 */
const productionConfig: SecurityConfig = {
  rateLimiting: {
    enabled: true,
    strictMode: true,
    // Use default production limits from rate-limiting.ts
  },
  validation: {
    strictValidation: true,
    sanitizeInput: true,
    validateFileTypes: true,
    maxFileSize: 50 * 1024 * 1024 // 50MB in production
  },
  headers: {
    enableSecurityHeaders: true,
    enableCors: true,
    corsOrigins: [
      process.env.NEXT_PUBLIC_APP_URL || 'https://luna.platform',
      // Add your production domains here
    ].filter(Boolean)
  },
  logging: {
    enableAuditLogging: true,
    logLevel: 'warn',
    logSensitiveData: false // Never log sensitive data in production
  },
  authentication: {
    requireAuth: true,
    sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
    maxLoginAttempts: 5
  }
};

/**
 * Test environment configuration
 */
const testConfig: SecurityConfig = {
  rateLimiting: {
    enabled: false, // Disable for tests
    strictMode: false
  },
  validation: {
    strictValidation: true,
    sanitizeInput: true,
    validateFileTypes: false, // Allow test files
    maxFileSize: 10 * 1024 * 1024 // 10MB
  },
  headers: {
    enableSecurityHeaders: false, // Simplify tests
    enableCors: true,
    corsOrigins: ['http://localhost:3000']
  },
  logging: {
    enableAuditLogging: false, // Reduce noise in tests
    logLevel: 'error',
    logSensitiveData: false
  },
  authentication: {
    requireAuth: false, // Allow test bypasses
    sessionTimeout: 60 * 60 * 1000, // 1 hour
    maxLoginAttempts: 100
  }
};

/**
 * Get security configuration based on environment
 */
export function getSecurityConfig(): SecurityConfig {
  const env = process.env.NODE_ENV || 'development';
  
  switch (env) {
    case 'production':
      return productionConfig;
    case 'test':
      return testConfig;
    case 'development':
    default:
      return developmentConfig;
  }
}

/**
 * Security feature flags
 */
export const SecurityFeatures = {
  /**
   * Check if rate limiting is enabled
   */
  isRateLimitingEnabled(): boolean {
    return getSecurityConfig().rateLimiting.enabled;
  },

  /**
   * Check if strict validation is enabled
   */
  isStrictValidationEnabled(): boolean {
    return getSecurityConfig().validation.strictValidation;
  },

  /**
   * Check if audit logging is enabled
   */
  isAuditLoggingEnabled(): boolean {
    return getSecurityConfig().logging.enableAuditLogging;
  },

  /**
   * Check if security headers are enabled
   */
  areSecurityHeadersEnabled(): boolean {
    return getSecurityConfig().headers.enableSecurityHeaders;
  },

  /**
   * Get maximum file size for uploads
   */
  getMaxFileSize(): number {
    return getSecurityConfig().validation.maxFileSize;
  },

  /**
   * Get session timeout
   */
  getSessionTimeout(): number {
    return getSecurityConfig().authentication.sessionTimeout;
  },

  /**
   * Get maximum login attempts
   */
  getMaxLoginAttempts(): number {
    return getSecurityConfig().authentication.maxLoginAttempts;
  },

  /**
   * Get CORS origins
   */
  getCorsOrigins(): string[] {
    return getSecurityConfig().headers.corsOrigins;
  }
};

/**
 * Security constants
 */
export const SecurityConstants = {
  // File upload limits
  MAX_FILE_SIZE_BYTES: getSecurityConfig().validation.maxFileSize,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/quicktime'],
  ALLOWED_AUDIO_TYPES: ['audio/mpeg', 'audio/wav', 'audio/ogg'],

  // Rate limiting
  DEFAULT_RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
  DEFAULT_RATE_LIMIT_MAX: 100,

  // Security headers
  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  },

  // Content Security Policy
  CSP_DIRECTIVES: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'https:'],
    'font-src': ["'self'", 'https:'],
    'connect-src': ["'self'", 'https:'],
    'media-src': ["'self'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': []
  }
};

/**
 * Validate security configuration
 */
export function validateSecurityConfig(config: SecurityConfig): boolean {
  try {
    // Basic validation
    if (!config.rateLimiting || !config.validation || !config.headers || !config.logging || !config.authentication) {
      return false;
    }

    // Validate rate limiting
    if (config.rateLimiting.enabled && config.rateLimiting.customLimits) {
      for (const [key, limit] of Object.entries(config.rateLimiting.customLimits)) {
        if (limit.windowMs <= 0 || limit.maxRequests <= 0) {
          console.warn(`Invalid rate limit configuration for ${key}`);
          return false;
        }
      }
    }

    // Validate file size limits
    if (config.validation.maxFileSize <= 0) {
      console.warn('Invalid max file size configuration');
      return false;
    }

    // Validate session timeout
    if (config.authentication.sessionTimeout <= 0) {
      console.warn('Invalid session timeout configuration');
      return false;
    }

    return true;
  } catch (error) {
    console.error('Security configuration validation error:', error);
    return false;
  }
}

// Validate current configuration on module load
const currentConfig = getSecurityConfig();
if (!validateSecurityConfig(currentConfig)) {
  console.error('Invalid security configuration detected!');
}
