import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';
import { requirePlatformAdmin, createAuthErrorResponse } from '@/lib/auth';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Get course with related data
    const { data: course, error: courseError } = await adminClient
      .from('courses')
      .select(`
        *,
        instructor:users!courses_instructor_id_fkey(
          id,
          full_name,
          email
        ),
        course_modules(
          id,
          name,
          description,
          estimated_duration,
          status,
          sequence_order,
          is_standalone,
          learning_objectives,
          course_lessons(
            id,
            name,
            description,
            lesson_type,
            status,
            sequence_order,
            estimated_duration,
            is_mandatory
          )
        ),
        course_prerequisites(
          id,
          prerequisite_type,
          prerequisite_id,
          is_required
        )
      `)
      .eq('id', id)
      .single();

    if (courseError) {
      console.error('Error fetching course:', courseError);
      return NextResponse.json(
        { error: courseError.message || 'Failed to fetch course' },
        { status: 500 }
      );
    }

    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(course);

  } catch (error: any) {
    console.error('Course GET API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Parse request body
    const body = await req.json();
    const {
      name,
      description,
      price,
      preview_video_url,
      cover_image_url,
      level,
      estimated_duration,
      status,
      slug,
      meta_description,
      tags,
      learning_objectives,
      target_audience,
      instructor_id,
      instructor_bio
    } = body;

    // Validate required fields
    if (!name || !description) {
      return NextResponse.json(
        { error: 'Name and description are required' },
        { status: 400 }
      );
    }

    // Create admin client
    const adminClient = createAdminClient();

    // If slug is being updated, check uniqueness
    if (slug) {
      const { data: existingCourse, error: slugError } = await adminClient
        .from('courses')
        .select('id')
        .eq('slug', slug)
        .neq('id', id)
        .single();

      if (slugError && slugError.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error checking slug uniqueness:', slugError);
        return NextResponse.json(
          { error: 'Failed to validate course slug' },
          { status: 500 }
        );
      }

      if (existingCourse) {
        return NextResponse.json(
          { error: 'A course with this slug already exists' },
          { status: 400 }
        );
      }
    }

    // Update course
    const updateData: any = {
      name: name.trim(),
      description: description.trim(),
      level: level || 'beginner',
      status: status || 'draft',
      meta_description: meta_description?.trim(),
      tags: tags || [],
      learning_objectives: learning_objectives || [],
      target_audience: target_audience?.trim(),
      instructor_bio: instructor_bio?.trim(),
      updated_at: new Date().toISOString()
    };

    // Add optional fields if provided
    if (price !== undefined) updateData.price = price ? parseFloat(price) : null;
    if (preview_video_url !== undefined) updateData.preview_video_url = preview_video_url;
    if (cover_image_url !== undefined) updateData.cover_image_url = cover_image_url;
    if (estimated_duration !== undefined) updateData.estimated_duration = estimated_duration ? parseInt(estimated_duration) : null;
    if (slug !== undefined) updateData.slug = slug;
    if (instructor_id !== undefined) updateData.instructor_id = instructor_id;

    const { data: course, error: courseError } = await adminClient
      .from('courses')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        instructor:users!courses_instructor_id_fkey(
          id,
          full_name,
          email
        )
      `)
      .single();

    if (courseError) {
      console.error('Error updating course:', courseError);
      return NextResponse.json(
        { error: courseError.message || 'Failed to update course' },
        { status: 500 }
      );
    }

    if (!course) {
      return NextResponse.json(
        { error: 'Course not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Course updated successfully',
      course
    });

  } catch (error: any) {
    console.error('Course update API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require platform admin access
    const authResult = await requirePlatformAdmin();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { id } = await params;

    // Create admin client
    const adminClient = createAdminClient();

    // Check if course has enrollments
    const { data: enrollments, error: enrollmentError } = await adminClient
      .from('course_enrollments')
      .select('id')
      .eq('course_id', id)
      .limit(1);

    if (enrollmentError) {
      console.error('Error checking enrollments:', enrollmentError);
      return NextResponse.json(
        { error: 'Failed to check course enrollments' },
        { status: 500 }
      );
    }

    if (enrollments && enrollments.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete course with active enrollments. Archive it instead.' },
        { status: 400 }
      );
    }

    // Delete course (cascades to modules, lessons, etc.)
    const { error: deleteError } = await adminClient
      .from('courses')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting course:', deleteError);
      return NextResponse.json(
        { error: deleteError.message || 'Failed to delete course' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Course deleted successfully'
    });

  } catch (error: any) {
    console.error('Course delete API error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred' },
      { status: 500 }
    );
  }
}
