import { useState, useEffect, useRef } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { PasswordInput } from "@/components/ui/password-input"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Bell, Camera, Check, Key, Lock, Mail, Phone, Shield, User, UserCog, Loader2 } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { supabase, supabaseUrl } from "@/lib/supabase"

// Define interfaces for user data
interface ContactInfo {
  phone?: string;
  location?: string;
  address?: string;
}

interface UserData {
  id: string;
  email: string;
  full_name: string;
  role: string;
  contact_info?: ContactInfo;
}

interface NotificationPreferences {
  email_notifications: boolean;
  sms_notifications: boolean;
  browser_notifications: boolean;
  training_reminders: boolean;
  assessment_deadlines: boolean;
  job_matches: boolean;
  interview_reminders: boolean;
}

export function AccountSettingsPage() {
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [isAvatarLoading, setIsAvatarLoading] = useState(false)
  const [isPageLoading, setIsPageLoading] = useState(true)
  const [userData, setUserData] = useState<UserData | null>(null)
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("")
  const [location, setLocation] = useState("us")
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [notifications, setNotifications] = useState<NotificationPreferences>({
    email_notifications: true,
    sms_notifications: false,
    browser_notifications: true,
    training_reminders: true,
    assessment_deadlines: true,
    job_matches: true,
    interview_reminders: true,
  })
  
  // Fetch user data on component mount
  useEffect(() => {
    async function fetchUserData() {
      setIsPageLoading(true)
      try {
        // Get the current authenticated user
        const { data: { user } } = await supabase.auth.getUser()
        
        if (!user) {
          toast({
            title: "Error",
            description: "No authenticated user found.",
            variant: "destructive",
          })
          return
        }
        
        // Fetch user details from the users table
        const { data: userData, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single()

        if (error) {
          console.error("Error fetching user data:", error)
          toast({
            title: "Error",
            description: "Failed to fetch user data.",
            variant: "destructive",
          })
          return
        }

        // Fetch additional user details depending on role
        if (userData.role === 'prospect') {
          const { data: prospectData, error: prospectError } = await supabase
            .from('prospects')
            .select('*')
            .eq('user_id', user.id)
            .single()

          if (!prospectError && prospectData) {
            userData.contact_info = prospectData.contact_info
          }
        }

        setUserData(userData)
        
        // Set form state from user data
        const nameParts = userData.full_name.split(' ')
        setFirstName(nameParts[0] || '')
        setLastName(nameParts.slice(1).join(' ') || '')
        setEmail(userData.email)
        setPhone(userData.contact_info?.phone || '')
        setLocation(userData.contact_info?.location || 'us')
        
        // Fetch avatar image if it exists
        await checkAndSetupStorage()
        await fetchAvatarImage(user.id)
        
      } catch (error) {
        console.error("Error in fetchUserData:", error)
        toast({
          title: "Error",
          description: "An unexpected error occurred.",
          variant: "destructive",
        })
      } finally {
        setIsPageLoading(false)
      }
    }

    fetchUserData()
  }, [toast])
  
  // Check if storage is properly configured
  const checkAndSetupStorage = async () => {
    try {
      // Test if we can list buckets
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets()
      
      if (bucketsError) {
        console.error('Cannot list buckets:', bucketsError)
        // We'll continue anyway as we'll handle this in the avatar functions
        return false
      }
      
      // Check if profile-images bucket exists
      const profileBucketExists = buckets?.some(bucket => bucket.name === 'profile-images')
      
      if (!profileBucketExists) {
        console.log('Profile images bucket does not exist')
        return false
      }
      
      // Test if we can list files in the bucket
      const { error: listError } = await supabase.storage
        .from('profile-images')
        .list('', { limit: 1 })
      
      if (listError) {
        console.error('Cannot list files in profile-images bucket:', listError)
        return false
      }
      
      console.log('Storage is properly configured')
      return true
    } catch (error) {
      console.error('Error checking storage configuration:', error)
      return false
    }
  }
  
  const fetchAvatarImage = async (userId: string) => {
    console.log('========== Fetching avatar for user ID:', userId);
    try {
      // Check if bucket exists first
      const { data: buckets, error: bucketsError } = await supabase
        .storage
        .listBuckets()
      
      console.log('Buckets:', buckets);
      console.log('Buckets error:', bucketsError);
      
      if (bucketsError) {
        console.error('Error listing buckets:', bucketsError);
        setAvatarUrl(null);
        return;
      }
      
      const profileBucketExists = buckets?.some(bucket => bucket.name === 'profile-images')
      
      if (!profileBucketExists) {
        console.log('Profile images bucket does not exist yet');
        setAvatarUrl(null);
        return;
      }
      
      // First check if the file exists by listing the directory
      console.log('Checking if avatar exists in folder:', userId);
      const { data: fileList, error: listError } = await supabase
        .storage
        .from('profile-images')
        .list(userId, { limit: 1, search: 'avatar' })
      
      console.log('File list:', fileList);
      console.log('List error:', listError);
      
      if (listError) {
        console.error('Error listing files:', listError);
        setAvatarUrl(null);
        return;
      }
      
      if (!fileList || fileList.length === 0) {
        console.log('No avatar found for user');
        setAvatarUrl(null);
        return;
      }
      
      // Get the public URL since we know the file exists
      const { data: urlData } = await supabase
        .storage
        .from('profile-images')
        .getPublicUrl(`${userId}/avatar`)
      
      console.log('URL data:', urlData);
      
      if (!urlData || !urlData.publicUrl) {
        console.log('Could not get public URL for avatar');
        setAvatarUrl(null);
        return;
      }
      
      // Add a cache-busting parameter to force refresh when updated
      const publicUrl = urlData.publicUrl + `?t=${new Date().getTime()}`
      console.log('Got avatar URL:', publicUrl);
      
      // Alternative direct URL construction if the above doesn't work
      const directUrl = `${supabaseUrl}/storage/v1/object/public/profile-images/${userId}/avatar?t=${new Date().getTime()}`;
      console.log('Direct URL alternative:', directUrl);
      
      // Try the direct URL if we're in a development environment
      const finalUrl = process.env.NODE_ENV === 'development' ? directUrl : publicUrl;
      console.log('Final URL to use:', finalUrl);
      
      setAvatarUrl(finalUrl);
      
    } catch (error) {
      console.error('Error getting avatar URL:', error);
      setAvatarUrl(null);
    }
  }
  
  const handleAvatarClick = () => {
    fileInputRef.current?.click()
  }
  
  const uploadAvatar = async (file: File) => {
    if (!userData) return
    
    setIsAvatarLoading(true)
    console.log('========== Starting avatar upload for user:', userData.id);
    
    try {
      // Check if bucket exists and create it if needed (requires admin rights)
      const { data: buckets, error: bucketsError } = await supabase
        .storage
        .listBuckets()
      
      console.log('Available buckets:', buckets);
      
      if (bucketsError) {
        console.error('Error listing buckets:', bucketsError);
        throw bucketsError;
      }
      
      const profileBucketExists = buckets?.some(bucket => bucket.name === 'profile-images')
      
      if (!profileBucketExists) {
        // Cannot create bucket from client - show helpful error
        console.error('Profile images bucket does not exist');
        toast({
          title: "Storage not configured",
          description: "The profile images storage hasn't been set up. Please contact the administrator.",
          variant: "destructive",
        })
        setIsAvatarLoading(false)
        return
      }
      
      // Create folder if it doesn't exist
      const folderPath = `${userData.id}`
      
      // Upload image to Supabase Storage
      const fileExt = file.name.split('.').pop()
      const fileName = `avatar` // No need for timestamp in filename as we use the same name to replace
      
      console.log(`Uploading avatar to ${folderPath}/${fileName}`);
      console.log('File size:', file.size, 'bytes');
      console.log('File type:', file.type);
      
      // First try to create the folder explicitly (not always necessary but helps with debugging)
      try {
        const { error: mkdirError } = await supabase
          .storage
          .from('profile-images')
          .upload(`${folderPath}/.folder`, new Blob([''], { type: 'text/plain' }), {
            upsert: true
          });
          
        if (mkdirError && !mkdirError.message.includes('already exists')) {
          console.warn('Could not create folder explicitly:', mkdirError);
          // Continue anyway as the folder will be created with the file upload
        } else {
          console.log('Folder created or already exists');
        }
      } catch (mkdirError) {
        console.warn('Error creating folder:', mkdirError);
        // Continue anyway
      }
      
      // Upload the actual file
      const { error: uploadError } = await supabase
        .storage
        .from('profile-images')
        .upload(`${folderPath}/${fileName}`, file, {
          upsert: true,
          contentType: file.type
        });
        
      if (uploadError) {
        console.error('Upload error:', uploadError);
        throw uploadError;
      }
      
      console.log('Upload successful! Getting URL...');
      
      // Get the URL of the uploaded file to confirm it worked
      const { data: urlData } = await supabase
        .storage
        .from('profile-images')
        .getPublicUrl(`${folderPath}/${fileName}`);
        
      console.log('Uploaded file URL:', urlData?.publicUrl);
      
      // Refresh the avatar image - wait a short time for storage to update
      console.log('Waiting for storage to update before refreshing avatar...');
      setTimeout(() => fetchAvatarImage(userData.id), 1000);
      
      toast({
        title: "Profile picture updated",
        description: "Your profile picture has been updated successfully."
      })
    } catch (error: any) {
      console.error("Error uploading avatar:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update profile picture. Storage may not be configured.",
        variant: "destructive",
      })
    } finally {
      setIsAvatarLoading(false)
    }
  }
  
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return
    
    const file = e.target.files[0]
    if (file.size > 2 * 1024 * 1024) { // 2MB limit
      toast({
        title: "File too large",
        description: "Profile image should be less than 2MB.",
        variant: "destructive",
      })
      return
    }
    
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file.",
        variant: "destructive",
      })
      return
    }
    
    uploadAvatar(file)
  }

  const handleSaveProfile = async () => {
    setIsLoading(true)
    
    try {
      if (!userData) return
      
      // Update the user record
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          full_name: `${firstName} ${lastName}`,
          email: email,
          updated_at: new Date().toISOString()
        })
        .eq('id', userData.id)
      
      if (userUpdateError) throw userUpdateError
      
      // Update contact info based on role
      if (userData.role === 'prospect') {
        const { error: prospectUpdateError } = await supabase
          .from('prospects')
          .update({
            contact_info: {
              ...userData.contact_info,
              phone: phone,
              location: location
            },
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userData.id)
        
        if (prospectUpdateError) throw prospectUpdateError
      }
      
      // Update the auth email if it changed
      if (email !== userData.email) {
        const { error: authUpdateError } = await supabase.auth.updateUser({
          email: email,
        })
        
        if (authUpdateError) throw authUpdateError
      }
      
      // Update userData state with new values
      setUserData({
        ...userData,
        full_name: `${firstName} ${lastName}`,
        email: email,
        contact_info: {
          ...userData.contact_info,
          phone: phone,
          location: location
        }
      })
      
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully."
      })
    } catch (error: any) {
      console.error("Error updating profile:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update profile.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handlePasswordChange = async () => {
    setIsLoading(true)
    
    try {
      // Validate password inputs
      if (newPassword !== confirmPassword) {
        toast({
          title: "Error",
          description: "New passwords don't match.",
          variant: "destructive",
        })
        return
      }
      
      if (newPassword.length < 8) {
        toast({
          title: "Error",
          description: "Password should be at least 8 characters long.",
          variant: "destructive",
        })
        return
      }
      
      // Update password in Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })
      
      if (error) throw error
      
      // Clear password fields
      setCurrentPassword("")
      setNewPassword("")
      setConfirmPassword("")
      
      toast({
        title: "Password updated",
        description: "Your password has been changed successfully."
      })
    } catch (error: any) {
      console.error("Error changing password:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update password.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveNotifications = async () => {
    setIsLoading(true)
    
    try {
      // Here you would update notification preferences in your database
      // This is a placeholder for when you add notification preferences to your schema
      
      toast({
        title: "Notifications updated",
        description: "Your notification preferences have been saved."
      })
    } catch (error: any) {
      console.error("Error saving notifications:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update notification preferences.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeactivateAccount = async () => {
    // Implement account deactivation logic
    if (confirm("Are you sure you want to deactivate your account? You can reactivate it at any time by logging in.")) {
      setIsLoading(true)
      
      try {
        if (!userData) return
        
        const { error } = await supabase
          .from('users')
          .update({
            status: 'inactive',
            updated_at: new Date().toISOString()
          })
          .eq('id', userData.id)
        
        if (error) throw error
        
        toast({
          title: "Account deactivated",
          description: "Your account has been deactivated. You can reactivate it by logging in again."
        })
        
        // Sign out the user
        await supabase.auth.signOut()
        
        // Redirect to login page
        window.location.href = '/login'
      } catch (error: any) {
        console.error("Error deactivating account:", error)
        toast({
          title: "Error",
          description: error.message || "Failed to deactivate account.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }
  }

  // If page is loading, show loading state
  if (isPageLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
        <p className="ml-2 text-lg text-muted-foreground">Loading your account settings...</p>
      </div>
    )
  }

  // If no user data loaded, show error state
  if (!userData) {
    return (
      <div className="p-4 border border-red-200 rounded-md">
        <h2 className="text-lg font-medium text-red-800">Error loading profile</h2>
        <p className="text-sm text-red-600">Unable to load your account information. Please try refreshing the page.</p>
        <Button 
          variant="outline" 
          className="mt-2"
          onClick={() => window.location.reload()}
        >
          Refresh Page
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Account Settings</h1>
        <p className="text-muted-foreground">Manage your personal account settings and preferences</p>
      </div>
      
      <div className="flex flex-col md:flex-row gap-6">
        <div className="md:w-1/3">
          <Card className="border-none shadow-sm overflow-hidden">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-24 relative"></div>
            <div className="px-6 pb-6">
              <div className="flex justify-center -mt-12">
                <Avatar className="h-24 w-24 border-4 border-white dark:border-gray-900 relative">
                  {avatarUrl ? (
                    <AvatarImage src={avatarUrl} alt="Profile" className="object-cover" />
                  ) : (
                    <AvatarFallback className="text-2xl bg-blue-100 text-blue-700">
                      {firstName.charAt(0)}{lastName.charAt(0)}
                    </AvatarFallback>
                  )}
                  {isAvatarLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full">
                      <Loader2 className="h-8 w-8 animate-spin text-white" />
                    </div>
                  )}
                </Avatar>
                <input 
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarChange}
                />
                <Button
                  size="icon"
                  variant="ghost"
                  className="absolute bottom-12 right-1/2 translate-x-8 h-8 w-8 rounded-full bg-white shadow"
                  onClick={handleAvatarClick}
                  disabled={isAvatarLoading}
                >
                  {isAvatarLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Camera className="h-4 w-4" />
                  )}
                  <span className="sr-only">Change profile picture</span>
                </Button>
              </div>

              <div className="text-center mt-4">
                <h2 className="text-xl font-semibold">{userData.full_name}</h2>
                <p className="text-sm text-muted-foreground">{userData.email}</p>
              </div>
            </div>
          </Card>
        </div>

        <div className="flex-1">
          <Tabs defaultValue="profile">
            <TabsList className="w-full">
              <TabsTrigger value="profile" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                <span>Profile</span>
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span>Security</span>
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                <span>Notifications</span>
              </TabsTrigger>
              <TabsTrigger value="account" className="flex items-center gap-2">
                <UserCog className="h-4 w-4" />
                <span>Account</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>Update your personal details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="first-name">First Name</Label>
                      <Input 
                        id="first-name" 
                        value={firstName}
                        onChange={e => setFirstName(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last-name">Last Name</Label>
                      <Input 
                        id="last-name" 
                        value={lastName}
                        onChange={e => setLastName(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <Input 
                        id="email" 
                        type="email"
                        value={email} 
                        onChange={e => setEmail(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <Input 
                        id="phone" 
                        value={phone}
                        onChange={e => setPhone(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Location</Label>
                    <Select 
                      value={location}
                      onValueChange={setLocation}
                    >
                      <SelectTrigger id="location">
                        <SelectValue placeholder="Select location" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="us">United States</SelectItem>
                        <SelectItem value="ca">Canada</SelectItem>
                        <SelectItem value="uk">United Kingdom</SelectItem>
                        <SelectItem value="au">Australia</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handleSaveProfile} disabled={isLoading}>
                    {isLoading ? "Saving..." : "Save Changes"}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Change Password</CardTitle>
                  <CardDescription>Update your password to keep your account secure</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="current-password">Current Password</Label>
                    <div className="flex items-center gap-2">
                      <Lock className="h-4 w-4 text-muted-foreground" />
                      <PasswordInput 
                        id="current-password"
                        value={currentPassword}
                        onChange={e => setCurrentPassword(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <div className="flex items-center gap-2">
                      <Key className="h-4 w-4 text-muted-foreground" />
                      <PasswordInput 
                        id="new-password"
                        value={newPassword}
                        onChange={e => setNewPassword(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <div className="flex items-center gap-2">
                      <Key className="h-4 w-4 text-muted-foreground" />
                      <PasswordInput 
                        id="confirm-password"
                        value={confirmPassword}
                        onChange={e => setConfirmPassword(e.target.value)}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handlePasswordChange} disabled={isLoading}>
                    {isLoading ? "Updating..." : "Update Password"}
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Two-Factor Authentication</CardTitle>
                  <CardDescription>Add an extra layer of security to your account</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Lock className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Enable Two-Factor Authentication</p>
                        <p className="text-sm text-muted-foreground">Require a verification code when logging in</p>
                      </div>
                    </div>
                    <Switch />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline">Set Up Two-Factor Authentication</Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                  <CardDescription>Choose how you want to be notified</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Email Notifications</p>
                        <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                      </div>
                    </div>
                    <Switch 
                      checked={notifications.email_notifications}
                      onCheckedChange={(checked) => 
                        setNotifications({...notifications, email_notifications: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">SMS Notifications</p>
                        <p className="text-sm text-muted-foreground">Receive notifications via text message</p>
                      </div>
                    </div>
                    <Switch 
                      checked={notifications.sms_notifications}
                      onCheckedChange={(checked) => 
                        setNotifications({...notifications, sms_notifications: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="font-medium">Browser Notifications</p>
                        <p className="text-sm text-muted-foreground">Receive notifications in your browser</p>
                      </div>
                    </div>
                    <Switch 
                      checked={notifications.browser_notifications}
                      onCheckedChange={(checked) => 
                        setNotifications({...notifications, browser_notifications: checked})
                      }
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notification Types</CardTitle>
                  <CardDescription>Select which types of notifications you want to receive</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Training Reminders</p>
                      <p className="text-sm text-muted-foreground">Reminders about upcoming training sessions</p>
                    </div>
                    <Switch 
                      checked={notifications.training_reminders}
                      onCheckedChange={(checked) => 
                        setNotifications({...notifications, training_reminders: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Assessment Deadlines</p>
                      <p className="text-sm text-muted-foreground">Notifications about upcoming assessment deadlines</p>
                    </div>
                    <Switch 
                      checked={notifications.assessment_deadlines}
                      onCheckedChange={(checked) => 
                        setNotifications({...notifications, assessment_deadlines: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">New Job Matches</p>
                      <p className="text-sm text-muted-foreground">Notifications about new job opportunities</p>
                    </div>
                    <Switch 
                      checked={notifications.job_matches}
                      onCheckedChange={(checked) => 
                        setNotifications({...notifications, job_matches: checked})
                      }
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">Interview Reminders</p>
                      <p className="text-sm text-muted-foreground">Reminders about upcoming interviews</p>
                    </div>
                    <Switch 
                      checked={notifications.interview_reminders}
                      onCheckedChange={(checked) => 
                        setNotifications({...notifications, interview_reminders: checked})
                      }
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button onClick={handleSaveNotifications} disabled={isLoading}>
                    {isLoading ? "Saving..." : "Save Preferences"}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="account" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Account Management</CardTitle>
                  <CardDescription>Manage your account settings and data</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="rounded-lg border p-4">
                    <h3 className="font-medium">Export Your Data</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      Download a copy of all your personal data, including profile information, 
                      training progress, and assessment results.
                    </p>
                    <Button variant="outline" className="mt-3">
                      Export Data
                    </Button>
                  </div>
                  
                  <div className="rounded-lg border border-red-200 p-4">
                    <h3 className="font-medium text-red-700">Deactivate Account</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      Temporarily deactivate your account. You can reactivate it at any time by logging in.
                    </p>
                    <Button
                      variant="outline"
                      className="mt-3 border-red-200 text-red-700 hover:bg-red-50 hover:text-red-800"
                      onClick={handleDeactivateAccount}
                      disabled={isLoading}
                    >
                      {isLoading ? "Processing..." : "Deactivate Account"}
                    </Button>
                  </div>
                  
                  <div className="rounded-lg border border-red-200 p-4">
                    <h3 className="font-medium text-red-700">Delete Account</h3>
                    <p className="mt-2 text-sm text-muted-foreground">
                      Permanently delete your account and all associated data. This action cannot be undone.
                    </p>
                    <Button
                      variant="outline"
                      className="mt-3 border-red-200 text-red-700 hover:bg-red-50 hover:text-red-800"
                    >
                      Delete Account
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
      <Toaster />
    </div>
  )
} 