"use client";

import { useState, useRef, useEffect } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Upload, Video, StopCircle, RotateCcw, Play, Pause } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface VideoUploadDialogProps {
  triggerComponent?: React.ReactNode
  buttonText?: string
  onSuccess?: (url: string) => void
}

export function VideoUploadDialog({
  triggerComponent,
  buttonText,
  onSuccess
}: VideoUploadDialogProps) {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<string>("upload")
  const [isLoading, setIsLoading] = useState(false)
  const [videoFile, setVideoFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const [isRecording, setIsRecording] = useState(false)
  const [recordedChunks, setRecordedChunks] = useState<Blob[]>([])
  const [recordingTime, setRecordingTime] = useState(0)
  const [recordingTimer, setRecordingTimer] = useState<NodeJS.Timeout | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const videoPreviewRef = useRef<HTMLVideoElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const router = useRouter()
  
  useEffect(() => {
    // Clean up when component unmounts
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
      }
      stopMediaStream()
      if (recordingTimer) {
        clearInterval(recordingTimer)
      }
    }
  }, [previewUrl, recordingTimer])
  
  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    
    // Reset state when changing tabs
    resetState()
    
    // If switching to record tab, request camera access
    if (value === "record") {
      requestCameraAccess()
    } else {
      stopMediaStream()
    }
  }
  
  // Reset all state
  const resetState = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
    }
    setVideoFile(null)
    setPreviewUrl(null)
    setRecordedChunks([])
    setRecordingTime(0)
    setIsRecording(false)
    setIsPlaying(false)
    setUploadProgress(0)
    
    if (recordingTimer) {
      clearInterval(recordingTimer)
      setRecordingTimer(null)
    }
  }
  
  // Stop media stream
  const stopMediaStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
  }
  
  // Request camera access
  const requestCameraAccess = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      })
      
      streamRef.current = stream
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
    } catch (error) {
      console.error("Error accessing camera:", error)
      toast.error("Unable to access camera and microphone. Please check permissions.")
      setActiveTab("upload")
    }
  }
  
  // Handle file selection for upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      
      // Check if file is a video
      if (!file.type.startsWith("video/")) {
        toast.error("Please select a video file")
        return
      }
      
      // Check file size (20MB max)
      if (file.size > 20 * 1024 * 1024) {
        toast.error("Video must be smaller than 20MB")
        return
      }
      
      setVideoFile(file)
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
    }
  }
  
  // Open file selector
  const openFileSelector = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }
  
  // Start recording
  const startRecording = () => {
    if (!streamRef.current) return
    
    try {
      const mediaRecorder = new MediaRecorder(streamRef.current, {
        mimeType: 'video/webm;codecs=vp9,opus'
      })
      
      mediaRecorderRef.current = mediaRecorder
      
      const chunks: Blob[] = []
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunks.push(e.data)
        }
      }
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'video/webm' })
        const url = URL.createObjectURL(blob)
        setPreviewUrl(url)
        setRecordedChunks(chunks)
        
        if (videoPreviewRef.current) {
          videoPreviewRef.current.src = url
        }
      }
      
      // Start recording
      mediaRecorder.start(1000) // Collect data in 1-second chunks
      setIsRecording(true)
      
      // Start timer
      const timer = setInterval(() => {
        setRecordingTime(prev => prev + 1)
      }, 1000)
      
      setRecordingTimer(timer)
      
      // Automatically stop after 60 seconds
      setTimeout(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
          stopRecording()
        }
      }, 60000) // 60 seconds
      
    } catch (error) {
      console.error("Error starting recording:", error)
      toast.error("Failed to start recording")
    }
  }
  
  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
      mediaRecorderRef.current.stop()
      setIsRecording(false)
      
      if (recordingTimer) {
        clearInterval(recordingTimer)
        setRecordingTimer(null)
      }
    }
  }
  
  // Reset recording
  const resetRecording = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
    }
    
    setPreviewUrl(null)
    setRecordedChunks([])
    setRecordingTime(0)
    
    if (streamRef.current && videoRef.current) {
      videoRef.current.srcObject = streamRef.current
    }
  }
  
  // Format time for display (mm:ss)
  const formatTime = (timeInSeconds: number) => {
    const minutes = Math.floor(timeInSeconds / 60)
    const seconds = timeInSeconds % 60
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }
  
  // Toggle preview playback
  const togglePlayback = () => {
    if (videoPreviewRef.current) {
      if (isPlaying) {
        videoPreviewRef.current.pause()
      } else {
        videoPreviewRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }
  
  // Handle video playback events
  const handleVideoEvents = () => {
    if (videoPreviewRef.current) {
      videoPreviewRef.current.onplay = () => setIsPlaying(true)
      videoPreviewRef.current.onpause = () => setIsPlaying(false)
      videoPreviewRef.current.onended = () => setIsPlaying(false)
    }
  }
  
  // Submit video (upload recorded or selected file)
  const onSubmit = async () => {
    // For upload tab, use the selected file
    let videoToUpload: File | Blob | null = videoFile
    
    // For record tab, create a file from recorded chunks
    if (activeTab === "record" && recordedChunks.length > 0) {
      const blob = new Blob(recordedChunks, { type: 'video/webm' })
      videoToUpload = blob
    }
    
    if (!videoToUpload) {
      toast.error("No video to upload")
      return
    }
    
    try {
      setIsLoading(true)
      
      const formData = new FormData()
      
      // If blob from recording, create a file object
      if (!(videoToUpload instanceof File)) {
        const fileName = `recorded-video-${new Date().getTime()}.webm`
        videoToUpload = new File([videoToUpload], fileName, { type: 'video/webm' })
      }
      
      // TypeScript type assertion - at this point videoToUpload is guaranteed to be a File
      const fileToUpload = videoToUpload as File
      
      console.log("Preparing to upload video:", {
        name: fileToUpload.name,
        type: fileToUpload.type,
        size: `${(fileToUpload.size / (1024 * 1024)).toFixed(2)} MB`
      })
      
      formData.append("file", fileToUpload)
      formData.append("fileType", "video")
      
      // Use fetch with ReadableStream for upload progress
      const uploadWithProgress = async () => {
        try {
          // Set a timeout for the fetch operation
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 120000); // 2 minute timeout
          
          console.log("Starting upload with video details:", {
            name: fileToUpload.name,
            type: fileToUpload.type,
            size: `${(fileToUpload.size / (1024 * 1024)).toFixed(2)} MB`,
            formData: Object.fromEntries(formData.entries())
          });
          
          // Upload the video
          const response = await fetch("/api/profile/upload-file", {
            method: "POST",
            body: formData,
            credentials: "include",
            signal: controller.signal,
            // Add explicit headers for clarity
            headers: {
              // Let the browser set the correct Content-Type with boundary for FormData
            }
          }).catch(error => {
            if (error.name === 'AbortError') {
              console.error("Upload aborted due to timeout");
              throw new Error('Upload timeout after 2 minutes. Try uploading a smaller video or check your connection.');
            }
            console.error("Network error during upload:", error);
            throw new Error(`Network error: ${error.message}`);
          });
          
          // Clear the timeout
          clearTimeout(timeoutId);
          
          // Log response status and headers for debugging
          console.log("Upload response:", {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries())
          });
          
          if (!response.ok) {
            let errorMessage = `Server returned ${response.status}: ${response.statusText}`;
            
            try {
              const errorData = await response.json();
              errorMessage = errorData.error || errorMessage;
              console.error("Upload failed:", errorData);
            } catch (parseError) {
              console.error("Error parsing error response:", parseError);
              // Try to get text response if JSON parsing fails
              try {
                const textResponse = await response.text();
                console.error("Raw error response:", textResponse);
              } catch (textError) {
                console.error("Could not get response text:", textError);
              }
            }
            
            throw new Error(errorMessage);
          }
          
          const result = await response.json();
          console.log("Upload succeeded:", result);
          
          // Show success toast and close dialog
          toast.success(result.message || "Video uploaded successfully");
          setOpen(false);
          
          // Reset form
          resetState();
          
          // Callback or refresh
          if (onSuccess && result.url) {
            onSuccess(result.url);
          } else {
            router.refresh();
          }
        } catch (error: any) {
          console.error("Upload error:", error);
          toast.error(error.message || "An error occurred during upload");
          setIsLoading(false);
        }
      }
      
      // Start a timer to simulate progress since fetch doesn't have progress events
      let progress = 0
      const progressInterval = setInterval(() => {
        // Simulate upload progress - increase slowly at first, then faster
        if (progress < 90) {
          const increment = progress < 30 ? 2 : progress < 60 ? 5 : 3
          progress = Math.min(90, progress + increment)
          setUploadProgress(progress)
        }
      }, 500)
      
      try {
        await uploadWithProgress()
        
        // If successful, set to 100%
        setUploadProgress(100)
      } finally {
        clearInterval(progressInterval)
        setIsLoading(false)
      }
    } catch (error: any) {
      console.error("Upload preparation error:", error);
      toast.error(error.message || "An error occurred");
      setIsLoading(false);
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      setOpen(isOpen)
      if (!isOpen) {
        resetState()
        stopMediaStream()
      }
    }}>
      <DialogTrigger asChild>
        {triggerComponent || (
          <Button>
            <Video className="mr-2 h-4 w-4" />
            {buttonText || "Add Video"}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Introduction Video</DialogTitle>
          <DialogDescription>
            Upload or record a 30-60 second video introducing yourself to potential employers
          </DialogDescription>
        </DialogHeader>
        
        <Tabs defaultValue="upload" value={activeTab} onValueChange={handleTabChange} className="my-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Upload</TabsTrigger>
            <TabsTrigger value="record">Record</TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload" className="pt-4">
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="video/*"
              onChange={handleFileChange}
            />
            
            {!previewUrl ? (
              <div 
                className="border-2 border-dashed rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer hover:border-primary/50 transition"
                onClick={openFileSelector}
              >
                <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <Video className="h-8 w-8 text-primary" />
                </div>
                <p className="font-medium text-sm">Select a video file</p>
                <p className="text-xs text-muted-foreground mt-1">
                  or drag and drop it here
                </p>
                <p className="text-xs text-muted-foreground mt-4">
                  MP4, WebM or MOV up to 20MB
                </p>
                <p className="text-xs text-muted-foreground">
                  Recommended length: 30-60 seconds
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="aspect-video bg-black rounded-lg overflow-hidden">
                  <video 
                    src={previewUrl}
                    controls
                    className="w-full h-full"
                    ref={videoPreviewRef}
                    onLoadedMetadata={handleVideoEvents}
                  />
                </div>
                <div className="flex justify-between items-center">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      if (previewUrl) {
                        URL.revokeObjectURL(previewUrl)
                      }
                      setVideoFile(null)
                      setPreviewUrl(null)
                    }}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Choose Different Video
                  </Button>
                  <p className="text-xs text-muted-foreground">
                    {videoFile && `${videoFile.name} (${Math.round(videoFile.size / (1024 * 1024) * 10) / 10} MB)`}
                  </p>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="record" className="pt-4">
            <div className="space-y-4">
              {!previewUrl ? (
                <div className="aspect-video bg-black rounded-lg overflow-hidden relative">
                  <video 
                    ref={videoRef}
                    autoPlay 
                    muted 
                    playsInline
                    className="w-full h-full"
                  />
                  {isRecording && (
                    <>
                      <div className="absolute top-4 right-4 bg-black/70 px-2 py-1 rounded-md text-white text-sm flex items-center">
                        <span className="h-2 w-2 rounded-full bg-red-500 mr-2 animate-pulse" />
                        {formatTime(recordingTime)}
                      </div>
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gray-200">
                        <div 
                          className="h-full bg-red-500" 
                          style={{ width: `${Math.min(recordingTime / 60, 1) * 100}%` }}
                        />
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <div className="aspect-video bg-black rounded-lg overflow-hidden relative">
                  <video 
                    ref={videoPreviewRef}
                    className="w-full h-full"
                    onLoadedMetadata={handleVideoEvents}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Button 
                      variant="outline" 
                      size="icon" 
                      className="rounded-full bg-black/50 backdrop-blur border-0 text-white hover:bg-black/70"
                      onClick={togglePlayback}
                    >
                      {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                    </Button>
                  </div>
                </div>
              )}
              
              <div className="flex justify-between items-center">
                {!previewUrl ? (
                  isRecording ? (
                    <Button 
                      variant="destructive" 
                      onClick={stopRecording}
                      disabled={recordingTime < 3} // Require at least 3 seconds
                    >
                      <StopCircle className="h-4 w-4 mr-2" />
                      Stop Recording
                    </Button>
                  ) : (
                    <Button 
                      onClick={startRecording}
                    >
                      <Video className="h-4 w-4 mr-2" />
                      Start Recording
                    </Button>
                  )
                ) : (
                  <Button 
                    variant="outline" 
                    onClick={resetRecording}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Record Again
                  </Button>
                )}
                
                <p className="text-xs text-muted-foreground">
                  {previewUrl ? `${formatTime(recordingTime)} recorded` : "Max 60 seconds"}
                </p>
              </div>
              
              <div className="text-xs text-muted-foreground space-y-1">
                <p>Tips for a great introduction:</p>
                <ul className="list-disc pl-4 space-y-1">
                  <li>Frame yourself well (head and shoulders visible)</li>
                  <li>Use good lighting (face clearly visible)</li>
                  <li>Speak clearly and directly to the camera</li>
                  <li>Mention your name, experience, and key skills</li>
                </ul>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        {isLoading && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Uploading video...</p>
            <Progress value={uploadProgress} className="h-2" />
            <p className="text-xs text-muted-foreground text-center">{uploadProgress}%</p>
          </div>
        )}
        
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" disabled={isLoading}>
              Cancel
            </Button>
          </DialogClose>
          <Button 
            type="button" 
            onClick={onSubmit} 
            disabled={
              isLoading || 
              (activeTab === "upload" && !videoFile) || 
              (activeTab === "record" && (!previewUrl || recordedChunks.length === 0))
            }
          >
            {isLoading ? "Uploading..." : "Upload Video"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 