"use client";

import { useState, useEffect } from "react";
import { 
  Eye, EyeOff, Edit, Plus, Search, AlertCircle,
  Clock, CheckCircle, FileText, BookOpen, ArrowUpDown, 
  Trash2, BarChart3, Filter, MoreHorizontal
} from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { Assessment, AssessmentStats, AssessmentInsert, AssessmentUpdate } from '@/types/assessment';
import { 
  getAllAssessments, 
  getAssessmentStats, 
  toggleAssessmentVisibility, 
  createAssessment,
  updateAssessment,
  deleteAssessment
} from '@/lib/api/assessments';

export default function AssessmentsAdminPage() {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [stats, setStats] = useState<AssessmentStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<'title' | 'category' | 'completions'>('title');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filter, setFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'Technical' | 'Soft Skills'>('all');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentAssessment, setCurrentAssessment] = useState<Assessment | null>(null);
  const [newAssessment, setNewAssessment] = useState<AssessmentInsert>({
    title: '',
    description: '',
    category: 'Technical',
    duration: '',
    instructions: '',
    total_questions: 0,
    passing_score: 0,
    what_it_checks: '',
    what_to_expect: '',
    is_active: true
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    async function loadData() {
      try {
        const [assessmentsData, statsData] = await Promise.all([
          getAllAssessments(),
          getAssessmentStats()
        ]);
        
        setAssessments(assessmentsData);
        setStats(statsData);
      } catch (err) {
        setError('Failed to load assessments. Please try again later.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, []);

  const toggleVisibility = async (id: string, currentState: boolean) => {
    try {
      await toggleAssessmentVisibility(id, !currentState);
      
      // Update the local state
      setAssessments(prev => 
        prev.map(assessment => 
          assessment.id === id 
            ? { ...assessment, is_active: !currentState } 
            : assessment
        )
      );
      
      // Refresh stats
      const statsData = await getAssessmentStats();
      setStats(statsData);
    } catch (err) {
      console.error('Failed to toggle assessment visibility:', err);
    }
  };
  
  const handleCreate = async () => {
    setIsSubmitting(true);
    try {
      const created = await createAssessment(newAssessment);
      setAssessments(prev => [created, ...prev]);
      
      // Reset form
      setNewAssessment({
        title: '',
        description: '',
        category: 'Technical',
        duration: '',
        instructions: '',
        total_questions: 0,
        passing_score: 0,
        what_it_checks: '',
        what_to_expect: '',
        is_active: true
      });
      
      // Refresh stats
      const statsData = await getAssessmentStats();
      setStats(statsData);
      
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Failed to create assessment:', err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleEdit = async () => {
    if (!currentAssessment) return;
    
    setIsSubmitting(true);
    try {
      const updated = await updateAssessment(currentAssessment.id, currentAssessment);
      setAssessments(prev => 
        prev.map(assessment => 
          assessment.id === updated.id ? updated : assessment
        )
      );
      
      // Refresh stats
      const statsData = await getAssessmentStats();
      setStats(statsData);
      
      setEditDialogOpen(false);
    } catch (err) {
      console.error('Failed to update assessment:', err);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDelete = async () => {
    if (!currentAssessment) return;
    
    setIsSubmitting(true);
    try {
      await deleteAssessment(currentAssessment.id);
      setAssessments(prev => 
        prev.filter(assessment => assessment.id !== currentAssessment.id)
      );
      
      // Refresh stats
      const statsData = await getAssessmentStats();
      setStats(statsData);
      
      setDeleteDialogOpen(false);
    } catch (err) {
      console.error('Failed to delete assessment:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter and sort assessments
  const filteredAssessments = assessments
    .filter(assessment => {
      // Text search
      const matchesSearch = assessment.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           assessment.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Status filter
      const matchesStatusFilter = filter === 'all' || 
                                 (filter === 'active' && assessment.is_active) ||
                                 (filter === 'inactive' && !assessment.is_active);
      
      // Category filter
      const matchesCategoryFilter = categoryFilter === 'all' || 
                                   assessment.category === categoryFilter;
      
      return matchesSearch && matchesStatusFilter && matchesCategoryFilter;
    })
    .sort((a, b) => {
      // Sort by selected column
      if (sortBy === 'title') {
        return sortDirection === 'asc' 
          ? a.title.localeCompare(b.title)
          : b.title.localeCompare(a.title);
      } else if (sortBy === 'category') {
        return sortDirection === 'asc'
          ? a.category.localeCompare(b.category)
          : b.category.localeCompare(a.category);
      } else if (sortBy === 'completions') {
        return sortDirection === 'asc'
          ? a.completions - b.completions
          : b.completions - a.completions;
      }
      return 0;
    });

  const handleSort = (column: 'title' | 'category' | 'completions') => {
    if (sortBy === column) {
      // Toggle direction if already sorting by this column
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort column and default to ascending
      setSortBy(column);
      setSortDirection('asc');
    }
  };

  if (loading) {
    return <AdminAssessmentsSkeleton />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Assessments</h1>
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Create Assessment
        </Button>
      </div>

      {/* Stats Row */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-4">
          <StatsCard
            title="Total Assessments"
            value={stats.total}
            icon={<FileText className="h-4 w-4 text-blue-500" />}
          />
          <StatsCard
            title="Active Assessments"
            value={stats.active}
            icon={<CheckCircle className="h-4 w-4 text-green-500" />}
          />
          <StatsCard
            title="Total Completions"
            value={stats.totalCompletions}
            icon={<BarChart3 className="h-4 w-4 text-purple-500" />}
          />
          <StatsCard
            title="Avg. Score"
            value={`${stats.avgScore}%`}
            icon={<BookOpen className="h-4 w-4 text-amber-500" />}
          />
        </div>
      )}
      
      {/* Management Card */}
      <Card>
        <CardHeader>
          <CardTitle>Assessment Library</CardTitle>
          <CardDescription>Manage your organization's assessments</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="sm:flex-1 relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search assessments..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Filter className="h-4 w-4" /> Filters
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[200px]">
                <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <div className="p-2">
                  <Label htmlFor="status-filter" className="text-xs">Status</Label>
                  <Select
                    value={filter}
                    onValueChange={(value) => setFilter(value as 'all' | 'active' | 'inactive')}
                  >
                    <SelectTrigger id="status-filter" className="w-full mt-1">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="p-2">
                  <Label htmlFor="category-filter" className="text-xs">Category</Label>
                  <Select
                    value={categoryFilter}
                    onValueChange={(value) => setCategoryFilter(value as 'all' | 'Technical' | 'Soft Skills')}
                  >
                    <SelectTrigger id="category-filter" className="w-full mt-1">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="Technical">Technical</SelectItem>
                      <SelectItem value="Soft Skills">Soft Skills</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          
          <div className="rounded-md border">
            <div className="min-w-full divide-y divide-gray-200">
              <div className="bg-muted">
                <div className="grid grid-cols-12 gap-2 px-4 py-3 text-left text-sm font-semibold">
                  <div className="col-span-5 flex items-center cursor-pointer" onClick={() => handleSort('title')}>
                    Title
                    {sortBy === 'title' && (
                      <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                  <div className="col-span-2 flex items-center cursor-pointer" onClick={() => handleSort('category')}>
                    Category
                    {sortBy === 'category' && (
                      <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                  <div className="col-span-1 text-center">Duration</div>
                  <div className="col-span-1 text-center cursor-pointer" onClick={() => handleSort('completions')}>
                    Completions
                    {sortBy === 'completions' && (
                      <span className="ml-1">{sortDirection === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                  <div className="col-span-1 text-center">Status</div>
                  <div className="col-span-2 text-right">Actions</div>
                </div>
              </div>
              <div className="divide-y divide-gray-200 bg-background">
                {filteredAssessments.length === 0 ? (
                  <div className="px-4 py-8 text-center text-sm text-muted-foreground">
                    No assessments match your search
                  </div>
                ) : (
                  filteredAssessments.map((assessment) => (
                    <div key={assessment.id} className="grid grid-cols-12 gap-2 px-4 py-3 text-sm">
                      <div className="col-span-5">
                        <div className="font-medium">{assessment.title}</div>
                        <div className="text-xs text-muted-foreground truncate max-w-xs">
                          {assessment.description}
                        </div>
                      </div>
                      <div className="col-span-2">
                        <Badge variant={assessment.category === 'Technical' ? 'default' : 'secondary'}>
                          {assessment.category}
                        </Badge>
                      </div>
                      <div className="col-span-1 text-center">{assessment.duration}</div>
                      <div className="col-span-1 text-center">
                        {assessment.completions}
                        {assessment.avg_score > 0 && (
                          <div className="text-xs text-muted-foreground">
                            Avg: {assessment.avg_score}%
                          </div>
                        )}
                      </div>
                      <div className="col-span-1 text-center">
                        <Badge variant={assessment.is_active ? 'success' : 'outline'}>
                          {assessment.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                      <div className="col-span-2 flex justify-end gap-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem 
                              onClick={() => {
                                setCurrentAssessment(assessment);
                                setEditDialogOpen(true);
                              }}
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => toggleVisibility(assessment.id, assessment.is_active)}
                            >
                              {assessment.is_active ? (
                                <>
                                  <EyeOff className="mr-2 h-4 w-4" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Eye className="mr-2 h-4 w-4" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={() => {
                                setCurrentAssessment(assessment);
                                setDeleteDialogOpen(true);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Create Assessment Dialog */}
      <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Assessment</DialogTitle>
            <DialogDescription>
              Create a new assessment for prospects to complete. All fields are required.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2">
                <Label htmlFor="title">Assessment Title</Label>
                <Input
                  id="title"
                  placeholder="e.g., Technical Skills Assessment"
                  value={newAssessment.title}
                  onChange={(e) => setNewAssessment({...newAssessment, title: e.target.value})}
                  className="mt-1"
                />
              </div>
              
              <div className="col-span-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Briefly describe what this assessment evaluates"
                  value={newAssessment.description}
                  onChange={(e) => setNewAssessment({...newAssessment, description: e.target.value})}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={newAssessment.category}
                  onValueChange={(value) => setNewAssessment({
                    ...newAssessment, 
                    category: value as 'Technical' | 'Soft Skills'
                  })}
                >
                  <SelectTrigger id="category" className="mt-1">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Technical">Technical</SelectItem>
                    <SelectItem value="Soft Skills">Soft Skills</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="duration">Duration</Label>
                <Input
                  id="duration"
                  placeholder="e.g., 30 minutes"
                  value={newAssessment.duration}
                  onChange={(e) => setNewAssessment({...newAssessment, duration: e.target.value})}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="total_questions">Total Questions</Label>
                <Input
                  id="total_questions"
                  type="number"
                  placeholder="e.g., 20"
                  value={newAssessment.total_questions.toString()}
                  onChange={(e) => setNewAssessment({
                    ...newAssessment, 
                    total_questions: parseInt(e.target.value) || 0
                  })}
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="passing_score">Passing Score (%)</Label>
                <Input
                  id="passing_score"
                  type="number"
                  placeholder="e.g., 70"
                  value={newAssessment.passing_score.toString()}
                  onChange={(e) => setNewAssessment({
                    ...newAssessment, 
                    passing_score: parseInt(e.target.value) || 0
                  })}
                  className="mt-1"
                />
              </div>
              
              <div className="col-span-2">
                <Label htmlFor="instructions">Instructions</Label>
                <Textarea
                  id="instructions"
                  placeholder="Detailed instructions for prospects taking this assessment"
                  value={newAssessment.instructions}
                  onChange={(e) => setNewAssessment({...newAssessment, instructions: e.target.value})}
                  className="mt-1"
                />
              </div>
              
              <div className="col-span-2">
                <Label htmlFor="what_it_checks">What It Checks</Label>
                <Textarea
                  id="what_it_checks"
                  placeholder="What skills or abilities does this assessment evaluate?"
                  value={newAssessment.what_it_checks}
                  onChange={(e) => setNewAssessment({...newAssessment, what_it_checks: e.target.value})}
                  className="mt-1"
                />
              </div>
              
              <div className="col-span-2">
                <Label htmlFor="what_to_expect">What To Expect</Label>
                <Textarea
                  id="what_to_expect"
                  placeholder="What should prospects expect when taking this assessment?"
                  value={newAssessment.what_to_expect}
                  onChange={(e) => setNewAssessment({...newAssessment, what_to_expect: e.target.value})}
                  className="mt-1"
                />
              </div>
              
              <div className="col-span-2 flex items-center space-x-2">
                <Switch
                  id="is_active"
                  checked={newAssessment.is_active}
                  onCheckedChange={(checked) => setNewAssessment({...newAssessment, is_active: checked})}
                />
                <Label htmlFor="is_active">Active</Label>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreate} disabled={isSubmitting}>
              {isSubmitting ? 'Creating...' : 'Create Assessment'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Assessment Dialog */}
      {currentAssessment && (
        <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Assessment</DialogTitle>
              <DialogDescription>
                Update the assessment details.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="col-span-2">
                  <Label htmlFor="edit-title">Assessment Title</Label>
                  <Input
                    id="edit-title"
                    value={currentAssessment.title}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      title: e.target.value
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div className="col-span-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    value={currentAssessment.description}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      description: e.target.value
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="edit-category">Category</Label>
                  <Select
                    value={currentAssessment.category}
                    onValueChange={(value) => setCurrentAssessment({
                      ...currentAssessment, 
                      category: value as 'Technical' | 'Soft Skills'
                    })}
                  >
                    <SelectTrigger id="edit-category" className="mt-1">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Technical">Technical</SelectItem>
                      <SelectItem value="Soft Skills">Soft Skills</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="edit-duration">Duration</Label>
                  <Input
                    id="edit-duration"
                    value={currentAssessment.duration}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      duration: e.target.value
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="edit-total_questions">Total Questions</Label>
                  <Input
                    id="edit-total_questions"
                    type="number"
                    value={currentAssessment.total_questions.toString()}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      total_questions: parseInt(e.target.value) || 0
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="edit-passing_score">Passing Score (%)</Label>
                  <Input
                    id="edit-passing_score"
                    type="number"
                    value={currentAssessment.passing_score.toString()}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      passing_score: parseInt(e.target.value) || 0
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div className="col-span-2">
                  <Label htmlFor="edit-instructions">Instructions</Label>
                  <Textarea
                    id="edit-instructions"
                    value={currentAssessment.instructions}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      instructions: e.target.value
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div className="col-span-2">
                  <Label htmlFor="edit-what_it_checks">What It Checks</Label>
                  <Textarea
                    id="edit-what_it_checks"
                    value={currentAssessment.what_it_checks}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      what_it_checks: e.target.value
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div className="col-span-2">
                  <Label htmlFor="edit-what_to_expect">What To Expect</Label>
                  <Textarea
                    id="edit-what_to_expect"
                    value={currentAssessment.what_to_expect}
                    onChange={(e) => setCurrentAssessment({
                      ...currentAssessment, 
                      what_to_expect: e.target.value
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div className="col-span-2 flex items-center space-x-2">
                  <Switch
                    id="edit-is_active"
                    checked={currentAssessment.is_active}
                    onCheckedChange={(checked) => setCurrentAssessment({
                      ...currentAssessment, 
                      is_active: checked
                    })}
                  />
                  <Label htmlFor="edit-is_active">Active</Label>
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleEdit} disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : 'Save Changes'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      {currentAssessment && (
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Assessment</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this assessment? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            
            <div className="py-4">
              <p><strong>Title:</strong> {currentAssessment.title}</p>
              <p className="text-muted-foreground mt-1">{currentAssessment.description}</p>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDelete} disabled={isSubmitting}>
                {isSubmitting ? 'Deleting...' : 'Delete Assessment'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

function StatsCard({ title, value, icon }: { title: string; value: string | number; icon: React.ReactNode }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
      </CardContent>
    </Card>
  );
}

function AdminAssessmentsSkeleton() {
  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-10 w-40" />
        <Skeleton className="h-10 w-36" />
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        {Array(4).fill(0).map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-5 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16" />
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between">
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-10 w-32" />
          </div>
          <Skeleton className="h-[400px] w-full" />
        </CardContent>
      </Card>
    </div>
  );
} 