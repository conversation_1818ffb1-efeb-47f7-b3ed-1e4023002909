import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { QuestionGenerationPipeline } from '@/lib/services/question-generation-pipeline';
import type { PipelineRequest } from '@/lib/services/question-generation-pipeline';

/**
 * Generate assessment questions using the complete pipeline
 * POST /api/assessments/generate
 */
export async function POST(req: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const body = await req.json();
    const {
      assessmentConfigId,
      sessionId,
      customParameters = {},
      priority = 'normal'
    } = body;

    if (!assessmentConfigId) {
      return NextResponse.json(
        { error: 'Assessment configuration ID is required' },
        { status: 400 }
      );
    }

    // Create pipeline request
    const pipelineRequest: PipelineRequest = {
      userId: authResult.user.id,
      assessmentConfigId,
      sessionId,
      customParameters,
      priority
    };

    console.log('Starting assessment generation pipeline for user:', authResult.user.id);

    // Execute the pipeline
    const pipeline = new QuestionGenerationPipeline();
    const result = await pipeline.execute(pipelineRequest);

    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Question generation failed',
          details: result.error,
          metadata: result.metadata
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      session_id: result.sessionId,
      questions_generated: result.questionsGenerated,
      quality_score: result.qualityScore,
      generation_time: result.generationTime,
      tokens_used: result.tokensUsed,
      pipeline_metadata: {
        pipeline_id: result.metadata.pipelineId,
        template_used: result.metadata.templateUsed,
        model_used: result.metadata.modelUsed,
        stages: result.metadata.stages.map(stage => ({
          name: stage.name,
          status: stage.status,
          duration: stage.duration,
          error: stage.error
        }))
      },
      message: `Successfully generated ${result.questionsGenerated} questions`
    });

  } catch (error: any) {
    console.error('Assessment generation API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to generate assessment',
        details: error.message
      },
      { status: 500 }
    );
  }
}

/**
 * Get generation status for a session
 * GET /api/assessments/generate?sessionId=xxx
 */
export async function GET(req: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('sessionId');

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Session ID is required' },
        { status: 400 }
      );
    }

    // Get session status from database
    const { createClient } = await import('@/lib/supabase/server');
    const supabase = createClient();

    const { data: session, error } = await supabase
      .from('ai_assessment_sessions')
      .select(`
        id,
        status,
        total_questions,
        questions_answered,
        generation_timestamp,
        generation_metadata,
        ai_assessment_config:ai_assessment_configs(
          name,
          category
        )
      `)
      .eq('id', sessionId)
      .eq('user_id', authResult.user.id)
      .single();

    if (error || !session) {
      return NextResponse.json(
        { error: 'Assessment session not found' },
        { status: 404 }
      );
    }

    // Get question count
    const { count: questionCount } = await supabase
      .from('ai_generated_assessment_questions')
      .select('*', { count: 'exact', head: true })
      .eq('assessment_session_id', sessionId);

    const generationStatus = {
      session_id: session.id,
      status: session.status,
      assessment_name: session.ai_assessment_config?.name || 'Unknown',
      assessment_category: session.ai_assessment_config?.category || 'unknown',
      total_questions: session.total_questions || 0,
      questions_generated: questionCount || 0,
      questions_answered: session.questions_answered || 0,
      generation_timestamp: session.generation_timestamp,
      generation_metadata: session.generation_metadata,
      is_ready: (questionCount || 0) > 0 && session.status === 'in_progress',
      is_completed: session.status === 'completed'
    };

    return NextResponse.json({
      success: true,
      generation_status: generationStatus
    });

  } catch (error: any) {
    console.error('Generation status check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check generation status',
        details: error.message
      },
      { status: 500 }
    );
  }
}
