
'use client';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  Plus,
  UserPlus,
  Building2,
  Users,
  Settings,
  AlertTriangle,
  BookOpen,
  FileText,
  GraduationCap,
  Zap,
  TrendingUp,
  Globe,
  CheckCircle2,
  Target,
  Star,
  BarChart3,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { AreaChart, Area, BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';
import { HardLogoutControl } from '@/components/admin/hard-logout-control';

// Enhanced data for more sophisticated charts
const userGrowthData = [
  { month: 'Jan', prospects: 45, organizations: 8, applications: 120, completions: 67 },
  { month: 'Feb', prospects: 67, organizations: 12, applications: 189, completions: 98 },
  { month: 'Mar', prospects: 89, organizations: 15, applications: 245, completions: 134 },
  { month: 'Apr', prospects: 123, organizations: 18, applications: 312, completions: 187 },
  { month: 'May', prospects: 156, organizations: 22, applications: 398, completions: 245 },
  { month: 'Jun', prospects: 198, organizations: 28, applications: 467, completions: 298 },
];

const modulePerformanceData = [
  { module: 'Communication Skills', completed: 342, failed: 23, avgScore: 87, satisfaction: 4.6 },
  { module: 'Customer Service', completed: 298, failed: 31, avgScore: 82, satisfaction: 4.4 },
  { module: 'Technical Skills', completed: 267, failed: 45, avgScore: 79, satisfaction: 4.2 },
  { module: 'Sales Training', completed: 389, failed: 19, avgScore: 91, satisfaction: 4.8 },
  { module: 'Problem Solving', completed: 321, failed: 28, avgScore: 85, satisfaction: 4.5 },
];

const revenueData = [
  { month: 'Jan', subscriptions: 12400, training: 8600, consulting: 5200 },
  { month: 'Feb', subscriptions: 14200, training: 9800, consulting: 6100 },
  { month: 'Mar', subscriptions: 15800, training: 11200, consulting: 7300 },
  { month: 'Apr', subscriptions: 17500, training: 12800, consulting: 8200 },
  { month: 'May', subscriptions: 19200, training: 14300, consulting: 9100 },
  { month: 'Jun', subscriptions: 21100, training: 15900, consulting: 10400 },
];

// Belize districts data for statistical display
const belizeDistrictsData = [
  {
    name: 'Belize District',
    users: 342,
    prospects: 198,
    organizations: 12,
    color: '#3b82f6',
    growth: '+12%',
    cities: ['Belize City', 'Ladyville', 'Hattieville']
  },
  {
    name: 'Cayo District',
    users: 189,
    prospects: 123,
    organizations: 8,
    color: '#8b5cf6',
    growth: '+8%',
    cities: ['San Ignacio', 'Benque Viejo', 'Spanish Lookout']
  },
  {
    name: 'Orange Walk District',
    users: 156,
    prospects: 89,
    organizations: 6,
    color: '#10b981',
    growth: '+15%',
    cities: ['Orange Walk Town', 'Carmelita', 'August Pine Ridge']
  },
  {
    name: 'Corozal District',
    users: 134,
    prospects: 76,
    organizations: 5,
    color: '#f59e0b',
    growth: '+6%',
    cities: ['Corozal Town', 'Consejo', 'Sarteneja']
  },
  {
    name: 'Stann Creek District',
    users: 98,
    prospects: 54,
    organizations: 3,
    color: '#ef4444',
    growth: '+4%',
    cities: ['Dangriga', 'Hopkins', 'Placencia']
  },
  {
    name: 'Toledo District',
    users: 67,
    prospects: 34,
    organizations: 2,
    color: '#6b7280',
    growth: '+2%',
    cities: ['Punta Gorda', 'Barranco', 'Blue Creek']
  },
];

const chartConfig = {
  prospects: { label: 'Prospects', color: '#8b5cf6' },
  organizations: { label: 'Organizations', color: '#3b82f6' },
  applications: { label: 'Applications', color: '#10b981' },
  completions: { label: 'Completions', color: '#f59e0b' },
  subscriptions: { label: 'Subscriptions', color: '#3b82f6' },
  training: { label: 'Training Revenue', color: '#10b981' },
  consulting: { label: 'Consulting', color: '#f59e0b' },
};

export default function AdminDashboard() {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [stats, setStats] = useState({
    // User & Organization Stats
    totalUsers: 0,
    totalOrganizations: 0,
    totalIndividuals: 0,
    totalDepartments: 0,
    totalEmployments: 0,

    // Employment Analytics
    organizationAdmins: 0,
    departmentAdmins: 0,
    staffMembers: 0,
    activeEmployments: 0,

    // Platform Analytics
    individualContextUsers: 0,
    organizationalContextUsers: 0,
    multiEmploymentUsers: 0,

    // System Health
    platformUptime: 99.9,
    avgResponseTime: 120,
    activeUsers24h: 0
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 3;

    async function fetchStats() {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/admin/stats', {
          credentials: 'include', // Ensure cookies are sent
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          let errorMessage = 'Failed to fetch stats';
          try {
            const errorData = await response.json();
            errorMessage = typeof errorData.error === 'string' ? errorData.error :
                          errorData.message || errorData.userMessage || errorMessage;
          } catch (e) {
            errorMessage = response.statusText || errorMessage;
          }

          // If it's an auth error and we haven't retried too many times, retry after a delay
          if (response.status === 500 && retryCount < maxRetries && errorMessage.includes('admin access required')) {
            retryCount++;
            console.log(`Auth not ready, retrying in ${retryCount}s... (${retryCount}/${maxRetries})`);
            setTimeout(fetchStats, retryCount * 1000);
            return;
          }

          throw new Error(errorMessage);
        }

        const data = await response.json();
        setStats(data.stats);
        setError(null);
        retryCount = 0; // Reset on success
      } catch (error: any) {
        console.error('Error fetching stats:', error);

        // Only set error state if we've exhausted retries
        if (retryCount >= maxRetries) {
          setError(error.message || 'Failed to load dashboard statistics');
        }

        // Set mock data for demo purposes when API fails
        setStats({
          // User & Organization Stats
          totalUsers: 12,
          totalOrganizations: 3,
          totalIndividuals: 8,
          totalDepartments: 6,
          totalEmployments: 11,

          // Employment Analytics
          organizationAdmins: 3,
          departmentAdmins: 3,
          staffMembers: 5,
          activeEmployments: 11,

          // Platform Analytics
          individualContextUsers: 8,
          organizationalContextUsers: 3,
          multiEmploymentUsers: 0,

          // System Health
          platformUptime: 99.9,
          avgResponseTime: 120,
          activeUsers24h: 5
        });
      } finally {
        if (retryCount >= maxRetries || retryCount === 0) {
          setLoading(false);
        }
      }
    }

    // For Phase 2, skip API call and use mock data directly
    console.log('Loading admin dashboard with mock data for Phase 2 portal development');
    setLoading(true);

    setTimeout(() => {
      setStats({
        // User & Organization Stats
        totalUsers: 12,
        totalOrganizations: 3,
        totalIndividuals: 8,
        totalDepartments: 6,
        totalEmployments: 11,

        // Employment Analytics
        organizationAdmins: 3,
        departmentAdmins: 3,
        staffMembers: 5,
        activeEmployments: 11,

        // Platform Analytics
        individualContextUsers: 8,
        organizationalContextUsers: 3,
        multiEmploymentUsers: 0,

        // System Health
        platformUptime: 99.9,
        avgResponseTime: 120,
        activeUsers24h: 5
      });
      setError(null);
      setLoading(false);
    }, 800);

    // TODO: Re-enable API call once training features are implemented
    // setTimeout(fetchStats, 500);
  }, []);

  const quickActions = [
    {
      title: 'Create User',
      icon: UserPlus,
      href: '/admin/users/create',
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      title: 'Add Organization',
      icon: Building2,
      href: '/admin/organizations/create',
      color: 'bg-purple-600 hover:bg-purple-700',
    },
    {
      title: 'Manage Employment',
      icon: Users,
      href: '/admin/employment',
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      title: 'Platform Settings',
      icon: Settings,
      href: '/admin/settings',
      color: 'bg-orange-600 hover:bg-orange-700',
    },
  ];

  // Get current time greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Welcome Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="px-[50px] py-6 pt-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                {getGreeting()}, Admin! 👋
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Welcome to Luna Platform! You're managing <span className="font-semibold text-blue-600">{stats.totalUsers || 0}</span> users across <span className="font-semibold text-green-600">{stats.totalOrganizations || 0}</span> organizations with <span className="font-semibold text-purple-600">{stats.totalEmployments || 0}</span> active employment relationships.
              </p>

              {/* Key Updates */}
              <div className="flex items-center gap-6 mt-4 text-sm">
                <div className="flex items-center gap-2 text-blue-600">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>{stats.totalIndividuals || 0} individual training accounts</span>
                </div>
                <div className="flex items-center gap-2 text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>{stats.totalDepartments || 0} departments across organizations</span>
                </div>
                <div className="flex items-center gap-2 text-orange-600">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Dual-context system active</span>
                </div>
            </div>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="px-[50px] pt-6">
          <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 text-red-700 dark:text-red-300">
                <AlertTriangle className="h-5 w-5" />
                <p className="text-sm">{error}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="px-[50px] py-6 space-y-6">
        {/* Quick Actions - Above KPIs */}
        <div className="flex items-center gap-3">
          {quickActions.map((action, index) => (
            <Link key={index} href={action.href}>
              <Button
                variant="outline"
                className="h-9 px-4 hover:shadow-sm transition-all duration-200 hover:border-blue-300 hover:text-blue-600"
              >
                <action.icon className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">{action.title}</span>
              </Button>
            </Link>
          ))}
        </div>

        {/* KPI Cards - Luna Employment System */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Platform Users */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                  All Users
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Platform Users</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalUsers || 0).toLocaleString()}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Including admins & individuals
                </p>
                    </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-blue-200 dark:bg-blue-800">
                <div className="h-full w-3/4 bg-blue-600"></div>
              </div>
            </CardContent>
          </Card>

          {/* Organizations */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                  Active
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Organizations</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalOrganizations || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Registered organizations
                </p>
              </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-green-200 dark:bg-green-800">
                <div className="h-full w-4/5 bg-green-600"></div>
              </div>
            </CardContent>
          </Card>

          {/* Departments */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                  <Target className="h-6 w-6 text-orange-600 dark:text-orange-400" />
        </div>
                <Badge variant="outline" className="text-orange-600 border-orange-200 bg-orange-50">
                  Departments
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Departments</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                      {loading ? '...' : (stats.totalDepartments || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Across all organizations
                </p>
                    </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-orange-200 dark:bg-orange-800">
                <div className="h-full w-4/5 bg-orange-600"></div>
              </div>
            </CardContent>
          </Card>

          {/* Employment Relationships */}
          <Card className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                  <Activity className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <Badge variant="outline" className="text-purple-600 border-purple-200 bg-purple-50">
                  Active
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Employment Relationships</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalEmployments || 0).toLocaleString()}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Active employment links
                </p>
              </div>
              <div className="absolute bottom-0 left-0 w-full h-1 bg-purple-200 dark:bg-purple-800">
                <div className="h-full w-3/4 bg-purple-600"></div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* LMS Content Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Courses */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <BookOpen className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <Badge variant="outline" className="text-blue-600 border-blue-200 bg-blue-50">
                  {stats.coursesInDraft || 0} draft
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Courses</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalCourses || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {stats.publishedCourses || 0} published
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Total Modules */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <FileText className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                  Content
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Course Modules</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalModules || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Learning modules
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Total Lessons */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                  <GraduationCap className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <Badge variant="outline" className="text-orange-600 border-orange-200 bg-orange-50">
                  Lessons
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Lessons</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalLessons || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Learning content
                </p>
              </div>
            </CardContent>
          </Card>

          {/* AI Quizzes */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <Badge variant="outline" className="text-purple-600 border-purple-200 bg-purple-50">
                  AI-Powered
                </Badge>
              </div>
              <div className="mt-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Quiz Configurations</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">
                  {loading ? '...' : (stats.totalQuizzes || 0)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Assessment tools
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue & Performance Analytics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Revenue & Performance
              </CardTitle>
              <CardDescription>Platform financial performance and key metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-80">
                <AreaChart data={revenueData}>
                  <defs>
                    <linearGradient id="revenue-gradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="training-gradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                    </linearGradient>
                    <linearGradient id="consulting-gradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8}/>
                      <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Area dataKey="subscriptions" stackId="1" stroke="#3b82f6" fill="url(#revenue-gradient)" />
                  <Area dataKey="training" stackId="1" stroke="#10b981" fill="url(#training-gradient)" />
                  <Area dataKey="consulting" stackId="1" stroke="#f59e0b" fill="url(#consulting-gradient)" />
                </AreaChart>
              </ChartContainer>
            </CardContent>
          </Card>

          {/* Belize Districts Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                District Performance
              </CardTitle>
              <CardDescription>User distribution and growth across Belize districts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {belizeDistrictsData.map((district, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:shadow-sm transition-shadow">
                    <div className="flex items-center gap-4">
                      <div 
                        className="w-4 h-4 rounded-full flex-shrink-0"
                        style={{ backgroundColor: district.color }}
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {district.name.replace(' District', '')}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {district.cities.slice(0, 2).join(', ')}
                          {district.cities.length > 2 && ` +${district.cities.length - 2} more`}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-6 text-sm">
                      <div className="text-center">
                        <p className="font-semibold text-gray-900 dark:text-white">{district.users}</p>
                        <p className="text-xs text-gray-500">Users</p>
                      </div>
                      <div className="text-center">
                        <p className="font-semibold text-gray-900 dark:text-white">{district.prospects}</p>
                        <p className="text-xs text-gray-500">Prospects</p>
                      </div>
                      <div className="text-center">
                        <p className="font-semibold text-gray-900 dark:text-white">{district.organizations}</p>
                        <p className="text-xs text-gray-500">Organizations</p>
              </div>
                      <Badge 
                        variant="outline" 
                        className="text-green-600 border-green-200 bg-green-50 dark:text-green-400 dark:border-green-800 dark:bg-green-950/20"
                      >
                        {district.growth}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>

              {/* Summary Stats */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {belizeDistrictsData.reduce((sum, d) => sum + d.users, 0)}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Users</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {belizeDistrictsData.reduce((sum, d) => sum + d.prospects, 0)}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Prospects</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {belizeDistrictsData.reduce((sum, d) => sum + d.organizations, 0)}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Total Organizations</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics & Insights */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Analytics & Insights</h2>
              <p className="text-gray-600 dark:text-gray-400 text-sm">Track platform performance and user engagement</p>
            </div>
            <TabsList className="grid w-auto grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="training">Training</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Platform Overview</CardTitle>
                  <CardDescription>Key metrics and performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">System Uptime</p>
                          <p className="text-sm text-gray-500">Last 30 days</p>
            </div>
          </div>
                      <div className="text-right">
                        <p className="font-semibold">{(stats.platformUptime || 99.9)}%</p>
                        <p className="text-sm text-green-600">+0.1%</p>
        </div>
      </div>
      
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                          <Target className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Completion Rate</p>
                          <p className="text-sm text-gray-500">Training modules</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{(stats.avgCompletionRate || 0)}%</p>
                        <p className="text-sm text-blue-600">+5.2%</p>
                      </div>
      </div>
      
                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
                          <Star className="h-4 w-4 text-yellow-600" />
                        </div>
                        <div>
                          <p className="font-medium">User Satisfaction</p>
                          <p className="text-sm text-gray-500">Average rating</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{(stats.avgRating || 0)}/5</p>
                        <p className="text-sm text-yellow-600">Course Rating</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Revenue Overview</CardTitle>
                  <CardDescription>Monthly revenue breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center mb-4">
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">
                      ${((stats.monthlyRevenue || 0) / 1000).toFixed(1)}k
                    </p>
                    <p className="text-sm text-gray-500">This month</p>
                    <Badge variant="outline" className="mt-2 text-green-600 border-green-200 bg-green-50">
                      +12.5% from last month
                    </Badge>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Subscriptions</span>
                      <span className="font-medium">$21.1k</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
        </div>
        
                    <div className="flex justify-between text-sm">
                      <span>Training Revenue</span>
                      <span className="font-medium">$15.9k</span>
              </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '34%' }}></div>
              </div>
                    
                    <div className="flex justify-between text-sm">
                      <span>Consulting</span>
                      <span className="font-medium">$10.4k</span>
              </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-orange-600 h-2 rounded-full" style={{ width: '21%' }}></div>
          </div>
        </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="training" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Training Module Performance
                </CardTitle>
                <CardDescription>
                  Detailed analytics on training effectiveness and completion rates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="h-80">
                  <BarChart data={modulePerformanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="module" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="completed" fill="#10b981" />
                    <Bar dataKey="failed" fill="#ef4444" />
                  </BarChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="revenue" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Revenue Analytics
                </CardTitle>
                <CardDescription>
                  Revenue breakdown by service category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer config={chartConfig} className="h-80">
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line type="monotone" dataKey="subscriptions" stroke="#3b82f6" strokeWidth={2} />
                    <Line type="monotone" dataKey="training" stroke="#10b981" strokeWidth={2} />
                    <Line type="monotone" dataKey="consulting" stroke="#f59e0b" strokeWidth={2} />
                  </LineChart>
                </ChartContainer>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Session Management Section */}
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Session Management</h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">Monitor and control user sessions across the platform</p>
          </div>
          <HardLogoutControl />
        </div>
      </div>
    </div>
  );
}