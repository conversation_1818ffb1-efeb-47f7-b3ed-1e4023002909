-- =============================================================================
-- THREE-TIER COURSE ARCHITECTURE MIGRATION
-- Extends existing schema to support Programs → Pathways (learning_paths) → Courses
-- =============================================================================

-- =============================================================================
-- STEP 1: CREATE PROGRAMS TABLE
-- =============================================================================

-- High-level industry programs (IT, BPO, Healthcare, Finance, etc.)
CREATE TABLE programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  industry TEXT,
  cover_image_url TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'archived')),
  sort_order INTEGER DEFAULT 0,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique program names
  UNIQUE(name)
);

-- =============================================================================
-- STEP 2: ADD PROGRAM_ID TO LEARNING_PATHS (MAKING THEM PATHWAYS)
-- =============================================================================

-- Add program_id to learning_paths to make them pathways within programs
ALTER TABLE learning_paths 
ADD COLUMN program_id UUID REFERENCES programs(id);

-- Add pathway-specific fields to learning_paths
ALTER TABLE learning_paths 
ADD COLUMN sort_order INTEGER DEFAULT 0,
ADD COLUMN is_featured BOOLEAN DEFAULT false,
ADD COLUMN cover_image_url TEXT;

-- =============================================================================
-- STEP 3: CREATE COURSES TABLE
-- =============================================================================

-- Individual courses that can be shared across multiple pathways
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  slug TEXT UNIQUE,
  level TEXT DEFAULT 'beginner' CHECK (level IN ('beginner', 'intermediate', 'advanced')),
  estimated_duration INTEGER, -- in minutes
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'review', 'published', 'archived')),
  price DECIMAL(10,2),
  cover_image_url TEXT,
  preview_video_url TEXT,
  meta_description TEXT,
  tags TEXT[] DEFAULT '{}',
  learning_objectives TEXT[] DEFAULT '{}',
  target_audience TEXT,
  instructor_id UUID REFERENCES users(id),
  instructor_bio TEXT,
  enrollment_count INTEGER DEFAULT 0,
  completion_rate DECIMAL(5,2) DEFAULT 0,
  average_rating DECIMAL(3,2) DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique course names and slugs
  UNIQUE(name),
  UNIQUE(slug)
);

-- =============================================================================
-- STEP 4: CREATE PATHWAY-COURSE JUNCTION TABLE
-- =============================================================================

-- Many-to-many relationship between pathways (learning_paths) and courses
CREATE TABLE pathway_courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  pathway_id UUID NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  sequence_order INTEGER NOT NULL,
  is_required BOOLEAN DEFAULT true,
  unlock_conditions JSONB DEFAULT '{}', -- Prerequisites, assessments, etc.
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique course order within each pathway
  UNIQUE(pathway_id, sequence_order),
  UNIQUE(pathway_id, course_id)
);

-- =============================================================================
-- STEP 5: CREATE COURSE CONTENT STRUCTURE
-- =============================================================================

-- Course modules (sections within a course)
CREATE TABLE course_modules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  sequence_order INTEGER NOT NULL,
  estimated_duration INTEGER, -- in minutes
  is_standalone BOOLEAN DEFAULT false, -- Can be taken independently
  single_price DECIMAL(10,2), -- Price if sold separately
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  learning_objectives TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique module order within each course
  UNIQUE(course_id, sequence_order)
);

-- Course lessons (individual lessons within modules)
CREATE TABLE course_lessons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  module_id UUID NOT NULL REFERENCES course_modules(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  lesson_type TEXT DEFAULT 'video' CHECK (lesson_type IN ('video', 'text', 'quiz', 'assignment', 'interactive')),
  content_url TEXT,
  content_data JSONB DEFAULT '{}', -- Flexible content storage
  sequence_order INTEGER NOT NULL,
  estimated_duration INTEGER, -- in minutes
  is_mandatory BOOLEAN DEFAULT true,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure unique lesson order within each module
  UNIQUE(module_id, sequence_order)
);

-- =============================================================================
-- STEP 6: CREATE COURSE PREREQUISITES TABLE
-- =============================================================================

-- Course prerequisites and dependencies
CREATE TABLE course_prerequisites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  prerequisite_type TEXT NOT NULL CHECK (prerequisite_type IN ('course', 'skill', 'assessment')),
  prerequisite_id UUID NOT NULL, -- References courses(id), skills(id), or assessments
  is_required BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Prevent duplicate prerequisites
  UNIQUE(course_id, prerequisite_type, prerequisite_id)
);

-- =============================================================================
-- STEP 7: CREATE USER ENROLLMENT AND PROGRESS TABLES
-- =============================================================================

-- User course enrollments
CREATE TABLE user_course_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  course_id UUID NOT NULL REFERENCES courses(id) ON DELETE CASCADE,
  enrollment_context TEXT DEFAULT 'individual' CHECK (enrollment_context IN ('individual', 'pathway', 'organization')),
  pathway_id UUID REFERENCES learning_paths(id), -- If enrolled via pathway
  organization_id UUID REFERENCES organizations(id), -- If enrolled via organization
  enrolled_at TIMESTAMPTZ DEFAULT NOW(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  current_module_id UUID REFERENCES course_modules(id),
  current_lesson_id UUID REFERENCES course_lessons(id),
  time_spent_minutes INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Prevent duplicate enrollments
  UNIQUE(user_id, course_id)
);

-- User pathway enrollments (using learning_paths)
CREATE TABLE user_pathway_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  pathway_id UUID NOT NULL REFERENCES learning_paths(id) ON DELETE CASCADE,
  enrollment_context TEXT DEFAULT 'individual' CHECK (enrollment_context IN ('individual', 'organization')),
  organization_id UUID REFERENCES organizations(id),
  enrolled_at TIMESTAMPTZ DEFAULT NOW(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  current_course_id UUID REFERENCES courses(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Prevent duplicate pathway enrollments
  UNIQUE(user_id, pathway_id)
);

-- =============================================================================
-- STEP 8: CREATE INDEXES FOR PERFORMANCE
-- =============================================================================

-- Programs indexes
CREATE INDEX idx_programs_status ON programs(status);
CREATE INDEX idx_programs_industry ON programs(industry);
CREATE INDEX idx_programs_sort_order ON programs(sort_order);

-- Learning paths (pathways) indexes
CREATE INDEX idx_learning_paths_program_id ON learning_paths(program_id);
CREATE INDEX idx_learning_paths_sort_order ON learning_paths(sort_order);

-- Courses indexes
CREATE INDEX idx_courses_status ON courses(status);
CREATE INDEX idx_courses_level ON courses(level);
CREATE INDEX idx_courses_instructor_id ON courses(instructor_id);
CREATE INDEX idx_courses_slug ON courses(slug);

-- Pathway courses indexes
CREATE INDEX idx_pathway_courses_pathway_id ON pathway_courses(pathway_id);
CREATE INDEX idx_pathway_courses_course_id ON pathway_courses(course_id);
CREATE INDEX idx_pathway_courses_sequence ON pathway_courses(pathway_id, sequence_order);

-- Course content indexes
CREATE INDEX idx_course_modules_course_id ON course_modules(course_id);
CREATE INDEX idx_course_modules_sequence ON course_modules(course_id, sequence_order);
CREATE INDEX idx_course_lessons_module_id ON course_lessons(module_id);
CREATE INDEX idx_course_lessons_sequence ON course_lessons(module_id, sequence_order);

-- Enrollment indexes
CREATE INDEX idx_user_course_enrollments_user_id ON user_course_enrollments(user_id);
CREATE INDEX idx_user_course_enrollments_course_id ON user_course_enrollments(course_id);
CREATE INDEX idx_user_pathway_enrollments_user_id ON user_pathway_enrollments(user_id);
CREATE INDEX idx_user_pathway_enrollments_pathway_id ON user_pathway_enrollments(pathway_id);

-- =============================================================================
-- STEP 9: ADD UPDATED_AT TRIGGERS
-- =============================================================================

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
CREATE TRIGGER update_programs_updated_at BEFORE UPDATE ON programs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_courses_updated_at BEFORE UPDATE ON courses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_modules_updated_at BEFORE UPDATE ON course_modules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_course_lessons_updated_at BEFORE UPDATE ON course_lessons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_course_enrollments_updated_at BEFORE UPDATE ON user_course_enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_pathway_enrollments_updated_at BEFORE UPDATE ON user_pathway_enrollments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- MIGRATION COMPLETE
-- =============================================================================

-- Verify the schema
SELECT 'Three-tier course architecture migration completed successfully!' as message;
SELECT 'Tables created: programs, courses, course_modules, course_lessons, pathway_courses, course_prerequisites, user_course_enrollments, user_pathway_enrollments' as details;
