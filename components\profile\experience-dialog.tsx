"use client";

import { useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus, Edit, Trash2 } from 'lucide-react';

const currentYear = new Date().getFullYear()
const years = Array.from({ length: 40 }, (_, i) => currentYear - i)

const months = [
  "January", "February", "March", "April", "May", "June",
  "July", "August", "September", "October", "November", "December"
]

const experienceSchema = z.object({
  title: z.string().min(2, {
    message: "Job title must be at least 2 characters.",
  }),
  company: z.string().min(2, {
    message: "Company name must be at least 2 characters.",
  }),
  start_month: z.string(),
  start_year: z.string(),
  end_month: z.string().optional(),
  end_year: z.string().optional(),
  current_position: z.boolean().default(false),
  description: z.string().optional(),
  location: z.string().optional(),
})

type ExperienceValues = z.infer<typeof experienceSchema>

interface ExperienceDialogProps {
  triggerComponent?: React.ReactNode
  experience?: any
  experienceIndex?: number
  action: "add" | "update" | "delete"
  onSuccess?: () => void
}

export function ExperienceDialog({
  triggerComponent,
  experience,
  experienceIndex,
  action = "add",
  onSuccess
}: ExperienceDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  
  const isUpdate = action === "update"
  const isDelete = action === "delete"
  
  // Parse experience data
  let defaultValues: ExperienceValues = {
    title: "",
    company: "",
    start_month: "January",
    start_year: currentYear.toString(),
    current_position: false,
    location: "",
    description: "",
  }
  
  if (isUpdate && experience) {
    const isCurrentPosition = !experience.end_date || !experience.end_month
    
    defaultValues = {
      title: experience.title || "",
      company: experience.company || "",
      location: experience.location || "",
      start_month: experience.start_month || "January",
      start_year: experience.start_year?.toString() || currentYear.toString(),
      end_month: !isCurrentPosition ? (experience.end_month || "January") : undefined,
      end_year: !isCurrentPosition ? (experience.end_year?.toString() || currentYear.toString()) : undefined,
      current_position: isCurrentPosition,
      description: experience.description || "",
    }
  }
  
  const form = useForm<ExperienceValues>({
    resolver: zodResolver(experienceSchema),
    defaultValues,
  })
  
  // Watch current position value to conditionally show/hide end date
  const currentPosition = form.watch("current_position")
  
  async function onSubmit(data: ExperienceValues) {
    try {
      setIsLoading(true)
      
      // Format experience data
      const experienceData = {
        title: data.title,
        company: data.company,
        location: data.location,
        start_date: `${data.start_month} ${data.start_year}`,
        start_month: data.start_month,
        start_year: parseInt(data.start_year),
        end_date: data.current_position ? null : `${data.end_month} ${data.end_year}`,
        end_month: data.current_position ? null : data.end_month,
        end_year: data.current_position ? null : parseInt(data.end_year || "0"),
        description: data.description,
      }
      
      console.log('ExperienceDialog - Submitting data:', {
        action: isDelete ? "delete" : isUpdate ? "update" : "add",
        experienceData,
        experienceIndex: isUpdate || isDelete ? experienceIndex : undefined,
      });
      
      // Function to make the API call with built-in retry
      const updateExperience = async (retryCount = 0): Promise<any> => {
        try {
          const response = await fetch('/api/profile/update-experience', {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              action: isDelete ? "delete" : isUpdate ? "update" : "add",
              experienceData: isDelete ? null : experienceData,
              experienceIndex: isUpdate || isDelete ? experienceIndex : undefined,
            }),
          });
          
          if (!response.ok) {
            const errorText = await response.text();
            console.error('ExperienceDialog - API error status:', response.status, response.statusText);
            console.error('ExperienceDialog - API error response:', errorText);
            
            // Retry logic - max 2 retries (3 attempts total)
            if (retryCount < 2) {
              console.log(`ExperienceDialog - Retrying (${retryCount + 1}/2)...`);
              return await updateExperience(retryCount + 1);
            }
            
            throw new Error(errorText || `Server error: ${response.status}`);
          }
          
          return response;
        } catch (error) {
          if (retryCount < 2) {
            console.log(`ExperienceDialog - Retrying after error (${retryCount + 1}/2)...`);
            // Add exponential backoff for retries
            await new Promise(resolve => setTimeout(resolve, (retryCount + 1) * 500));
            return await updateExperience(retryCount + 1);
          }
          throw error;
        }
      };
      
      // Make the API call with retries
      const result = await updateExperience();
      
      console.log('ExperienceDialog - Success:', result);
      
      // Show success toast and close dialog
      const message = isDelete 
        ? "Experience deleted successfully" 
        : isUpdate 
          ? "Experience updated successfully" 
          : "Experience added successfully"
          
      toast.success(message)
      setOpen(false)
      
      // Refresh data
      if (onSuccess) {
        onSuccess()
      } else {
        router.refresh()
      }
    } catch (error: any) {
      console.error('ExperienceDialog - Error:', error);
      toast.error(error.message || "Failed to update experience. Please try again.");
    } finally {
      setIsLoading(false)
    }
  }
  
  // Handle delete experience
  async function handleDelete() {
    if (isDelete) {
      onSubmit({} as ExperienceValues)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {triggerComponent || (
          isDelete ? (
            <Button size="icon" variant="ghost" className="text-destructive hover:text-destructive">
              <Trash2 className="h-4 w-4" />
            </Button>
          ) : isUpdate ? (
            <Button size="icon" variant="ghost">
              <Edit className="h-4 w-4" />
            </Button>
          ) : (
            <Button className="mt-3" variant="outline" size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Experience
            </Button>
          )
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[550px]">
        {isDelete ? (
          <>
            <DialogHeader>
              <DialogTitle>Delete Experience</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this experience entry?
              </DialogDescription>
            </DialogHeader>
            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
              <Button variant="destructive" onClick={handleDelete} disabled={isLoading}>
                {isLoading ? "Deleting..." : "Delete"}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle>{isUpdate ? "Edit Experience" : "Add Experience"}</DialogTitle>
              <DialogDescription>
                {isUpdate ? "Update your work experience details." : "Add a new work experience to your profile."}
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Customer Service Representative" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g. Acme Corporation" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location (Optional)</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="e.g. New York, USA" 
                          {...field} 
                          value={field.value || ""} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium mb-2">Start Date</p>
                    <div className="grid grid-cols-2 gap-2">
                      <FormField
                        control={form.control}
                        name="start_month"
                        render={({ field }) => (
                          <FormItem>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Month" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {months.map((month) => (
                                  <SelectItem key={month} value={month}>
                                    {month}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="start_year"
                        render={({ field }) => (
                          <FormItem>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Year" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {years.map((year) => (
                                  <SelectItem key={year} value={year.toString()}>
                                    {year}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  {!currentPosition && (
                    <div>
                      <p className="text-sm font-medium mb-2">End Date</p>
                      <div className="grid grid-cols-2 gap-2">
                        <FormField
                          control={form.control}
                          name="end_month"
                          render={({ field }) => (
                            <FormItem>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Month" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {months.map((month) => (
                                    <SelectItem key={month} value={month}>
                                      {month}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="end_year"
                          render={({ field }) => (
                            <FormItem>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Year" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {years.map((year) => (
                                    <SelectItem key={year} value={year.toString()}>
                                      {year}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  )}
                </div>
                
                <FormField
                  control={form.control}
                  name="current_position"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>I currently work here</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe your role and responsibilities" 
                          className="resize-none min-h-[100px]"
                          {...field} 
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <DialogFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Saving..." : (isUpdate ? "Update" : "Add")}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
} 