/**
 * File Upload Security Module
 * Handles secure file upload validation and processing
 */

// File upload security utilities
export const FILE_UPLOAD_SECURITY = {
  // Maximum file sizes by type
  MAX_SIZES: {
    image: 5 * 1024 * 1024, // 5MB
    document: 10 * 1024 * 1024, // 10MB
    video: 100 * 1024 * 1024, // 100MB
    audio: 50 * 1024 * 1024, // 50MB
  },
  
  // Allowed MIME types
  ALLOWED_TYPES: {
    image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    document: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ],
    video: ['video/mp4', 'video/webm', 'video/quicktime'],
    audio: ['audio/mpeg', 'audio/wav', 'audio/ogg']
  },
  
  // Blocked file extensions for security
  BLOCKED_EXTENSIONS: [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
    '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.sh', '.ps1'
  ]
} as const;

/**
 * Validate uploaded file security
 */
export function validateFileUpload(
  file: File,
  allowedCategory: keyof typeof FILE_UPLOAD_SECURITY.ALLOWED_TYPES
): { isValid: boolean; error?: string } {
  // Check file size
  const maxSize = FILE_UPLOAD_SECURITY.MAX_SIZES[allowedCategory];
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File too large. Maximum size: ${Math.round(maxSize / (1024 * 1024))}MB`
    };
  }
  
  // Check MIME type
  const allowedTypes = FILE_UPLOAD_SECURITY.ALLOWED_TYPES[allowedCategory];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
    };
  }
  
  // Check file extension
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (FILE_UPLOAD_SECURITY.BLOCKED_EXTENSIONS.includes(extension)) {
    return {
      isValid: false,
      error: 'File type not allowed for security reasons'
    };
  }
  
  // Additional security checks
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    return {
      isValid: false,
      error: 'Invalid file name'
    };
  }
  
  return { isValid: true };
}

/**
 * Sanitize file name for safe storage
 */
export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace special chars with underscore
    .replace(/_{2,}/g, '_') // Replace multiple underscores with single
    .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
    .toLowerCase();
}

/**
 * Generate secure file path
 */
export function generateSecureFilePath(
  fileName: string,
  category: string,
  userId?: string
): string {
  const sanitizedName = sanitizeFileName(fileName);
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  
  const basePath = userId ? `${category}/${userId}` : category;
  return `${basePath}/${timestamp}_${random}_${sanitizedName}`;
}

/**
 * Validate file content (basic checks)
 */
export async function validateFileContent(file: File): Promise<{ isValid: boolean; error?: string }> {
  try {
    // Check if file is empty
    if (file.size === 0) {
      return { isValid: false, error: 'File is empty' };
    }
    
    // For images, check if it's a valid image
    if (file.type.startsWith('image/')) {
      return validateImageFile(file);
    }
    
    // For documents, perform basic validation
    if (file.type.includes('pdf') || file.type.includes('document')) {
      return validateDocumentFile(file);
    }
    
    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'File validation failed' };
  }
}

/**
 * Validate image file
 */
async function validateImageFile(file: File): Promise<{ isValid: boolean; error?: string }> {
  return new Promise((resolve) => {
    const img = new Image();
    const url = URL.createObjectURL(file);
    
    img.onload = () => {
      URL.revokeObjectURL(url);
      
      // Check image dimensions (optional limits)
      const maxWidth = 4096;
      const maxHeight = 4096;
      
      if (img.width > maxWidth || img.height > maxHeight) {
        resolve({
          isValid: false,
          error: `Image dimensions too large. Maximum: ${maxWidth}x${maxHeight}`
        });
      } else {
        resolve({ isValid: true });
      }
    };
    
    img.onerror = () => {
      URL.revokeObjectURL(url);
      resolve({ isValid: false, error: 'Invalid image file' });
    };
    
    img.src = url;
  });
}

/**
 * Validate document file
 */
async function validateDocumentFile(file: File): Promise<{ isValid: boolean; error?: string }> {
  try {
    // Read first few bytes to check file signature
    const buffer = await file.slice(0, 8).arrayBuffer();
    const bytes = new Uint8Array(buffer);
    
    // PDF signature
    if (file.type === 'application/pdf') {
      const pdfSignature = [0x25, 0x50, 0x44, 0x46]; // %PDF
      const matches = pdfSignature.every((byte, index) => bytes[index] === byte);
      
      if (!matches) {
        return { isValid: false, error: 'Invalid PDF file' };
      }
    }
    
    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Document validation failed' };
  }
}

/**
 * File upload configuration
 */
export interface FileUploadConfig {
  category: keyof typeof FILE_UPLOAD_SECURITY.ALLOWED_TYPES;
  maxSize?: number;
  allowedTypes?: string[];
  validateContent?: boolean;
  generateSecurePath?: boolean;
}

/**
 * Comprehensive file validation
 */
export async function validateUploadedFile(
  file: File,
  config: FileUploadConfig
): Promise<{ isValid: boolean; error?: string; securePath?: string }> {
  // Basic validation
  const basicValidation = validateFileUpload(file, config.category);
  if (!basicValidation.isValid) {
    return basicValidation;
  }
  
  // Custom size limit
  if (config.maxSize && file.size > config.maxSize) {
    return {
      isValid: false,
      error: `File too large. Maximum size: ${Math.round(config.maxSize / (1024 * 1024))}MB`
    };
  }
  
  // Custom allowed types
  if (config.allowedTypes && !config.allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid file type. Allowed types: ${config.allowedTypes.join(', ')}`
    };
  }
  
  // Content validation
  if (config.validateContent) {
    const contentValidation = await validateFileContent(file);
    if (!contentValidation.isValid) {
      return contentValidation;
    }
  }
  
  // Generate secure path
  let securePath: string | undefined;
  if (config.generateSecurePath) {
    securePath = generateSecureFilePath(file.name, config.category);
  }
  
  return { isValid: true, securePath };
}

/**
 * Get file category from MIME type
 */
export function getFileCategoryFromMimeType(mimeType: string): keyof typeof FILE_UPLOAD_SECURITY.ALLOWED_TYPES | null {
  if (mimeType.startsWith('image/')) return 'image';
  if (mimeType.startsWith('video/')) return 'video';
  if (mimeType.startsWith('audio/')) return 'audio';
  if (mimeType.includes('pdf') || mimeType.includes('document') || mimeType.includes('text/')) return 'document';
  return null;
}

/**
 * Get human-readable file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if file type is supported
 */
export function isFileTypeSupported(mimeType: string): boolean {
  const allAllowedTypes = Object.values(FILE_UPLOAD_SECURITY.ALLOWED_TYPES).flat();
  return allAllowedTypes.includes(mimeType);
}
