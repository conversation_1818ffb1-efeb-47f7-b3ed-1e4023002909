'use client'

import { useEffect, useState } from 'react'
import {
  Bread<PERSON>rumbLink,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb"

interface DynamicCourseBreadcrumbProps {
  type: 'course' | 'course-link' | 'module' | 'module-link' | 'lesson'
  courseId: string
  moduleId?: string
  lessonId?: string
}

export function DynamicCourseBreadcrumb({ type, courseId, moduleId, lessonId }: DynamicCourseBreadcrumbProps) {
  const [name, setName] = useState<string>('Loading...')

  useEffect(() => {
    const fetchName = async () => {
      try {
        let url = ''
        let nameKey = ''
        
        switch (type) {
          case 'course':
          case 'course-link':
            url = `/api/individual/courses/${courseId}/breadcrumb`
            nameKey = 'courseName'
            break
          case 'module':
          case 'module-link':
            url = `/api/individual/modules/${moduleId}/breadcrumb`
            nameKey = 'moduleName'
            break
          case 'lesson':
            url = `/api/individual/lessons/${lessonId}/breadcrumb`
            nameKey = 'lessonName'
            break
        }

        const response = await fetch(url)
        if (response.ok) {
          const data = await response.json()
          setName(data[nameKey] || 'Unknown')
        } else {
          setName('Unknown')
        }
      } catch (error) {
        console.error('Error fetching breadcrumb name:', error)
        setName('Unknown')
      }
    }

    fetchName()
  }, [type, courseId, moduleId, lessonId])

  // Render based on type
  switch (type) {
    case 'course':
      return <BreadcrumbPage className="text-base font-semibold text-foreground">{name}</BreadcrumbPage>
    
    case 'course-link':
      return (
        <BreadcrumbLink href={`/individual/courses/${courseId}`} className="text-base font-medium">
          {name}
        </BreadcrumbLink>
      )
    
    case 'module':
      return <BreadcrumbPage className="text-base font-semibold text-foreground">{name}</BreadcrumbPage>
    
    case 'module-link':
      return (
        <BreadcrumbLink href={`/individual/courses/${courseId}/modules/${moduleId}`} className="text-base font-medium">
          {name}
        </BreadcrumbLink>
      )
    
    case 'lesson':
      return <BreadcrumbPage className="text-base font-semibold text-foreground">{name}</BreadcrumbPage>
    
    default:
      return <BreadcrumbPage className="text-base font-semibold text-foreground">{name}</BreadcrumbPage>
  }
}
