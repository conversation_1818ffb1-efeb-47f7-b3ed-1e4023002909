'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/components/ui/use-toast';
import { Toaster } from '@/components/ui/toaster';
import { v4 as uuidv4 } from 'uuid';
import { 
  ArrowLeft, 
  Plus, 
  Loader2, 
  BookOpen, 
  Edit, 
  Trash, 
  Grip,
  MoreHorizontal,
  ChevronRight,
  Clock,
  Youtube,
  Presentation,
  Image as ImageIcon,
  Move,
  FileQuestion,
  Brain,
  CheckSquare
} from 'lucide-react';
import { 
  DndContext, 
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { FileUpload } from '@/components/ui/file-upload';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

type Module = {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'published' | 'archived';
};

type Lesson = {
  id: string;
  title: string;
  description: string | null;
  order_index: number;
  duration_minutes: number | null;
  created_at: string;
  updated_at: string;
  lesson_type?: 'video' | 'slides' | 'image' | 'quiz';
  video_url?: string | null;
  media_url?: string | null;
  total_questions: number;
  is_required: boolean;
  passing_required: boolean;
};

// Define form schema for lesson
const lessonFormSchema = z.object({
  title: z.string().min(3, {
    message: 'Title must be at least 3 characters.',
  }).max(100, {
    message: 'Title must not be longer than 100 characters.',
  }),
  description: z.string().optional(),
  durationMinutes: z.coerce.number().int().positive().optional(),
  orderIndex: z.coerce.number().int().nonnegative(),
  lessonType: z.enum(['video', 'slides', 'image', 'quiz']),
  videoUrl: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
  mediaUrl: z.string().optional(),
  videoFile: z.string().optional(), // For uploaded video files
  thumbnailUrl: z.string().optional(), // For video thumbnails
  // Quiz specific fields
  isModuleQuiz: z.boolean().optional().default(false),
  passingScore: z.coerce.number().int().min(0).max(100).optional(),
  quizInstructions: z.string().optional(),
  // Additional quiz fields to match assessments table
  category: z.string().optional().default('Technical'),
  instructions: z.string().optional(),
  whatItChecks: z.string().optional().default('Module comprehension'),
  whatToExpect: z.string().optional().default('Questions about the content of this module')
});

type LessonFormValues = z.infer<typeof lessonFormSchema>;

// Create a sortable lesson item component
function SortableLesson({ lesson, onEdit, onDelete }: { 
  lesson: Lesson, 
  onEdit: (lesson: Lesson) => void, 
  onDelete: (id: string) => void 
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: lesson.id });
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? 10 : 1,
    opacity: isDragging ? 0.8 : 1,
    position: isDragging ? 'relative' as const : 'static' as const,
    boxShadow: isDragging ? '0 0 12px rgba(0, 0, 0, 0.12)' : 'none',
  };

  // Generate a placeholder image URL if no media_url is available
  const getFeatureImage = () => {
    if (lesson.media_url) {
      return lesson.media_url;
    }
    
    if (lesson.lesson_type === 'video' && lesson.video_url) {
      // Extract video ID from YouTube URL
      const videoId = lesson.video_url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
      if (videoId && videoId[1]) {
        return `https://img.youtube.com/vi/${videoId[1]}/mqdefault.jpg`;
      }
    }
    
    // Default placeholders based on lesson type
    if (lesson.lesson_type === 'video') {
      return 'https://placehold.co/80x45/f87171/ffffff?text=Video';
    } else if (lesson.lesson_type === 'slides') {
      return 'https://placehold.co/80x45/60a5fa/ffffff?text=Slides';
    } else if (lesson.lesson_type === 'quiz') {
      return 'https://placehold.co/80x45/a855f7/ffffff?text=Quiz';
    } else {
      return 'https://placehold.co/80x45/4ade80/ffffff?text=Image';
    }
  };

  return (
    <div 
      ref={setNodeRef}
      style={style}
      className={`p-5 hover:bg-accent/50 transition-colors flex items-center justify-between ${isDragging ? 'bg-accent/80' : ''}`}
    >
      <div className="flex items-center flex-1 gap-4">
        <div 
          {...attributes} 
          {...listeners}
          className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full font-medium text-primary cursor-move hover:bg-primary/20"
        >
          <Move className="h-4 w-4" />
        </div>
        <div className="flex items-center gap-3 flex-1">
          <div className="relative overflow-hidden rounded-md h-12 w-20 flex-shrink-0">
            <div className="absolute inset-0">
              <img 
                src={getFeatureImage()} 
                alt={lesson.title} 
                className="h-full w-full object-cover"
                onError={(e) => {
                  e.currentTarget.src = 'https://placehold.co/80x45/cbd5e1/64748b?text=BPO';
                }}
              />
            </div>
            <div className={`absolute bottom-0 right-0 p-1 ${
              lesson.lesson_type === 'video' ? 'bg-red-100 text-red-600' : 
              lesson.lesson_type === 'slides' ? 'bg-blue-100 text-blue-600' : 
              lesson.lesson_type === 'quiz' ? 'bg-purple-100 text-purple-600' :
              'bg-green-100 text-green-600'
            }`}>
              {lesson.lesson_type === 'video' && <Youtube className="h-3 w-3" />}
              {lesson.lesson_type === 'slides' && <Presentation className="h-3 w-3" />}
              {lesson.lesson_type === 'image' && <ImageIcon className="h-3 w-3" />}
              {lesson.lesson_type === 'quiz' && <FileQuestion className="h-3 w-3" />}
            </div>
          </div>
          <div>
            <h3 className="font-medium">{lesson.title}</h3>
            {lesson.description && (
              <p 
                className="text-sm text-muted-foreground line-clamp-1"
                dangerouslySetInnerHTML={{ __html: lesson.description }}
              ></p>
            )}
            {lesson.lesson_type === 'quiz' && (
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 border border-purple-200">
                  Quiz
                </span>
                {lesson.total_questions > 0 && (
                  <span className="text-xs text-muted-foreground">
                    {lesson.total_questions} {lesson.total_questions === 1 ? 'question' : 'questions'}
                  </span>
                )}
                {lesson.is_required && (
                  <span className="text-xs px-2 py-0.5 rounded-full bg-blue-50 text-blue-700 border border-blue-200">
                    Required
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-4">
        {lesson.duration_minutes && (
          <span className="text-sm text-muted-foreground flex items-center">
            <Clock className="h-4 w-4 mr-1" />
            {lesson.duration_minutes} mins
          </span>
        )}
        <div className="flex gap-2">
          <Button variant="ghost" size="icon" onClick={() => onEdit(lesson)} className="h-8 w-8">
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" onClick={() => onDelete(lesson.id)} className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50">
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default function LessonsPage() {
  const { moduleId } = useParams();
  const router = useRouter();
  const { toast } = useToast();

  const [module, setModule] = useState<Module | null>(null);
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Form states
  const [showLessonForm, setShowLessonForm] = useState(false);
  const [currentLessonId, setCurrentLessonId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [lessonToDelete, setLessonToDelete] = useState<string | null>(null);

  const form = useForm<LessonFormValues>({
    resolver: zodResolver(lessonFormSchema),
    defaultValues: {
      title: '',
      description: '',
      durationMinutes: undefined,
      orderIndex: 0,
      lessonType: 'video',
      videoUrl: '',
      mediaUrl: '',
      videoFile: '',
      thumbnailUrl: '',
      isModuleQuiz: false,
      passingScore: undefined,
      quizInstructions: '',
      category: 'Technical',
      instructions: '',
      whatItChecks: 'Module comprehension',
      whatToExpect: 'Questions about the content of this module'
    }
  });

  // Setup DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Add a new state for quiz questions and a selected question for editing
  const [quizQuestions, setQuizQuestions] = useState<Array<{
    id: string;
    question: string;
    questionType: 'multiple_choice' | 'true_false' | 'text_input';
    options: Array<{
      id: string;
      text: string;
      isCorrect: boolean;
    }>;
    points: number;
  }>>([]);
  
  const [selectedQuestion, setSelectedQuestion] = useState<string | null>(null);
  
  // Function to add a new question
  const addQuestion = () => {
    const newQuestion = {
      id: uuidv4(),
      question: '',
      questionType: 'multiple_choice' as const,
      options: [
        { id: uuidv4(), text: '', isCorrect: false },
        { id: uuidv4(), text: '', isCorrect: false },
        { id: uuidv4(), text: '', isCorrect: false },
        { id: uuidv4(), text: '', isCorrect: false }
      ],
      points: 1
    };
    
    setQuizQuestions([...quizQuestions, newQuestion]);
    setSelectedQuestion(newQuestion.id);
  };
  
  // Function to update a question
  const updateQuestion = (id: string, updates: Partial<typeof quizQuestions[0]>) => {
    setQuizQuestions(quizQuestions.map(q => 
      q.id === id ? { ...q, ...updates } : q
    ));
  };
  
  // Function to delete a question
  const deleteQuestion = (id: string) => {
    setQuizQuestions(quizQuestions.filter(q => q.id !== id));
    if (selectedQuestion === id) {
      setSelectedQuestion(quizQuestions.length > 1 ? quizQuestions[0].id : null);
    }
  };
  
  // Function to add an option to a question
  const addOption = (questionId: string) => {
    setQuizQuestions(quizQuestions.map(q => {
      if (q.id === questionId) {
        return {
          ...q,
          options: [
            ...q.options,
            { id: uuidv4(), text: '', isCorrect: false }
          ]
        };
      }
      return q;
    }));
  };
  
  // Function to update an option
  const updateOption = (questionId: string, optionId: string, updates: Partial<{ text: string, isCorrect: boolean }>) => {
    setQuizQuestions(quizQuestions.map(q => {
      if (q.id === questionId) {
        return {
          ...q,
          options: q.options.map(o => 
            o.id === optionId ? { ...o, ...updates } : 
            // If setting this option as correct and question type is single-answer,
            // set all other options to incorrect
            updates.isCorrect && q.questionType !== 'text_input' ? 
              { ...o, isCorrect: o.id === optionId } : o
          )
        };
      }
      return q;
    }));
  };
  
  // Function to delete an option
  const deleteOption = (questionId: string, optionId: string) => {
    setQuizQuestions(quizQuestions.map(q => {
      if (q.id === questionId) {
        return {
          ...q,
          options: q.options.filter(o => o.id !== optionId)
        };
      }
      return q;
    }));
  };

  useEffect(() => {
    if (moduleId) {
      fetchModuleAndLessons(moduleId as string);
    }
  }, [moduleId]);

  async function fetchModuleAndLessons(id: string) {
    setLoading(true);

    try {
      // Fetch module details
      const { data: moduleData, error: moduleError } = await supabase
        .from('training_modules')
        .select('id, title, description, status')
        .eq('id', id)
        .single();

      if (moduleError) throw moduleError;
      setModule(moduleData);

      // Fetch lessons
      const { data: lessonsData, error: lessonsError } = await supabase
        .from('lessons')
        .select('*')
        .eq('module_id', id)
        .order('order_index', { ascending: true });

      if (lessonsError) throw lessonsError;
      
      // Fetch module assessments
      const { data: moduleAssessmentsData, error: moduleAssessmentsError } = await supabase
        .from('module_assessments')
        .select(`
          id, 
          order_index, 
          is_required, 
          passing_required,
          assessments:assessment_id (
            id, 
            title, 
            description, 
            duration, 
            total_questions
          )
        `)
        .eq('module_id', id)
        .order('order_index', { ascending: true });
      
      if (moduleAssessmentsError) {
        console.error('Error fetching module assessments:', moduleAssessmentsError);
      }
      
      // Convert module assessments to lesson format for display
      const quizLessons = moduleAssessmentsData ? moduleAssessmentsData.map(moduleAssessment => {
        // Access the nested assessment data
        const assessment = moduleAssessment.assessments as any;
        
        // Extract the duration in minutes from the duration string
        let durationMinutes = null;
        if (assessment && assessment.duration) {
          const match = assessment.duration.match(/(\d+)/);
          if (match) {
            durationMinutes = parseInt(match[1], 10);
          }
        }
        
        return {
          id: moduleAssessment.id,
          title: assessment?.title || 'Untitled Quiz',
          description: assessment?.description || null,
          order_index: moduleAssessment.order_index,
          duration_minutes: durationMinutes,
          created_at: new Date().toISOString(), // Placeholder
          updated_at: new Date().toISOString(), // Placeholder
          lesson_type: 'quiz',
          // Custom fields for quizzes
          is_quiz: true,
          assessment_id: assessment?.id,
          is_required: moduleAssessment.is_required || false,
          passing_required: moduleAssessment.passing_required || false,
          total_questions: assessment?.total_questions || 0
        };
      }) : [];
      
      // Create a map of all content ids to avoid duplicates
      const allContentMap = new Map();
      
      // Add lessons to the map first
      lessonsData?.forEach(lesson => {
        allContentMap.set(lesson.id, lesson);
      });
      
      // Add quizzes to the map, will overwrite any duplicates
      quizLessons?.forEach(quiz => {
        allContentMap.set(quiz.id, quiz);
      });
      
      // Convert map to array and sort by order_index
      const allContent = Array.from(allContentMap.values())
        .sort((a, b) => a.order_index - b.order_index);
      
      setLessons(allContent);
    } catch (error: any) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to load module and lessons',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }

  const handleBackClick = () => {
    router.push('/admin/training-modules');
  };

  const handleAddLesson = () => {
    setCurrentLessonId(null);
    
    // Set the next order index as default
    const nextOrderIndex = lessons.length > 0 
      ? Math.max(...lessons.map(l => l.order_index)) + 1 
      : 0;
    
    form.reset({
      title: '',
      description: '',
      durationMinutes: undefined,
      orderIndex: nextOrderIndex,
      lessonType: 'video',
      videoUrl: '',
      mediaUrl: '',
      videoFile: '',
      thumbnailUrl: '',
      isModuleQuiz: false,
      passingScore: undefined,
      quizInstructions: '',
      category: 'Technical',
      instructions: '',
      whatItChecks: 'Module comprehension',
      whatToExpect: 'Questions about the content of this module'
    });
    
    setShowLessonForm(true);
  };

  const handleEditLesson = (lesson: Lesson) => {
    setCurrentLessonId(lesson.id);
    
    form.reset({
      title: lesson.title,
      description: lesson.description || '',
      durationMinutes: lesson.duration_minutes || undefined,
      orderIndex: lesson.order_index,
      lessonType: lesson.lesson_type || 'video',
      videoUrl: lesson.video_url || '',
      mediaUrl: lesson.media_url || '',
      videoFile: '',
      thumbnailUrl: '',
      isModuleQuiz: false,
      passingScore: undefined,
      quizInstructions: '',
      category: 'Technical',
      instructions: '',
      whatItChecks: 'Module comprehension',
      whatToExpect: 'Questions about the content of this module'
    });
    
    setShowLessonForm(true);
  };

  const handleDeleteLesson = (id: string) => {
    setLessonToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteLesson = async () => {
    if (!lessonToDelete) return;
    
    try {
      const { error } = await supabase
        .from('lessons')
        .delete()
        .eq('id', lessonToDelete);
      
      if (error) throw error;
      
      toast({
        title: 'Lesson Deleted',
        description: 'The lesson has been deleted successfully.',
      });
      
      // Refresh lessons
      if (moduleId) {
        fetchModuleAndLessons(moduleId as string);
      }
    } catch (error: any) {
      console.error('Error deleting lesson:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete the lesson',
        variant: 'destructive',
      });
    } finally {
      setDeleteDialogOpen(false);
      setLessonToDelete(null);
    }
  };

  const onSubmitLesson = async (values: LessonFormValues) => {
    try {
      console.log('Submitting lesson with values:', values);
      
      // If this is a quiz, handle it differently
      if (values.lessonType === 'quiz') {
        console.log('Creating or updating a quiz/assessment');
        
        if (quizQuestions.length === 0) {
          toast({
            title: 'Error',
            description: 'Please add at least one question to your quiz.',
            variant: 'destructive'
          });
          return;
        }
        
        // Check if we're updating an existing quiz
        if (currentLessonId) {
          try {
            console.log('Updating existing quiz with ID:', currentLessonId);
            
            // Fetch the existing module assessment data
            const { data: existingModuleAssessment, error: fetchError } = await supabase
              .from('module_assessments')
              .select(`
                id, 
                assessment_id
              `)
              .eq('id', currentLessonId)
              .single();
              
            if (fetchError) {
              console.error('Error fetching module assessment:', fetchError);
              throw new Error('Failed to fetch module assessment details');
            }
            
            if (!existingModuleAssessment) {
              throw new Error('Module assessment not found');
            }
            
            console.log('Found existing module assessment:', existingModuleAssessment);
            
            // Prepare the update data
            const moduleAssessmentUpdateData = {
              title: values.title,
              description: values.description || '',
              category: values.category || 'Technical',
              duration: values.durationMinutes ? `${values.durationMinutes} minutes` : '15 minutes',
              instructions: values.instructions || values.quizInstructions || '',
              total_questions: quizQuestions.length,
              passing_score: values.passingScore || 70,
              what_it_checks: values.whatItChecks || 'Module comprehension',
              what_to_expect: values.whatToExpect || 'Questions about the content of this module',
              is_required: values.isModuleQuiz || true,
              order_index: values.orderIndex,
              passing_required: values.isModuleQuiz || false,
              updated_at: new Date().toISOString()
            };
            
            // Update the module assessment directly
            const { error: updateModuleError } = await supabase
              .from('module_assessments')
              .update(moduleAssessmentUpdateData)
              .eq('id', currentLessonId);
              
            if (updateModuleError) {
              console.error('Error updating module assessment:', updateModuleError);
              throw new Error('Failed to update module assessment');
            }
            
            // If there's a linked assessment, also update it for backward compatibility
            if (existingModuleAssessment.assessment_id) {
              const assessmentUpdateData = {
                title: values.title,
                description: values.description || '',
                category: values.category || 'Technical',
                duration: values.durationMinutes ? `${values.durationMinutes} minutes` : '15 minutes',
                instructions: values.instructions || values.quizInstructions || '',
                total_questions: quizQuestions.length,
                passing_score: values.passingScore || 70,
                what_it_checks: values.whatItChecks || 'Module comprehension',
                what_to_expect: values.whatToExpect || 'Questions about the content of this module'
              };
              
              const { error: updateAssessmentError } = await supabase
                .from('assessments')
                .update(assessmentUpdateData)
                .eq('id', existingModuleAssessment.assessment_id);
                
              if (updateAssessmentError) {
                console.log('Warning: Could not update linked assessment:', updateAssessmentError);
                // Continue anyway as we primarily want to update the module_assessment
              }
            }
            
            // Delete existing questions
            const { error: deleteQuestionsError } = await supabase
              .from('module_assessment_questions')
              .delete()
              .eq('module_assessment_id', currentLessonId);
              
            if (deleteQuestionsError) {
              console.error('Error deleting existing questions:', deleteQuestionsError);
              throw new Error('Failed to delete existing questions');
            }
            
            // Create new questions
            const questionPromises = quizQuestions.map(async (question, index) => {
              try {
                // Prepare the options as JSON
                const optionsJson = question.options.map(option => ({
                  id: option.id,
                  text: option.text,
                  is_correct: option.isCorrect
                }));
                
                // Get the correct answer(s)
                const correctAnswers = question.options
                  .filter(option => option.isCorrect)
                  .map(option => option.text)
                  .join(', ');
                
                // Create the question
                const questionData = {
                  module_assessment_id: currentLessonId,
                  question_text: question.question,
                  question_type: question.questionType,
                  options: optionsJson,
                  correct_answer: correctAnswers,
                  points: question.points,
                  time_limit: null,
                  order_index: index
                };
                
                const { data, error } = await supabase
                  .from('module_assessment_questions')
                  .insert([questionData])
                  .select();
                  
                if (error) {
                  console.error(`Error creating question ${index + 1}:`, error);
                  throw error;
                }
                
                return data;
              } catch (e) {
                console.error(`Error creating question ${index + 1}:`, e);
                throw e;
              }
            });
            
            await Promise.all(questionPromises);
            
            toast({
              title: 'Quiz Updated',
              description: `Quiz "${values.title}" has been updated with ${quizQuestions.length} questions.`
            });
            
            // Reset and close form
            setQuizQuestions([]);
            setSelectedQuestion(null);
            setShowLessonForm(false);
            fetchModuleAndLessons(moduleId as string);
            return;
          } catch (error: any) {
            console.error('Error updating quiz:', error);
            toast({
              title: 'Error',
              description: `Failed to update quiz: ${error.message || 'Unknown error'}`,
              variant: 'destructive'
            });
            return;
          }
        }
        
        // If not updating, proceed with creating a new quiz
        // We need to create an assessment first, then link it to the module
        // Step 1: Create the assessment directly in module_assessments table
        const moduleAssessmentData = {
          module_id: moduleId as string,
          title: values.title,
          description: values.description || '',
          category: values.category || 'Technical',
          duration: values.durationMinutes ? `${values.durationMinutes} min` : '15 min',
          instructions: values.instructions || values.quizInstructions || 'Please answer all questions to the best of your ability.',
          total_questions: quizQuestions.length,
          passing_score: values.passingScore || 70,
          what_it_checks: values.whatItChecks || 'Module comprehension',
          what_to_expect: values.whatToExpect || 'Questions about the content of this module',
          is_active: true,
          completions: 0,
          avg_score: 0,
          is_required: values.isModuleQuiz || true,
          order_index: values.orderIndex,
          passing_required: values.isModuleQuiz || false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        
        const { data: moduleAssessment, error: moduleAssessmentError } = await supabase
          .from('module_assessments')
          .insert([moduleAssessmentData])
          .select();
          
        if (moduleAssessmentError) {
          console.error('Error creating module assessment:', JSON.stringify(moduleAssessmentError, null, 2));
          throw new Error(`Failed to create module assessment: ${moduleAssessmentError.message || moduleAssessmentError.details || JSON.stringify(moduleAssessmentError)}`);
        }
        
        console.log('Module assessment created:', moduleAssessment);
        
        if (!moduleAssessment || moduleAssessment.length === 0) {
          throw new Error('Module assessment was created but no data was returned');
        }
        
        // Create new questions
        const questionPromises = quizQuestions.map(async (question, index) => {
          try {
            // Prepare the options as JSON
            const optionsJson = question.options.map(option => ({
              id: option.id,
              text: option.text,
              is_correct: option.isCorrect
            }));
            
            // Get the correct answer(s)
            const correctAnswers = question.options
              .filter(option => option.isCorrect)
              .map(option => option.text)
              .join(', ');
            
            // Create the question
            const questionData = {
              module_assessment_id: moduleAssessment[0].id,
              question_text: question.question,
              question_type: question.questionType,
              options: optionsJson,
              correct_answer: correctAnswers,
              points: question.points,
              time_limit: null,
              order_index: index
            };
            
            console.log(`Creating question ${index + 1} with data:`, questionData);
            
            const { data, error } = await supabase
              .from('module_assessment_questions')
              .insert([questionData])
              .select();
              
            if (error) {
              console.error(`Error creating question ${index + 1}:`, error);
              throw error;
            }
            
            console.log(`Question ${index + 1} created successfully:`, data);
            return data;
          } catch (e) {
            console.error(`Error creating question ${index + 1}:`, e);
            throw e;
          }
        });
        
        try {
          const questionResults = await Promise.all(questionPromises);
          console.log('All questions created successfully', questionResults);
          
          toast({
            title: 'Quiz Created',
            description: `Quiz "${values.title}" has been created with ${quizQuestions.length} questions.`
          });
          
          // Reset the questions state
          setQuizQuestions([]);
          setSelectedQuestion(null);
          
          // Close form and refresh
          setShowLessonForm(false);
          if (moduleId) {
            fetchModuleAndLessons(moduleId as string);
          }
        } catch (error) {
          console.error('Error creating questions:', error);
          throw new Error('Failed to create one or more questions');
        }
      }
      
      // For regular lessons, continue with the existing flow
      // Determine the video URL to use (either external or uploaded file)
      const videoUrl = values.videoFile || values.videoUrl || null;
      
      // Create lesson data with all fields including the new ones
      const lessonData: {
        module_id: string,
        title: string,
        description: string | null,
        order_index: number,
        lesson_type: string,
        video_url: string | null,
        media_url: string | null,
        duration_minutes?: number
      } = {
        module_id: moduleId as string,
        title: values.title,
        description: values.description || null,
        order_index: values.orderIndex,
        lesson_type: values.lessonType,
        video_url: values.lessonType === 'video' ? videoUrl : null,
        media_url: values.lessonType !== 'video' ? (values.mediaUrl || null) : null
      };
      
      // Optional fields
      if (values.durationMinutes) {
        lessonData.duration_minutes = values.durationMinutes;
      }
      
      // Log the data we're sending to Supabase
      console.log('Sending to Supabase:', lessonData);
      
      try {
        if (currentLessonId) {
          // Update lesson with all fields
          console.log('Updating lesson with ID:', currentLessonId);
          const { error } = await supabase
            .from('lessons')
            .update(lessonData)
            .eq('id', currentLessonId);
          
          if (error) {
            console.error('Error updating lesson:', error);
            throw error;
          }
          
          toast({
            title: 'Lesson Updated',
            description: 'The lesson has been updated successfully.'
          });
        } else {
          // Create new lesson with all fields
          console.log('Creating new lesson');
          const { data, error } = await supabase
            .from('lessons')
            .insert([lessonData])
            .select();
          
          if (error) {
            console.error('Error creating lesson:', JSON.stringify(error, null, 2));
            throw new Error(`Failed to create lesson: ${error.message || error.details || JSON.stringify(error)}`);
          }
          
          console.log('Lesson created successfully:', data);
          
          toast({
            title: 'Lesson Created',
            description: 'The new lesson has been created successfully.'
          });
        }
      } catch (innerError: any) {
        console.error('Error in Supabase operation:', 
          innerError instanceof Error 
            ? innerError.message 
            : JSON.stringify(innerError, null, 2)
        );
        throw innerError;
      }
        
      // Close form and refresh
      setShowLessonForm(false);
      if (moduleId) {
        fetchModuleAndLessons(moduleId as string);
      }
    } catch (error: any) {
      console.error('Error saving lesson:', 
        error instanceof Error 
          ? error.message 
          : JSON.stringify(error, null, 2)
      );
      
      // Get more detailed error message
      let errorDetails = '';
      
      if (error instanceof Error && error.message) {
        errorDetails = error.message;
      } else if (error && typeof error === 'object') {
        if (error.message) {
          errorDetails = error.message;
        } else if (error.details) {
          errorDetails = typeof error.details === 'string' 
            ? error.details 
            : JSON.stringify(error.details);
        } else if (error.code) {
          errorDetails = `Error code: ${error.code}`;
        } else {
          errorDetails = JSON.stringify(error);
        }
      } else {
        errorDetails = String(error);
      }
      
      toast({
        title: 'Error',
        description: `Failed to save the lesson: ${errorDetails}`,
        variant: 'destructive',
      });
    }
  };

  // Add a new handler for drag end
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      // Find the indices of the dragged item and the drop target
      const oldIndex = lessons.findIndex(lesson => lesson.id === active.id);
      const newIndex = lessons.findIndex(lesson => lesson.id === over.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        // Create a new array with the items reordered
        const newLessons = arrayMove(lessons, oldIndex, newIndex);
        
        // Update the UI immediately for a responsive feel
        setLessons(newLessons);
        
        // Prepare a batch of updates with new order_index values
        const updates = newLessons.map((lesson: Lesson, index: number) => ({
          id: lesson.id,
          order_index: index
        }));
        
        try {
          // Update all lessons with their new order_index
          const { error } = await supabase
            .from('lessons')
            .upsert(updates, { onConflict: 'id' });
          
          if (error) throw error;
          
          toast({
            title: 'Lessons Reordered',
            description: 'The lesson order has been updated successfully.'
          });
        } catch (error) {
          console.error('Error updating lesson order:', error);
          toast({
            title: 'Error',
            description: 'Failed to update lesson order. Please try again.',
            variant: 'destructive',
          });
          
          // Revert to the original order if there was an error
          if (moduleId) {
            fetchModuleAndLessons(moduleId as string);
          }
        }
      }
    }
  };

  if (loading) {
    return (
      <div className="p-6 flex flex-col items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="mt-4 text-muted-foreground">Loading module lessons...</p>
      </div>
    );
  }

  if (!module) {
    return (
      <div className="p-6 space-y-6">
        <Button variant="outline" onClick={handleBackClick} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Modules
        </Button>
        
        <div className="flex flex-col items-center justify-center py-12 border rounded-lg">
          <p className="text-muted-foreground">Module not found</p>
          <Button onClick={handleBackClick} className="mt-4">
            Return to Training Modules
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-8 max-w-7xl mx-auto">
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={handleBackClick} className="gap-1 text-muted-foreground hover:text-foreground">
          <ArrowLeft className="h-4 w-4" />
          Back to Modules
        </Button>
      </div>
      
      <div className="flex flex-col gap-2">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 bg-gradient-to-r from-primary/10 to-primary/5 p-6 rounded-lg border border-muted">
          <div>
            <h1 className="text-3xl font-bold tracking-tight text-primary">{module.title}</h1>
            <p className="text-muted-foreground mt-2 max-w-2xl">
              {module.description ? module.description.slice(0, 150) + (module.description.length > 150 ? '...' : '') : 'No description provided.'}
            </p>
          </div>
          <Button onClick={handleAddLesson} size="lg" className="shadow-sm">
            <Plus className="mr-2 h-4 w-4" />
            <span>Add Content</span>
          </Button>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-[250px_1fr] gap-6 mt-4">
          <div className="bg-muted/30 p-4 rounded-lg border hidden lg:block">
            <h3 className="font-medium text-lg mb-4">Module Information</h3>
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium text-muted-foreground">Status</h4>
                <div className="mt-1 flex items-center">
                  <div className={`h-2 w-2 rounded-full mr-2 ${
                    module.status === 'published' ? 'bg-green-500' : 
                    module.status === 'draft' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="capitalize">{module.status}</span>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-muted-foreground">Total Lessons</h4>
                <p className="mt-1">{lessons.length}</p>
              </div>
            </div>
          </div>
          
          <div>
            {lessons.length === 0 ? (
              <div className="flex flex-col items-center justify-center border rounded-xl py-16 bg-muted/10">
                <div className="bg-primary/10 p-5 rounded-full mb-4">
                  <BookOpen className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-medium">No lessons found</h3>
                <p className="mt-2 text-muted-foreground text-center max-w-sm">
                  Get started by adding your first lesson to this module
                </p>
                <Button onClick={handleAddLesson} className="mt-6">
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Lesson
                </Button>
              </div>
            ) : (
              <div className="bg-card rounded-xl border shadow-sm">
                <div className="p-6 border-b flex justify-between items-center">
                  <div>
                    <h2 className="text-xl font-semibold">Lessons</h2>
                    <p className="text-sm text-muted-foreground">Organize and manage module content</p>
                  </div>
                  <Button variant="outline" onClick={handleAddLesson} className="ml-auto">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Content
                  </Button>
                </div>
                
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext 
                    items={lessons.map(lesson => lesson.id)} 
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="divide-y">
                      {lessons.map((lesson) => (
                        <SortableLesson
                          key={lesson.id}
                          lesson={lesson}
                          onEdit={handleEditLesson}
                          onDelete={handleDeleteLesson}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Lesson Form Sheet */}
      <Sheet open={showLessonForm} onOpenChange={setShowLessonForm}>
        <SheetContent className="sm:max-w-[600px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>{currentLessonId ? 'Edit Content' : 'Add New Content'}</SheetTitle>
            <SheetDescription>
              {currentLessonId 
                ? 'Update the details of this content.' 
                : 'Add a lesson or quiz to this training module.'}
            </SheetDescription>
          </SheetHeader>
          
          <div className="py-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmitLesson)} className="space-y-4">
                {/* Content Type Selector */}
                <div className="mb-6">
                  <FormField
                    control={form.control}
                    name="lessonType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base">Content Type</FormLabel>
                        <div className="grid grid-cols-2 gap-4 pt-2">
                          <div
                            className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer transition-all ${
                              field.value !== 'quiz' 
                                ? 'bg-primary/10 border-primary/30 ring-2 ring-primary/20' 
                                : 'bg-card hover:bg-muted/50'
                            }`}
                            onClick={() => field.onChange('video')}
                          >
                            <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
                              <Presentation className="h-6 w-6 text-primary" />
                            </div>
                            <h3 className="font-medium">Lesson</h3>
                            <p className="text-xs text-center text-muted-foreground mt-1">
                              Add videos, slides, or images
                            </p>
                          </div>
                          
                          <div
                            className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer transition-all ${
                              field.value === 'quiz' 
                                ? 'bg-primary/10 border-primary/30 ring-2 ring-primary/20' 
                                : 'bg-card hover:bg-muted/50'
                            }`}
                            onClick={() => field.onChange('quiz')}
                          >
                            <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center mb-3">
                              <FileQuestion className="h-6 w-6 text-purple-600" />
                            </div>
                            <h3 className="font-medium">Quiz</h3>
                            <p className="text-xs text-center text-muted-foreground mt-1">
                              Create an assessment or quiz
                            </p>
                          </div>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{form.watch('lessonType') === 'quiz' ? 'Quiz Title' : 'Lesson Title'}</FormLabel>
                      <FormControl>
                        <Input placeholder={form.watch('lessonType') === 'quiz' ? "Enter quiz title" : "Enter lesson title"} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <RichTextEditor 
                          value={field.value || ""} 
                          onChange={field.onChange}
                          placeholder="Describe this content..."
                          minHeight="150px"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="durationMinutes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Duration (minutes)</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            placeholder="e.g. 30"
                            {...field}
                            value={field.value || ''}
                            onChange={(e) => {
                              const value = e.target.value === '' ? undefined : parseInt(e.target.value, 10);
                              field.onChange(value);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="orderIndex"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Order</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field}
                            onChange={(e) => {
                              field.onChange(parseInt(e.target.value, 10));
                            }}
                          />
                        </FormControl>
                        <FormDescription>
                          Sequence in the module
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                {/* Display lesson type selector only when Lesson is selected */}
                {form.watch('lessonType') !== 'quiz' && (
                  <FormField
                    control={form.control}
                    name="lessonType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lesson Format</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a format" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="video">
                              <div className="flex items-center gap-2">
                                <Youtube className="h-4 w-4" />
                                <span>Video</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="slides">
                              <div className="flex items-center gap-2">
                                <Presentation className="h-4 w-4" />
                                <span>Presentation</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="image">
                              <div className="flex items-center gap-2">
                                <ImageIcon className="h-4 w-4" />
                                <span>Image</span>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                {form.watch('lessonType') === 'video' && (
                  <>
                    <div className="flex flex-col gap-4 p-4 border rounded-md">
                      <div className="text-sm font-medium">Choose one of the following options:</div>
                      
                      <FormField
                        control={form.control}
                        name="videoUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Video URL (External Link)</FormLabel>
                            <FormControl>
                              <Input 
                                placeholder="Enter video URL" 
                                {...field} 
                                onChange={(e) => {
                                  field.onChange(e);
                                  // Clear videoFile when URL is provided
                                  if (e.target.value) {
                                    form.setValue('videoFile', '');
                                  }
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              Enter a direct link to a video file (mp4, webm, etc.) or a YouTube video URL
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="relative flex items-center py-2">
                        <div className="flex-grow border-t border-gray-200"></div>
                        <span className="flex-shrink mx-4 text-gray-400 text-sm">OR</span>
                        <div className="flex-grow border-t border-gray-200"></div>
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="videoFile"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Upload Video File</FormLabel>
                            <FormControl>
                              <FileUpload
                                bucket="training-media"
                                path="videos"
                                acceptedFileTypes="video/*"
                                maxSize={200 * 1024 * 1024} // 200MB limit
                                onUploadComplete={(url) => {
                                  field.onChange(url);
                                  // Clear videoUrl when file is uploaded
                                  if (url) {
                                    form.setValue('videoUrl', '');
                                  }
                                }}
                                currentFileUrl={field.value}
                                label="Upload Video"
                              />
                            </FormControl>
                            <FormDescription>
                              Upload a video file (max 200MB). Supported formats: MP4, WebM.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="relative flex items-center py-2">
                        <div className="flex-grow border-t border-gray-200"></div>
                        <span className="flex-shrink mx-4 text-gray-400 text-sm">Optional</span>
                        <div className="flex-grow border-t border-gray-200"></div>
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="thumbnailUrl"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Video Thumbnail (Optional)</FormLabel>
                            <FormControl>
                              <FileUpload
                                bucket="training-media"
                                path="thumbnails"
                                acceptedFileTypes="image/*"
                                maxSize={5 * 1024 * 1024} // 5MB limit
                                onUploadComplete={(url) => field.onChange(url)}
                                currentFileUrl={field.value}
                                label="Upload Thumbnail"
                              />
                            </FormControl>
                            <FormDescription>
                              Upload a thumbnail image for the video (max 5MB). If not provided, a default thumbnail will be used.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </>
                )}
                
                {form.watch('lessonType') !== 'video' && (
                  <FormField
                    control={form.control}
                    name="mediaUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {form.watch('lessonType') === 'slides' ? 'Upload Presentation' : 'Upload Image'}
                        </FormLabel>
                        <FormControl>
                          <FileUpload
                            bucket="training-media"
                            path={form.watch('lessonType') === 'slides' ? 'slides' : 'images'}
                            acceptedFileTypes={form.watch('lessonType') === 'slides' ? '.pdf,.pptx' : 'image/*'}
                            maxSize={form.watch('lessonType') === 'slides' ? 10 * 1024 * 1024 : 5 * 1024 * 1024}
                            onUploadComplete={(url) => field.onChange(url)}
                            currentFileUrl={field.value}
                            label={form.watch('lessonType') === 'slides' ? 'Upload Presentation File' : 'Upload Image File'}
                          />
                        </FormControl>
                        <FormDescription>
                          {form.watch('lessonType') === 'slides' 
                            ? 'Upload a PDF or PowerPoint file (max 10MB)'
                            : 'Upload an image file (max 5MB)'}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                
                {form.watch('lessonType') === 'quiz' && (
                  <div className="space-y-6">
                    <div className="flex flex-col gap-4 p-4 border rounded-md bg-card">
                      <div className="text-base font-medium flex items-center gap-2">
                        <Brain className="h-4 w-4 text-primary" />
                        <span>Quiz Settings</span>
                      </div>
                      
                      <FormField
                        control={form.control}
                        name="isModuleQuiz"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Module Quiz
                              </FormLabel>
                              <FormDescription>
                                This is the final assessment for the entire module
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="passingScore"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Passing Score (%)</FormLabel>
                            <FormControl>
                              <Input 
                                type="number" 
                                placeholder="e.g. 70"
                                {...field}
                                value={field.value || ''}
                                onChange={(e) => {
                                  const value = e.target.value === '' ? undefined : parseInt(e.target.value, 10);
                                  field.onChange(value);
                                }}
                              />
                            </FormControl>
                            <FormDescription>
                              The minimum percentage required to pass this quiz
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="quizInstructions"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Instructions</FormLabel>
                            <FormControl>
                              <RichTextEditor 
                                value={field.value || ""} 
                                onChange={field.onChange}
                                placeholder="Provide instructions for taking this quiz..."
                                minHeight="120px"
                              />
                            </FormControl>
                            <FormDescription>
                              Instructions will be shown to prospects before they start the quiz
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="category"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Category</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a category" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="Technical">Technical Skills</SelectItem>
                                <SelectItem value="Soft Skills">Soft Skills</SelectItem>
                                <SelectItem value="Critical Thinking">Critical Thinking</SelectItem>
                                <SelectItem value="Temperament">Temperament</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Categorize this quiz for better organization
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="whatItChecks"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>What it Checks</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="e.g., Customer service skills"
                                  {...field}
                                  value={field.value || ''}
                                />
                              </FormControl>
                              <FormDescription>
                                Brief description of what skills this quiz assesses
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name="whatToExpect"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>What to Expect</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="e.g., Multiple choice questions about module content"
                                  {...field}
                                  value={field.value || ''}
                                />
                              </FormControl>
                              <FormDescription>
                                Brief description of the quiz format
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                    
                    {/* Question Editor */}
                    <div className="border rounded-md overflow-hidden">
                      <div className="bg-muted/40 p-4 border-b">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-base">Quiz Questions</h3>
                          <Button 
                            type="button" 
                            variant="outline" 
                            size="sm" 
                            onClick={addQuestion}
                            className="gap-1"
                          >
                            <Plus className="h-4 w-4" />
                            Add Question
                          </Button>
                        </div>
                      </div>
                      
                      {quizQuestions.length === 0 ? (
                        <div className="p-8 text-center">
                          <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-3">
                            <FileQuestion className="h-6 w-6 text-muted-foreground" />
                          </div>
                          <h3 className="text-lg font-medium mb-1">No Questions Added</h3>
                          <p className="text-sm text-muted-foreground mb-4 max-w-md mx-auto">
                            Add questions to your quiz to test learners' understanding.
                            You need at least one question for a valid quiz.
                          </p>
                          <Button 
                            type="button" 
                            onClick={addQuestion}
                            variant="secondary"
                            className="gap-1"
                          >
                            <Plus className="h-4 w-4" />
                            Add Your First Question
                          </Button>
                        </div>
                      ) : (
                        <div className="flex">
                          {/* Question List */}
                          <div className="w-1/3 border-r">
                            <div className="p-2">
                              {quizQuestions.map((question, index) => (
                                <div 
                                  key={question.id}
                                  className={`p-3 rounded-md cursor-pointer transition-colors mb-1 ${
                                    selectedQuestion === question.id 
                                      ? 'bg-primary/10 text-primary' 
                                      : 'hover:bg-muted'
                                  }`}
                                  onClick={() => setSelectedQuestion(question.id)}
                                >
                                  <div className="flex items-center justify-between mb-1">
                                    <div className="font-medium text-sm">Question {index + 1}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {question.questionType === 'multiple_choice' ? 'Multiple Choice' : 
                                       question.questionType === 'true_false' ? 'True/False' :
                                       'Text Input'}
                                    </div>
                                  </div>
                                  <p className="text-sm truncate">
                                    {question.question || 'Untitled question'}
                                  </p>
                                </div>
                              ))}
                            </div>
                          </div>
                          
                          {/* Question Editor */}
                          <div className="w-2/3 p-4">
                            {selectedQuestion ? (
                              <>
                                {(() => {
                                  const question = quizQuestions.find(q => q.id === selectedQuestion);
                                  if (!question) return null;
                                  
                                  return (
                                    <div className="space-y-4">
                                      {/* Question Text */}
                                      <div className="space-y-2">
                                        <Label>Question</Label>
                                        <Input 
                                          value={question.question} 
                                          onChange={(e) => updateQuestion(question.id, { question: e.target.value })}
                                          placeholder="Enter your question here"
                                        />
                                      </div>
                                      
                                      {/* Question Type */}
                                      <div className="space-y-2">
                                        <Label>Question Type</Label>
                                        <Select
                                          value={question.questionType}
                                          onValueChange={(value: 'multiple_choice' | 'true_false' | 'text_input') => {
                                            let options = [...question.options];
                                            
                                            // Reset options based on question type
                                            if (value === 'true_false') {
                                              options = [
                                                { id: uuidv4(), text: 'True', isCorrect: false },
                                                { id: uuidv4(), text: 'False', isCorrect: false }
                                              ];
                                            } else if (value === 'text_input') {
                                              options = [
                                                { id: uuidv4(), text: '', isCorrect: true }
                                              ];
                                            } else if (options.length < 2) {
                                              // Ensure at least 2 options for multiple choice
                                              while (options.length < 2) {
                                                options.push({ id: uuidv4(), text: '', isCorrect: false });
                                              }
                                            }
                                            
                                            updateQuestion(question.id, { 
                                              questionType: value,
                                              options
                                            });
                                          }}
                                        >
                                          <SelectTrigger>
                                            <SelectValue placeholder="Select question type" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                                            <SelectItem value="true_false">True/False</SelectItem>
                                            <SelectItem value="text_input">Text Input</SelectItem>
                                          </SelectContent>
                                        </Select>
                                      </div>
                                      
                                      {/* Points */}
                                      <div className="space-y-2">
                                        <Label>Points</Label>
                                        <Input 
                                          type="number" 
                                          value={question.points} 
                                          onChange={(e) => updateQuestion(question.id, { 
                                            points: parseInt(e.target.value) || 1 
                                          })}
                                          min="1"
                                          max="100"
                                        />
                                      </div>
                                      
                                      {/* Options */}
                                      {question.questionType !== 'text_input' ? (
                                        <div className="space-y-3">
                                          <div className="flex items-center justify-between">
                                            <Label>Answer Options</Label>
                                            {question.questionType === 'multiple_choice' && (
                                              <Button 
                                                type="button" 
                                                variant="outline" 
                                                size="sm" 
                                                onClick={() => addOption(question.id)}
                                                className="h-8"
                                              >
                                                Add Option
                                              </Button>
                                            )}
                                          </div>
                                          
                                          {question.options.map((option, optIndex) => (
                                            <div key={option.id} className="flex items-center gap-2">
                                              <Checkbox
                                                checked={option.isCorrect}
                                                onCheckedChange={(checked) => 
                                                  updateOption(question.id, option.id, { isCorrect: !!checked })
                                                }
                                                id={`option-${option.id}`}
                                              />
                                              <Input 
                                                value={option.text} 
                                                onChange={(e) => 
                                                  updateOption(question.id, option.id, { text: e.target.value })
                                                }
                                                placeholder={`Option ${optIndex + 1}`}
                                                className="flex-1"
                                              />
                                              {question.questionType === 'multiple_choice' && question.options.length > 2 && (
                                                <Button 
                                                  type="button" 
                                                  variant="ghost" 
                                                  size="icon"
                                                  className="h-8 w-8 text-muted-foreground hover:text-destructive"
                                                  onClick={() => deleteOption(question.id, option.id)}
                                                >
                                                  <Trash className="h-4 w-4" />
                                                </Button>
                                              )}
                                            </div>
                                          ))}
                                        </div>
                                      ) : (
                                        <div className="space-y-2">
                                          <Label>Correct Answer(s)</Label>
                                          <Input 
                                            value={question.options[0]?.text || ''} 
                                            onChange={(e) => 
                                              question.options[0] && updateOption(question.id, question.options[0].id, { text: e.target.value })
                                            }
                                            placeholder="Enter the correct answer"
                                          />
                                          <p className="text-xs text-muted-foreground">
                                            For text input questions, enter the expected answer. Multiple correct answers can be separated by commas.
                                          </p>
                                        </div>
                                      )}
                                      
                                      {/* Delete Question Button */}
                                      <div className="pt-4 flex justify-end">
                                        <Button 
                                          type="button" 
                                          variant="destructive"
                                          size="sm" 
                                          onClick={() => deleteQuestion(question.id)}
                                          className="gap-1"
                                        >
                                          <Trash className="h-4 w-4" />
                                          Delete Question
                                        </Button>
                                      </div>
                                    </div>
                                  );
                                })()}
                              </>
                            ) : (
                              <div className="h-[300px] flex items-center justify-center">
                                <p className="text-muted-foreground">
                                  Select a question to edit or add a new one
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
                
                <SheetFooter className="pt-4">
                  <Button type="submit">
                    {currentLessonId ? 'Update Content' : form.watch('lessonType') === 'quiz' ? 'Create Quiz' : 'Create Lesson'}
                  </Button>
                </SheetFooter>
              </form>
            </Form>
          </div>
        </SheetContent>
      </Sheet>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this lesson and all its content.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDeleteLesson} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      <Toaster />
    </div>
  );
} 