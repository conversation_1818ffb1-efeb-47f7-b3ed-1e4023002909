import { NextRequest, NextResponse } from 'next/server';
import { createApiClient } from '@/lib/supabase-server';

/**
 * GET /api/org/[orgSlug]/departments
 * Get all departments for an organization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orgSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug } = params;

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Get departments with employee count
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select(`
        id,
        name,
        description,
        department_head_id,
        created_at,
        updated_at
      `)
      .eq('organization_id', organization.id)
      .order('created_at', { ascending: false });

    if (deptError) {
      console.error('Error fetching departments:', deptError);
      return NextResponse.json(
        { error: 'Failed to fetch departments' },
        { status: 500 }
      );
    }

    // Get employee counts for each department
    const departmentsWithCount = [];
    for (const dept of departments || []) {
      const { data: employments } = await supabase
        .from('employment_relationships')
        .select('id')
        .eq('department_id', dept.id)
        .eq('status', 'active');

      departmentsWithCount.push({
        id: dept.id,
        name: dept.name,
        description: dept.description,
        department_head_id: dept.department_head_id,
        status: 'active', // Default status since column doesn't exist
        created_at: dept.created_at,
        updated_at: dept.updated_at,
        employee_count: employments?.length || 0
      });
    }

    return NextResponse.json(departmentsWithCount);

  } catch (error) {
    console.error('Error in departments API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/org/[orgSlug]/departments
 * Create a new department
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { orgSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug } = params;
    const body = await request.json();

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: 'Department name is required' },
        { status: 400 }
      );
    }

    // Check if department name already exists in this organization
    const { data: existingDept } = await supabase
      .from('departments')
      .select('id')
      .eq('organization_id', organization.id)
      .eq('name', body.name)
      .single();

    if (existingDept) {
      return NextResponse.json(
        { error: 'A department with this name already exists' },
        { status: 409 }
      );
    }

    // Create department
    const { data: newDepartment, error: createError } = await supabase
      .from('departments')
      .insert({
        organization_id: organization.id,
        name: body.name,
        description: body.description || null,
        department_head_id: body.department_head_id || null
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating department:', createError);
      return NextResponse.json(
        { error: 'Failed to create department' },
        { status: 500 }
      );
    }

    // Handle staff assignments
    if (body.staff_members && Array.isArray(body.staff_members) && body.staff_members.length > 0) {
      try {
        // Update employment relationships for selected staff members
        for (const userId of body.staff_members) {
          // Check if user already has employment relationship in this organization
          const { data: existingEmployment } = await supabase
            .from('employment_relationships')
            .select('id, role')
            .eq('user_id', userId)
            .eq('organization_id', organization.id)
            .single();

          if (existingEmployment) {
            // Update existing employment to new department
            const newRole = userId === body.department_head_id ? 'department_admin' : 'staff_member';
            await supabase
              .from('employment_relationships')
              .update({
                department_id: newDepartment.id,
                role: newRole
              })
              .eq('id', existingEmployment.id);
          } else {
            // Create new employment relationship
            const newRole = userId === body.department_head_id ? 'department_admin' : 'staff_member';
            await supabase
              .from('employment_relationships')
              .insert({
                user_id: userId,
                organization_id: organization.id,
                department_id: newDepartment.id,
                role: newRole,
                status: 'active',
                job_title: 'Employee'
              });
          }
        }
      } catch (staffError) {
        console.error('Error assigning staff to department:', staffError);
        // Department was created successfully, but staff assignment failed
        // We could either rollback or continue - continuing for now
      }
    }

    return NextResponse.json(newDepartment, { status: 201 });

  } catch (error) {
    console.error('Error in create department API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
