'use client';

import { useState, useEffect, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { createBrowserClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import {
  Users,
  Search,
  Filter,
  Grid3X3,
  List,
  MoreVertical,
  Mail,
  Phone,
  MapPin,
  Calendar,
  BookOpen,
  Target,
  Building2,
  Crown,
  Activity
} from 'lucide-react';

// Define types based on our database schema
interface StaffMember {
  id: string;
  full_name: string;
  slug: string;
  email: string;
  job_title: string | null;
  hire_date: string | null;
  status: 'active' | 'inactive' | 'on_leave';
  performance_score: number;
  training_progress: number;
  skills: string[];
  recent_activity: string;
  avatar_url?: string;
  department: {
    id: string;
    name: string;
    slug: string;
  };
  role: string;
}

interface Organization {
  id: string;
  name: string;
  slug: string;
}

interface StaffStats {
  totalStaff: number;
  activeStaff: number;
  averagePerformance: number;
  trainingCompletion: number;
  departmentCount: number;
}

export default function OrganizationStaffPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);

  const supabase = createBrowserClient();
  const [staffMembers, setStaffMembers] = useState<StaffMember[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const orgSlug = params.orgSlug as string;

  // Calculate stats
  const stats = useMemo((): StaffStats => {
    const totalStaff = staffMembers.length;
    const activeStaff = staffMembers.filter(staff => staff.status === 'active').length;
    const averagePerformance = totalStaff > 0
      ? Math.round(staffMembers.reduce((sum, staff) => sum + staff.performance_score, 0) / totalStaff)
      : 0;
    const trainingCompletion = totalStaff > 0
      ? Math.round(staffMembers.reduce((sum, staff) => sum + staff.training_progress, 0) / totalStaff)
      : 0;
    const departmentCount = new Set(staffMembers.map(staff => staff.department.id)).size;

    return {
      totalStaff,
      activeStaff,
      averagePerformance,
      trainingCompletion,
      departmentCount
    };
  }, [staffMembers]);

  // Get unique departments for filter
  const departments = useMemo(() => {
    const uniqueDepts = new Map();
    staffMembers.forEach(staff => {
      if (!uniqueDepts.has(staff.department.id)) {
        uniqueDepts.set(staff.department.id, staff.department);
      }
    });
    return Array.from(uniqueDepts.values());
  }, [staffMembers]);

  // Filter and sort staff members
  const filteredStaffMembers = useMemo(() => {
    let filtered = staffMembers.filter(staff => {
      const matchesSearch = searchQuery === '' ||
        staff.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        staff.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (staff.job_title && staff.job_title.toLowerCase().includes(searchQuery.toLowerCase())) ||
        staff.department.name.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesDepartment = departmentFilter === 'all' ||
        staff.department.id === departmentFilter;

      const matchesRole = roleFilter === 'all' ||
        staff.role === roleFilter;

      const matchesStatus = statusFilter === 'all' ||
        staff.status === statusFilter;

      return matchesSearch && matchesDepartment && matchesRole && matchesStatus;
    });

    // Sort by name
    filtered.sort((a, b) => a.full_name.localeCompare(b.full_name));

    return filtered;
  }, [staffMembers, searchQuery, departmentFilter, roleFilter, statusFilter]);

  useEffect(() => {
    fetchOrganizationStaff();
  }, [orgSlug]);

  const fetchOrganizationStaff = async () => {
    try {
      setLoading(true);

      // Step 1: Get the organization
      console.log('🔍 Fetching organization with slug:', orgSlug);
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id, name, slug')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('❌ Organization query failed:', orgError);
        console.error('❌ Full error details:', JSON.stringify(orgError, null, 2));
        toast({
          title: "Error",
          description: "Failed to load organization data.",
          variant: "destructive",
        });
        return;
      }

      if (!orgData) {
        console.error('❌ Organization not found for slug:', orgSlug);
        return;
      }

      console.log('✅ Organization found:', orgData);
      setOrganization(orgData);

      // Step 2: Get all employment relationships for this organization
      console.log('🔍 Querying employment relationships for organization ID:', orgData.id);
      const { data: employments, error: empError } = await supabase
        .from('employment_relationships')
        .select('id, user_id, department_id, organization_id, role, status, job_title, hire_date')
        .eq('organization_id', orgData.id)
        .eq('status', 'active');

      if (empError) {
        console.error('❌ Employment relationships query failed:', empError);
        console.error('❌ Organization ID that failed:', orgData.id);
        console.error('❌ Full error details:', JSON.stringify(empError, null, 2));
        setStaffMembers([]);
        return;
      }

      console.log('✅ Employment relationships found:', employments?.length || 0);
      console.log('✅ Employment data:', employments);

      if (!employments || employments.length === 0) {
        console.log('ℹ️ No employment relationships found for this organization');
        setStaffMembers([]);
        return;
      }

      // Step 3: Get departments data
      const departmentIds = [...new Set(employments.map(emp => emp.department_id))];
      console.log('🔍 Department IDs to fetch:', departmentIds);

      const { data: departments, error: deptError } = await supabase
        .from('departments')
        .select('id, name')
        .in('id', departmentIds);

      if (deptError) {
        console.error('❌ Departments query failed:', deptError);
        console.error('❌ Department IDs that failed:', departmentIds);
        console.error('❌ Full error details:', JSON.stringify(deptError, null, 2));
        setStaffMembers([]);
        return;
      }

      console.log('✅ Departments found:', departments?.length || 0);

      // Step 4: Get users data
      const userIds = employments.map(emp => emp.user_id);
      console.log('🔍 User IDs to fetch:', userIds);

      if (userIds.length === 0) {
        console.log('ℹ️ No user IDs to fetch');
        setStaffMembers([]);
        return;
      }

      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, full_name, email')
        .in('id', userIds);

      if (usersError) {
        console.error('❌ Users query failed:', usersError);
        console.error('❌ User IDs that failed:', userIds);
        console.error('❌ Full error details:', JSON.stringify(usersError, null, 2));
        setStaffMembers([]);
        return;
      }

      console.log('✅ Users found:', users?.length || 0);

      // Step 5: Get individuals data for additional profile info
      const { data: individuals, error: individualsError } = await supabase
        .from('individuals')
        .select('user_id, skills, profile_image_url, learning_status, completed_courses, total_learning_hours')
        .in('user_id', userIds);

      if (individualsError) {
        console.error('❌ Individuals query failed:', individualsError);
        console.error('❌ Full error details:', JSON.stringify(individualsError, null, 2));
        // Don't return here, continue without individuals data
      }

      console.log('✅ Individuals found:', individuals?.length || 0);

      // Step 6: Build staff members combining all data
      const staffMembers: StaffMember[] = employments.map(emp => {
        const user = users?.find(u => u.id === emp.user_id);
        if (!user) {
          console.warn('⚠️ User not found for employment:', emp.user_id);
          return null;
        }

        const department = departments?.find(d => d.id === emp.department_id);
        if (!department) {
          console.warn('⚠️ Department not found for employment:', emp.department_id);
          return null;
        }

        const individual = individuals?.find(ind => ind.user_id === emp.user_id);
        const userSlug = user.full_name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

        // Extract skills from individuals table (JSON array)
        let skills: string[] = ['General Skills'];
        if (individual?.skills && Array.isArray(individual.skills)) {
          skills = individual.skills.map(skill =>
            typeof skill === 'string' ? skill : JSON.stringify(skill)
          );
        }

        // Calculate training progress from individuals data
        const trainingProgress = individual?.total_learning_hours
          ? Math.min(Math.floor((individual.total_learning_hours / 100) * 100), 100)
          : Math.floor(Math.random() * 40) + 60;

        // Calculate performance score based on completed courses
        const performanceScore = individual?.completed_courses
          ? Math.min(Math.floor((individual.completed_courses / 10) * 100), 100)
          : Math.floor(Math.random() * 30) + 70;

        return {
          id: user.id,
          full_name: user.full_name,
          slug: userSlug,
          email: user.email,
          job_title: emp.job_title || (emp.role === 'department_admin' ? 'Department Head' : 'Staff Member'),
          hire_date: emp.hire_date,
          status: emp.status as 'active' | 'inactive' | 'on_leave',
          performance_score: performanceScore,
          training_progress: trainingProgress,
          skills: skills,
          recent_activity: individual?.learning_status === 'active' ? 'Currently learning' : 'Recent platform activity',
          avatar_url: individual?.profile_image_url || null,
          department: {
            id: department.id,
            name: department.name,
            slug: department.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
          },
          role: emp.role
        };
      }).filter(Boolean) as StaffMember[];

      console.log('✅ Final staff members:', staffMembers.length);
      setStaffMembers(staffMembers);

    } catch (error) {
      console.error('💥 Unexpected error in fetchOrganizationStaff:', error);
      toast({
        title: "Error",
        description: "Failed to load staff members.",
        variant: "destructive",
      });
      setStaffMembers([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStaffClick = (staff: StaffMember) => {
    router.push(`/org/${orgSlug}/departments/${staff.department.slug}/staff/${staff.slug}/profile`);
  };

  if (loading && staffMembers.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading staff members...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {organization?.name} Staff
          </h1>
          <p className="text-muted-foreground">
            All staff members across the organization
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="mr-2 h-4 w-4" />
            Grid
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="mr-2 h-4 w-4" />
            List
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden">
        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
              <Users className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Staff</div>
              <div className="text-3xl font-bold mt-1">{stats.totalStaff}</div>
              <p className="text-xs text-muted-foreground">
                Organization-wide
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
              <Activity className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Staff</div>
              <div className="text-3xl font-bold mt-1">{stats.activeStaff}</div>
              <p className="text-xs text-muted-foreground">
                Currently active
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/20">
              <Target className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Avg Performance</div>
              <div className="text-3xl font-bold mt-1">{stats.averagePerformance}%</div>
              <p className="text-xs text-muted-foreground">
                Performance score
              </p>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg shadow-orange-500/20">
              <BookOpen className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Training Progress</div>
              <div className="text-3xl font-bold mt-1">{stats.trainingCompletion}%</div>
              <p className="text-xs text-muted-foreground">
                Average completion
              </p>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-teal-500 to-cyan-600 text-white shadow-lg shadow-teal-500/20">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Departments</div>
              <div className="text-3xl font-bold mt-1">{stats.departmentCount}</div>
              <p className="text-xs text-muted-foreground">
                Active departments
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-2 flex-1">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search staff members..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>

          <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Departments</SelectItem>
              {departments.map((dept) => (
                <SelectItem key={dept.id} value={dept.id}>
                  {dept.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="organization_admin">Org Admin</SelectItem>
              <SelectItem value="department_admin">Dept Head</SelectItem>
              <SelectItem value="staff_member">Staff</SelectItem>
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
              <SelectItem value="on_leave">On Leave</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="text-sm text-muted-foreground">
          {filteredStaffMembers.length} of {staffMembers.length} staff members
        </div>
      </div>

      {/* Staff Members Display */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="flex items-center gap-4 mb-4">
                  <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-24"></div>
                    <div className="h-3 bg-gray-200 rounded w-32"></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : filteredStaffMembers.length === 0 ? (
        <Card className="p-12">
          <div className="text-center">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Staff Members Found</h3>
            <p className="text-muted-foreground">
              {searchQuery || departmentFilter !== 'all' || roleFilter !== 'all' || statusFilter !== 'all'
                ? 'No staff members match your current filters'
                : 'No staff members found in this organization'}
            </p>
          </div>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStaffMembers.map((staff) => (
            <Card
              key={staff.id}
              className="p-6 cursor-pointer hover:shadow-lg transition-all duration-200 relative group"
              onClick={() => handleStaffClick(staff)}
            >
              {/* Role Badge */}
              {staff.role === 'organization_admin' && (
                <div className="absolute top-3 right-3">
                  <Badge variant="default" className="bg-purple-600 text-white text-xs">
                    <Crown className="h-3 w-3 mr-1" />
                    Org Admin
                  </Badge>
                </div>
              )}
              {staff.role === 'department_admin' && (
                <div className="absolute top-3 right-3">
                  <Badge variant="default" className="bg-blue-600 text-white text-xs">
                    <Crown className="h-3 w-3 mr-1" />
                    Dept Head
                  </Badge>
                </div>
              )}

              {/* Profile Section */}
              <div className="flex items-center gap-4 mb-4">
                <div className="relative">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={staff.avatar_url} alt={staff.full_name} />
                    <AvatarFallback className="text-lg font-bold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                      {staff.full_name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  {staff.status === 'active' && (
                    <div className="absolute -bottom-1 -right-1 h-5 w-5 bg-green-500 border-2 border-white rounded-full"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-lg truncate">{staff.full_name}</h3>
                  <p className="text-sm text-muted-foreground truncate">{staff.job_title}</p>
                  <p className="text-xs text-blue-600 truncate">{staff.department.name}</p>
                </div>
              </div>

              {/* Contact Info */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  <span className="truncate">{staff.email}</span>
                </div>
                {staff.hire_date && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {new Date(staff.hire_date).toLocaleDateString()}</span>
                  </div>
                )}
              </div>

              {/* Performance Metrics */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{staff.performance_score}%</div>
                  <div className="text-xs text-muted-foreground">Performance</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{staff.training_progress}%</div>
                  <div className="text-xs text-muted-foreground">Training</div>
                </div>
              </div>

              {/* Skills */}
              <div className="mb-4">
                <div className="text-sm font-medium mb-2">Skills</div>
                <div className="flex flex-wrap gap-1">
                  {staff.skills.slice(0, 3).map((skill, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {skill}
                    </Badge>
                  ))}
                  {staff.skills.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{staff.skills.length - 3} more
                    </Badge>
                  )}
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center justify-between">
                <Badge
                  variant={staff.status === 'active' ? 'default' : 'secondary'}
                  className={staff.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                >
                  {staff.status === 'active' ? 'Active' : staff.status === 'on_leave' ? 'On Leave' : 'Inactive'}
                </Badge>
                <div className="text-xs text-muted-foreground">
                  {staff.recent_activity}
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        // List View
        <div className="space-y-4">
          {filteredStaffMembers.map((staff) => (
            <Card
              key={staff.id}
              className="p-4 cursor-pointer hover:shadow-md transition-all duration-200"
              onClick={() => handleStaffClick(staff)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 flex-1 min-w-0">
                  <div className="relative">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={staff.avatar_url} alt={staff.full_name} />
                      <AvatarFallback className="text-sm font-bold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                        {staff.full_name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    {staff.status === 'active' && (
                      <div className="absolute -bottom-1 -right-1 h-4 w-4 bg-green-500 border-2 border-white rounded-full"></div>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold truncate">{staff.full_name}</h3>
                      {staff.role === 'organization_admin' && (
                        <Badge variant="default" className="bg-purple-600 text-white text-xs">
                          <Crown className="h-3 w-3 mr-1" />
                          Org Admin
                        </Badge>
                      )}
                      {staff.role === 'department_admin' && (
                        <Badge variant="default" className="bg-blue-600 text-white text-xs">
                          <Crown className="h-3 w-3 mr-1" />
                          Dept Head
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground truncate">{staff.job_title}</p>
                    <p className="text-xs text-blue-600 truncate">{staff.department.name}</p>
                  </div>
                </div>

                <div className="flex items-center gap-6">
                  <div className="text-center">
                    <div className="text-sm font-bold text-blue-600">{staff.performance_score}%</div>
                    <div className="text-xs text-muted-foreground">Performance</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-bold text-green-600">{staff.training_progress}%</div>
                    <div className="text-xs text-muted-foreground">Training</div>
                  </div>
                  <div className="flex flex-wrap gap-1 max-w-[200px]">
                    {staff.skills.slice(0, 2).map((skill, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {staff.skills.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{staff.skills.length - 2}
                      </Badge>
                    )}
                  </div>
                  <Badge
                    variant={staff.status === 'active' ? 'default' : 'secondary'}
                    className={staff.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                  >
                    {staff.status === 'active' ? 'Active' : staff.status === 'on_leave' ? 'On Leave' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}