# ===============================================================================
# LUNA DATABASE RESET AND SETUP SCRIPT (PowerShell)
# ===============================================================================

Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "LUNA DATABASE RESET AND SETUP SCRIPT" -ForegroundColor Cyan
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will:" -ForegroundColor Yellow
Write-Host "1. Reset your current Supabase database" -ForegroundColor Yellow
Write-Host "2. Create the new Luna Phase 1 schema" -ForegroundColor Yellow
Write-Host "3. Set up all tables, functions, and security policies" -ForegroundColor Yellow
Write-Host "4. Validate the deployment" -ForegroundColor Yellow
Write-Host ""
Write-Host "WARNING: This will DELETE ALL existing data in your database!" -ForegroundColor Red
Write-Host ""

$confirm = Read-Host "Are you sure you want to continue? (y/N)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "STEP 1: CHECKING ENVIRONMENT VARIABLES" -ForegroundColor Cyan
Write-Host "===============================================================================" -ForegroundColor Cyan

# Load environment variables from .env.local
if (Test-Path ".env.local") {
    Get-Content ".env.local" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
    Write-Host "✓ Loaded environment variables from .env.local" -ForegroundColor Green
} else {
    Write-Host "WARNING: .env.local file not found" -ForegroundColor Yellow
}

$supabaseUrl = [Environment]::GetEnvironmentVariable("NEXT_PUBLIC_SUPABASE_URL")
$serviceRoleKey = [Environment]::GetEnvironmentVariable("SUPABASE_SERVICE_ROLE_KEY")

if (-not $supabaseUrl -or $supabaseUrl -eq "your_supabase_url") {
    Write-Host "ERROR: NEXT_PUBLIC_SUPABASE_URL is not set or is placeholder" -ForegroundColor Red
    Write-Host "Please set your actual Supabase URL in .env.local" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not $serviceRoleKey -or $serviceRoleKey -eq "your_service_role_key") {
    Write-Host "ERROR: SUPABASE_SERVICE_ROLE_KEY is not set or is placeholder" -ForegroundColor Red
    Write-Host "Please set your actual Supabase service role key in .env.local" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Environment variables are properly configured" -ForegroundColor Green

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "STEP 2: CHECKING REQUIRED FILES" -ForegroundColor Cyan
Write-Host "===============================================================================" -ForegroundColor Cyan

$requiredFiles = @(
    "reset-and-setup-luna-database.sql",
    "luna-database-functions-and-policies.sql"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ Found $file" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Missing required file: $file" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "STEP 3: DATABASE SETUP OPTIONS" -ForegroundColor Cyan
Write-Host "===============================================================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Choose your setup method:" -ForegroundColor Yellow
Write-Host "1. Manual setup via Supabase Dashboard (Recommended)" -ForegroundColor White
Write-Host "2. Automatic setup via REST API (Advanced)" -ForegroundColor White
Write-Host "3. Generate psql commands for command line" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Enter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host "MANUAL SETUP INSTRUCTIONS" -ForegroundColor Cyan
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "To complete the database setup manually:" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "1. Open your Supabase dashboard at: https://app.supabase.com" -ForegroundColor White
        Write-Host "2. Navigate to your project" -ForegroundColor White
        Write-Host "3. Go to SQL Editor" -ForegroundColor White
        Write-Host "4. Create a new query" -ForegroundColor White
        Write-Host "5. Copy and paste the contents of 'reset-and-setup-luna-database.sql'" -ForegroundColor White
        Write-Host "6. Execute the script (this will reset and create the main schema)" -ForegroundColor White
        Write-Host "7. Create another new query" -ForegroundColor White
        Write-Host "8. Copy and paste the contents of 'luna-database-functions-and-policies.sql'" -ForegroundColor White
        Write-Host "9. Execute the second script (this will add functions and security policies)" -ForegroundColor White
        Write-Host "10. Check the validation results at the end" -ForegroundColor White
        Write-Host ""
        Write-Host "Your Supabase URL: $supabaseUrl" -ForegroundColor Cyan
        Write-Host ""
    }
    
    "2" {
        Write-Host ""
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host "AUTOMATIC SETUP VIA REST API" -ForegroundColor Cyan
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "This feature is not yet implemented." -ForegroundColor Yellow
        Write-Host "Please use the manual setup method for now." -ForegroundColor Yellow
        Write-Host ""
    }
    
    "3" {
        Write-Host ""
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host "PSQL COMMAND LINE SETUP" -ForegroundColor Cyan
        Write-Host "===============================================================================" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "If you have psql installed and configured, use these commands:" -ForegroundColor Yellow
        Write-Host ""
        
        # Extract connection details from Supabase URL
        if ($supabaseUrl -match "https://([^.]+)\.supabase\.co") {
            $projectRef = $matches[1]
            Write-Host "# Replace [PASSWORD] with your database password" -ForegroundColor Gray
            Write-Host "psql 'postgresql://postgres:[PASSWORD]@db.$projectRef.supabase.co:5432/postgres' -f reset-and-setup-luna-database.sql" -ForegroundColor White
            Write-Host "psql 'postgresql://postgres:[PASSWORD]@db.$projectRef.supabase.co:5432/postgres' -f luna-database-functions-and-policies.sql" -ForegroundColor White
        } else {
            Write-Host "# Replace connection details with your Supabase database info" -ForegroundColor Gray
            Write-Host "psql 'postgresql://postgres:[PASSWORD]@[HOST]:[PORT]/postgres' -f reset-and-setup-luna-database.sql" -ForegroundColor White
            Write-Host "psql 'postgresql://postgres:[PASSWORD]@[HOST]:[PORT]/postgres' -f luna-database-functions-and-policies.sql" -ForegroundColor White
        }
        Write-Host ""
    }
    
    default {
        Write-Host "Invalid choice. Please run the script again." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "SETUP COMPLETE" -ForegroundColor Cyan
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "The database schema files are ready:" -ForegroundColor Green
Write-Host "✓ reset-and-setup-luna-database.sql (main schema)" -ForegroundColor Green
Write-Host "✓ luna-database-functions-and-policies.sql (functions and security)" -ForegroundColor Green
Write-Host ""
Write-Host "After executing the SQL scripts, your database will have:" -ForegroundColor Yellow
Write-Host "• Individual-first user design with team collaboration" -ForegroundColor White
Write-Host "• Multi-tenant architecture with proper data isolation" -ForegroundColor White
Write-Host "• Context switching system for individual/team modes" -ForegroundColor White
Write-Host "• Comprehensive RLS policies for security" -ForegroundColor White
Write-Host "• Data continuity when users switch contexts" -ForegroundColor White
Write-Host "• Scalable team management with role-based permissions" -ForegroundColor White
Write-Host ""
Write-Host "Next steps after database setup:" -ForegroundColor Cyan
Write-Host "1. Update your application code to use the new schema" -ForegroundColor White
Write-Host "2. Test all functionality with the new database structure" -ForegroundColor White
Write-Host "3. Begin Phase 1 implementation tasks from your plan-of-action" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
