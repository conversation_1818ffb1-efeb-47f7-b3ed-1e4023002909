/**
 * Security Audit Logging System
 * Comprehensive security event tracking and monitoring
 */

import { NextRequest } from 'next/server';

// Security event types
export enum SecurityEventType {
  // Authentication events
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGI<PERSON>_FAILURE',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST',
  PASSWORD_RESET_SUCCESS = 'PASSWORD_RESET_SUCCESS',
  
  // Authorization events
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',
  ADMIN_ACCESS = 'ADMIN_ACCESS',
  
  // API security events
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INVALID_API_VERSION = 'INVALID_API_VERSION',
  MALFORMED_REQUEST = 'MALFORMED_REQUEST',
  LARGE_PAYLOAD = 'LARGE_PAYLOAD',
  
  // File upload events
  FILE_UPLOAD_SUCCESS = 'FILE_UPLOAD_SUCCESS',
  FILE_UPLOAD_BLOCKED = 'FILE_UPLOAD_BLOCKED',
  MALICIOUS_FILE_DETECTED = 'MALICIOUS_FILE_DETECTED',
  
  // Data access events
  SENSITIVE_DATA_ACCESS = 'SENSITIVE_DATA_ACCESS',
  DATA_EXPORT = 'DATA_EXPORT',
  BULK_DATA_ACCESS = 'BULK_DATA_ACCESS',
  
  // Security violations
  XSS_ATTEMPT = 'XSS_ATTEMPT',
  SQL_INJECTION_ATTEMPT = 'SQL_INJECTION_ATTEMPT',
  CSRF_ATTEMPT = 'CSRF_ATTEMPT',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  
  // System events
  SECURITY_CONFIG_CHANGE = 'SECURITY_CONFIG_CHANGE',
  USER_CREATED = 'USER_CREATED',
  USER_DELETED = 'USER_DELETED',
  ROLE_CHANGED = 'ROLE_CHANGED'
}

// Security event severity levels
export enum SecuritySeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Security event interface
export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecuritySeverity;
  timestamp: string;
  userId?: string;
  userEmail?: string;
  userRole?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  message: string;
  details: Record<string, any>;
  riskScore: number;
  tags: string[];
}

// Risk scoring configuration
const RISK_SCORES = {
  [SecurityEventType.LOGIN_SUCCESS]: 1,
  [SecurityEventType.LOGIN_FAILURE]: 3,
  [SecurityEventType.LOGOUT]: 1,
  [SecurityEventType.PASSWORD_CHANGE]: 2,
  [SecurityEventType.PASSWORD_RESET_REQUEST]: 2,
  [SecurityEventType.PASSWORD_RESET_SUCCESS]: 3,
  
  [SecurityEventType.UNAUTHORIZED_ACCESS]: 7,
  [SecurityEventType.PRIVILEGE_ESCALATION]: 9,
  [SecurityEventType.ADMIN_ACCESS]: 4,
  
  [SecurityEventType.RATE_LIMIT_EXCEEDED]: 5,
  [SecurityEventType.INVALID_API_VERSION]: 2,
  [SecurityEventType.MALFORMED_REQUEST]: 4,
  [SecurityEventType.LARGE_PAYLOAD]: 3,
  
  [SecurityEventType.FILE_UPLOAD_SUCCESS]: 1,
  [SecurityEventType.FILE_UPLOAD_BLOCKED]: 5,
  [SecurityEventType.MALICIOUS_FILE_DETECTED]: 8,
  
  [SecurityEventType.SENSITIVE_DATA_ACCESS]: 3,
  [SecurityEventType.DATA_EXPORT]: 5,
  [SecurityEventType.BULK_DATA_ACCESS]: 6,
  
  [SecurityEventType.XSS_ATTEMPT]: 8,
  [SecurityEventType.SQL_INJECTION_ATTEMPT]: 9,
  [SecurityEventType.CSRF_ATTEMPT]: 7,
  [SecurityEventType.SUSPICIOUS_ACTIVITY]: 6,
  
  [SecurityEventType.SECURITY_CONFIG_CHANGE]: 7,
  [SecurityEventType.USER_CREATED]: 2,
  [SecurityEventType.USER_DELETED]: 4,
  [SecurityEventType.ROLE_CHANGED]: 5
} as const;

// Severity mapping based on risk score
function getSeverityFromRiskScore(riskScore: number): SecuritySeverity {
  if (riskScore >= 8) return SecuritySeverity.CRITICAL;
  if (riskScore >= 6) return SecuritySeverity.HIGH;
  if (riskScore >= 3) return SecuritySeverity.MEDIUM;
  return SecuritySeverity.LOW;
}

/**
 * Security Audit Logger
 */
export class SecurityAuditLogger {
  private static instance: SecurityAuditLogger;
  private events: SecurityEvent[] = [];
  private maxEvents = 10000; // Keep last 10k events in memory
  
  private constructor() {}
  
  static getInstance(): SecurityAuditLogger {
    if (!SecurityAuditLogger.instance) {
      SecurityAuditLogger.instance = new SecurityAuditLogger();
    }
    return SecurityAuditLogger.instance;
  }
  
  /**
   * Log a security event
   */
  logEvent(
    type: SecurityEventType,
    message: string,
    details: Record<string, any> = {},
    context?: {
      request?: NextRequest;
      userId?: string;
      userEmail?: string;
      userRole?: string;
    }
  ): SecurityEvent {
    const riskScore = RISK_SCORES[type] || 1;
    const severity = getSeverityFromRiskScore(riskScore);
    
    const event: SecurityEvent = {
      id: generateEventId(),
      type,
      severity,
      timestamp: new Date().toISOString(),
      userId: context?.userId,
      userEmail: context?.userEmail,
      userRole: context?.userRole,
      ipAddress: this.extractIpAddress(context?.request),
      userAgent: context?.request?.headers.get('user-agent') || undefined,
      endpoint: context?.request?.nextUrl.pathname,
      method: context?.request?.method,
      message,
      details,
      riskScore,
      tags: this.generateTags(type, severity, details)
    };
    
    // Store event
    this.storeEvent(event);
    
    // Log to console based on severity
    this.logToConsole(event);
    
    // Trigger alerts for high-risk events
    if (severity === SecuritySeverity.CRITICAL || severity === SecuritySeverity.HIGH) {
      this.triggerAlert(event);
    }
    
    return event;
  }
  
  /**
   * Log authentication events
   */
  logAuthEvent(
    type: SecurityEventType,
    userId: string,
    userEmail: string,
    success: boolean,
    request?: NextRequest,
    details: Record<string, any> = {}
  ): SecurityEvent {
    return this.logEvent(
      type,
      `Authentication ${success ? 'successful' : 'failed'} for user ${userEmail}`,
      {
        success,
        ...details
      },
      {
        request,
        userId,
        userEmail
      }
    );
  }
  
  /**
   * Log API security events
   */
  logApiSecurityEvent(
    type: SecurityEventType,
    request: NextRequest,
    message: string,
    details: Record<string, any> = {}
  ): SecurityEvent {
    return this.logEvent(
      type,
      message,
      {
        endpoint: request.nextUrl.pathname,
        method: request.method,
        ...details
      },
      { request }
    );
  }
  
  /**
   * Log file upload events
   */
  logFileUploadEvent(
    type: SecurityEventType,
    fileName: string,
    fileSize: number,
    fileType: string,
    userId?: string,
    request?: NextRequest,
    details: Record<string, any> = {}
  ): SecurityEvent {
    return this.logEvent(
      type,
      `File upload: ${fileName}`,
      {
        fileName,
        fileSize,
        fileType,
        ...details
      },
      {
        request,
        userId
      }
    );
  }
  
  /**
   * Get recent security events
   */
  getRecentEvents(limit: number = 100, severity?: SecuritySeverity): SecurityEvent[] {
    let events = [...this.events].reverse(); // Most recent first
    
    if (severity) {
      events = events.filter(event => event.severity === severity);
    }
    
    return events.slice(0, limit);
  }
  
  /**
   * Get security metrics
   */
  getSecurityMetrics(timeRange: number = 24 * 60 * 60 * 1000): {
    totalEvents: number;
    eventsBySeverity: Record<SecuritySeverity, number>;
    eventsByType: Record<string, number>;
    riskScore: number;
    topRisks: Array<{ type: SecurityEventType; count: number; avgRisk: number }>;
  } {
    const cutoff = new Date(Date.now() - timeRange);
    const recentEvents = this.events.filter(
      event => new Date(event.timestamp) > cutoff
    );
    
    const eventsBySeverity = {
      [SecuritySeverity.LOW]: 0,
      [SecuritySeverity.MEDIUM]: 0,
      [SecuritySeverity.HIGH]: 0,
      [SecuritySeverity.CRITICAL]: 0
    };
    
    const eventsByType: Record<string, number> = {};
    const riskByType: Record<string, { total: number; count: number }> = {};
    
    let totalRiskScore = 0;
    
    recentEvents.forEach(event => {
      eventsBySeverity[event.severity]++;
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
      
      if (!riskByType[event.type]) {
        riskByType[event.type] = { total: 0, count: 0 };
      }
      riskByType[event.type].total += event.riskScore;
      riskByType[event.type].count++;
      
      totalRiskScore += event.riskScore;
    });
    
    const topRisks = Object.entries(riskByType)
      .map(([type, data]) => ({
        type: type as SecurityEventType,
        count: data.count,
        avgRisk: data.total / data.count
      }))
      .sort((a, b) => b.avgRisk - a.avgRisk)
      .slice(0, 10);
    
    return {
      totalEvents: recentEvents.length,
      eventsBySeverity,
      eventsByType,
      riskScore: recentEvents.length > 0 ? totalRiskScore / recentEvents.length : 0,
      topRisks
    };
  }
  
  private storeEvent(event: SecurityEvent): void {
    this.events.push(event);
    
    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }
    
    // In production, also store to database or external logging service
    if (process.env.NODE_ENV === 'production') {
      this.storeToDatabase(event);
    }
  }
  
  private logToConsole(event: SecurityEvent): void {
    const logLevel = event.severity === SecuritySeverity.CRITICAL || event.severity === SecuritySeverity.HIGH
      ? 'error'
      : event.severity === SecuritySeverity.MEDIUM
      ? 'warn'
      : 'info';
    
    console[logLevel](`[SECURITY_AUDIT] ${event.type}:`, {
      message: event.message,
      severity: event.severity,
      riskScore: event.riskScore,
      userId: event.userId,
      endpoint: event.endpoint,
      details: event.details
    });
  }
  
  private triggerAlert(event: SecurityEvent): void {
    // In production, integrate with alerting systems (email, Slack, PagerDuty, etc.)
    console.error(`[SECURITY_ALERT] ${event.severity} security event detected:`, {
      type: event.type,
      message: event.message,
      riskScore: event.riskScore,
      timestamp: event.timestamp,
      details: event.details
    });
  }
  
  private async storeToDatabase(event: SecurityEvent): Promise<void> {
    // In production, store to database or external logging service
    // This could be Supabase, MongoDB, Elasticsearch, etc.
    try {
      // Example: await supabase.from('security_events').insert(event);
    } catch (error) {
      console.error('Failed to store security event to database:', error);
    }
  }
  
  private extractIpAddress(request?: NextRequest): string | undefined {
    if (!request) return undefined;
    
    return request.ip ||
      request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
      request.headers.get('x-real-ip') ||
      undefined;
  }
  
  private generateTags(
    type: SecurityEventType,
    severity: SecuritySeverity,
    details: Record<string, any>
  ): string[] {
    const tags = [type.toLowerCase(), severity.toLowerCase()];
    
    // Add contextual tags
    if (type.includes('LOGIN') || type.includes('PASSWORD')) {
      tags.push('authentication');
    }
    
    if (type.includes('UNAUTHORIZED') || type.includes('PRIVILEGE')) {
      tags.push('authorization');
    }
    
    if (type.includes('FILE')) {
      tags.push('file-upload');
    }
    
    if (type.includes('API') || type.includes('RATE_LIMIT')) {
      tags.push('api-security');
    }
    
    if (details.endpoint) {
      tags.push(`endpoint:${details.endpoint}`);
    }
    
    return tags;
  }
}

// Helper functions
function generateEventId(): string {
  return `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Export singleton instance
export const securityAudit = SecurityAuditLogger.getInstance();
