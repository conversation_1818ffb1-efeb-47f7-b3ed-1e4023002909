'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';

export function UserInfo() {
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    async function getUserInfo() {
      try {
        // Get the logged in user
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          setIsLoading(false);
          return;
        }
        
        setUserEmail(session.user.email || null);
        
        // Get user role from the database
        const { data: userData, error } = await supabase
          .from('users')
          .select('role, last_login')
          .eq('id', session.user.id)
          .single();
        
        if (error) {
          console.error('Error fetching user data:', error);
          setError('Could not load user data. Please try again later.');
          setUserRole('Unknown');
        } else if (userData) {
          setUserRole(userData.role || 'Unknown');
          
          // Update last_login on every session
          try {
            const { error: updateError } = await supabase
              .from('users')
              .update({ last_login: new Date().toISOString() })
              .eq('id', session.user.id);
            
            if (updateError) {
              console.error('Error updating last_login:', updateError);
            }
          } catch (updateErr) {
            console.error('Failed to update last_login:', updateErr);
          }
        }
      } catch (error) {
        console.error('Error getting user info:', error);
        setError('Could not load user information');
      } finally {
        setIsLoading(false);
      }
    }
    
    getUserInfo();
  }, []);
  
  const handleLogout = () => {
    router.push('/logout');
  };

  const goToAdminDashboard = () => {
    router.push('/admin');
  };
  
  if (isLoading) {
    return <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">Loading user info...</div>;
  }
  
  if (!userEmail) {
    return <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">Not logged in</div>;
  }
  
  return (
    <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
      <h2 className="text-lg font-semibold mb-2">User Information</h2>
      {error && <div className="text-red-500 mb-2">{error}</div>}
      <p><strong>Email:</strong> {userEmail}</p>
      <p><strong>Role:</strong> {userRole || 'Unknown'}</p>
      <div className="mt-4">
        {userRole === 'admin' && (
          <Button 
            className="mr-2 bg-blue-600 hover:bg-blue-700" 
            onClick={goToAdminDashboard}
          >
            Admin Dashboard
          </Button>
        )}
        <Button 
          variant="destructive" 
          onClick={handleLogout}
        >
          Logout
        </Button>
      </div>
    </div>
  );
} 