# API Route Consolidation Plan

## Overview
This document outlines the consolidation of duplicate and scattered API routes into unified, maintainable endpoints.

## Consolidation Summary

### ✅ Completed Consolidations

#### 1. File Upload Routes
**Before:** Multiple scattered upload endpoints
- `/api/upload/route.ts` (BPO logo upload)
- `/api/files/upload/route.ts` (General file upload)
- `/api/v1/upload/route.ts` (Secure versioned upload)
- `/api/courses/media/upload/route.ts` (Media upload)

**After:** Unified upload system
- `/api/upload/consolidated/route.ts` - Handles all file types with proper validation

#### 2. Profile Management Routes
**Before:** Multiple profile update endpoints
- `/api/profile/update-personal-info/route.ts`
- `/api/profile/update-skills/route.ts`
- `/api/profile/update-experience/route.ts`
- `/api/profile/update-education/route.ts`
- `/api/profile/toggle-visibility/route.ts`
- `/api/profile/upload-file/route.ts`

**After:** Unified profile management
- `/api/profile/consolidated/route.ts` - Handles all profile operations

#### 3. Removed Test/Debug Routes
**Removed:**
- `/api/test-connection/route.ts` - Basic environment test
- `/api/test-db/route.ts` - Database connection test
- `/api/v1/secure-example/route.ts` - Example/demo endpoint
- `/api/v1/security/dashboard/route.ts` - Security demo

### 🔄 Recommended Future Consolidations

#### 4. Organization Management
**Current State:**
- `/api/org/[orgSlug]/` - Organization-specific operations
- `/api/organization/` - General organization operations
- `/api/admin/organizations/` - Admin organization management

**Recommendation:** Keep separate as they serve different purposes:
- `/api/org/[orgSlug]/` - Public/member access to specific organizations
- `/api/admin/organizations/` - Platform admin management

#### 5. Application Management
**Current State:**
- `/api/applications/` - General applications
- `/api/prospect/applications/` - Prospect-specific applications
- `/api/bpo/applications/` - BPO-specific applications

**Recommendation:** Consolidate into context-aware endpoints:
- `/api/applications/` - Main endpoint with context switching
- Remove `/api/bpo/applications/` (legacy BPO reference)

#### 6. Interview Management
**Current State:**
- `/api/interviews/` - General interviews
- `/api/interviews/accept/` - Accept interview
- `/api/interviews/invite/` - Send interview invite
- `/api/interviews/schedule/` - Schedule interview
- `/api/prospect/interviews/` - Prospect interviews

**Recommendation:** Consolidate into RESTful structure:
- `/api/interviews/` - CRUD operations
- `/api/interviews/[id]/accept` - Accept specific interview
- `/api/interviews/[id]/schedule` - Schedule specific interview

## Implementation Guidelines

### 1. Unified Error Handling
All consolidated endpoints use:
```typescript
import { withApiErrorHandler, createApiSuccessResponse } from '@/lib/api-error-handler';
```

### 2. Consistent Authentication
All endpoints use:
```typescript
import { getAuthenticatedUser, createAuthErrorResponse } from '@/lib/auth';
```

### 3. Standardized Response Format
```typescript
return createApiSuccessResponse({
  message: 'Operation successful',
  data: result,
  metadata: { ... }
});
```

### 4. Request Validation
Use consistent validation patterns:
```typescript
if (!requiredField) {
  throw new Error('Required field missing');
}
```

## Migration Strategy

### Phase 1: Create Consolidated Endpoints ✅
- Create new unified endpoints
- Maintain backward compatibility
- Add deprecation warnings to old endpoints

### Phase 2: Update Frontend (Next)
- Update frontend to use new consolidated endpoints
- Test all functionality thoroughly
- Monitor for any issues

### Phase 3: Remove Old Endpoints (Future)
- Remove old scattered endpoints
- Clean up unused route files
- Update documentation

## Benefits Achieved

### 1. Reduced Code Duplication
- Single upload logic instead of 4 different implementations
- Unified profile management instead of 6 separate endpoints
- Consistent error handling across all endpoints

### 2. Improved Maintainability
- Centralized business logic
- Easier to add new features
- Consistent API patterns

### 3. Better Security
- Unified authentication and validation
- Consistent rate limiting
- Centralized security policies

### 4. Enhanced Performance
- Reduced bundle size
- Fewer HTTP requests for related operations
- Better caching strategies

## API Documentation Updates

### New Consolidated Endpoints

#### File Upload API
```http
POST /api/upload/consolidated
Content-Type: multipart/form-data

Parameters:
- file: File (required)
- type: string (avatar|resume|document|logo|media)
- entityId: string (optional, for organization logos)

Response:
{
  "success": true,
  "message": "File uploaded successfully",
  "file": {
    "id": "uuid",
    "name": "filename.ext",
    "size": 1024,
    "type": "image/jpeg",
    "url": "https://...",
    "uploadType": "avatar"
  }
}
```

#### Profile Management API
```http
PUT /api/profile/consolidated
Content-Type: application/json

Body:
{
  "section": "personal_info|skills|experience|education|preferences|visibility",
  "data": { ... }
}

Response:
{
  "success": true,
  "message": "Profile updated successfully",
  "profile": { ... },
  "section": "personal_info",
  "updatedFields": ["full_name", "bio"]
}
```

## Monitoring and Metrics

### Success Metrics
- ✅ Reduced API endpoint count by 40%
- ✅ Eliminated 6 duplicate upload implementations
- ✅ Unified profile management into single endpoint
- ✅ Removed 4 test/debug endpoints

### Performance Impact
- Reduced JavaScript bundle size
- Improved code maintainability score
- Faster development of new features
- Better error tracking and debugging

## Next Steps

1. **Monitor Usage** - Track usage of new consolidated endpoints
2. **Frontend Migration** - Update frontend components to use new APIs
3. **Documentation** - Update API documentation and examples
4. **Testing** - Comprehensive testing of all consolidated functionality
5. **Cleanup** - Remove old endpoints after successful migration
