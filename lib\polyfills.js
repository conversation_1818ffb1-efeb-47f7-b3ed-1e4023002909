// Runtime polyfills for browser compatibility
// This file provides browser globals that are expected by some libraries

if (typeof window !== 'undefined') {
  // Client-side polyfills
  console.log('Client-side polyfills loaded');
} else {
  // Server-side polyfills
  if (typeof global !== 'undefined') {
    // Define self if it doesn't exist
    if (typeof global.self === 'undefined') {
      global.self = global;
    }

    // Define window if it doesn't exist
    if (typeof global.window === 'undefined') {
      global.window = global;
    }

    // Define document if it doesn't exist
    if (typeof global.document === 'undefined') {
      global.document = {
        createElement: () => ({}),
        getElementById: () => null,
        querySelector: () => null,
        querySelectorAll: () => [],
        addEventListener: () => {},
        removeEventListener: () => {},
        title: '',
        head: { appendChild: () => {} },
        body: { appendChild: () => {} }
      };
    }

    // Define navigator if it doesn't exist
    if (typeof global.navigator === 'undefined') {
      global.navigator = { userAgent: 'Node.js' };
    }

    // Define location if it doesn't exist
    if (typeof global.location === 'undefined') {
      global.location = { href: '', origin: '' };
    }
  }

  console.log('Server-side polyfills loaded');
}
