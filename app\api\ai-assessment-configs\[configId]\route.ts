import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { createClient } from '@/lib/supabase-server';

/**
 * Get specific AI assessment configuration
 * GET /api/ai-assessment-configs/[configId]
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { configId: string } }
) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { configId } = params;
    const supabase = createClient();

    // Fetch AI assessment configuration
    const { data: config, error } = await supabase
      .from('ai_assessment_configs')
      .select(`
        id,
        name,
        description,
        category,
        question_count,
        duration_minutes,
        passing_score,
        retake_limit,
        retake_fee,
        certification_eligible,
        subcategories,
        difficulty_distribution,
        question_types,
        scoring_rubric,
        competency_levels,
        ai_model_name,
        temperature,
        max_tokens,
        status,
        created_at,
        updated_at,
        prompt_template:ai_prompt_templates(
          id,
          name,
          system_prompt,
          default_parameters
        )
      `)
      .eq('id', configId)
      .eq('status', 'published')
      .single();

    if (error || !config) {
      console.error('Error fetching AI assessment config:', error);
      return NextResponse.json(
        { error: 'AI assessment configuration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      config
    });

  } catch (error: any) {
    console.error('AI assessment config fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch AI assessment configuration' },
      { status: 500 }
    );
  }
}
