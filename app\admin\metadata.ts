import { Metadata } from 'next';

export const adminMetadata: Metadata = {
  title: "Admin Dashboard | Luna Skills Platform",
  description: "Comprehensive administration panel for managing users, organizations, training modules, and platform settings",
  robots: {
    index: false,
    follow: false,
  },
};

export const adminPagesMetadata = {
  dashboard: {
    title: "Admin Dashboard | Luna Skills Platform",
    description: "Platform administration overview with key metrics, user management, and system insights",
  },
  users: {
    title: "User Management | Luna Skills Platform",
    description: "Manage platform users, roles, permissions, and account settings",
  },
  organizations: {
    title: "Organization Management | Luna Skills Platform", 
    description: "Manage organizations, memberships, and organizational settings",
  },
  bpos: {
    title: "Organization Management | Luna Skills Platform",
    description: "Manage organizations, memberships, and organizational settings",
  },
  trainingModules: {
    title: "Training Module Management | Luna Skills Platform",
    description: "Create, edit, and manage training modules, lessons, and learning content",
  },
  assessments: {
    title: "Assessment Management | Luna Skills Platform",
    description: "Manage assessments, quizzes, and evaluation tools for skills testing",
  },
  settings: {
    title: "Platform Settings | Luna Skills Platform",
    description: "Configure platform settings, integrations, and system preferences",
  },
  account: {
    title: "Admin Account | Luna Skills Platform",
    description: "Manage your administrator account settings and preferences",
  },
};
