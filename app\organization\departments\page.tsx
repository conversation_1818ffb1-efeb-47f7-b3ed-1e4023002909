'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { UserPlus, MoreVertical, UserCheck, UserX, Mail, RefreshCw } from 'lucide-react';
import { checkBPOTeamMembership } from '@/lib/auth-utils';

// Define types for team members and invitations
type User = {
  id: string | null;
  email: string;
  created_at: string | null;
  is_placeholder?: boolean;
};

// Interface for the Supabase response structure
interface TeamMemberResponse {
  id: string;
  role: string;
  permissions: any;
  invited_at: string | null;
  accepted_at: string | null;
  is_placeholder: boolean | null;
  user_id: string;
  users: User | null;
}

type TeamMember = {
  id: string;
  role: string;
  permissions?: any;
  invited_at?: string | null;
  accepted_at?: string | null;
  is_placeholder?: boolean | null;
  is_invitation?: boolean;
  user_id?: string;
  users: User;
};

export default function OrganizationTeamPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [inviting, setInviting] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [organizationData, setOrganizationData] = useState<any>(null);
  const [teamMembers, setTeamMembers] = useState<any[]>([]);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('recruiter');
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [currentUserRole, setCurrentUserRole] = useState<string | null>(null);

  // Function to fetch Organization and team data
  const fetchData = async () => {
    try {
      setLoading(true);

      // Use the standardized membership check
      // Get current user session first
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        setUser(session.user);
      }

      const membershipResult = await checkBPOTeamMembership(supabase, session.user.id);
      
      if (!membershipResult.isMember) {
        console.log('User not authorized:', membershipResult.error?.message || 'Not an organization team member');
        toast({
          title: "Access error",
          description: "You don't have permission to access this organization team page.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      // We now have the organizationId from the membership check (returned as bpoId for legacy compatibility)
      const organizationId = membershipResult.bpoId;
      
      // Get the current user's role using direct query
      try {
        const { data: userMembership, error: roleError } = await supabase
          .from('organization_memberships')
          .select('role')
          .eq('organization_id', organizationId)
          .eq('user_id', session.user.id)
          .eq('status', 'active')
          .single();

        if (!roleError && userMembership) {
          console.log('User role found:', userMembership.role);
          setCurrentUserRole(userMembership.role);
        } else {
          console.log('No user role found or error occurred', roleError);
        }
      } catch (roleQueryError) {
        console.error('Error querying user role:', roleQueryError);
      }

      // Get Organization data using the verified organizationId
      const { data: organization, error: organizationError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', organizationId)
        .single();

      if (organizationError || !organization) {
        console.error('Error fetching Organization data:', organizationError);
        toast({
          title: "Data error",
          description: "Could not load company data.",
          variant: "destructive",
        });
        setLoading(false);
        return;
      }

      setOrganizationData(organization);
      
      // Get both team members and pending invitations in parallel
      try {
        // Fetch team members first (without user join to avoid RLS issues)
        const { data: teamMembers, error: teamError } = await supabase
          .from('organization_memberships')
          .select(`
            id,
            user_id,
            role,
            status,
            invited_at,
            joined_at
          `)
          .eq('organization_id', organizationId)
          .eq('status', 'active');
        
        if (teamError) {
          console.error('Error fetching team members:', teamError);
          console.error('Team error details:', {
            message: teamError.message,
            details: teamError.details,
            hint: teamError.hint,
            code: teamError.code
          });
          toast({
            title: "Team data error",
            description: "There was a problem fetching team members. Some data might be incomplete.",
            variant: "destructive",
          });
        }

        console.log('Team members query result:', { teamMembers, teamError });
        
        // Start with empty members array
        const allMembers: any[] = [];
        
        // If we got team members, process them
        if (teamMembers && Array.isArray(teamMembers) && teamMembers.length > 0) {
          console.log(`Successfully retrieved ${teamMembers.length} team members`);

          // For now, just add the members without user data to avoid RLS issues
          for (const member of teamMembers) {
            try {
              if (!member.user_id) continue;

              // Add to our members list with placeholder user data
              allMembers.push({
                ...member,
                users: {
                  email: `User ${member.user_id.substring(0, 8)}...`,
                  id: member.user_id,
                  created_at: member.joined_at || member.invited_at
                }
              });
            } catch (err) {
              console.error('Error processing team member:', err);
            }
          }
        } else {
          console.log('No team members found or error in format');
        }
        
        // Try to fetch invitations (skip if table doesn't exist)
        try {
          const { data: invitationsData, error: invitationsError } = await supabase
            .from('organization_invitations')
            .select('*')
            .eq('organization_id', organizationId)
            .eq('status', 'pending');

          if (invitationsError) {
            console.log('Invitations table may not exist or no access:', invitationsError.message);
          } else if (invitationsData && invitationsData.length > 0) {
            // Add invitations to our members list
            for (const invitation of invitationsData) {
              allMembers.push({
                id: `invitation-${invitation.id}`,
                role: invitation.role,
                invited_at: invitation.invited_at,
                accepted_at: null,
                is_invitation: true,
                users: {
                  id: null,
                  email: invitation.email,
                  created_at: null,
                  is_placeholder: true
                }
              });
            }
          }
        } catch (invitationError) {
          console.log('Could not fetch invitations:', invitationError);
        }
        
        // Set the team members
        setTeamMembers(allMembers);
        
      } catch (error) {
        console.error('Error in team data processing:', error);
        toast({
          title: "Data error",
          description: "An error occurred while processing team data.",
          variant: "destructive",
        });
      }
      
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleInviteSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!organizationData || !inviteEmail) return;
    
    try {
      setInviting(true);
      
      // Send invitation request to API
      const response = await fetch('/api/team/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: inviteEmail,
          role: inviteRole,
          organizationId: organizationData.id,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to send invitation');
      }
      
      const result = await response.json();
      
      // Reload team members to show latest data
      await fetchData();
      
      toast({
        title: "Invitation sent",
        description: `An invitation has been sent to ${inviteEmail}`,
      });
      
      // Reset form
      setInviteEmail('');
      setInviteRole('recruiter');
      setIsInviteDialogOpen(false);
      
    } catch (error: any) {
      console.error('Error inviting team member:', error);
      toast({
        title: "Invitation failed",
        description: error.message || "There was an error inviting the team member",
        variant: "destructive",
      });
    } finally {
      setInviting(false);
    }
  };

  const handleRemoveTeamMember = async (teamMemberId: string) => {
    if (!window.confirm('Are you sure you want to remove this team member?')) {
      return;
    }
    
    try {
      setLoading(true);
      
      // Use direct query to remove team member
      const { error } = await supabase
        .from('organization_memberships')
        .delete()
        .eq('id', teamMemberId);
        
      if (error) {
        throw error;
      }
      
      // Update local state
      setTeamMembers(teamMembers.filter(member => member.id !== teamMemberId));
      
      toast({
        title: "Team member removed",
        description: "The team member has been removed from your team",
      });
      
    } catch (error) {
      console.error('Error removing team member:', error);
      toast({
        title: "Removal failed",
        description: "There was an error removing the team member",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateRole = async (teamMemberId: string, newRole: string) => {
    try {
      setLoading(true);
      
      // Use direct query to update team member role
      const { error } = await supabase
        .from('organization_memberships')
        .update({ role: newRole })
        .eq('id', teamMemberId);
        
      if (error) {
        throw error;
      }
      
      // Update local state
      setTeamMembers(teamMembers.map(member => {
        if (member.id === teamMemberId) {
          return { ...member, role: newRole };
        }
        return member;
      }));
      
      toast({
        title: "Role updated",
        description: "The team member's role has been updated",
      });
      
    } catch (error) {
      console.error('Error updating role:', error);
      toast({
        title: "Update failed",
        description: "There was an error updating the team member's role",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const resendInvitation = async (email: string) => {
    if (!organizationData) return;
    
    try {
      setLoading(true);
      
      // Resend invitation through the API
      const response = await fetch('/api/team/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email,
          role: 'recruiter', // Default role, could be improved by passing the actual role
          organizationId: organizationData.id,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to resend invitation');
      }
      
      toast({
        title: "Invitation resent",
        description: `A new invitation has been sent to ${email}`,
      });
    } catch (error: any) {
      console.error('Error resending invitation:', error);
      toast({
        title: "Resend failed",
        description: error.message || "There was an error resending the invitation",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge className="bg-blue-500">Admin</Badge>;
      case 'recruiter':
        return <Badge className="bg-green-500">Recruiter</Badge>;
      case 'interviewer':
        return <Badge className="bg-purple-500">Interviewer</Badge>;
      default:
        return <Badge>{role}</Badge>;
    }
  };

  const getInitials = (email: string) => {
    if (!email) return 'U';
    return email.substring(0, 2).toUpperCase();
  };

  if (loading && teamMembers.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading team members...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Team Management</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            Manage your organization team members and their roles
          </p>
        </div>
        
        <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="h-4 w-4 mr-2" />
              <span>Invite Team Member</span>
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite New Team Member</DialogTitle>
              <DialogDescription>
                Send an invitation to join your organization team. They'll receive access based on their assigned role.
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleInviteSubmit} className="space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={inviteRole} onValueChange={setInviteRole}>
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="recruiter">Recruiter</SelectItem>
                    <SelectItem value="interviewer">Interviewer</SelectItem>
                  </SelectContent>
                </Select>
                
                <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  {inviteRole === 'admin' && (
                    <p>Admins have full access to manage the organization account, team members, and all recruitment activities.</p>
                  )}
                  {inviteRole === 'recruiter' && (
                    <p>Recruiters can create job postings, review applications, and manage the recruitment pipeline.</p>
                  )}
                  {inviteRole === 'interviewer' && (
                    <p>Interviewers can view candidates, schedule and conduct interviews, and provide feedback.</p>
                  )}
                </div>
              </div>
              
              <DialogFooter className="mt-6">
                <Button type="button" variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={inviting}>
                  {inviting ? 'Sending Invitation...' : 'Send Invitation'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>
            {teamMembers.length} {teamMembers.length === 1 ? 'person' : 'people'} in your organization team
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teamMembers.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage 
                          src={member.users?.id ? `/avatars/${member.users.id}.png` : ''} 
                          alt={member.users?.email || ''} 
                        />
                        <AvatarFallback>{getInitials(member.users?.email || '')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{member.users?.email}</p>
                        {user?.id === member.users?.id && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">That's you</p>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getRoleLabel(member.role)}</TableCell>
                  <TableCell>
                    {member.accepted_at ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800">
                        <UserCheck className="h-3 w-3 mr-1" />
                        Active
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800">
                        Pending
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {member.accepted_at ? (
                      new Date(member.accepted_at).toLocaleDateString()
                    ) : (
                      'Not joined yet'
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Manage Team Member</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        
                        {!member.accepted_at && member.users?.email && (
                          <DropdownMenuItem onClick={() => resendInvitation(member.users.email)}>
                            <RefreshCw className="h-4 w-4 mr-2" />
                            <span>Resend Invitation</span>
                          </DropdownMenuItem>
                        )}
                        
                        <DropdownMenuItem onClick={() => handleUpdateRole(member.id, 'admin')}>
                          <span>Make Admin</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleUpdateRole(member.id, 'recruiter')}>
                          <span>Make Recruiter</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleUpdateRole(member.id, 'interviewer')}>
                          <span>Make Interviewer</span>
                        </DropdownMenuItem>
                        
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          className="text-red-600 focus:text-red-600"
                          onClick={() => handleRemoveTeamMember(member.id)}
                        >
                          <UserX className="h-4 w-4 mr-2" />
                          <span>Remove from Team</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
              
              {teamMembers.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center">
                      <UserPlus className="h-8 w-8 mb-2 text-gray-400" />
                      <p>No team members yet</p>
                      <p className="text-sm mt-1">Invite colleagues to join your organization team</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>About Roles & Permissions</CardTitle>
          <CardDescription>
            Understanding team roles and access levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <Badge className="bg-blue-500 mb-2">Admin</Badge>
              <h3 className="font-medium">Full Access</h3>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1 list-disc list-inside">
                <li>Manage company profile</li>
                <li>Invite and manage team members</li>
                <li>Create and manage job postings</li>
                <li>Full candidate management</li>
                <li>Access to analytics and reporting</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <Badge className="bg-green-500 mb-2">Recruiter</Badge>
              <h3 className="font-medium">Recruitment Focus</h3>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1 list-disc list-inside">
                <li>Create and manage job postings</li>
                <li>Review applications</li>
                <li>Manage candidate pipeline</li>
                <li>Schedule interviews</li>
                <li>Limited team management</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <Badge className="bg-purple-500 mb-2">Interviewer</Badge>
              <h3 className="font-medium">Interview Focus</h3>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1 list-disc list-inside">
                <li>View assigned candidates</li>
                <li>Conduct and schedule interviews</li>
                <li>Provide candidate feedback</li>
                <li>View job postings</li>
                <li>Read-only access to most features</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 