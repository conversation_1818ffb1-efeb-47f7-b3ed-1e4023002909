/**
 * API Security Utilities - Legacy Compatibility Layer
 *
 * This file maintains backward compatibility while the codebase transitions
 * to the new modular security system in lib/security/
 *
 * @deprecated Use lib/security/ modules directly for new code
 */

// Re-export everything from the new modular security system
export * from './security/index';

// Legacy imports for backward compatibility
import { NextRequest, NextResponse } from 'next/server';
import { createError, ErrorType } from '@/lib/utils';
import { handleApiError } from '@/lib/api-error-handler';

// All security functionality has been moved to lib/security/ modules
// This file now serves as a compatibility layer for existing imports

// Legacy aliases for backward compatibility
export async function generateRequestId(): Promise<string> {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function getClientIdentifier(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ip = forwarded?.split(',')[0] || realIp || 'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';
  return `${ip}:${userAgent.substring(0, 50)}`;
}
