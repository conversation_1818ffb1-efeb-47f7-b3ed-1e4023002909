'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useLunaAuth } from '@/hooks/use-luna-auth';
import { CheckCircle2, Clock, AlertCircle } from 'lucide-react';

// Mock assessment data for Luna platform
interface MockAssessment {
  id: string;
  title: string;
  description: string;
  category: string;
  status: 'not_started' | 'in_progress' | 'completed';
  is_active: boolean;
  score?: number;
  completed_at?: string;
}

export default function AssessmentsPage() {
  const { user } = useLunaAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock assessments for Luna platform
  const assessments: MockAssessment[] = [
    {
      id: 'skills-1',
      title: 'Skills Assessment',
      description: 'Evaluate your current technical and soft skills',
      category: 'Technical',
      status: 'not_started',
      is_active: true
    },
    {
      id: 'communication-1',
      title: 'Communication Skills',
      description: 'Assess your communication and interpersonal abilities',
      category: 'Soft Skills',
      status: 'not_started',
      is_active: true
    },
    {
      id: 'leadership-1',
      title: 'Leadership Potential',
      description: 'Evaluate your leadership and management capabilities',
      category: 'Soft Skills',
      status: 'not_started',
      is_active: true
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [user]);

  // Organize assessments by status
  const notStartedAssessments = assessments.filter(a => a.status === 'not_started' && a.is_active);
  const inProgressAssessments = assessments.filter(a => a.status === 'in_progress');
  const completedAssessments = assessments.filter(a => a.status === 'completed');
  
  // Organize by category
  const technicalAssessments = assessments.filter(a => a.category === 'Technical' && a.is_active);
  const softSkillsAssessments = assessments.filter(a => a.category === 'Soft Skills' && a.is_active);

  if (loading) {
    return <AssessmentsSkeleton />;
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="container py-6">
      <div className="flex flex-col gap-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight mb-1">Assessments</h1>
          <p className="text-muted-foreground">
            Complete these assessments to showcase your skills to potential employers
          </p>
        </div>

        {inProgressAssessments.length > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>In-Progress Assessments</AlertTitle>
            <AlertDescription>
              You have {inProgressAssessments.length} assessment{inProgressAssessments.length > 1 ? 's' : ''} in progress. 
              Continue from where you left off.
            </AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="status">
          <TabsList>
            <TabsTrigger value="status">By Status</TabsTrigger>
            <TabsTrigger value="category">By Category</TabsTrigger>
          </TabsList>
          
          <TabsContent value="status" className="space-y-6">
            {inProgressAssessments.length > 0 && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold tracking-tight">In Progress</h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {inProgressAssessments.map((assessment) => (
                    <AssessmentCard key={assessment.id} assessment={assessment} />
                  ))}
                </div>
              </div>
            )}
            
            {notStartedAssessments.length > 0 && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold tracking-tight">Available Assessments</h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {notStartedAssessments.map((assessment) => (
                    <AssessmentCard key={assessment.id} assessment={assessment} />
                  ))}
                </div>
              </div>
            )}
            
            {completedAssessments.length > 0 && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold tracking-tight">Completed</h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {completedAssessments.map((assessment) => (
                    <AssessmentCard key={assessment.id} assessment={assessment} />
                  ))}
                </div>
              </div>
            )}
            
            {assessments.length === 0 && (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium mb-2">No assessments available yet</h3>
                <p className="text-muted-foreground">
                  Check back later for new assessments to complete
                </p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="category" className="space-y-6">
            {technicalAssessments.length > 0 && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold tracking-tight">Technical Skills</h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {technicalAssessments.map((assessment) => (
                    <AssessmentCard key={assessment.id} assessment={assessment} />
                  ))}
                </div>
              </div>
            )}
            
            {softSkillsAssessments.length > 0 && (
              <div className="space-y-4">
                <h2 className="text-xl font-semibold tracking-tight">Soft Skills</h2>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {softSkillsAssessments.map((assessment) => (
                    <AssessmentCard key={assessment.id} assessment={assessment} />
                  ))}
                </div>
              </div>
            )}
            
            {assessments.length === 0 && (
              <div className="text-center py-12">
                <h3 className="text-lg font-medium mb-2">No assessments available yet</h3>
                <p className="text-muted-foreground">
                  Check back later for new assessments to complete
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

function AssessmentCard({ assessment }: { assessment: AssessmentWithStatus }) {
  return (
    <Card className="flex flex-col h-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{assessment.title}</CardTitle>
            <CardDescription className="mt-1">{assessment.description}</CardDescription>
          </div>
          <Badge variant={assessment.category === 'Technical' ? 'default' : 'secondary'}>
            {assessment.category}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="flex items-start gap-2 text-sm text-muted-foreground mb-2">
          <Clock className="h-4 w-4 mt-0.5" />
          <span>{assessment.duration}</span>
        </div>
        
        {assessment.status === 'completed' && (
          <div className="flex items-center gap-2 text-sm mb-4">
            <CheckCircle2 className="h-4 w-4 text-green-500" />
            <span className="font-medium">
              Score: {assessment.score || 0}% {assessment.score && assessment.score >= assessment.passing_score ? '(Passed)' : ''}
            </span>
          </div>
        )}
        
        <p className="text-sm mb-4 line-clamp-2">{assessment.what_it_checks}</p>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <Button asChild className="w-full">
          <Link href={`/prospect/assessments/${assessment.id}`}>
            {assessment.status === 'completed'
              ? 'View Results'
              : assessment.status === 'in_progress'
              ? 'Continue Assessment'
              : 'Start Assessment'}
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

function AssessmentsSkeleton() {
  return (
    <div className="container py-6">
      <div className="flex flex-col gap-6">
        <div>
          <Skeleton className="h-9 w-64 mb-2" />
          <Skeleton className="h-5 w-96" />
        </div>
        
        <div className="space-y-2">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-[1px] w-full" />
        </div>
        
        <div className="space-y-4">
          <Skeleton className="h-7 w-36" />
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {Array(3).fill(0).map((_, i) => (
              <Card key={i} className="border-border">
                <CardHeader>
                  <div className="flex justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-6 w-48" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                    <Skeleton className="h-6 w-24" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-16 w-full" />
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-4">
                  <Skeleton className="h-10 w-full" />
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 