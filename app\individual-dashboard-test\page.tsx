"use client"

import { LunaLayout } from "@/components/luna-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { CheckCircle2, Home, User, Route, ClipboardCheck, Phone, BookOpen, Briefcase, FileText, MessageSquare, HelpCircle, Settings } from "lucide-react"

export default function IndividualDashboardTestPage() {
  return (
    <LunaLayout>
      <div className="p-6 space-y-6">
        {/* Page Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">🎯 Individual Dashboard Portal</h1>
          <p className="text-muted-foreground">
            Clean, organized navigation structure for the individual user experience.
          </p>
        </div>

        {/* Navigation Structure Overview */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Main Menu</CardTitle>
              <Home className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2 Items</div>
              <p className="text-xs text-muted-foreground">
                Core dashboard and profile
              </p>
              <div className="mt-4 space-y-1">
                <div className="text-xs">• Dashboard</div>
                <div className="text-xs">• Profile</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Learning</CardTitle>
              <BookOpen className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4 Items</div>
              <p className="text-xs text-muted-foreground">
                Educational resources and training
              </p>
              <div className="mt-4 space-y-1">
                <div className="text-xs">• Learning Paths</div>
                <div className="text-xs">• Assessments</div>
                <div className="text-xs">• Role-Call Training</div>
                <div className="text-xs">• Courses Marketplace</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Career</CardTitle>
              <Briefcase className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3 Items</div>
              <p className="text-xs text-muted-foreground">
                Job search and career development
              </p>
              <div className="mt-4 space-y-1">
                <div className="text-xs">• Job Board</div>
                <div className="text-xs">• My Applications</div>
                <div className="text-xs">• Interviews</div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Settings</CardTitle>
              <Settings className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2 Items</div>
              <p className="text-xs text-muted-foreground">
                Support and configuration
              </p>
              <div className="mt-4 space-y-1">
                <div className="text-xs">• Help & Support</div>
                <div className="text-xs">• Settings</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Navigation Structure */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              Complete Navigation Structure
            </CardTitle>
            <CardDescription>
              Organized navigation groups for the individual dashboard portal
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Main Menu */}
              <div className="space-y-3">
                <h3 className="font-semibold text-blue-600 flex items-center gap-2">
                  <Home className="w-4 h-4" />
                  Main Menu
                </h3>
                <div className="space-y-2 pl-6">
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-blue-50">
                    <Home className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Dashboard</span>
                    <Badge variant="outline" className="ml-auto text-xs">Primary</Badge>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-blue-50">
                    <User className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Profile</span>
                  </div>
                </div>
              </div>

              {/* Learning */}
              <div className="space-y-3">
                <h3 className="font-semibold text-green-600 flex items-center gap-2">
                  <BookOpen className="w-4 h-4" />
                  Learning
                </h3>
                <div className="space-y-2 pl-6">
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-green-50">
                    <Route className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Learning Paths</span>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-green-50">
                    <ClipboardCheck className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Assessments</span>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-green-50">
                    <Phone className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Role-Call Training</span>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-green-50">
                    <BookOpen className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Courses Marketplace</span>
                  </div>
                </div>
              </div>

              {/* Career */}
              <div className="space-y-3">
                <h3 className="font-semibold text-purple-600 flex items-center gap-2">
                  <Briefcase className="w-4 h-4" />
                  Career
                </h3>
                <div className="space-y-2 pl-6">
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-purple-50">
                    <Briefcase className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Job Board</span>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-purple-50">
                    <FileText className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">My Applications</span>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-purple-50">
                    <MessageSquare className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Interviews</span>
                  </div>
                </div>
              </div>

              {/* Settings */}
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-600 flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Settings
                </h3>
                <div className="space-y-2 pl-6">
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-50">
                    <HelpCircle className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Help & Support</span>
                  </div>
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-50">
                    <Settings className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium">Settings</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Design Improvements */}
        <Card>
          <CardHeader>
            <CardTitle>Design Refinements</CardTitle>
            <CardDescription>
              Pixel-perfect adjustments made to match the inspiration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Tighter Spacing</div>
                    <div className="text-sm text-muted-foreground">
                      Reduced padding and margins for cleaner look
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Precise Active States</div>
                    <div className="text-sm text-muted-foreground">
                      Blue left border extends to sidebar edge
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Clean Typography</div>
                    <div className="text-sm text-muted-foreground">
                      Consistent font weights and sizes
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Organized Structure</div>
                    <div className="text-sm text-muted-foreground">
                      Logical grouping of related features
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Consistent Icons</div>
                    <div className="text-sm text-muted-foreground">
                      Appropriate icons for each navigation item
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div>
                    <div className="font-medium">Clean Implementation</div>
                    <div className="text-sm text-muted-foreground">
                      Organized code structure and maintainable design
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Navigation */}
        <Card>
          <CardHeader>
            <CardTitle>Test Navigation</CardTitle>
            <CardDescription>
              Quick links to test the new navigation structure
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <Button variant="outline" asChild>
                <a href="/individual">Dashboard</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/individual/profile">Profile</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/individual/learning-paths">Learning Paths</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/individual/job-board">Job Board</a>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Success Message */}
        <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CardContent className="pt-6">
            <div className="flex items-center gap-3">
              <CheckCircle2 className="h-6 w-6 text-green-600" />
              <div>
                <div className="font-semibold text-green-800 dark:text-green-200">
                  Individual Dashboard Portal Ready!
                </div>
                <div className="text-sm text-green-700 dark:text-green-300">
                  The sidebar has been updated with the exact navigation structure you specified. 
                  The design is now tighter and more pixel-perfect to match your inspiration.
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </LunaLayout>
  )
}
