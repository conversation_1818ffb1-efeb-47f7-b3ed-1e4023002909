"use client";

import { useState, useRef, useCallback } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alog<PERSON>itle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import { Upload, FileText, Image, Camera } from "lucide-react"

interface FileUploadDialogProps {
  fileType: "resume" | "avatar" | "profile_image"
  triggerComponent?: React.ReactNode
  buttonText?: string
  onSuccess?: (url: string) => void
}

export function FileUploadDialog({
  fileType,
  triggerComponent,
  buttonText,
  onSuccess
}: FileUploadDialogProps) {
  const [open, setOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [file, setFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()
  
  const isResume = fileType === "resume"
  const isImage = fileType === "avatar" || fileType === "profile_image"
  const acceptTypes = isResume 
    ? ".pdf,.doc,.docx"
    : "image/jpeg,image/png,image/jpg"
  
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0]
      
      // Validate file type
      if (isImage && !selectedFile.type.startsWith('image/')) {
        toast.error("Please select an image file (JPEG, PNG)")
        return
      }
      
      if (isResume && 
          !selectedFile.name.endsWith('.pdf') && 
          !selectedFile.name.endsWith('.doc') && 
          !selectedFile.name.endsWith('.docx')) {
        toast.error("Please select a valid document (PDF, DOC, DOCX)")
        return
      }
      
      // Validate file size
      const maxSize = isResume ? 5 * 1024 * 1024 : 2 * 1024 * 1024
      if (selectedFile.size > maxSize) {
        toast.error(`File is too large. Maximum size is ${maxSize / (1024 * 1024)}MB.`)
        return
      }
      
      setFile(selectedFile)
      
      // Create preview URL for images
      if (isImage) {
        const url = URL.createObjectURL(selectedFile)
        setPreviewUrl(url)
      }
    }
  }, [isImage, isResume])
  
  const openFileSelector = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [])
  
  // Handle camera capture (for mobile devices)
  const handleCameraCapture = useCallback(() => {
    if (fileInputRef.current) {
      // Set capture attribute dynamically
      fileInputRef.current.setAttribute('capture', 'user')
      fileInputRef.current.click()
      // Remove capture attribute after click to allow file selection on subsequent clicks
      setTimeout(() => {
        if (fileInputRef.current) {
          fileInputRef.current.removeAttribute('capture')
        }
      }, 100)
    }
  }, [])
  
  async function onSubmit() {
    if (!file) {
      toast.error("Please select a file first")
      return
    }
    
    try {
      setIsLoading(true)
      
      console.log("Starting upload process for:", {
        fileType,
        fileName: file.name, 
        fileSize: file.size,
        mimeType: file.type
      });
      
      const formData = new FormData()
      formData.append("file", file)
      formData.append("fileType", fileType)
      
      console.log("Sending request to API...");
      
      const response = await fetch('/api/profile/upload-file', {
        method: "POST",
        body: formData,
        credentials: "include",
      })
      
      console.log("Response status:", response.status);
      
      if (!response.ok) {
        let errorMessage = "Failed to upload file"
        let errorData = null;
        
        try {
          errorData = await response.json()
          console.error("Error response:", errorData);
          errorMessage = errorData.error || errorMessage
        } catch (parseError) {
          console.error("Failed to parse error response:", parseError);
          try {
            const text = await response.text();
            console.error("Raw response:", text);
          } catch (e) {
            console.error("Could not get response text");
          }
        }
        
        throw new Error(errorMessage)
      }
      
      const result = await response.json()
      console.log("Upload success result:", result);

      // Show success toast and close dialog
      toast.success(result.message || "File uploaded successfully")
      setOpen(false)
      
      // Clean up preview URL
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
      }
      
      // Reset form
      setFile(null)
      setPreviewUrl(null)
      
      // For avatar uploads, we need a full page reload to see the changes
      if (fileType === 'avatar' && result.url) {
        console.log("Avatar updated, performing hard refresh");
        setTimeout(() => {
          window.location.reload();
        }, 300);
      } 
      // For other file types, use the callback or router refresh
      else if (onSuccess && result.url) {
        console.log("Calling onSuccess with URL:", result.url);
        onSuccess(result.url);
      } else {
        console.log("Refreshing page via router");
        router.refresh();
      }
    } catch (error: any) {
      console.error("Upload error:", error.message);
      toast.error(error.message || "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }
  
  // Get dialog title and description based on file type
  const getDialogTitle = () => {
    switch (fileType) {
      case "resume": return "Upload Resume";
      case "avatar": return "Upload Profile Photo";
      case "profile_image": return "Upload Profile Image";
      default: return "Upload File";
    }
  }
  
  const getDialogDescription = () => {
    switch (fileType) {
      case "resume": return "Upload your resume in PDF, DOC, or DOCX format.";
      case "avatar": return "Upload a profile photo. This will be visible to recruiters and employers.";
      case "profile_image": return "Upload a professional profile image (JPG or PNG) visible to organizations.";
      default: return "Upload a file.";
    }
  }
  
  const getButtonText = () => {
    if (buttonText) return buttonText;
    switch (fileType) {
      case "resume": return "Upload Resume";
      case "avatar": return "Upload Photo";
      case "profile_image": return "Upload Image";
      default: return "Upload File";
    }
  }
  
  // Clean up when dialog closes
  const handleDialogChange = (open: boolean) => {
    setOpen(open)
    if (!open) {
      // Clean up preview URL
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
      }
      
      // Reset state
      setFile(null)
      setPreviewUrl(null)
    }
  }
  
  return (
    <Dialog open={open} onOpenChange={handleDialogChange}>
      <DialogTrigger asChild>
        {triggerComponent || (
          <Button size="sm" variant={isResume ? "outline" : "ghost"}>
            <Upload className="mr-2 h-4 w-4" />
            {getButtonText()}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>
            {getDialogDescription()}
          </DialogDescription>
        </DialogHeader>
        
        <div className="my-6">
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept={acceptTypes}
            onChange={handleFileChange}
          />
          
          {/* Different UI for avatar vs resume */}
          {isImage ? (
            <div className="space-y-4">
              {!file ? (
                <div className="flex flex-col items-center">
                  <div 
                    className="w-32 h-32 rounded-full bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-700 flex items-center justify-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-800/80 transition mb-4"
                    onClick={openFileSelector}
                  >
                    <div className="text-center">
                      <Image className="h-8 w-8 mx-auto text-gray-400 dark:text-gray-500" />
                      <p className="text-xs mt-1 text-muted-foreground">Click to browse</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={openFileSelector} size="sm" variant="outline">
                      <Upload className="h-4 w-4 mr-1" />
                      Browse
                    </Button>
                    {/* Only show camera button on devices that support it */}
                    <Button onClick={handleCameraCapture} size="sm" variant="outline">
                      <Camera className="h-4 w-4 mr-1" />
                      Camera
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    JPG or PNG up to 2MB
                  </p>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="w-32 h-32 rounded-full overflow-hidden border-2 border-primary">
                    <img 
                      src={previewUrl || ""} 
                      alt="Profile Preview" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="mt-4 flex gap-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => {
                        if (previewUrl) URL.revokeObjectURL(previewUrl);
                        setFile(null);
                        setPreviewUrl(null);
                      }}
                    >
                      Choose Different
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div 
              className="border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-primary/50 transition"
              onClick={openFileSelector}
            >
              {!file && (
                <>
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                    <FileText className="h-6 w-6 text-primary" />
                  </div>
                  <p className="font-medium text-sm">Select a file</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    or drag and drop it here
                  </p>
                </>
              )}
              
              {file && fileType === "resume" && (
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100 text-blue-700">
                    <FileText className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="font-medium text-sm">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {Math.round(file.size / 1024)} KB
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
          
          {!file && !isImage && (
            <p className="text-xs text-muted-foreground mt-2 text-center">
              PDF, DOC, or DOCX up to 5MB
            </p>
          )}
        </div>
        
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button type="button" onClick={onSubmit} disabled={isLoading || !file}>
            {isLoading ? "Uploading..." : "Upload"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 