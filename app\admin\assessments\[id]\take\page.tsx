import React from 'react';
import { ErrorBoundary } from '@/components/error-boundary';
import { AssessmentTake } from './assessment-take';

export default function AssessmentTakePage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params promise with React.use()
  const resolvedParams = React.use(params);
  const id = resolvedParams.id;
  
  return (
    <ErrorBoundary>
      <AssessmentTake assessmentId={id} />
    </ErrorBoundary>
  );
} 