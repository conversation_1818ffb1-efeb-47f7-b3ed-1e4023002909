-- Luna Database Seeding Script
-- Insert test data for organizations, teams, and user assignments

-- First, let's insert some organizations
INSERT INTO organizations (
  id, name, slug, description, industry, size_range, 
  subscription_tier, branding_config, subdomain, website_url
) VALUES 
(
  '11111111-1111-1111-1111-111111111111',
  'TechCorp Solutions',
  'techcorp',
  'Leading technology solutions provider',
  'Technology',
  'large',
  'enterprise',
  '{"primary_color": "#2563eb"}',
  'techcorp',
  'https://techcorp.com'
),
(
  '22222222-2222-2222-2222-222222222222',
  'Creative Agency Inc',
  'creative-agency',
  'Full-service creative and marketing agency',
  'Marketing',
  'medium',
  'professional',
  '{"primary_color": "#7c3aed"}',
  'creative',
  'https://creativeagency.com'
),
(
  '33333333-3333-3333-3333-333333333333',
  'StartupHub',
  'startuphub',
  'Innovation and startup incubator',
  'Consulting',
  'small',
  'basic',
  '{"primary_color": "#059669"}',
  'startup',
  'https://startuphub.com'
);

-- Insert teams for each organization
INSERT INTO teams (
  id, organization_id, name, slug, description, 
  max_members, is_public, enable_chat, enable_file_sharing
) VALUES 
-- TechCorp Teams
(
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111',
  'Engineering Team',
  'engineering',
  'Core software development team',
  25,
  false,
  true,
  true
),
(
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '11111111-1111-1111-1111-111111111111',
  'Product Team',
  'product',
  'Product management and strategy',
  15,
  false,
  true,
  true
),
-- Creative Agency Teams
(
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  '22222222-2222-2222-2222-222222222222',
  'Design Team',
  'design',
  'Creative design and branding',
  12,
  false,
  true,
  true
),
(
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  '22222222-2222-2222-2222-222222222222',
  'Marketing Team',
  'marketing',
  'Digital marketing and campaigns',
  8,
  false,
  true,
  true
),
-- StartupHub Teams
(
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  '33333333-3333-3333-3333-333333333333',
  'Innovation Lab',
  'innovation',
  'Research and development initiatives',
  6,
  true,
  true,
  true
);

-- Now let's assign some existing users to teams
-- First, let's see what users we have and assign them to teams
INSERT INTO team_memberships (
  user_id, team_id, role, status, joined_at
) VALUES 
-- Assign some users to TechCorp Engineering
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'owner',
  'active',
  NOW()
),
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'admin',
  'active',
  NOW()
),
-- Assign users to Creative Agency Design
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'owner',
  'active',
  NOW()
),
-- Assign users to StartupHub Innovation
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'owner',
  'active',
  NOW()
);

-- Create user contexts for team members
INSERT INTO user_contexts (
  user_id, context_type, active_team_id, active_organization_id
) VALUES 
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'team',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111'
),
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'team',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111'
),
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'team',
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  '22222222-2222-2222-2222-222222222222'
),
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'team',
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  '33333333-3333-3333-3333-333333333333'
);

-- Create individual contexts for users not in teams
INSERT INTO user_contexts (
  user_id, context_type, active_team_id, active_organization_id
) 
SELECT 
  id,
  'individual',
  NULL,
  NULL
FROM users 
WHERE email NOT IN (
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>'
)
AND email != '<EMAIL>';
