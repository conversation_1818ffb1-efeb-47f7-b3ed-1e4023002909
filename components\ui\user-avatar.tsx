"use client"

import * as React from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"

interface UserAvatarProps {
  user: {
    full_name?: string | null
    email?: string | null
    avatar_url?: string | null
  }
  size?: "sm" | "md" | "lg" | "xl"
  className?: string
}

const sizeClasses = {
  sm: "h-8 w-8",
  md: "h-10 w-10", 
  lg: "h-12 w-12",
  xl: "h-16 w-16"
}

const fallbackSizeClasses = {
  sm: "text-xs",
  md: "text-sm",
  lg: "text-base", 
  xl: "text-lg"
}

export function UserAvatar({ user, size = "md", className }: UserAvatarProps) {
  // Generate initials from full name or email
  const getInitials = () => {
    if (user.full_name) {
      return user.full_name
        .split(' ')
        .map(name => name.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2)
    }
    
    if (user.email) {
      return user.email.charAt(0).toUpperCase()
    }
    
    return "U"
  }

  // Validate avatar URL
  const getValidAvatarUrl = () => {
    if (!user.avatar_url) return null
    
    // Check if it's a valid URL format
    if (user.avatar_url.includes('supabase.co') || 
        user.avatar_url.includes('storage.googleapis.com') || 
        user.avatar_url.startsWith('/') ||
        user.avatar_url.startsWith('http')) {
      return user.avatar_url
    }
    
    return null
  }

  const initials = getInitials()
  const avatarUrl = getValidAvatarUrl()

  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      {avatarUrl && (
        <AvatarImage 
          src={avatarUrl} 
          alt={user.full_name || user.email || "User"} 
          onError={(e) => {
            // If image fails to load, hide it and show fallback
            const imgElement = e.target as HTMLImageElement
            imgElement.style.display = 'none'
          }}
        />
      )}
      <AvatarFallback className={cn(
        "bg-blue-100 text-blue-700 dark:bg-blue-950 dark:text-blue-300",
        fallbackSizeClasses[size]
      )}>
        {initials}
      </AvatarFallback>
    </Avatar>
  )
}
