import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';
import { Database } from '@/types/supabase';

// Create admin client to bypass Row Level Security policies
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);

// Define experience entry type for type safety
interface ExperienceEntry {
  title?: string;
  company?: string;
  location?: string;
  start_date?: string;
  start_month?: string;
  start_year?: number;
  end_date?: string | null;
  end_month?: string | null;
  end_year?: number | null;
  description?: string;
  [key: string]: any; // For other potential fields
}

export async function POST(req: NextRequest) {
  try {
    console.log("🔍 Experience update endpoint called");
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    // Get session to verify user is authenticated
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log("❌ No session found");
      return NextResponse.json(
        { error: 'Unauthorized - You must be logged in' },
        { status: 401 }
      );
    }
    
    console.log("✅ User authenticated:", session.user.id);
    
    // Parse request body
    const body = await req.json();
    console.log("📦 Request body:", body);
    const { action, experienceData, experienceIndex } = body;
    
    // Get the prospect's profile using admin client to bypass RLS
    const { data: prospectData, error: prospectError } = await adminClient
      .from('prospects')
      .select('id, experience')
      .eq('user_id', session.user.id)
      .single();
    
    if (prospectError) {
      console.log("❌ Failed to find prospect profile:", prospectError.message);
      return NextResponse.json(
        { error: prospectError.message || 'Failed to find prospect profile' },
        { status: 500 }
      );
    }
    
    console.log("✅ Found prospect profile:", prospectData.id);
    
    let experience: ExperienceEntry[] = Array.isArray(prospectData.experience) 
      ? prospectData.experience as ExperienceEntry[] 
      : [];
    console.log("📋 Current experience entries:", experience.length);
    
    // Handle the different actions
    switch (action) {
      case 'add':
        // Add a new experience entry
        console.log("➕ Adding new experience entry");
        experience.push(experienceData as ExperienceEntry);
        break;
        
      case 'update':
        // Update an existing experience entry
        if (experienceIndex >= 0 && experienceIndex < experience.length) {
          console.log("🔄 Updating experience entry at index:", experienceIndex);
          experience[experienceIndex] = {
            ...experience[experienceIndex],
            ...(experienceData as ExperienceEntry)
          };
        } else {
          console.log("❌ Invalid experience index:", experienceIndex);
          return NextResponse.json(
            { error: 'Invalid experience index' },
            { status: 400 }
          );
        }
        break;
        
      case 'delete':
        // Delete an experience entry
        if (experienceIndex >= 0 && experienceIndex < experience.length) {
          console.log("🗑️ Deleting experience entry at index:", experienceIndex);
          experience = experience.filter((_, index) => index !== experienceIndex);
        } else {
          console.log("❌ Invalid experience index:", experienceIndex);
          return NextResponse.json(
            { error: 'Invalid experience index' },
            { status: 400 }
          );
        }
        break;
        
      default:
        console.log("❌ Invalid action:", action);
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
    
    // Update the prospect's experience using admin client to bypass RLS
    console.log("🔄 Updating prospect experience data");
    
    try {
      // Execute a direct database update bypassing all RLS policies
      console.log('Experience API - Directly updating prospect with ID:', prospectData.id);
      
      // Make a direct API call to update experience
      const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_VERCEL_URL || "http://localhost:3000";
      const response = await fetch(`${baseUrl}/api/direct-update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.API_SECRET_KEY || "bpo-training-platform-api-key"}`
        },
        body: JSON.stringify({
          table: "prospects",
          id: prospectData.id,
          data: { experience }
        })
      });
      
      if (!response.ok) {
        throw new Error("Failed to update experience through direct API");
      }
      
      // Since we can't guarantee our API call worked, let's also try a direct update
      // fallback approach through the admin client
      const { error: updateError } = await adminClient
        .from('prospects')
        .update({ experience })
        .eq('id', prospectData.id);
        
      if (updateError) {
        console.error("Admin client update failed as fallback, but direct API may have succeeded:", updateError);
      }
      
      console.log("✅ Experience updated successfully");
      
      return NextResponse.json({
        success: true,
        message: `Experience ${action === 'add' ? 'added' : action === 'update' ? 'updated' : 'deleted'} successfully`,
        experience
      });
    } catch (dbError: any) {
      console.error('Experience API - Database operation error:', dbError);
      
      // Last resort fallback using the admin client if the first approach failed
      try {
        const { data: updateResult, error: fallbackError } = await adminClient
          .from('prospects')
          .update({ experience })
          .eq('id', prospectData.id)
          .select();
          
        if (fallbackError) {
          console.error('Experience API - Fallback update error:', fallbackError);
          return NextResponse.json(
            { error: fallbackError.message || 'Failed to update experience' },
            { status: 500 }
          );
        }
        
        console.log("✅ Experience updated successfully (fallback)");
        
        return NextResponse.json({
          success: true,
          message: `Experience ${action === 'add' ? 'added' : action === 'update' ? 'updated' : 'deleted'} successfully (fallback)`,
          experience
        });
      } catch (fallbackError: any) {
        console.error('Experience API - Fallback error:', fallbackError);
        return NextResponse.json(
          { error: "All update methods failed" },
          { status: 500 }
        );
      }
    }
  } catch (error: any) {
    console.error('❌ Error updating experience:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
} 