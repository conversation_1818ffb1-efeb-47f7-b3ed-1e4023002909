import { createClient } from '@supabase/supabase-js';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/types/database.types';

export const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
export const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// =============================================================================
// STANDARDIZED SUPABASE CLIENT CREATION
// =============================================================================

/**
 * Create a Supabase client for browser/client-side operations
 * Use this in client components and browser-side code
 */
export const createBrowserClient = () => {
  // Use explicit createClient to avoid environment variable caching issues
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('[SUPABASE] Missing environment variables:', {
      url: supabaseUrl || 'MISSING',
      key: supabaseAnonKey ? 'SET' : 'MISSING'
    });
    throw new Error('Missing Supabase environment variables');
  }

  return createClient<Database>(supabaseUrl, supabaseAnonKey);
};

// Server-side clients are now in lib/supabase-server.ts to avoid import issues

/**
 * Create a Supabase client with service role (admin privileges)
 * ONLY use this when you need to bypass RLS policies
 * Most operations should use the new secure RLS functions instead
 */
export const createAdminClient = () => {
  return createClient<Database>(
    supabaseUrl,
    process.env.SUPABASE_SERVICE_ROLE_KEY || '',
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );
};

/**
 * Create a storage client for file operations
 */
export function createStorageClient() {
  return createClient(supabaseUrl, supabaseAnonKey).storage.from('training-media');
}

// Create a single instance for the browser client (legacy support)
export const supabase = createBrowserClient();