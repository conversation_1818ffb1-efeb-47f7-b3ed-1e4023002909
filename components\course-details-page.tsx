'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  ArrowLeft,
  Clock,
  Users,
  Star,
  BookOpen,
  Layout,
  BarChart,
  CheckCircle,
  PlayCircle,
  FileQuestion,
  Video,
  AlertCircle,
  FileText,
  ChevronRight,
  Play,
  Lock
} from 'lucide-react'
import { toast } from "sonner"
import { RichTextDisplay } from "@/components/ui/rich-text-display"

interface CourseDetailsPageProps {
  course: {
    id: string
    name: string
    description: string
    instructor: string
    instructorEmail: string
    level: string
    estimatedDuration: number
    price: number
    coverImageUrl: string
    previewVideoUrl?: string | null
    tags: string[]
    learningObjectives: string[]
    targetAudience: string
    instructorBio: string
    enrollmentCount: number
    averageRating: number
    completionRate: number
    certificationAvailable: boolean
    language: string
    requiredSoftware: string[]
    hardwareRequirements: string
    accessibilityFeatures: string[]
    modules: Array<{
      id: string
      name: string
      description: string
      sequenceOrder: number
      estimatedDuration: number
      status: string
      learningObjectives: string[]
      lessons: Array<{
        id: string
        name: string
        description: string
        lessonType: string
        sequenceOrder: number
        estimatedDuration: number
        isMandatory: boolean
        status: string
      }>
    }>
    isEnrolled: boolean
    enrollment?: {
      id: string
      enrolledAt: string
      startedAt?: string | null
      completedAt?: string | null
      progressPercentage: number
      currentModuleId?: string | null
      currentLessonId?: string | null
    } | null
  }
}

export default function CourseDetailsPage({ course }: CourseDetailsPageProps) {
  const router = useRouter()
  const [enrolling, setEnrolling] = useState(false)

  const handleEnroll = async () => {
    try {
      setEnrolling(true)
      const response = await fetch(`/api/individual/courses/${course.id}/enroll`, {
        method: 'POST',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to enroll')
      }

      toast.success(`Successfully enrolled in ${course.name}!`)
      // Refresh the page to show enrolled state
      window.location.reload()

    } catch (error: any) {
      console.error('Enrollment error:', error)
      toast.error(error.message || 'Failed to enroll in course')
    } finally {
      setEnrolling(false)
    }
  }

  const handleAccessCourse = () => {
    // Navigate to the first module/lesson
    if (course.modules.length > 0) {
      const firstModule = course.modules[0]
      if (firstModule.lessons.length > 0) {
        const firstLesson = firstModule.lessons[0]
        router.push(`/individual/courses/${course.id}/modules/${firstModule.id}/lessons/${firstLesson.id}`)
      } else {
        router.push(`/individual/courses/${course.id}/modules/${firstModule.id}`)
      }
    } else {
      router.push('/individual/courses-marketplace')
    }
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  // Calculate progress statistics
  const totalLessons = course.modules.reduce((total, module) => total + module.lessons.length, 0)
  const completedLessons = course.modules.reduce((total, module) =>
    total + module.lessons.filter(lesson => lesson.status === 'completed').length, 0
  )
  const inProgressLessons = course.modules.reduce((total, module) =>
    total + module.lessons.filter(lesson => lesson.status === 'in_progress').length, 0
  )

  const progressPercentage = course.enrollment?.progressPercentage || 0
  const courseStatus = course.enrollment?.completedAt
    ? "completed"
    : course.enrollment?.startedAt
      ? "in_progress"
      : "not_started"

  // Get next lesson for continue learning
  const getNextLesson = () => {
    for (const module of course.modules) {
      for (const lesson of module.lessons) {
        if (lesson.status !== 'completed') {
          return { moduleId: module.id, lessonId: lesson.id, lesson }
        }
      }
    }
    return course.modules[0]?.lessons[0] ? {
      moduleId: course.modules[0].id,
      lessonId: course.modules[0].lessons[0].id,
      lesson: course.modules[0].lessons[0]
    } : null
  }

  const nextLesson = getNextLesson()

  return (
    <main className="min-h-screen bg-background">

      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 py-6 md:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          {/* Main content column */}
          <div className="lg:col-span-8 space-y-6">

            {/* Course info card */}
            <Card className="overflow-hidden border-none shadow-sm">
              <CardContent className="p-0">
                {/* Course status bar */}
                {courseStatus !== "not_started" && (
                  <div className="bg-primary/5 dark:bg-primary/10 px-6 py-3 flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <BarChart className="h-4 w-4 text-primary" />
                      <span className="text-sm font-medium">
                        {courseStatus === "completed"
                          ? "Course completed"
                          : `${progressPercentage}% complete`}
                      </span>
                    </div>
                    <Progress
                      value={progressPercentage}
                      className="w-24 h-2 bg-primary/10 [&>div]:bg-primary"
                    />
                  </div>
                )}

                <div className="p-6 bg-blue-50 dark:bg-blue-900/10">
                  {/* Course metadata */}
                  <div className="flex flex-wrap gap-4 mb-6">
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Layout className="h-4 w-4 mr-2" />
                      <span>{totalLessons} {totalLessons === 1 ? 'lesson' : 'lessons'}</span>
                    </div>

                    {course.estimatedDuration && (
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-4 w-4 mr-2" />
                        <span>
                          {Math.round(course.estimatedDuration / 60)} hours
                        </span>
                      </div>
                    )}

                    {/* Example of another metadata field if needed */}
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Users className="h-4 w-4 mr-2" />
                      <span>For all learners</span>
                    </div>
                  </div>

                  {/* Description */}
                  <div className="prose prose-xs max-w-none dark:prose-invert text-sm [&>p]:text-sm [&>ul]:text-sm [&>ol]:text-sm">
                    <RichTextDisplay
                      content={course.description || "<p>No description available for this course.</p>"}
                    />
                  </div>

                  {/* Continue button - only if enrolled and has content */}
                  {course.isEnrolled && nextLesson && (
                    <div className="mt-6">
                      <Button
                        size="lg"
                        className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white"
                        onClick={handleAccessCourse}
                      >
                        {courseStatus === "completed" ? (
                          <>Review Course</>
                        ) : courseStatus === "in_progress" ? (
                          <>Continue Learning</>
                        ) : (
                          <>Start Course</>
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>


            {/* Modules section */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Course Modules</h2>
                <div className="text-sm text-muted-foreground">
                  {course.modules.length} {course.modules.length === 1 ? 'module' : 'modules'}
                </div>
              </div>

              {/* Empty state */}
              {(!course.modules || course.modules.length === 0) && (
                <Card className="border-dashed bg-transparent py-12">
                  <CardContent className="flex flex-col items-center text-center p-6">
                    <AlertCircle className="h-10 w-10 text-muted-foreground mb-3" />
                    <h3 className="text-lg font-medium mb-1">No modules available</h3>
                    <p className="text-sm text-muted-foreground max-w-md">
                      This course doesn't have any modules yet. Please check back later.
                    </p>
                  </CardContent>
                </Card>
              )}

              {/* Module cards grid */}
              {course.modules && course.modules.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {course.modules.map((module, index) => {
                    const moduleCompletedLessons = module.lessons.filter(l => l.status === 'completed').length
                    const moduleInProgressLessons = module.lessons.filter(l => l.status === 'in_progress').length
                    const moduleProgressPercentage = module.lessons.length > 0
                      ? Math.round((moduleCompletedLessons / module.lessons.length) * 100)
                      : 0

                    const moduleStatus = moduleCompletedLessons === module.lessons.length && module.lessons.length > 0
                      ? "completed"
                      : moduleCompletedLessons > 0 || moduleInProgressLessons > 0
                        ? "in_progress"
                        : "not_started"

                    return (
                      <Card key={module.id} className="group hover:shadow-md transition-all duration-200 cursor-pointer">
                        <Link href={`/individual/courses/${course.id}/modules/${module.id}`}>
                          <CardContent className="p-6">
                            {/* Module header */}
                            <div className="flex items-start justify-between mb-4">
                              <div className="flex-1">
                                <div className="flex items-center mb-2">
                                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                                    moduleStatus === 'completed'
                                      ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
                                      : moduleStatus === 'in_progress'
                                        ? "bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400"
                                        : "bg-primary/10 text-primary"
                                  }`}>
                                    {moduleStatus === 'completed' ? (
                                      <CheckCircle className="h-4 w-4" />
                                    ) : moduleStatus === 'in_progress' ? (
                                      <PlayCircle className="h-4 w-4" />
                                    ) : (
                                      <BookOpen className="h-4 w-4" />
                                    )}
                                  </div>
                                  <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
                                    {module.name}
                                  </h3>
                                </div>

                                <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                                  {module.description || "No description available"}
                                </p>
                              </div>

                              <ChevronRight className="h-5 w-5 text-muted-foreground/50 group-hover:text-primary transition-colors" />
                            </div>

                            {/* Module metadata */}
                            <div className="flex items-center gap-4 mb-4 text-xs text-muted-foreground">
                              <div className="flex items-center">
                                <Layout className="h-3 w-3 mr-1" />
                                <span>{module.lessons.length} lessons</span>
                              </div>
                              {module.estimatedDuration && (
                                <div className="flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  <span>{Math.round(module.estimatedDuration / 60)} hours</span>
                                </div>
                              )}
                              <Badge variant={
                                moduleStatus === 'completed' ? 'default' :
                                moduleStatus === 'in_progress' ? 'secondary' : 'outline'
                              } className="text-xs">
                                {moduleStatus === 'completed' ? 'Completed' :
                                 moduleStatus === 'in_progress' ? 'In Progress' : 'Not Started'}
                              </Badge>
                            </div>

                            {/* Progress bar */}
                            {moduleStatus !== 'not_started' && (
                              <div className="space-y-2">
                                <div className="flex justify-between text-xs">
                                  <span className="text-muted-foreground">Progress</span>
                                  <span className="font-medium">{moduleProgressPercentage}%</span>
                                </div>
                                <Progress
                                  value={moduleProgressPercentage}
                                  className="h-2"
                                />
                              </div>
                            )}
                          </CardContent>
                        </Link>
                      </Card>
                    );
                  })}
                </div>
              )}
            </div>
          </div>


          {/* Sidebar column */}
          <div className="lg:col-span-4">
            <div className="lg:sticky lg:top-24 space-y-6">
              {/* Course progress card */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-medium mb-4">Your Progress</h3>

                  <div className="space-y-3 mb-6">
                    <div className="flex justify-between text-sm">
                      <span>Completion</span>
                      <span className="font-medium">{progressPercentage}%</span>
                    </div>
                    <Progress
                      value={progressPercentage}
                      className="h-2 [&>div]:bg-primary"
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div className="bg-primary/5 dark:bg-primary/10 rounded-md p-3">
                      <div className="text-2xl font-bold">{totalLessons}</div>
                      <div className="text-xs text-muted-foreground">Total</div>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 rounded-md p-3">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">{completedLessons}</div>
                      <div className="text-xs text-muted-foreground">Completed</div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3">
                      <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{inProgressLessons}</div>
                      <div className="text-xs text-muted-foreground">In Progress</div>
                    </div>
                  </div>

                  {/* Action button based on status */}
                  {course.isEnrolled && totalLessons > 0 && (
                    <div className="mt-5">
                      <Button
                        variant={courseStatus === "not_started" ? "default" : "secondary"}
                        className="w-full"
                        onClick={handleAccessCourse}
                      >
                        <span className="flex items-center justify-center">
                          {courseStatus === "completed" ? (
                            <>
                              <FileText className="h-4 w-4 mr-2" />
                              Review First Lesson
                            </>
                          ) : courseStatus === "in_progress" ? (
                            <>
                              <BookOpen className="h-4 w-4 mr-2" />
                              Continue Learning
                            </>
                          ) : (
                            <>
                              <Play className="h-4 w-4 mr-2" />
                              Start First Lesson
                            </>
                          )}
                        </span>
                      </Button>
                    </div>
                  )}

                  {/* Enrollment section for non-enrolled users */}
                  {!course.isEnrolled && (
                    <div className="mt-5">
                      <div className="text-center mb-4">
                        <div className="text-2xl font-bold mb-2">
                          ${course.price === 0 ? 'Free' : course.price}
                        </div>
                        {course.price > 0 && (
                          <p className="text-sm text-muted-foreground">One-time payment</p>
                        )}
                      </div>

                      <Button
                        className="w-full"
                        onClick={handleEnroll}
                        disabled={enrolling}
                      >
                        {enrolling ? 'Enrolling...' : course.price === 0 ? 'Enroll for Free' : 'Enroll Now'}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Additional card for related courses, resources, or help */}
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-medium mb-4">Need Help?</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    If you need assistance with this course or have questions, please reach out to our support team.
                  </p>
                  <Button variant="outline" className="w-full">Contact Support</Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
