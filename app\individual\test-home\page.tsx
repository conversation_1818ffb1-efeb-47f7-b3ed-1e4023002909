"use client"

import React from 'react'
import { TestSidebar } from '@/components/test-sidebar'

export default function TestHomePage() {
  return (
    <TestSidebar>
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Find, Contact & Close</h1>
        <p className="text-xl text-gray-600 mb-2">
          Your Next <span className="text-blue-600 font-semibold">Firm</span> On Topline
        </p>
        <p className="text-gray-500">
          Where Millions of Top Professionals Get Discovered & Grow.
        </p>

        <div className="mt-8 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">🎯 Topline-Style Sidebar Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">✨ Interactive Features</h3>
              <ul className="space-y-1 text-gray-600 text-sm">
                <li>• <strong>Hover to expand</strong> - Sidebar grows from 64px to 240px</li>
                <li>• <strong>Pin toggle</strong> - Click pin icon to keep expanded</li>
                <li>• <strong>Trigger icon</strong> - Menu button in header to toggle</li>
                <li>• <strong>Context switcher</strong> - Individual/Organization dropdown</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">🎨 Design Elements</h3>
              <ul className="space-y-1 text-gray-600 text-sm">
                <li>• <strong>Topline.com colors</strong> - Exact indigo active states</li>
                <li>• <strong>Inter font family</strong> - Professional typography</li>
                <li>• <strong>Smooth animations</strong> - 300ms transitions</li>
                <li>• <strong>Tooltips</strong> - Show labels when collapsed</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-3 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-800">
              <strong>Try it:</strong> Hover over the sidebar, click the menu trigger, use the pin toggle, and open the context switcher!
            </p>
          </div>
        </div>
      </div>
    </TestSidebar>
  )
}