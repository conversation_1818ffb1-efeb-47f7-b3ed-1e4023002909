import { NextRequest, NextResponse } from 'next/server';
import { createApiClient } from '@/lib/supabase-server';

/**
 * GET /api/org/[orgSlug]/activities
 * Get recent activities for organization dashboard
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orgSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug } = params;

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    const organizationId = organization.id;
    const activities: any[] = [];

    // Get recent employment relationships (employees joined)
    const { data: recentEmployments } = await supabase
      .from('employment_relationships')
      .select(`
        id,
        created_at,
        job_title,
        users!inner(full_name)
      `)
      .eq('organization_id', organizationId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(5);

    if (recentEmployments) {
      recentEmployments.forEach(emp => {
        activities.push({
          id: `emp-${emp.id}`,
          type: 'employee_joined',
          title: 'New Employee Joined',
          description: `${emp.users.full_name} joined as ${emp.job_title}`,
          timestamp: emp.created_at
        });
      });
    }

    // Get recent job postings (vacancies posted)
    const { data: recentVacancies } = await supabase
      .from('job_postings')
      .select('id, title, created_at')
      .eq('organization_id', organizationId)
      .eq('status', 'published')
      .order('created_at', { ascending: false })
      .limit(5);

    if (recentVacancies) {
      recentVacancies.forEach(vacancy => {
        activities.push({
          id: `vacancy-${vacancy.id}`,
          type: 'vacancy_posted',
          title: 'New Vacancy Posted',
          description: `${vacancy.title} position is now open for applications`,
          timestamp: vacancy.created_at
        });
      });
    }

    // Get recent applications
    if (recentVacancies && recentVacancies.length > 0) {
      const vacancyIds = recentVacancies.map(v => v.id);
      
      const { data: recentApplications } = await supabase
        .from('applications')
        .select(`
          id,
          submitted_at,
          job_postings!inner(title),
          users!inner(full_name)
        `)
        .in('job_id', vacancyIds)
        .order('submitted_at', { ascending: false })
        .limit(5);

      if (recentApplications) {
        recentApplications.forEach(app => {
          activities.push({
            id: `app-${app.id}`,
            type: 'application_received',
            title: 'New Application Received',
            description: `${app.users.full_name} applied for ${app.job_postings.title}`,
            timestamp: app.submitted_at
          });
        });
      }

      // Get recent interviews
      const { data: applicationData } = await supabase
        .from('applications')
        .select('id')
        .in('job_id', vacancyIds);

      if (applicationData && applicationData.length > 0) {
        const applicationIds = applicationData.map(a => a.id);
        
        const { data: recentInterviews } = await supabase
          .from('interviews')
          .select(`
            id,
            scheduled_at,
            applications!inner(
              job_postings!inner(title),
              users!inner(full_name)
            )
          `)
          .in('application_id', applicationIds)
          .eq('status', 'scheduled')
          .order('scheduled_at', { ascending: false })
          .limit(5);

        if (recentInterviews) {
          recentInterviews.forEach(interview => {
            activities.push({
              id: `interview-${interview.id}`,
              type: 'interview_scheduled',
              title: 'Interview Scheduled',
              description: `Interview scheduled with ${interview.applications.users.full_name} for ${interview.applications.job_postings.title}`,
              timestamp: interview.scheduled_at
            });
          });
        }
      }
    }

    // Sort all activities by timestamp (most recent first)
    activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Return top 10 activities
    return NextResponse.json(activities.slice(0, 10));

  } catch (error) {
    console.error('Error fetching organization activities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch organization activities' },
      { status: 500 }
    );
  }
}
