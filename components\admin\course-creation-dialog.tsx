"use client"

import { useState, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { BookOpen, Zap, Users, GraduationCap } from 'lucide-react'
import { toast } from "sonner"

interface CourseCreationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function CourseCreationDialog({ open, onOpenChange, onSuccess }: CourseCreationDialogProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    level: 'beginner',
    course_complexity: 'basic',
    price: '',
    estimated_duration: '',
    certification_available: false,
    is_standalone: true
  })

  // Memoized change handlers to prevent re-renders that cause focus loss
  const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: e.target.value }))
  }, [])

  const handleDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({ ...prev, description: e.target.value }))
  }, [])

  const handleLevelChange = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, level: value }))
  }, [])

  const handleComplexityChange = useCallback((value: string) => {
    setFormData(prev => ({ ...prev, course_complexity: value }))
  }, [])

  const handleDurationChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, estimated_duration: e.target.value }))
  }, [])

  const handlePriceChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, price: e.target.value }))
  }, [])

  const handleCertificationChange = useCallback((checked: boolean) => {
    setFormData(prev => ({ ...prev, certification_available: checked }))
  }, [])

  const handleStandaloneChange = useCallback((checked: boolean) => {
    setFormData(prev => ({ ...prev, is_standalone: checked }))
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('Course name is required')
      return
    }

    try {
      setLoading(true)
      
      const payload = {
        name: formData.name.trim(),
        description: formData.description.trim() || '',
        level: formData.level,
        course_complexity: formData.course_complexity,
        price: formData.price ? parseFloat(formData.price) : 0,
        estimated_duration: formData.estimated_duration ? parseInt(formData.estimated_duration) : 0,
        certification_available: formData.certification_available,
        is_standalone: formData.is_standalone,
        status: 'draft',
        language: 'en',
        tags: [],
        learning_objectives: [],
        required_software: [],
        accessibility_features: []
      }

      const response = await fetch('/api/admin/courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create course')
      }

      toast.success('Course created successfully!')
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        level: 'beginner',
        course_complexity: 'basic',
        price: '',
        estimated_duration: '',
        certification_available: false,
        is_standalone: true
      })
      
      onSuccess()
      
    } catch (error: any) {
      console.error('Error creating course:', error)
      toast.error(error.message || 'Failed to create course')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      name: '',
      description: '',
      level: 'beginner',
      course_complexity: 'basic',
      price: '',
      estimated_duration: '',
      certification_available: false,
      is_standalone: true
    })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Create New Course
          </DialogTitle>
          <DialogDescription>
            Set up the basic information for your new course. You can add modules, lessons, and detailed settings after creation.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Course Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Course Name *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={handleNameChange}
              placeholder="e.g., Introduction to Customer Service"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Course Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={handleDescriptionChange}
              placeholder="Brief description of what this course covers..."
              rows={3}
            />
          </div>

          {/* Course Settings Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="level">Difficulty Level</Label>
              <Select value={formData.level} onValueChange={handleLevelChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Beginner
                    </div>
                  </SelectItem>
                  <SelectItem value="intermediate">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      Intermediate
                    </div>
                  </SelectItem>
                  <SelectItem value="advanced">
                    <div className="flex items-center gap-2">
                      <GraduationCap className="h-4 w-4" />
                      Advanced
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="complexity">Course Complexity</Label>
              <Select value={formData.course_complexity} onValueChange={handleComplexityChange}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">Basic - Simple concepts</SelectItem>
                  <SelectItem value="advanced">Advanced - Complex topics</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Duration and Price */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="duration">Estimated Duration (hours)</Label>
              <Input
                id="duration"
                type="number"
                value={formData.estimated_duration}
                onChange={handleDurationChange}
                placeholder="e.g., 8"
                min="0"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">Price (USD)</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={handlePriceChange}
                placeholder="e.g., 99.00"
                min="0"
              />
            </div>
          </div>

          <Separator />

          {/* Course Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="certification">Certification Available</Label>
                <p className="text-sm text-muted-foreground">
                  Students can earn a certificate upon completion
                </p>
              </div>
              <Switch
                id="certification"
                checked={formData.certification_available}
                onCheckedChange={handleCertificationChange}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="standalone">Standalone Course</Label>
                <p className="text-sm text-muted-foreground">
                  Can be purchased and taken independently
                </p>
              </div>
              <Switch
                id="standalone"
                checked={formData.is_standalone}
                onCheckedChange={handleStandaloneChange}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Course'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
