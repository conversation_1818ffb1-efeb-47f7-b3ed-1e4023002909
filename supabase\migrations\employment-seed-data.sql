-- Luna Employment-Based Seed Data
-- Create organizations, departments, and employment relationships with new dual-context system

-- Step 1: Set platform admin role for admin user
UPDATE users SET role = 'platform_admin' WHERE email = '<EMAIL>';

-- Step 2: Set all other users to individual role
UPDATE users SET role = 'individual' WHERE email != '<EMAIL>';

-- Step 3: Insert Organizations (created_by is now nullable)
INSERT INTO organizations (
  id, name, slug, description, industry, size_range,
  subscription_tier, subdomain, website_url, status
) VALUES
(
  '11111111-1111-1111-1111-111111111111',
  'TechCorp Solutions',
  'techcorp',
  'Leading technology solutions provider',
  'Technology',
  'large',
  'enterprise',
  'techcorp',
  'https://techcorp.com',
  'active'
),
(
  '22222222-2222-2222-2222-222222222222',
  'Creative Agency Inc',
  'creative-agency',
  'Full-service creative and marketing agency',
  'Marketing',
  'medium',
  'professional',
  'creative',
  'https://creativeagency.com',
  'active'
),
(
  '33333333-3333-3333-3333-333333333333',
  'StartupHub',
  'startuphub',
  'Innovation and startup incubator',
  'Consulting',
  'small',
  'basic',
  'startup',
  'https://startuphub.com',
  'active'
);

-- Step 2: Insert Departments
INSERT INTO departments (
  id, organization_id, name, description
) VALUES 
-- TechCorp Departments
(
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '11111111-1111-1111-1111-111111111111',
  'Engineering',
  'Software development and technical operations'
),
(
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  '11111111-1111-1111-1111-111111111111',
  'Product Management',
  'Product strategy and roadmap planning'
),
(
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  '11111111-1111-1111-1111-111111111111',
  'Human Resources',
  'People operations and talent management'
),
-- Creative Agency Departments
(
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  '22222222-2222-2222-2222-222222222222',
  'Design',
  'Creative design and visual branding'
),
(
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  '22222222-2222-2222-2222-222222222222',
  'Marketing',
  'Digital marketing and campaign management'
),
-- StartupHub Departments
(
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  '33333333-3333-3333-3333-333333333333',
  'Innovation Lab',
  'Research and development initiatives'
);

-- Step 4: Create Employment Relationships with new employment_role enum
-- Assign existing users to departments with appropriate roles
INSERT INTO employment_relationships (
  user_id, organization_id, department_id, role, status,
  job_title, hire_date, joined_at
) VALUES
-- TechCorp: Organization Admin
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'organization_admin',
  'active',
  'Chief Technology Officer',
  CURRENT_DATE - INTERVAL '1 year',
  NOW()
),
-- TechCorp: Department Admin for Engineering
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'department_admin',
  'active',
  'Engineering Manager',
  CURRENT_DATE - INTERVAL '8 months',
  NOW()
),
-- TechCorp: Staff Member in Product Management
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '11111111-1111-1111-1111-111111111111',
  'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
  'staff_member',
  'active',
  'Product Manager',
  CURRENT_DATE - INTERVAL '6 months',
  NOW()
),
-- Creative Agency: Organization Admin
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '22222222-2222-2222-2222-222222222222',
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'organization_admin',
  'active',
  'Creative Director',
  CURRENT_DATE - INTERVAL '2 years',
  NOW()
),
-- Creative Agency: Department Admin for Marketing
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '22222222-2222-2222-2222-222222222222',
  'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
  'department_admin',
  'active',
  'Marketing Manager',
  CURRENT_DATE - INTERVAL '1 year',
  NOW()
),
-- StartupHub: Organization Admin
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '33333333-3333-3333-3333-333333333333',
  'ffffffff-ffff-ffff-ffff-ffffffffffff',
  'organization_admin',
  'active',
  'Innovation Director',
  CURRENT_DATE - INTERVAL '6 months',
  NOW()
),
-- Additional Staff Members
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '11111111-1111-1111-1111-111111111111',
  'cccccccc-cccc-cccc-cccc-cccccccccccc',
  'staff_member',
  'active',
  'HR Specialist',
  CURRENT_DATE - INTERVAL '4 months',
  NOW()
),
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  '22222222-2222-2222-2222-222222222222',
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'staff_member',
  'active',
  'Junior Designer',
  CURRENT_DATE - INTERVAL '3 months',
  NOW()
);

-- Step 4: Update Department Heads
UPDATE departments SET department_head_id = (
  SELECT user_id FROM employment_relationships 
  WHERE department_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa' 
  AND role = 'department_admin' 
  LIMIT 1
) WHERE id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

UPDATE departments SET department_head_id = (
  SELECT user_id FROM employment_relationships 
  WHERE department_id = 'dddddddd-dddd-dddd-dddd-dddddddddddd' 
  AND role = 'department_admin' 
  LIMIT 1
) WHERE id = 'dddddddd-dddd-dddd-dddd-dddddddddddd';

-- Step 5: Set Department Heads
UPDATE departments SET department_head_id = (
  SELECT user_id FROM employment_relationships
  WHERE department_id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa'
  AND role = 'department_admin'
  LIMIT 1
) WHERE id = 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa';

UPDATE departments SET department_head_id = (
  SELECT user_id FROM employment_relationships
  WHERE department_id = 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee'
  AND role = 'department_admin'
  LIMIT 1
) WHERE id = 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee';

-- Step 6: Create Individual Contexts for ALL Users (dual-context system)
-- Everyone defaults to individual context, but employed users can switch to org context
INSERT INTO user_contexts (
  user_id, context_type, active_organization_id,
  active_department_id, active_employment_id, last_org_context
)
SELECT
  u.id,
  'individual', -- Everyone starts in individual context
  NULL,
  NULL,
  NULL,
  er.organization_id -- Remember their last/primary organization for quick switching
FROM users u
LEFT JOIN employment_relationships er ON u.id = er.user_id AND er.status = 'active'
WHERE u.email != '<EMAIL>' -- Skip platform admin
ON CONFLICT (user_id) DO UPDATE SET
  context_type = 'individual',
  last_org_context = EXCLUDED.last_org_context;

-- Step 7: Create some sample employment invitations (pending)
INSERT INTO employment_invitations (
  email, organization_id, department_id, role, job_title, invited_by
) VALUES
(
  '<EMAIL>',
  '11111111-1111-1111-1111-111111111111',
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  'staff_member',
  'Software Engineer',
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1)
),
(
  '<EMAIL>',
  '22222222-2222-2222-2222-222222222222',
  'dddddddd-dddd-dddd-dddd-dddddddddddd',
  'staff_member',
  'Graphic Designer',
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1)
);

-- Step 8: Create some training data in different contexts
INSERT INTO user_training_data (
  user_id, training_context, employment_id, department_id,
  skills_data, progress_data, performance_metrics
) VALUES
-- Individual training data
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'individual',
  NULL,
  NULL,
  '{"programming": 85, "leadership": 70}',
  '{"courses_completed": 12, "total_hours": 45}',
  '{"avg_score": 88, "completion_rate": 95}'
),
-- Organizational training data
(
  (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1),
  'organization',
  (SELECT id FROM employment_relationships WHERE user_id = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1) LIMIT 1),
  'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
  '{"team_management": 90, "project_planning": 85}',
  '{"company_courses": 8, "certifications": 3}',
  '{"team_performance": 92, "goal_achievement": 88}'
);
