"use client"

import { useEffect } from 'react'

export function ScrollHandler() {
  useEffect(() => {
    // Guard against server-side execution
    if (typeof window === 'undefined' || typeof document === 'undefined') return;

    let scrollTimeout: NodeJS.Timeout

    const handleScroll = () => {
      // Add scrolling class to body
      document.body.classList.add('scrolling')

      // Clear existing timeout
      clearTimeout(scrollTimeout)

      // Remove scrolling class after scroll ends
      scrollTimeout = setTimeout(() => {
        document.body.classList.remove('scrolling')
      }, 1000) // Hide scrollbar 1 second after scrolling stops
    }

    // Add scroll listener to window
    window.addEventListener('scroll', handleScroll, { passive: true })

    // Also handle scroll on any scrollable element
    const scrollableElements = document.querySelectorAll('[data-scrollable]')
    scrollableElements.forEach(element => {
      element.addEventListener('scroll', handleScroll, { passive: true })
    })

    return () => {
      window.removeEventListener('scroll', handleScroll)
      scrollableElements.forEach(element => {
        element.removeEventListener('scroll', handleScroll)
      })
      clearTimeout(scrollTimeout)
    }
  }, [])

  return null
}
