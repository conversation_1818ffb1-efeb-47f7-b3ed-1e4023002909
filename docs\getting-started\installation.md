# 📦 Installation Guide

This guide will walk you through setting up the Luna Skills Platform on your local development environment with the employment-based dual-context architecture.

## 🔧 Prerequisites

Before you begin, ensure you have the following installed on your system:

### Required Software
- **Node.js 18.0 or higher** - [Download Node.js](https://nodejs.org/)
- **npm 9.0 or higher** (comes with Node.js)
- **Git** - [Download Git](https://git-scm.com/)
- **VS Code** (recommended) - [Download VS Code](https://code.visualstudio.com/)

### Required Accounts
- **Supabase Account** - [Sign up at supabase.com](https://supabase.com/)
- **GitHub Account** - [Sign up at github.com](https://github.com/)
- **Vercel Account** (for deployment) - [Sign up at vercel.com](https://vercel.com/)

### System Requirements
- **Operating System**: Windows 10+, macOS 10.15+, or Linux
- **RAM**: 8GB minimum, 16GB recommended (for optimal development experience)
- **Storage**: 3GB free space
- **Internet**: Stable internet connection for Supabase and real-time features

## 🚀 Step-by-Step Installation

### 1. Clone the Repository

```bash
# Clone the repository
git clone https://github.com/JennineHamilton/luna.git

# Navigate to the project directory
cd luna

# Verify the clone was successful
ls -la
```

### 2. Install Dependencies

**Important**: Use the `--legacy-peer-deps` flag to handle dependency conflicts:

```bash
# Install all dependencies with legacy peer deps
npm install --legacy-peer-deps

# Verify installation
npm list --depth=0
```

#### Using yarn
```bash
# Install all dependencies
yarn install

# Verify installation
yarn list --depth=0
```

### 3. Set Up Environment Variables

```bash
# Copy the example environment file
cp .env.example .env.local

# Open the file in your editor
code .env.local
```

Fill in the required environment variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# Optional: Analytics and Monitoring
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
```

### 4. Set Up Supabase Project

#### Create a New Supabase Project
1. Go to [supabase.com](https://supabase.com/)
2. Click "Start your project"
3. Create a new organization (if needed)
4. Click "New project"
5. Fill in project details:
   - **Name**: BPO Training Platform
   - **Database Password**: Choose a strong password
   - **Region**: Select closest to your location
6. Click "Create new project"

#### Get Your Project Credentials
1. Go to **Settings** → **API**
2. Copy the following values to your `.env.local`:
   - **Project URL** → `NEXT_PUBLIC_SUPABASE_URL`
   - **anon public** key → `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - **service_role** key → `SUPABASE_SERVICE_ROLE_KEY`

### 5. Set Up the Database

#### Run Database Migrations
```bash
# Install Supabase CLI (if not already installed)
npm install -g supabase

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref your-project-ref

# Run migrations
npm run db:migrate
```

#### Alternative: Manual Database Setup
If you prefer to set up the database manually:

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**
3. Run the migration files in order from `database/migrations/`

### 6. Seed Initial Data (Optional)

```bash
# Seed the database with sample data
npm run db:seed
```

This will create:
- Sample training modules
- Test user accounts
- Demo BPO companies
- Example job postings

### 7. Start the Development Server

```bash
# Start the development server
npm run dev

# Or with yarn
yarn dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

## 🔍 Verification

### Check Installation Success

1. **Application Loads**: Visit [http://localhost:3000](http://localhost:3000)
2. **Database Connection**: Try logging in with test credentials
3. **API Endpoints**: Check [http://localhost:3000/api/health](http://localhost:3000/api/health)

### Test User Accounts

After seeding, you can use these test accounts:

```
Platform Admin:
Email: <EMAIL>
Password: admin123

BPO Admin:
Email: <EMAIL>
Password: bpo123

Prospect:
Email: <EMAIL>
Password: prospect123
```

## 🛠️ Development Tools Setup

### VS Code Extensions (Recommended)

Install these extensions for the best development experience:

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### Git Hooks Setup

```bash
# Install husky for git hooks
npm run prepare

# Verify pre-commit hooks
git add .
git commit -m "test commit"
```

## 🐛 Troubleshooting

### Common Issues

#### Node.js Version Issues
```bash
# Check your Node.js version
node --version

# If version is too old, update Node.js
# Use nvm (recommended) or download from nodejs.org
```

#### Port Already in Use
```bash
# If port 3000 is busy, use a different port
npm run dev -- --port 3001
```

#### Database Connection Issues
1. Verify your Supabase credentials in `.env.local`
2. Check if your Supabase project is active
3. Ensure your IP is not blocked by Supabase

#### Module Not Found Errors
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### Getting Help

If you encounter issues:

1. **Check the logs**: Look at the terminal output for error messages
2. **Verify environment**: Ensure all environment variables are set correctly
3. **Check dependencies**: Make sure all packages are installed
4. **Restart services**: Try restarting the development server
5. **Clear cache**: Clear Next.js cache with `npm run clean`

## 📚 Next Steps

After successful installation:

1. **Read the [Quick Start Guide](quick-start.md)** for a 5-minute tutorial
2. **Explore the [Architecture Overview](../development/architecture.md)** to understand the system
3. **Check out the [Component Library](../development/component-library.md)** for UI components
4. **Review the [Development Workflow](../contributing/development-workflow.md)** for contribution guidelines

## 🔄 Keeping Updated

### Update Dependencies
```bash
# Check for outdated packages
npm outdated

# Update all packages
npm update

# Update specific package
npm install package-name@latest
```

### Pull Latest Changes
```bash
# Pull latest changes from main branch
git pull origin main

# Install any new dependencies
npm install

# Run any new migrations
npm run db:migrate
```

---

**Need help?** Check our [Troubleshooting Guide](../deployment/troubleshooting.md) or open an issue on GitHub.
