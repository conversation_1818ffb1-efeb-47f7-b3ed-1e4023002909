import { NextRequest, NextResponse } from 'next/server';
import { requireAuth, createAuthErrorResponse } from '@/lib/auth';
import { assessmentSessionService } from '@/lib/services/assessment-session';

/**
 * Get specific assessment session with questions
 * GET /api/assessments/sessions/[sessionId]
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { sessionId } = params;

    // Get session with questions
    const sessionData = await assessmentSessionService.getSessionWithQuestions(sessionId);

    if (!sessionData) {
      return NextResponse.json(
        { error: 'Assessment session not found' },
        { status: 404 }
      );
    }

    // Verify user owns this session
    if (sessionData.session.user_id !== authResult.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      session: sessionData.session,
      questions: sessionData.questions,
      config: sessionData.config
    });

  } catch (error: any) {
    console.error('Session fetch error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch assessment session' },
      { status: 500 }
    );
  }
}

/**
 * Update assessment session
 * PATCH /api/assessments/sessions/[sessionId]
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    // Require authentication
    const authResult = await requireAuth();
    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const { sessionId } = params;
    const body = await req.json();
    const { status, action } = body;

    // Get session to verify ownership
    const sessionData = await assessmentSessionService.getSessionWithQuestions(sessionId);

    if (!sessionData) {
      return NextResponse.json(
        { error: 'Assessment session not found' },
        { status: 404 }
      );
    }

    // Verify user owns this session
    if (sessionData.session.user_id !== authResult.user.id) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Handle different actions
    switch (action) {
      case 'pause':
        await assessmentSessionService.updateSessionStatus(sessionId, 'paused');
        break;
      
      case 'resume':
        await assessmentSessionService.updateSessionStatus(sessionId, 'in_progress');
        break;
      
      case 'abandon':
        await assessmentSessionService.updateSessionStatus(sessionId, 'abandoned');
        break;
      
      case 'complete':
        await assessmentSessionService.updateSessionStatus(sessionId, 'completed');
        break;
      
      default:
        if (status) {
          await assessmentSessionService.updateSessionStatus(sessionId, status);
        }
    }

    // Get updated session
    const updatedSessionData = await assessmentSessionService.getSessionWithQuestions(sessionId);

    return NextResponse.json({
      success: true,
      session: updatedSessionData?.session,
      message: `Assessment session ${action || 'updated'} successfully`
    });

  } catch (error: any) {
    console.error('Session update error:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to update assessment session' },
      { status: 500 }
    );
  }
}
