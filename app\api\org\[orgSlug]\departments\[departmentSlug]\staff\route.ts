import { NextRequest, NextResponse } from 'next/server';
import { createApiClient } from '@/lib/supabase-server';

/**
 * GET /api/org/[orgSlug]/departments/[departmentSlug]/staff
 * Get all staff members for a department
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { orgSlug: string; departmentSlug: string } }
) {
  try {
    const supabase = await createApiClient();
    const { orgSlug, departmentSlug } = params;

    // Get organization by slug
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .eq('slug', orgSlug)
      .single();

    if (orgError || !organization) {
      return NextResponse.json(
        { error: 'Organization not found' },
        { status: 404 }
      );
    }

    // Get department by slug
    const { data: department, error: deptError } = await supabase
      .from('departments')
      .select('id, name, slug')
      .eq('organization_id', organization.id)
      .eq('slug', departmentSlug)
      .single();

    if (deptError || !department) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    // Get staff members from employment_relationships and individuals tables
    const { data: employments, error: empError } = await supabase
      .from('employment_relationships')
      .select(`
        id,
        user_id,
        role,
        status,
        job_title,
        created_at,
        updated_at,
        individuals!inner (
          id,
          user_id,
          full_name,
          profile_image_url,
          phone,
          location,
          skills,
          learning_status,
          users!inner (
            id,
            email
          )
        )
      `)
      .eq('department_id', department.id)
      .eq('status', 'active');

    if (empError) {
      console.error('Error fetching staff:', empError);
      return NextResponse.json(
        { error: 'Failed to fetch staff members' },
        { status: 500 }
      );
    }

    // Transform the data to match our interface
    const staffMembers = (employments || []).map(emp => {
      const individual = emp.individuals;
      const user = individual?.users;
      
      // Calculate mock training progress and performance score
      const trainingProgress = Math.floor(Math.random() * 100);
      const performanceScore = Math.floor(Math.random() * 40) + 60; // 60-100 range
      
      return {
        id: emp.id,
        user_id: emp.user_id,
        full_name: individual?.full_name || 'Unknown',
        email: user?.email || '',
        job_title: emp.job_title || 'Staff Member',
        role: emp.role,
        status: emp.status,
        profile_image_url: individual?.profile_image_url || null,
        phone: individual?.phone || null,
        location: individual?.location || null,
        skills: individual?.skills || [],
        training_progress: trainingProgress,
        performance_score: performanceScore,
        recent_activity: individual?.learning_status === 'active' ? 'Currently learning' : 'Recent platform activity',
      };
    });

    return NextResponse.json(staffMembers);

  } catch (error) {
    console.error('Error in department staff API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
