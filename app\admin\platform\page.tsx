'use client';

import { useState, useEffect } from 'react';
import { createBrowserClient } from '@/lib/supabase';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Brain,
  Target,
  TrendingUp,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Palette,
  BookOpen,
  Activity,
  Building2,
  Users,
  Settings,
  Download
} from 'lucide-react';
import { SkillCategoryFormDialog } from '@/components/admin/skill-category-form-dialog';
import { SkillFormDialog } from '@/components/admin/skill-form-dialog';

interface SkillCategory {
  id: string;
  name: string;
  description: string;
  color_code: string;
  icon: string;
  is_active: boolean;
  sort_order: number;
  skill_count?: number;
}

interface Skill {
  id: string;
  name: string;
  category_id: string;
  subcategory: string;
  skill_type: 'hard' | 'soft' | 'hybrid';
  description: string;
  certification_available: boolean;
  market_demand: 'low' | 'medium' | 'high' | 'critical';
  is_active: boolean;
  created_at: string;
  skill_categories?: SkillCategory;
}

interface PlatformStats {
  totalSkills: number;
  totalCategories: number;
  activeSkills: number;
  highDemandSkills: number;
  certifiedSkills: number;
  skillsGrowth: number;
}

export default function PlatformConfigurationPage() {
  const { toast } = useToast();
  const supabase = createBrowserClient();
  
  // State management
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('skills');
  
  // Skills data
  const [skillCategories, setSkillCategories] = useState<SkillCategory[]>([]);
  const [skills, setSkills] = useState<Skill[]>([]);
  const [platformStats, setPlatformStats] = useState<PlatformStats>({
    totalSkills: 0,
    totalCategories: 0,
    activeSkills: 0,
    highDemandSkills: 0,
    certifiedSkills: 0,
    skillsGrowth: 0
  });
  
  // Filters and search
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [demandFilter, setDemandFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Dialog states
  const [selectedCategory, setSelectedCategory] = useState<SkillCategory | null>(null);
  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [skillDialogOpen, setSkillDialogOpen] = useState(false);

  useEffect(() => {
    if (activeTab === 'skills' || activeTab === 'analytics') {
      fetchSkillsData();
    }
  }, [activeTab]);

  const fetchSkillsData = async () => {
    try {
      setLoading(true);

      // Fetch skill categories with skill counts
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('skill_categories')
        .select(`
          *,
          skills(count)
        `)
        .order('sort_order');

      if (categoriesError) {
        console.error('Error fetching skill categories:', categoriesError);
        toast({
          title: "Error",
          description: "Failed to fetch skill categories",
          variant: "destructive"
        });
        return;
      }

      const transformedCategories: SkillCategory[] = (categoriesData || []).map((cat: any) => ({
        ...cat,
        skill_count: Array.isArray(cat.skills) ? cat.skills.length : 0
      }));

      setSkillCategories(transformedCategories);

      // Fetch skills with category info
      const { data: skillsData, error: skillsError } = await supabase
        .from('skills')
        .select(`
          *,
          skill_categories(name, color_code, icon)
        `)
        .order('name');

      if (skillsError) {
        console.error('Error fetching skills:', skillsError);
        toast({
          title: "Error",
          description: "Failed to fetch skills",
          variant: "destructive"
        });
        return;
      }

      setSkills(skillsData || []);

      // Calculate platform stats
      const totalSkills = skillsData?.length || 0;
      const totalCategories = categoriesData?.length || 0;
      const activeSkills = skillsData?.filter(s => s.is_active).length || 0;
      const highDemandSkills = skillsData?.filter(s => s.market_demand === 'critical' || s.market_demand === 'high').length || 0;
      const certifiedSkills = skillsData?.filter(s => s.certification_available).length || 0;

      setPlatformStats({
        totalSkills,
        totalCategories,
        activeSkills,
        highDemandSkills,
        certifiedSkills,
        skillsGrowth: 15.2 // Mock data - would be calculated from historical data
      });

    } catch (error) {
      console.error('Error fetching skills data:', error);
      toast({
        title: "Error",
        description: "Failed to fetch platform data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter skills based on search and filters
  const filteredSkills = skills.filter(skill => {
    const matchesSearch = skill.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         skill.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         skill.subcategory?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = categoryFilter === 'all' || skill.category_id === categoryFilter;
    const matchesType = typeFilter === 'all' || skill.skill_type === typeFilter;
    const matchesDemand = demandFilter === 'all' || skill.market_demand === demandFilter;
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && skill.is_active) ||
                         (statusFilter === 'inactive' && !skill.is_active);

    return matchesSearch && matchesCategory && matchesType && matchesDemand && matchesStatus;
  });

  const handleCreateCategory = () => {
    setSelectedCategory(null);
    setCategoryDialogOpen(true);
  };

  const handleEditCategory = (category: SkillCategory) => {
    setSelectedCategory(category);
    setCategoryDialogOpen(true);
  };

  const handleCreateSkill = () => {
    setSelectedSkill(null);
    setSkillDialogOpen(true);
  };

  const handleEditSkill = (skill: Skill) => {
    setSelectedSkill(skill);
    setSkillDialogOpen(true);
  };

  const handleDeleteCategory = async (category: SkillCategory) => {
    if (!confirm(`Are you sure you want to delete the "${category.name}" category? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/skill-categories/${category.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete category');
      }

      toast({
        title: "Success",
        description: "Category deleted successfully",
      });

      fetchSkillsData();
    } catch (error: any) {
      console.error('Error deleting category:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete category",
        variant: "destructive"
      });
    }
  };

  const handleDeleteSkill = async (skill: Skill) => {
    if (!confirm(`Are you sure you want to delete the "${skill.name}" skill? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/skills/${skill.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete skill');
      }

      toast({
        title: "Success",
        description: "Skill deleted successfully",
      });

      fetchSkillsData();
    } catch (error: any) {
      console.error('Error deleting skill:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete skill",
        variant: "destructive"
      });
    }
  };

  const getDemandBadgeColor = (demand: string) => {
    switch (demand) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'hard': return 'bg-blue-100 text-blue-800';
      case 'soft': return 'bg-green-100 text-green-800';
      case 'hybrid': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading platform configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 px-[50px] pt-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Platform Configuration</h1>
          <p className="text-muted-foreground">
            Manage skills registry, job role templates, and platform-wide settings
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="skills" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Skills Registry
          </TabsTrigger>
          <TabsTrigger value="job-roles" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Job Role Templates
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Platform Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="skills" className="space-y-6">
          {/* Skills KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden">
            <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
                  <Brain className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Skills</div>
                  <div className="text-3xl font-bold mt-1">{platformStats.totalSkills}</div>
                  <p className="text-xs text-muted-foreground">
                    +{platformStats.skillsGrowth}% growth
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
                  <Palette className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Categories</div>
                  <div className="text-3xl font-bold mt-1">{platformStats.totalCategories}</div>
                  <p className="text-xs text-muted-foreground">
                    Skill categories
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/20">
                  <Target className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">High Demand</div>
                  <div className="text-3xl font-bold mt-1">{platformStats.highDemandSkills}</div>
                  <p className="text-xs text-muted-foreground">
                    Critical & high
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg shadow-orange-500/20">
                  <BookOpen className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Certified</div>
                  <div className="text-3xl font-bold mt-1">{platformStats.certifiedSkills}</div>
                  <p className="text-xs text-muted-foreground">
                    Certification available
                  </p>
                </div>
              </div>
            </div>

            <div className="p-6">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-teal-500 to-cyan-600 text-white shadow-lg shadow-teal-500/20">
                  <Activity className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Active</div>
                  <div className="text-3xl font-bold mt-1">{platformStats.activeSkills}</div>
                  <p className="text-xs text-muted-foreground">
                    Currently active
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Skills Categories Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Skill Categories</CardTitle>
                  <CardDescription>
                    Manage skill categories and their organization
                  </CardDescription>
                </div>
                <Button onClick={handleCreateCategory}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Category
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {skillCategories.map((category) => (
                  <div
                    key={category.id}
                    className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                    style={{ borderColor: category.color_code + '40' }}
                    onClick={() => setCategoryFilter(category.id)}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                        style={{ backgroundColor: category.color_code }}
                      >
                        {category.name.charAt(0)}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-sm">{category.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {category.skill_count || 0} skills
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" size="icon" className="h-6 w-6">
                            <MoreVertical className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditCategory(category)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteCategory(category)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <Badge variant={category.is_active ? 'default' : 'secondary'} className="text-xs">
                      {category.is_active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Skills Management */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Skills Registry</CardTitle>
                  <CardDescription>
                    Manage individual skills within categories
                  </CardDescription>
                </div>
                <Button onClick={handleCreateSkill}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Skill
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search skills..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <div className="flex gap-2">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {skillCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={typeFilter} onValueChange={setTypeFilter}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                      <SelectItem value="soft">Soft</SelectItem>
                      <SelectItem value="hybrid">Hybrid</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={demandFilter} onValueChange={setDemandFilter}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Demand" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Demand</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>

                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Skills Table */}
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[300px]">Skill</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Demand</TableHead>
                      <TableHead>Certification</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center h-24 text-muted-foreground">
                          Loading skills...
                        </TableCell>
                      </TableRow>
                    ) : filteredSkills.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center h-24">
                          <div className="flex flex-col items-center justify-center py-8">
                            <Brain className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Skills Found</h3>
                            <p className="text-muted-foreground text-center mb-4">
                              {searchQuery || categoryFilter !== 'all' || typeFilter !== 'all' || demandFilter !== 'all' || statusFilter !== 'all'
                                ? 'No skills match your current filters'
                                : 'Create your first skill to get started'
                              }
                            </p>
                            <Button onClick={handleCreateSkill}>
                              <Plus className="mr-2 h-4 w-4" />
                              Add Skill
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredSkills.map((skill) => (
                        <TableRow
                          key={skill.id}
                          className="cursor-pointer hover:bg-muted/50"
                          onClick={() => handleEditSkill(skill)}
                        >
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: skill.skill_categories?.color_code }}
                              />
                              <div>
                                <div className="font-medium">{skill.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {skill.subcategory && `${skill.subcategory} • `}
                                  {skill.description && skill.description.length > 50
                                    ? `${skill.description.substring(0, 50)}...`
                                    : skill.description
                                  }
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: skill.skill_categories?.color_code }}
                              />
                              <span className="text-sm">{skill.skill_categories?.name}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge className={getTypeBadgeColor(skill.skill_type)}>
                              {skill.skill_type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getDemandBadgeColor(skill.market_demand)}>
                              {skill.market_demand}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {skill.certification_available ? (
                              <Badge variant="outline" className="text-xs">
                                Available
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm">—</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge variant={skill.is_active ? 'default' : 'secondary'}>
                              {skill.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                <Button variant="ghost" size="icon">
                                  <MoreVertical className="h-4 w-4" />
                                  <span className="sr-only">Actions</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditSkill(skill);
                                  }}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  <span>Edit Skill</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // Handle view details
                                  }}
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteSkill(skill);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Results Summary */}
              {!loading && filteredSkills.length > 0 && (
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div>
                    Showing {filteredSkills.length} of {skills.length} skills
                  </div>
                  <div className="flex items-center gap-4">
                    <span>Active: {filteredSkills.filter(s => s.is_active).length}</span>
                    <span>High Demand: {filteredSkills.filter(s => s.market_demand === 'critical' || s.market_demand === 'high').length}</span>
                    <span>Certified: {filteredSkills.filter(s => s.certification_available).length}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="job-roles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Job Role Templates</CardTitle>
              <CardDescription>
                Manage reusable job role templates across organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-muted-foreground">
                <Target className="h-16 w-16 mx-auto mb-4 text-muted-foreground/50" />
                <h3 className="text-lg font-semibold mb-2">Job Role Templates</h3>
                <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                  Create reusable job role templates that organizations can use as starting points.
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Template
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden">
            <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
                  <TrendingUp className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Skills Growth</div>
                  <div className="text-3xl font-bold mt-1">+{platformStats.skillsGrowth}%</div>
                  <p className="text-xs text-muted-foreground">This month</p>
                </div>
              </div>
            </div>

            <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
                  <Activity className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Platform Usage</div>
                  <div className="text-3xl font-bold mt-1">98.5%</div>
                  <p className="text-xs text-muted-foreground">Uptime</p>
                </div>
              </div>
            </div>

            <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-100 dark:border-gray-800">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/20">
                  <Building2 className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Organizations</div>
                  <div className="text-3xl font-bold mt-1">12</div>
                  <p className="text-xs text-muted-foreground">Active</p>
                </div>
              </div>
            </div>

            <div className="p-6">
              <div className="flex items-center gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-orange-500 to-red-600 text-white shadow-lg shadow-orange-500/20">
                  <Users className="h-6 w-6" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Users</div>
                  <div className="text-3xl font-bold mt-1">1,247</div>
                  <p className="text-xs text-muted-foreground">Platform-wide</p>
                </div>
              </div>
            </div>
          </div>

          {/* Skills Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Skill Categories</CardTitle>
                <CardDescription>Most popular skill categories by count</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {skillCategories
                    .sort((a, b) => (b.skill_count || 0) - (a.skill_count || 0))
                    .slice(0, 5)
                    .map((category, index) => (
                      <div key={category.id} className="flex items-center gap-4">
                        <div className="w-8 text-sm font-medium text-muted-foreground">
                          #{index + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-medium">{category.name}</span>
                            <span className="text-sm text-muted-foreground">
                              {category.skill_count || 0} skills
                            </span>
                          </div>
                          <Progress
                            value={platformStats.totalSkills > 0 ? ((category.skill_count || 0) / platformStats.totalSkills) * 100 : 0}
                            className="h-2"
                          />
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Skill Demand Distribution</CardTitle>
                <CardDescription>Skills by market demand level</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['critical', 'high', 'medium', 'low'].map((demand) => {
                    const count = skills.filter(s => s.market_demand === demand).length;
                    const percentage = platformStats.totalSkills > 0 ? (count / platformStats.totalSkills) * 100 : 0;

                    return (
                      <div key={demand} className="flex items-center gap-4">
                        <div className="w-16 text-sm font-medium capitalize">
                          {demand}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">{count} skills</span>
                            <span className="text-sm text-muted-foreground">
                              {percentage.toFixed(1)}%
                            </span>
                          </div>
                          <Progress value={percentage} className="h-2" />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* CRUD Dialogs */}
      <SkillCategoryFormDialog
        open={categoryDialogOpen}
        onOpenChange={setCategoryDialogOpen}
        category={selectedCategory}
        onSuccess={fetchSkillsData}
      />

      <SkillFormDialog
        open={skillDialogOpen}
        onOpenChange={setSkillDialogOpen}
        skill={selectedSkill}
        categories={skillCategories}
        onSuccess={fetchSkillsData}
      />
    </div>
  );
}
