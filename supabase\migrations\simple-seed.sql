-- Simple seed data for Luna database
-- Insert organizations
INSERT INTO organizations (name, slug, description, industry, size_range, subscription_tier, subdomain, website_url) VALUES 
('TechCorp Solutions', 'techcorp', 'Leading technology solutions provider', 'Technology', 'large', 'enterprise', 'techcorp', 'https://techcorp.com'),
('Creative Agency Inc', 'creative-agency', 'Full-service creative and marketing agency', 'Marketing', 'medium', 'professional', 'creative', 'https://creativeagency.com');

-- Insert teams (using the organization IDs from above)
INSERT INTO teams (organization_id, name, slug, description, max_members, is_public) VALUES 
((SELECT id FROM organizations WHERE slug = 'techcorp'), 'Engineering Team', 'engineering', 'Core software development team', 25, false),
((SELECT id FROM organizations WHERE slug = 'creative-agency'), 'Design Team', 'design', 'Creative design and branding', 12, false);

-- Assign users to teams (using existing user emails)
INSERT INTO team_memberships (user_id, team_id, role, status) VALUES 
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM teams WHERE slug = 'engineering'), 'owner', 'active'),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM teams WHERE slug = 'design'), 'owner', 'active');

-- Create user contexts
INSERT INTO user_contexts (user_id, context_type, active_team_id, active_organization_id) VALUES 
((SELECT id FROM users WHERE email = '<EMAIL>'), 'team', (SELECT id FROM teams WHERE slug = 'engineering'), (SELECT id FROM organizations WHERE slug = 'techcorp')),
((SELECT id FROM users WHERE email = '<EMAIL>'), 'team', (SELECT id FROM teams WHERE slug = 'design'), (SELECT id FROM organizations WHERE slug = 'creative-agency'));
