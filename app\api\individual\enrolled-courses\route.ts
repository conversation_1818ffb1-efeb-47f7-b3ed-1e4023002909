import { NextRequest, NextResponse } from 'next/server'
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { Database } from '@/types/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerComponentClient<Database>({ cookies })
    
    // Check authentication
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's enrolled courses with progress
    const { data: enrollments, error } = await supabase
      .from('user_course_enrollments')
      .select(`
        id,
        enrolled_at,
        started_at,
        completed_at,
        progress_percentage,
        current_module_id,
        current_lesson_id,
        course:courses(
          id,
          name,
          description,
          slug,
          level,
          estimated_duration,
          status,
          cover_image_url,
          tags,
          learning_objectives,
          instructor:users!courses_instructor_id_fkey(
            id,
            full_name,
            email
          ),
          course_modules(
            id,
            name,
            description,
            sequence_order,
            estimated_duration,
            status,
            learning_objectives,
            course_lessons(
              id,
              name,
              description,
              lesson_type,
              sequence_order,
              estimated_duration,
              is_mandatory,
              status
            )
          )
        )
      `)
      .eq('user_id', session.user.id)
      .order('enrolled_at', { ascending: false })

    if (error) {
      console.error('Error fetching enrolled courses:', error)
      return NextResponse.json(
        { error: 'Failed to fetch enrolled courses' },
        { status: 500 }
      )
    }

    // Transform the data for the frontend
    const transformedCourses = enrollments?.map(enrollment => ({
      id: enrollment.course.id,
      enrollmentId: enrollment.id,
      title: enrollment.course.name,
      description: enrollment.course.description || '',
      slug: enrollment.course.slug, // Add slug for navigation
      instructor: enrollment.course.instructor?.full_name || 'Unknown Instructor',
      level: enrollment.course.level?.charAt(0).toUpperCase() + enrollment.course.level?.slice(1) || 'Beginner',
      duration: enrollment.course.estimated_duration || 0,
      thumbnail: enrollment.course.cover_image_url || '/api/placeholder/300/200',
      tags: enrollment.course.tags || [],
      learningObjectives: enrollment.course.learning_objectives || [],
      
      // Progress information
      progressPercentage: enrollment.progress_percentage || 0,
      enrolledAt: enrollment.enrolled_at,
      startedAt: enrollment.started_at,
      completedAt: enrollment.completed_at,
      currentModuleId: enrollment.current_module_id,
      currentLessonId: enrollment.current_lesson_id,
      
      // Status based on progress
      status: enrollment.completed_at 
        ? 'completed' 
        : enrollment.started_at 
          ? 'in_progress' 
          : 'not_started',
      
      // Modules and lessons
      modules: enrollment.course.course_modules?.map(module => ({
        id: module.id,
        name: module.name,
        description: module.description || '',
        sequenceOrder: module.sequence_order,
        estimatedDuration: module.estimated_duration || 0,
        status: module.status,
        learningObjectives: module.learning_objectives || [],
        lessons: module.course_lessons?.map(lesson => ({
          id: lesson.id,
          name: lesson.name,
          description: lesson.description || '',
          lessonType: lesson.lesson_type,
          sequenceOrder: lesson.sequence_order,
          estimatedDuration: lesson.estimated_duration || 0,
          isMandatory: lesson.is_mandatory,
          status: lesson.status
        })).sort((a, b) => a.sequenceOrder - b.sequenceOrder) || []
      })).sort((a, b) => a.sequenceOrder - b.sequenceOrder) || []
    })) || []

    // Separate courses by status
    const completedCourses = transformedCourses.filter(course => course.status === 'completed')
    const inProgressCourses = transformedCourses.filter(course => course.status === 'in_progress')
    const notStartedCourses = transformedCourses.filter(course => course.status === 'not_started')

    return NextResponse.json({
      courses: transformedCourses,
      summary: {
        total: transformedCourses.length,
        completed: completedCourses.length,
        inProgress: inProgressCourses.length,
        notStarted: notStartedCourses.length
      },
      categorized: {
        completed: completedCourses,
        inProgress: inProgressCourses,
        notStarted: notStartedCourses
      }
    })

  } catch (error) {
    console.error('Error in enrolled courses API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
