# 🔧 Troubleshooting Guide

Common issues and solutions for the Luna Skills Platform deployment and development.

## 🚨 Critical Deployment Issues

### "self is not defined" Error

**Error Message:**
```
unhandledRejection ReferenceError: self is not defined
    at Object.<anonymous> (.next/server/vendors.js:1:1)
```

**Cause**: Browser-specific libraries (like `canvas-confetti`) being executed during server-side rendering.

**Solution**:
1. Use dynamic imports for client-side only libraries:
   ```typescript
   // ❌ Wrong
   import confetti from 'canvas-confetti'
   
   // ✅ Correct
   const triggerConfetti = async () => {
     if (typeof window !== "undefined") {
       try {
         const confetti = (await import('canvas-confetti')).default
         confetti({ /* options */ })
       } catch (error) {
         console.warn('Failed to load confetti:', error)
       }
     }
   }
   ```

2. Add proper webpack configuration:
   ```javascript
   // next.config.mjs
   webpack: (config, { isServer }) => {
     if (isServer) {
       config.resolve.fallback = {
         ...config.resolve.fallback,
         fs: false,
         net: false,
         tls: false,
       };
     }
     return config
   }
   ```

### "Cannot find module './vendor-chunks/@supabase.js'" Error

**Error Message:**
```
Error: Cannot find module './vendor-chunks/@supabase.js'
```

**Cause**: Webpack vendor chunk splitting conflicts with Supabase imports.

**Solution**:
1. Simplify webpack configuration:
   ```javascript
   // Remove complex splitChunks configuration
   // Keep minimal webpack settings
   ```

2. Clean build cache:
   ```bash
   npm run clean
   npm run build
   ```

3. Ensure proper serverExternalPackages configuration:
   ```javascript
   serverExternalPackages: [],
   ```

### ENOENT: no such file or directory '_document.js'

**Error Message:**
```
Error: ENOENT: no such file or directory, open '.next/server/pages/_document.js'
```

**Cause**: Corrupted build cache or conflicting polyfill imports.

**Solution**:
1. Clean build cache:
   ```bash
   rm -rf .next
   npm cache clean --force
   npm run build
   ```

2. Remove problematic polyfill imports from `next.config.mjs`
3. Avoid importing polyfills at build time

## 🔧 Development Issues

### 404 Bad Request Errors Locally

**Symptoms**: Getting 404 errors when accessing the site locally.

**Causes & Solutions**:

1. **Environment Variables Not Loaded**:
   ```bash
   # Check if .env.local exists and has correct variables
   cat .env.local | grep SUPABASE
   
   # Restart dev server after env changes
   npm run dev
   ```

2. **Supabase Connection Issues**:
   - Verify Supabase project is active
   - Check URL and keys are correct
   - Test with: `http://localhost:3000/api/test-connection`

3. **Port Conflicts**:
   ```bash
   # Check if port 3000 is in use
   lsof -i :3000
   
   # Use different port
   npm run dev -- -p 3001
   ```

### Authentication Issues

**Problem**: Users can't log in or sessions expire immediately.

**Solutions**:

1. **Check Supabase Configuration**:
   ```bash
   # Test API connection
   curl http://localhost:3000/api/auth/user
   ```

2. **Verify RLS Policies**:
   - Check if Row Level Security is properly configured
   - Ensure policies allow user access
   - Test with platform admin account

3. **Clear Browser Data**:
   - Clear cookies and localStorage
   - Try incognito/private browsing mode

### Database Connection Errors

**Problem**: Database queries failing or timing out.

**Solutions**:

1. **Check Supabase Status**:
   - Visit Supabase dashboard
   - Check project health
   - Verify database is not paused

2. **Test Connection**:
   ```bash
   # Use diagnostic endpoint
   curl http://localhost:3000/api/test-connection
   ```

3. **Check Service Role Key**:
   - Ensure `SUPABASE_SERVICE_ROLE_KEY` is set
   - Verify key has proper permissions
   - Check key hasn't expired

## 📦 Build Issues

### Dependency Conflicts

**Problem**: npm install fails with peer dependency warnings.

**Solution**:
```bash
# Use legacy peer deps flag
npm install --legacy-peer-deps

# Or use yarn
yarn install
```

### Memory Issues During Build

**Problem**: Build process runs out of memory.

**Solutions**:

1. **Increase Node.js Memory**:
   ```bash
   # Temporary fix
   NODE_OPTIONS="--max-old-space-size=4096" npm run build
   ```

2. **Update package.json**:
   ```json
   {
     "scripts": {
       "build": "NODE_OPTIONS='--max-old-space-size=4096' next build"
     }
   }
   ```

### TypeScript Errors

**Problem**: TypeScript compilation errors during build.

**Solutions**:

1. **Skip TypeScript Errors** (temporary):
   ```javascript
   // next.config.mjs
   typescript: {
     ignoreBuildErrors: true,
   }
   ```

2. **Fix Type Issues**:
   ```bash
   # Check TypeScript errors
   npx tsc --noEmit
   
   # Update type definitions
   npm install --save-dev @types/node @types/react @types/react-dom
   ```

## 🌐 Production Issues

### Vercel Deployment Failures

**Problem**: Deployment fails on Vercel.

**Solutions**:

1. **Check Build Logs**:
   - Review Vercel deployment logs
   - Look for specific error messages
   - Check function timeout issues

2. **Environment Variables**:
   - Ensure all required env vars are set in Vercel
   - Check variable names match exactly
   - Verify no trailing spaces or special characters

3. **Build Settings**:
   ```json
   // vercel.json
   {
     "buildCommand": "npm run build",
     "outputDirectory": ".next",
     "framework": "nextjs"
   }
   ```

### Performance Issues

**Problem**: Slow page loads or high response times.

**Solutions**:

1. **Enable Caching**:
   ```javascript
   // Add caching headers
   export const revalidate = 3600 // 1 hour
   ```

2. **Optimize Images**:
   ```jsx
   // Use Next.js Image component
   import Image from 'next/image'
   ```

3. **Database Optimization**:
   - Add proper indexes
   - Optimize queries
   - Use connection pooling

## 🔍 Diagnostic Tools

### Built-in Diagnostic Endpoints

1. **Connection Test**: `GET /api/test-connection`
   - Tests basic API functionality
   - Checks environment variables
   - Verifies Supabase connection

2. **Auth Test**: `GET /api/auth/user`
   - Tests authentication system
   - Checks user session
   - Verifies employment relationships

3. **Database Test**: `GET /api/test-db`
   - Tests database connectivity
   - Checks RLS policies
   - Verifies table access

### Debug Mode

Enable debug logging:

```bash
# Set debug environment variable
DEBUG=luna:* npm run dev
```

### Browser Developer Tools

1. **Network Tab**: Check for failed API requests
2. **Console**: Look for JavaScript errors
3. **Application Tab**: Check localStorage and cookies
4. **Performance Tab**: Analyze page load times

## 📞 Getting Help

### Before Asking for Help

1. Check this troubleshooting guide
2. Search existing GitHub issues
3. Test with diagnostic endpoints
4. Check Vercel deployment logs
5. Verify environment variables

### Reporting Issues

When reporting issues, include:

1. **Error Message**: Full error text and stack trace
2. **Environment**: Development/Production, OS, Node version
3. **Steps to Reproduce**: Detailed steps to recreate the issue
4. **Expected vs Actual**: What should happen vs what actually happens
5. **Diagnostic Results**: Output from `/api/test-connection`

### Support Channels

- **GitHub Issues**: For bugs and feature requests
- **Documentation**: Check all relevant docs first
- **Community**: GitHub Discussions for questions

---

**Remember**: Most issues can be resolved by cleaning the build cache and ensuring environment variables are properly configured.
