'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { createBrowserClient } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft,
  Mail,
  Phone,
  Calendar,
  Award,
  BookOpen,
  Code,
  Briefcase,
  Target,
  TrendingUp,
  Activity,
  Users,
  Star,
  Clock,
  CheckCircle2
} from 'lucide-react';

// Define types
interface StaffProfile {
  id: string;
  full_name: string;
  slug: string;
  email: string;
  phone?: string;
  job_title: string | null;
  hire_date: string | null;
  status: 'active' | 'inactive' | 'on_leave';
  performance_score: number;
  training_progress: number;
  skills: string[];
  avatar_url?: string;
  bio?: string;
}

interface Department {
  id: string;
  name: string;
  slug: string;
  description: string | null;
}

export default function StaffProfilePage() {
  const params = useParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [staff, setStaff] = useState<StaffProfile | null>(null);
  const [department, setDepartment] = useState<Department | null>(null);

  const orgSlug = params.orgSlug as string;
  const departmentSlug = params.departmentSlug as string;
  const staffSlug = params.staffSlug as string;

  const supabase = createBrowserClient();

  useEffect(() => {
    fetchDepartmentData();
    fetchStaffProfile();
  }, [orgSlug, departmentSlug, staffSlug]);

  const fetchDepartmentData = async () => {
    try {
      // Get organization ID first
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError);
        return;
      }

      // Find department by slug
      const { data: departmentsData, error: deptError } = await supabase
        .from('departments')
        .select('*')
        .eq('organization_id', orgData.id);

      if (deptError) {
        console.error('Error fetching departments:', deptError);
        return;
      }

      const department = departmentsData?.find(dept => {
        const deptSlug = dept.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        return deptSlug === departmentSlug;
      });

      if (department) {
        setDepartment({
          ...department,
          slug: departmentSlug
        });
      }
    } catch (error) {
      console.error('Error fetching department:', error);
    }
  };

  const fetchStaffProfile = async () => {
    try {
      setLoading(true);

      // Get organization ID first
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('id')
        .eq('slug', orgSlug)
        .single();

      if (orgError) {
        console.error('Error fetching organization:', orgError);
        return;
      }

      // Find department by slug
      const { data: departmentsData, error: deptError } = await supabase
        .from('departments')
        .select('id, name')
        .eq('organization_id', orgData.id);

      if (deptError) {
        console.error('Error fetching departments:', deptError);
        return;
      }

      const department = departmentsData?.find(dept => {
        const deptSlug = dept.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        return deptSlug === departmentSlug;
      });

      if (!department) {
        console.error('Department not found');
        return;
      }

      // Get employment relationships for this department
      const { data: employmentData, error: empError } = await supabase
        .from('employment_relationships')
        .select('*')
        .eq('department_id', department.id)
        .eq('status', 'active');

      if (empError) {
        console.error('Error fetching employment relationships:', empError);
        return;
      }

      if (!employmentData || employmentData.length === 0) {
        console.log('No employment relationships found');
        return;
      }

      // Get users data
      const userIds = employmentData.map(emp => emp.user_id);
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, full_name, email, bio')
        .in('id', userIds);

      if (usersError) {
        console.error('Error fetching users:', usersError);
        return;
      }

      // Get individuals data
      const { data: individuals, error: individualsError } = await supabase
        .from('individuals')
        .select('user_id, skills, profile_image_url, learning_status, completed_courses, total_learning_hours')
        .in('user_id', userIds);

      if (individualsError) {
        console.error('Error fetching individuals:', individualsError);
        // Continue without individuals data
      }

      // Find the staff member that matches the slug
      const staffMember = employmentData?.find(emp => {
        const user = users?.find(u => u.id === emp.user_id);
        if (!user) return false;
        const userSlug = user.full_name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
        return userSlug === staffSlug;
      });

      if (staffMember) {
        const user = users?.find(u => u.id === staffMember.user_id);
        const individual = individuals?.find(ind => ind.user_id === staffMember.user_id);

        if (!user) {
          console.error('User not found for staff member');
          return;
        }

        // Extract skills from individuals table
        let skills: string[] = [];
        if (individual?.skills && Array.isArray(individual.skills)) {
          skills = individual.skills.map(skill =>
            typeof skill === 'string' ? skill : JSON.stringify(skill)
          );
        }

        // Calculate training progress and performance from individuals data
        const trainingProgress = individual?.total_learning_hours
          ? Math.min(Math.floor((individual.total_learning_hours / 100) * 100), 100)
          : Math.floor(Math.random() * 40) + 60;

        const performanceScore = individual?.completed_courses
          ? Math.min(Math.floor((individual.completed_courses / 10) * 100), 100)
          : Math.floor(Math.random() * 30) + 70;

        setStaff({
          id: user.id,
          full_name: user.full_name,
          slug: staffSlug,
          email: user.email,
          phone: null, // Not stored in current schema
          job_title: staffMember.job_title || (staffMember.role === 'department_admin' ? 'Department Head' : 'Staff Member'),
          hire_date: staffMember.hire_date,
          status: staffMember.status as 'active' | 'inactive' | 'on_leave',
          performance_score: performanceScore,
          training_progress: trainingProgress,
          skills: skills,
          avatar_url: individual?.profile_image_url || null,
          bio: user.bio || 'Professional team member focused on delivering excellent results.'
        });
      }
    } catch (error) {
      console.error('Error fetching staff profile:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!staff) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">Staff Member Not Found</h2>
        <Button onClick={() => router.back()}>Go Back</Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Profile Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          
          <div className="relative">
            <Avatar className="h-24 w-24">
              <AvatarImage src={staff.avatar_url} alt={staff.full_name} />
              <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                {staff.full_name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            {staff.status === 'active' && (
              <div className="absolute -bottom-2 -right-2 h-6 w-6 bg-green-500 border-4 border-white rounded-full"></div>
            )}
          </div>

          <div>
            <h1 className="text-3xl font-bold tracking-tight">{staff.full_name}</h1>
            <p className="text-xl text-muted-foreground mt-1">{staff.job_title}</p>
            <p className="text-sm text-muted-foreground mt-1">{department?.name}</p>
            
            <div className="flex items-center gap-4 mt-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{staff.email}</span>
              </div>
              {staff.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{staff.phone}</span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  Joined {staff.hire_date ? new Date(staff.hire_date).toLocaleDateString() : 'Unknown'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{staff.performance_score}%</div>
            <div className="text-sm text-muted-foreground">Performance</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{staff.training_progress}%</div>
            <div className="text-sm text-muted-foreground">Training</div>
          </div>
          <Badge
            variant={staff.status === 'active' ? 'default' : 'secondary'}
            className={staff.status === 'active' ? 'bg-green-100 text-green-800' : ''}
          >
            {staff.status === 'active' ? 'Active' : staff.status === 'on_leave' ? 'On Leave' : 'Inactive'}
          </Badge>
        </div>
      </div>

      {/* Bio */}
      {staff.bio && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              About
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{staff.bio}</p>
          </CardContent>
        </Card>
      )}

      {/* Skills */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Code className="h-5 w-5" />
            Skills & Technologies
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {staff.skills.map((skill) => (
              <Badge 
                key={skill} 
                variant="secondary" 
                className="px-3 py-1"
              >
                {skill}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Dashboard */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="training">Training</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overall Score</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{staff.performance_score}%</div>
                <Progress value={staff.performance_score} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Training Progress</CardTitle>
                <BookOpen className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{staff.training_progress}%</div>
                <Progress value={staff.training_progress} className="mt-2" />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Goal Achievement</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87%</div>
                <Progress value={87} className="mt-2" />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="training" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Training History</CardTitle>
              <CardDescription>Completed courses and certifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div className="flex-1">
                    <div className="font-medium">Advanced React Development</div>
                    <div className="text-sm text-muted-foreground">Completed 2 weeks ago</div>
                  </div>
                  <Badge variant="outline">Certified</Badge>
                </div>
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-500" />
                  <div className="flex-1">
                    <div className="font-medium">AWS Solutions Architect</div>
                    <div className="text-sm text-muted-foreground">Completed 1 month ago</div>
                  </div>
                  <Badge variant="outline">Certified</Badge>
                </div>
                <div className="flex items-center gap-3">
                  <Clock className="h-5 w-5 text-yellow-500" />
                  <div className="flex-1">
                    <div className="font-medium">Kubernetes Administration</div>
                    <div className="text-sm text-muted-foreground">In progress - 60% complete</div>
                  </div>
                  <Progress value={60} className="w-24" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Goals</CardTitle>
              <CardDescription>Active objectives and milestones</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Target className="h-5 w-5 text-blue-500" />
                  <div className="flex-1">
                    <div className="font-medium">Complete Microservices Architecture Course</div>
                    <div className="text-sm text-muted-foreground">Due: End of month</div>
                  </div>
                  <Progress value={75} className="w-24" />
                </div>
                <div className="flex items-center gap-3">
                  <Target className="h-5 w-5 text-blue-500" />
                  <div className="flex-1">
                    <div className="font-medium">Mentor 2 Junior Developers</div>
                    <div className="text-sm text-muted-foreground">Due: End of quarter</div>
                  </div>
                  <Progress value={50} className="w-24" />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>Latest platform interactions and achievements</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Activity className="h-5 w-5 text-green-500" />
                  <div className="flex-1">
                    <div className="font-medium">Completed Advanced React course</div>
                    <div className="text-sm text-muted-foreground">2 days ago</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Activity className="h-5 w-5 text-blue-500" />
                  <div className="flex-1">
                    <div className="font-medium">Updated project documentation</div>
                    <div className="text-sm text-muted-foreground">1 week ago</div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Activity className="h-5 w-5 text-purple-500" />
                  <div className="flex-1">
                    <div className="font-medium">Participated in code review session</div>
                    <div className="text-sm text-muted-foreground">1 week ago</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
