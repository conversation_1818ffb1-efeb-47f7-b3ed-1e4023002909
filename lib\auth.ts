/**
 * Authentication Module - Legacy Compatibility Layer
 *
 * This file maintains backward compatibility while the codebase transitions
 * to the new modular authentication system in lib/auth/
 *
 * @deprecated Use lib/auth/ modules directly for new code
 */

// Re-export everything from the new modular auth system
export * from './auth/index';

// Legacy imports for backward compatibility
import { SupabaseClient, User } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import type { Database } from '@/types/database.types';

// All authentication functionality has been moved to lib/auth/ modules
// This file now serves as a compatibility layer for existing imports

// Legacy aliases for backward compatibility
export async function requireAuth() {
  const { getAuthenticatedUser } = await import('./auth/core');
  return getAuthenticatedUser();
}

export async function getServerUser() {
  const { getServerAuthUser } = await import('./auth/core');
  return getServerAuthUser();
}

export async function requireIndividualUser() {
  const { getAuthenticatedUser } = await import('./auth/core');
  return getAuthenticatedUser();
}

export async function requireOrganizationAccess(organizationId?: string) {
  const { requireOrganizationAdmin } = await import('./auth/middleware');
  if (organizationId) {
    return requireOrganizationAdmin(organizationId);
  }
  const { getAuthenticatedUser } = await import('./auth/core');
  return getAuthenticatedUser();
}
