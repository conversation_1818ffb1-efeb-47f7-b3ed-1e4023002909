import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { cookies } from 'next/headers';
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database.types';

export const maxDuration = 60; // Set to maximum allowed for hobby plan

// Create admin client to bypass Row Level Security policies
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const adminClient = createClient<Database>(supabaseUrl, supabaseServiceKey);

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}

export async function POST(request: NextRequest) {
  // Set CORS headers with more permissive settings
  const origin = request.headers.get('origin') || '*';
  const headers = {
    'Access-Control-Allow-Origin': origin,
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Allow-Credentials': 'true'
  };
  
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, { status: 200, headers });
  }
  
  try {
    // Create Supabase server client with proper async cookie handling
    const cookieStore = cookies();
    const supabase = createServerComponentClient<Database>({ cookies: () => cookieStore });
    
    // Get the current user session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Session error:', sessionError);
      return NextResponse.json(
        { error: `Authentication error: ${sessionError.message}` },
        { status: 401, headers }
      );
    }
    
    const session = sessionData.session;
    
    if (!session || !session.user) {
      console.error('No authenticated user found');
      return NextResponse.json(
        { error: 'Unauthorized - Please sign in again' },
        { status: 401, headers }
      );
    }
    
    const user = session.user;
    console.log('Authenticated as user:', user.id);
    
    // Get user profile - use adminClient to bypass RLS policies
    const { data: profile, error: profileError } = await adminClient
      .from('prospects')
      .select('id')
      .eq('user_id', user.id)
      .single();
    
    if (profileError || !profile) {
      console.error('Profile error:', profileError);
      return NextResponse.json(
        { error: profileError ? `Profile error: ${profileError.message}` : 'Profile not found' },
        { status: 404, headers }
      );
    }
    
    // Parse the form data
    let formData;
    try {
      formData = await request.formData();
    } catch (e: any) {
      console.error('FormData parse error:', e);
      return NextResponse.json(
        { error: `Failed to parse form data: ${e.message}` },
        { status: 400, headers }
      );
    }
    
    const file = formData.get('file') as File;
    const fileType = formData.get('fileType') as string;
    
    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400, headers }
      );
    }
    
    // Convert File to Buffer for Supabase upload
    let fileBuffer;
    try {
      fileBuffer = Buffer.from(await file.arrayBuffer());
    } catch (e: any) {
      console.error('File buffer error:', e);
      return NextResponse.json(
        { error: `Failed to process file: ${e.message}` },
        { status: 400, headers }
      );
    }
    
    const fileContentType = file.type;
    
    // Log file details for debugging
    console.log('File details:', {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: file.lastModified
    });
    
    // Validate file type and size
    let maxSize = 5 * 1024 * 1024; // 5MB default
    let allowedTypes: string[] = [];
    let storageBucket = '';
    let columnToUpdate = '';
    
    switch (fileType) {
      case 'resume':
        allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        maxSize = 5 * 1024 * 1024; // 5MB
        storageBucket = 'resumes';
        columnToUpdate = 'resume_url';
        break;
        
      case 'avatar':
        allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        maxSize = 2 * 1024 * 1024; // 2MB
        storageBucket = 'profile-images';
        columnToUpdate = 'profile_image';
        break;
        
      case 'video':
        allowedTypes = ['video/mp4', 'video/webm', 'video/quicktime', 'video/x-matroska'];
        maxSize = 50 * 1024 * 1024; // Increased to 50MB
        storageBucket = 'profile-videos';
        columnToUpdate = 'intro_video_url';
        break;
        
      default:
        return NextResponse.json(
          { error: `Invalid file type: ${fileType}` },
          { status: 400, headers }
        );
    }
    
    // Skip content type validation for blobs from recording (they might have a generic type)
    if (fileType === 'video' && (
      file.type === '' || 
      file.type === 'application/octet-stream' || 
      file.name.startsWith('recorded-video-')
    )) {
      console.log('Allowing recorded video with non-standard MIME type');
      // Continue with upload, assuming it's a valid recorded video
    } else if (!allowedTypes.includes(file.type)) {
      console.log(`File type validation failed: ${file.type} not in [${allowedTypes.join(', ')}]`);
      return NextResponse.json(
        { error: `Invalid file type: ${file.type}. Allowed types: ${allowedTypes.join(', ')}` },
        { status: 400, headers }
      );
    }
    
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `File too large. Maximum size: ${maxSize / (1024 * 1024)}MB` },
        { status: 400, headers }
      );
    }
    
    // Upload file to storage
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const safeFileName = file.name.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_.-]/g, '');

    // For videos, ensure we have a consistent naming pattern
    let fileName = '';
    let displayName = file.name; // Keep original name for display

    if (fileType === 'video') {
      const isRecorded = file.name.startsWith('recorded-video-');
      const sourceType = isRecorded ? 'recorded' : 'uploaded';
      fileName = `user_${user.id}_${sourceType}_${timestamp}_${safeFileName}`;
    } else if (fileType === 'resume') {
      // For resumes, use a shorter internal name but keep original for display
      fileName = `resume_${profile.id}_${timestamp}.${file.name.split('.').pop()}`;
      displayName = file.name; // Keep original filename for user display
    } else {
      fileName = `${profile.id}_${uuidv4()}_${safeFileName}`;
    }
    
    console.log(`Uploading file ${fileName} to bucket ${storageBucket}, file type: ${fileContentType}, size: ${file.size} bytes`);
    
    try {
      // Set appropriate content type
      let options = {
        cacheControl: '3600',
        upsert: true,
        contentType: fileContentType || 'application/octet-stream'
      };
      
      // For recorded videos, explicitly set the content type
      if (fileType === 'video') {
        if (file.name.startsWith('recorded-video-')) {
          options.contentType = 'video/webm';
        } else {
          options.contentType = fileContentType || 'video/mp4';
        }
      }
      
      // Upload the file - use adminClient to bypass potential bucket RLS issues
      const { data: uploadData, error: uploadError } = await adminClient.storage
        .from(storageBucket)
        .upload(fileName, fileBuffer, options);
      
      if (uploadError) {
        console.error('Upload error:', uploadError);
        return NextResponse.json(
          { error: `Failed to upload file: ${uploadError.message}` },
          { status: 500, headers }
        );
      }
      
      // Get public URL
      const { data: { publicUrl } } = adminClient.storage
        .from(storageBucket)
        .getPublicUrl(fileName);
      
      console.log(`File uploaded successfully. Public URL: ${publicUrl}`);
      
      // Update user profile with file URL
      let updateData: any = {};
      updateData[columnToUpdate] = publicUrl;
      
      // If updating avatar in auth.users
      if (fileType === 'avatar') {
        console.log('Updating avatar in auth.users with URL:', publicUrl);
        
        const { data: authUserData, error: userUpdateError } = await supabase.auth
          .updateUser({
            data: { avatar_url: publicUrl }
          });
          
        if (userUpdateError) {
          console.error('User update error:', userUpdateError);
          // Continue anyway - the avatar will still be updated in the profile table
        } else {
          console.log('Auth user avatar updated successfully:', authUserData);
        }
        
        // Also update the avatar_url in the users table
        const { error: userTableUpdateError } = await adminClient
          .from('users')
          .update({ avatar_url: publicUrl })
          .eq('id', user.id);
        
        if (userTableUpdateError) {
          console.error('User table update error:', userTableUpdateError);
        } else {
          console.log('User table avatar updated successfully');
        }
      }
      
      // Update profile record - ALWAYS use adminClient to bypass RLS
      console.log('Updating profile record with data:', updateData);
      console.log('For profile ID:', profile.id);
      
      const { data: updatedProfile, error: profileUpdateError } = await adminClient
        .from('prospects')
        .update(updateData)
        .eq('id', profile.id)
        .select();
      
      if (profileUpdateError) {
        console.error('Profile update error:', profileUpdateError);
        return NextResponse.json(
          { error: `Failed to update profile: ${profileUpdateError.message}` },
          { status: 500, headers }
        );
      } else {
        console.log('Profile updated successfully:', updatedProfile);
      }
      
      // Return success with URL
      return NextResponse.json({
        message: 'File uploaded successfully',
        url: publicUrl
      }, { headers });
    } catch (error: any) {
      console.error('Error in file upload process:', error);
      return NextResponse.json(
        { error: `File upload process error: ${error.message || 'Unknown error'}` },
        { status: 500, headers }
      );
    }
  } catch (error: any) {
    console.error('Error in upload-file route:', error);
    return NextResponse.json(
      { error: `Internal server error: ${error.message || 'Unknown error'}` },
      { status: 500, headers }
    );
  }
} 